#!/usr/bin/env python3
"""
Validación rápida de la modificación realizada
"""

import oracledb
import duckdb
import boto3

def validacion_rapida():
    """Validación rápida de los resultados después de la modificación"""
    print("🥷 VALIDACIÓN RÁPIDA DESPUÉS DE MODIFICACIÓN")
    print("=" * 60)
    
    try:
        # Oracle
        connection = oracledb.connect(
            user='usr_datalake',
            password='U2024b1mD4t4l4k5',
            dsn='10.240.131.10:1521/MMONEY'
        )
        cursor = connection.cursor()
        
        # DuckDB + S3
        conn = duckdb.connect()
        session = boto3.Session()
        credentials = session.get_credentials().get_frozen_credentials()
        
        conn.sql("INSTALL httpfs;")
        conn.sql("LOAD httpfs;")
        conn.sql("SET s3_region='us-east-1';")
        conn.sql("SET s3_use_ssl=true;")
        conn.sql("SET s3_url_style='path';")
        conn.sql(f"SET s3_access_key_id='{credentials.access_key}';")
        conn.sql(f"SET s3_secret_access_key='{credentials.secret_key}';")
        if credentials.token:
            conn.sql(f"SET s3_session_token='{credentials.token}';")
        
        fecha = '2025-06-18'
        
        # Contar Oracle
        cursor.execute(f"""
            SELECT COUNT(*)
            FROM USR_DATALAKE.LOG_TRX_FINAL
            WHERE TRUNC(TO_DATE("DateTime", 'YYYY-MM-DD HH24:MI:SS')) = TO_DATE('{fecha}', 'YYYY-MM-DD')
        """)
        oracle_count = cursor.fetchone()[0]
        
        # Contar S3 nuevo
        s3_count = conn.execute(f"""
            SELECT COUNT(*)
            FROM read_parquet('/home/<USER>/REPORTE_2025/reports/generate/S3_LOG_TRANSACCIONES/TEMP_LOGS_TRANSACCIONES/20250618/LOG_TRX_FINAL.parquet')
        """).fetchone()[0]
        
        print(f"📊 COMPARACIÓN DESPUÉS DE MODIFICACIÓN:")
        print(f"  Oracle LOG_TRX_FINAL: {oracle_count:,} registros")
        print(f"  S3 LOG_TRX_FINAL:     {s3_count:,} registros")
        print(f"  Diferencia:           {s3_count - oracle_count:+,} registros")
        
        if s3_count == oracle_count:
            print(f"  ✅ HOMOLOGACIÓN PERFECTA: 100% idéntico")
        else:
            print(f"  ⚠️ Aún hay diferencia: {abs(s3_count - oracle_count)} registros")
            
            # Verificar si aún tenemos los casos problemáticos
            casos_57 = [
                '175020514200061', '175020514200182', '175020514200241', '175020514200421', '175020514200530',
                '175020514200646', '175020514200728', '175020514200898', '175020514200959', '175020514201099',
                '175020514201116', '175020514201208', '175020514201436', '175020514201546', '175020514201647',
                '175020514201723', '175020514201817', '175020514201946', '175020514202084', '175020514202162',
                '175020514202275', '175020514202363', '175020514202579', '175020514202681', '175020514202724',
                '175020514202865', '175020514202972', '175020514203090', '175020514203132', '175020514203215',
                '175020514203322', '175020514203490', '175020514203653', '175020514203745', '175020514203909',
                '175020514204059', '175020514204182', '175020514204290', '175020514204351', '175020514204462',
                '175020514204530', '175020514204717', '175020514204983', '175020514205033', '175020514205132',
                '175020514205361', '175020514205488', '175020514205532', '175020514205638', '175020514205826',
                '175020514205920', '175020514206091', '175020514206185', '175020514299909', '175029151203224',
                '175029151206399', '5000077909'
            ]
            
            casos_str = "', '".join(casos_57)
            
            casos_aun_presentes = conn.execute(f"""
                SELECT COUNT(*)
                FROM read_parquet('/home/<USER>/REPORTE_2025/reports/generate/S3_LOG_TRANSACCIONES/TEMP_LOGS_TRANSACCIONES/20250618/LOG_TRX_FINAL.parquet')
                WHERE "TransactionID" IN ('{casos_str}')
            """).fetchone()[0]
            
            print(f"  📊 Casos de los 57 aún presentes: {casos_aun_presentes}")
            
            if casos_aun_presentes > 0:
                print(f"  ⚠️ Los casos problemáticos AÚN están presentes")
                print(f"  🔍 Necesitamos identificar otra lógica que los está generando")
                
                # Analizar qué tipos de casos aún tenemos
                tipos_presentes = conn.execute(f"""
                    SELECT "TransactionType", "Context", COUNT(*) as count
                    FROM read_parquet('/home/<USER>/REPORTE_2025/reports/generate/S3_LOG_TRANSACCIONES/TEMP_LOGS_TRANSACCIONES/20250618/LOG_TRX_FINAL.parquet')
                    WHERE "TransactionID" IN ('{casos_str}')
                    GROUP BY "TransactionType", "Context"
                    ORDER BY count DESC
                """).fetchall()
                
                print(f"  📋 Tipos de casos aún presentes:")
                for row in tipos_presentes:
                    print(f"    {row[0]} | {row[1]} | {row[2]} casos")
            else:
                print(f"  ✅ Los 57 casos problemáticos fueron eliminados")
                print(f"  🔍 Pero aún hay {s3_count - oracle_count} registros adicionales diferentes")
        
        cursor.close()
        connection.close()
        conn.close()
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    validacion_rapida()
