#!/usr/bin/env python3
"""
Investigación profunda: ¿Dónde Oracle filtra estos 57 casos?
"""

import oracledb
import duckdb
import boto3

def investigacion_profunda():
    """Investigar exactamente dónde Oracle filtra los 57 casos"""
    print("🥷 INVESTIGACIÓN PROFUNDA: ¿DÓNDE ORACLE FILTRA LOS 57 CASOS?")
    print("=" * 80)
    
    try:
        # Oracle
        connection = oracledb.connect(
            user='usr_datalake',
            password='U2024b1mD4t4l4k5',
            dsn='10.240.131.10:1521/MMONEY'
        )
        cursor = connection.cursor()
        
        # DuckDB + S3
        conn = duckdb.connect()
        session = boto3.Session()
        credentials = session.get_credentials().get_frozen_credentials()
        
        conn.sql("INSTALL httpfs;")
        conn.sql("LOAD httpfs;")
        conn.sql("SET s3_region='us-east-1';")
        conn.sql("SET s3_use_ssl=true;")
        conn.sql("SET s3_url_style='path';")
        conn.sql(f"SET s3_access_key_id='{credentials.access_key}';")
        conn.sql(f"SET s3_secret_access_key='{credentials.secret_key}';")
        if credentials.token:
            conn.sql(f"SET s3_session_token='{credentials.token}';")
        
        fecha = '2025-06-18'
        
        # Los 57 casos problemáticos
        casos_57 = [
            '175020514200061', '175020514200182', '175020514200241', '175020514200421', '175020514200530',
            '175020514200646', '175020514200728', '175020514200898', '175020514200959', '175020514201099',
            '175020514201116', '175020514201208', '175020514201436', '175020514201546', '175020514201647',
            '175020514201723', '175020514201817', '175020514201946', '175020514202084', '175020514202162',
            '175020514202275', '175020514202363', '175020514202579', '175020514202681', '175020514202724',
            '175020514202865', '175020514202972', '175020514203090', '175020514203132', '175020514203215',
            '175020514203322', '175020514203490', '175020514203653', '175020514203745', '175020514203909',
            '175020514204059', '175020514204182', '175020514204290', '175020514204351', '175020514204462',
            '175020514204530', '175020514204717', '175020514204983', '175020514205033', '175020514205132',
            '175020514205361', '175020514205488', '175020514205532', '175020514205638', '175020514205826',
            '175020514205920', '175020514206091', '175020514206185', '175020514299909', '175029151203224',
            '175029151206399', '5000077909'
        ]
        
        casos_str = "', '".join(casos_57)
        
        print("1️⃣ VERIFICANDO SI ESTOS CASOS EXISTEN EN ORACLE MTX_TRANSACTION_HEADER:")
        print("-" * 70)
        
        # Verificar si existen en Oracle MTX_TRANSACTION_HEADER
        cursor.execute(f"""
            SELECT COUNT(*)
            FROM USR_DATALAKE.MTX_TRANSACTION_HEADER
            WHERE FIELD7 IN ('{casos_str}')
            AND TRUNC(TRANSFER_DATE) = TO_DATE('{fecha}', 'YYYY-MM-DD')
        """)
        oracle_header_count = cursor.fetchone()[0]
        
        print(f"  📊 Casos en Oracle MTX_TRANSACTION_HEADER: {oracle_header_count}")
        
        if oracle_header_count > 0:
            print(f"  ✅ Los casos SÍ existen en Oracle MTX_TRANSACTION_HEADER")
            
            # Analizar características de estos casos en Oracle
            cursor.execute(f"""
                SELECT 
                    SERVICE_TYPE,
                    SOURCE,
                    TRANSFER_STATUS,
                    COUNT(*) as count
                FROM USR_DATALAKE.MTX_TRANSACTION_HEADER
                WHERE FIELD7 IN ('{casos_str}')
                AND TRUNC(TRANSFER_DATE) = TO_DATE('{fecha}', 'YYYY-MM-DD')
                GROUP BY SERVICE_TYPE, SOURCE, TRANSFER_STATUS
                ORDER BY count DESC
            """)
            
            oracle_caracteristicas = cursor.fetchall()
            print(f"  📋 Características en Oracle MTX_TRANSACTION_HEADER:")
            for row in oracle_caracteristicas:
                service_type = row[0] if row[0] is not None else 'NULL'
                source = row[1] if row[1] is not None else 'NULL'
                status = row[2] if row[2] is not None else 'NULL'
                count = row[3]
                print(f"    SERVICE_TYPE: {service_type} | SOURCE: {source} | STATUS: {status} | Count: {count}")
        else:
            print(f"  ❌ Los casos NO existen en Oracle MTX_TRANSACTION_HEADER")
        
        print(f"\n2️⃣ VERIFICANDO SI ESTOS CASOS EXISTEN EN ORACLE PRE_LOG_TRX:")
        print("-" * 70)
        
        # Verificar si existen en Oracle PRE_LOG_TRX (tabla temporal)
        try:
            cursor.execute(f"""
                SELECT COUNT(*)
                FROM USR_DATALAKE.PRE_LOG_TRX
                WHERE "TransferID" IN ('{casos_str}')
            """)
            oracle_pre_count = cursor.fetchone()[0]
            print(f"  📊 Casos en Oracle PRE_LOG_TRX: {oracle_pre_count}")
            
            if oracle_pre_count > 0:
                print(f"  ✅ Los casos SÍ existen en Oracle PRE_LOG_TRX")
                print(f"  🔍 Esto significa que Oracle los procesa en SP_PRE_LOG_TRX pero los filtra en SP_LOG_TRX")
            else:
                print(f"  ❌ Los casos NO existen en Oracle PRE_LOG_TRX")
                print(f"  🔍 Esto significa que Oracle los filtra en SP_PRE_LOG_TRX")
        except Exception as e:
            print(f"  ⚠️ Error accediendo PRE_LOG_TRX: {e}")
        
        print(f"\n3️⃣ ANALIZANDO FILTROS EN SYS_SERVICE_TYPES:")
        print("-" * 70)
        
        # Verificar si hay filtros en SYS_SERVICE_TYPES que Oracle aplica
        cursor.execute(f"""
            SELECT 
                MTH.SERVICE_TYPE,
                SST.IS_FINANCIAL,
                COUNT(*) as count
            FROM USR_DATALAKE.MTX_TRANSACTION_HEADER MTH
            LEFT JOIN USR_DATALAKE.SYS_SERVICE_TYPES SST ON MTH.SERVICE_TYPE = SST.SERVICE_TYPE
            WHERE MTH.FIELD7 IN ('{casos_str}')
            AND TRUNC(MTH.TRANSFER_DATE) = TO_DATE('{fecha}', 'YYYY-MM-DD')
            GROUP BY MTH.SERVICE_TYPE, SST.IS_FINANCIAL
            ORDER BY count DESC
        """)
        
        service_types_info = cursor.fetchall()
        print(f"  📋 Información SYS_SERVICE_TYPES para los 57 casos:")
        for row in service_types_info:
            service_type = row[0] if row[0] is not None else 'NULL'
            is_financial = row[1] if row[1] is not None else 'NULL'
            count = row[2]
            print(f"    SERVICE_TYPE: {service_type} | IS_FINANCIAL: {is_financial} | Count: {count}")
        
        print(f"\n4️⃣ COMPARANDO CON NUESTROS DATOS S3:")
        print("-" * 70)
        
        # Verificar en nuestros datos S3
        s3_casos = conn.execute(f"""
            SELECT 
                "TransactionType",
                "Context",
                COUNT(*) as count
            FROM read_parquet('/home/<USER>/REPORTE_2025/reports/generate/S3_LOG_TRANSACCIONES/TEMP_LOGS_TRANSACCIONES/20250618/LOG_TRX_FINAL.parquet')
            WHERE "TransactionID" IN ('{casos_str}')
            GROUP BY "TransactionType", "Context"
            ORDER BY count DESC
        """).fetchall()
        
        print(f"  📋 Casos en nuestro S3 LOG_TRX_FINAL:")
        for row in s3_casos:
            txn_type = row[0] if row[0] is not None else 'NULL'
            context = row[1] if row[1] is not None else 'NULL'
            count = row[2]
            print(f"    TransactionType: {txn_type} | Context: {context} | Count: {count}")
        
        print(f"\n5️⃣ INVESTIGANDO FILTROS ESPECÍFICOS DE ORACLE:")
        print("-" * 70)
        
        # Verificar si Oracle tiene filtros específicos por SOURCE
        cursor.execute(f"""
            SELECT DISTINCT SOURCE
            FROM USR_DATALAKE.MTX_TRANSACTION_HEADER
            WHERE TRUNC(TRANSFER_DATE) = TO_DATE('{fecha}', 'YYYY-MM-DD')
            AND SOURCE IS NOT NULL
            ORDER BY SOURCE
        """)
        
        oracle_sources = [row[0] for row in cursor.fetchall()]
        print(f"  📋 Fuentes (SOURCE) que Oracle SÍ procesa:")
        for source in oracle_sources[:10]:  # Mostrar solo las primeras 10
            print(f"    {source}")
        
        # Verificar si 'mySource' está en la lista
        if 'mySource' in oracle_sources:
            print(f"  ✅ 'mySource' SÍ está en las fuentes que Oracle procesa")
        else:
            print(f"  ❌ 'mySource' NO está en las fuentes que Oracle procesa")
            print(f"  🎯 AQUÍ ESTÁ EL PROBLEMA: Oracle filtra SOURCE = 'mySource'")
        
        cursor.close()
        connection.close()
        conn.close()
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    investigacion_profunda()
