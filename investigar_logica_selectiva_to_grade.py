#!/usr/bin/env python3
"""
Investigar lógica selectiva para To_Grade
"""

import oracledb

def investigar_logica_selectiva():
    """Investiga cuándo Oracle aplica la transformación"""
    print("🔍 INVESTIGACIÓN: LÓGICA SELECTIVA To_Grade")
    print("=" * 80)
    
    fecha = '2025-06-15'
    
    try:
        connection = oracledb.connect(
            user='usr_datalake',
            password='U2024b1mD4t4l4k5',
            dsn='10.240.131.10:1521/MMONEY'
        )
        
        cursor = connection.cursor()
        
        print("1️⃣ CASOS DONDE ORACLE TRANSFORMA:")
        print("-" * 60)
        
        # Buscar casos donde Oracle tiene "NORMAL ACCOUNT PROFILE" 
        # pero CHANNEL_GRADES tiene "Normal General Account Profile"
        cursor.execute("""
            SELECT DISTINCT
                PLT."ToID_Mobiquity",
                PLT."To_Grade" as ORACLE_GRADE,
                CG.GRADE_NAME as CHAN<PERSON><PERSON>_GRA<PERSON>,
                MW.USER_GRADE as WALL<PERSON>_GRADE,
                PLT."TransferID"
            FROM USR_DATALAKE.PRE_LOG_TRX PLT
            INNER JOIN PDP_PROD10_MAINDBBUS.MTX_WALLET MW ON PLT."ToID_Mobiquity" = MW.USER_ID
            INNER JOIN PDP_PROD10_MAINDB.CHANNEL_GRADES CG ON MW.USER_GRADE = CG.GRADE_CODE
            WHERE TRUNC(PLT."TransferDate") = TO_DATE(:fecha, 'YYYY-MM-DD')
            AND PLT."To_Grade" = 'NORMAL ACCOUNT PROFILE'
            AND CG.GRADE_NAME = 'Normal General Account Profile'
            AND ROWNUM <= 10
        """, {'fecha': fecha})
        
        casos_transformados = cursor.fetchall()
        
        print(f"  Casos donde Oracle transforma (primeros 10):")
        print(f"{'USER_ID':<15} {'ORACLE_GRADE':<25} {'CHANNEL_GRADE':<30} {'WALLET_GRADE':<15} {'TRANSFER_ID'}")
        print("-" * 100)
        
        for row in casos_transformados:
            print(f"{row[0]:<15} {row[1]:<25} {row[2]:<30} {row[3]:<15} {row[4]}")
        
        print(f"\n2️⃣ CASOS DONDE ORACLE NO TRANSFORMA:")
        print("-" * 60)
        
        # Buscar casos donde Oracle mantiene "NORMAL GENERAL ACCOUNT PROFILE"
        cursor.execute("""
            SELECT DISTINCT
                PLT."ToID_Mobiquity",
                PLT."To_Grade" as ORACLE_GRADE,
                CG.GRADE_NAME as CHANNEL_GRADE,
                MW.USER_GRADE as WALLET_GRADE,
                PLT."TransferID"
            FROM USR_DATALAKE.PRE_LOG_TRX PLT
            INNER JOIN PDP_PROD10_MAINDBBUS.MTX_WALLET MW ON PLT."ToID_Mobiquity" = MW.USER_ID
            INNER JOIN PDP_PROD10_MAINDB.CHANNEL_GRADES CG ON MW.USER_GRADE = CG.GRADE_CODE
            WHERE TRUNC(PLT."TransferDate") = TO_DATE(:fecha, 'YYYY-MM-DD')
            AND PLT."To_Grade" = 'NORMAL GENERAL ACCOUNT PROFILE'
            AND CG.GRADE_NAME = 'Normal General Account Profile'
            AND ROWNUM <= 10
        """, {'fecha': fecha})
        
        casos_no_transformados = cursor.fetchall()
        
        print(f"  Casos donde Oracle NO transforma (primeros 10):")
        print(f"{'USER_ID':<15} {'ORACLE_GRADE':<25} {'CHANNEL_GRADE':<30} {'WALLET_GRADE':<15} {'TRANSFER_ID'}")
        print("-" * 100)
        
        for row in casos_no_transformados:
            print(f"{row[0]:<15} {row[1]:<25} {row[2]:<30} {row[3]:<15} {row[4]}")
        
        print(f"\n3️⃣ ANÁLISIS DE PATRONES:")
        print("-" * 60)
        
        # Analizar si hay algún patrón en los casos transformados
        if casos_transformados and casos_no_transformados:
            print(f"  Casos transformados: {len(casos_transformados)}")
            print(f"  Casos no transformados: {len(casos_no_transformados)}")
            
            # Verificar si hay diferencias en USER_GRADE
            user_grades_transformados = set(row[3] for row in casos_transformados)
            user_grades_no_transformados = set(row[3] for row in casos_no_transformados)
            
            print(f"\n  USER_GRADE en casos transformados: {user_grades_transformados}")
            print(f"  USER_GRADE en casos no transformados: {user_grades_no_transformados}")
            
            if user_grades_transformados != user_grades_no_transformados:
                print(f"  🔍 PATRÓN IDENTIFICADO: Diferentes USER_GRADE")
            else:
                print(f"  ❓ No hay diferencia en USER_GRADE")
        
        print(f"\n4️⃣ INVESTIGAR CASO ESPECÍFICO 5000074554:")
        print("-" * 60)
        
        # Investigar el caso específico
        cursor.execute("""
            SELECT 
                PLT."TransferID",
                PLT."ToID_Mobiquity",
                PLT."To_Grade" as ORACLE_GRADE,
                CG.GRADE_NAME as CHANNEL_GRADE,
                MW.USER_GRADE as WALLET_GRADE,
                MW.STATUS as WALLET_STATUS,
                MW.MODIFIED_ON,
                ROW_NUMBER() OVER (PARTITION BY MW.USER_ID ORDER BY MW.MODIFIED_ON DESC) as WALLET_ORDER
            FROM USR_DATALAKE.PRE_LOG_TRX PLT
            INNER JOIN PDP_PROD10_MAINDBBUS.MTX_WALLET MW ON PLT."ToID_Mobiquity" = MW.USER_ID
            INNER JOIN PDP_PROD10_MAINDB.CHANNEL_GRADES CG ON MW.USER_GRADE = CG.GRADE_CODE
            WHERE PLT."TransferID" = '5000074554'
        """)
        
        caso_especifico = cursor.fetchall()
        
        if caso_especifico:
            print(f"  Caso 5000074554:")
            for row in caso_especifico:
                print(f"    TransferID: {row[0]}")
                print(f"    ToID_Mobiquity: {row[1]}")
                print(f"    Oracle Grade: {row[2]}")
                print(f"    Channel Grade: {row[3]}")
                print(f"    Wallet Grade: {row[4]}")
                print(f"    Wallet Status: {row[5]}")
                print(f"    Wallet Order: {row[7]}")
                print()
        
        cursor.close()
        connection.close()
        
        print(f"5️⃣ CONCLUSIONES:")
        print("-" * 60)
        
        if casos_transformados and casos_no_transformados:
            print(f"1. Oracle aplica transformación selectivamente")
            print(f"2. Algunos casos mantienen 'GENERAL', otros no")
            print(f"3. Necesario identificar el criterio específico")
        else:
            print(f"1. No se encontraron patrones claros")
            print(f"2. Puede ser lógica más compleja")
        
    except Exception as e:
        print(f"❌ Error en investigación: {e}")
        import traceback
        traceback.print_exc()

def main():
    print("🚀 INVESTIGACIÓN LÓGICA SELECTIVA To_Grade")
    print("=" * 80)
    print("OBJETIVO: Entender cuándo Oracle transforma y cuándo no")
    print()
    
    investigar_logica_selectiva()
    
    print("\n🏁 INVESTIGACIÓN COMPLETADA")

if __name__ == "__main__":
    main()
