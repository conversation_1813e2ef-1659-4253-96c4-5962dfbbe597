#!/usr/bin/env python3
"""
CODE NINJA: Búsqueda Sin Restricciones de Fecha
Buscar los 57 casos en CUALQUIER fecha en las tablas origen
"""

import oracledb
import duckdb
import boto3

def busqueda_sin_restricciones_fecha():
    """Buscar los 57 casos en cualquier fecha"""
    print("🥷 CODE NINJA: BÚSQUEDA SIN RESTRICCIONES DE FECHA")
    print("=" * 80)
    print("OBJETIVO: Encontrar los 57 casos en CUALQUIER fecha")
    print()
    
    # Los 57 casos específicos
    casos_57 = [
        '175020514200061', '175020514200182', '175020514200241', '175020514200421', '175020514200530',
        '175020514200646', '175020514200728', '175020514200898', '175020514200959', '175020514201099',
        '175020514201116', '175020514201208', '175020514201436', '175020514201546', '175020514201647',
        '175020514201723', '175020514201817', '175020514201946', '175020514202084', '175020514202162',
        '175020514202275', '175020514202363', '175020514202579', '175020514202681', '175020514202724',
        '175020514202865', '175020514202972', '175020514203090', '175020514203132', '175020514203215',
        '175020514203322', '175020514203490', '175020514203653', '175020514203745', '175020514203909',
        '175020514204059', '175020514204182', '175020514204290', '175020514204351', '175020514204462',
        '175020514204530', '175020514204717', '175020514204983', '175020514205033', '175020514205132',
        '175020514205361', '175020514205488', '175020514205532', '175020514205638', '175020514205826',
        '175020514205920', '175020514206091', '175020514206185', '175020514299909', '175029151203224',
        '175029151206399', '5000077909'
    ]
    
    try:
        # CONFIGURAR CONEXIONES
        print("🔧 CONFIGURANDO CONEXIONES:")
        print("-" * 60)
        
        # Oracle
        connection = oracledb.connect(
            user='usr_datalake',
            password='U2024b1mD4t4l4k5',
            dsn='10.240.131.10:1521/MMONEY'
        )
        cursor = connection.cursor()
        
        # DuckDB + S3
        conn = duckdb.connect()
        session = boto3.Session()
        credentials = session.get_credentials().get_frozen_credentials()
        
        conn.sql("INSTALL httpfs;")
        conn.sql("LOAD httpfs;")
        conn.sql("SET s3_region='us-east-1';")
        conn.sql("SET s3_use_ssl=true;")
        conn.sql("SET s3_url_style='path';")
        conn.sql(f"SET s3_access_key_id='{credentials.access_key}';")
        conn.sql(f"SET s3_secret_access_key='{credentials.secret_key}';")
        if credentials.token:
            conn.sql(f"SET s3_session_token='{credentials.token}';")
        
        print("  ✅ Conexiones establecidas")
        
        # Convertir casos a string para SQL
        casos_str = "', '".join(casos_57)
        
        # 1. BUSCAR EN ORACLE MTX_TRANSACTION_HEADER (SIN FILTRO DE FECHA)
        print(f"\n1️⃣ BÚSQUEDA EN ORACLE MTX_TRANSACTION_HEADER (SIN FILTRO FECHA):")
        print("-" * 60)
        
        cursor.execute(f"""
            SELECT 
                TRANSFER_ID,
                TRANSFER_DATE,
                TRANSFER_STATUS,
                TRANSFER_VALUE,
                SERVICE_TYPE
            FROM PDP_PROD10_MAINDBBUS.MTX_TRANSACTION_HEADER
            WHERE TRANSFER_ID IN ('{casos_str}')
            ORDER BY TRANSFER_DATE, TRANSFER_ID
        """)
        
        oracle_header_results = cursor.fetchall()
        
        print(f"  📊 Casos encontrados en Oracle HEADER: {len(oracle_header_results)}")
        
        if oracle_header_results:
            print(f"  📋 DETALLES ENCONTRADOS EN ORACLE:")
            print(f"  {'TRANSFER_ID':<20} {'TRANSFER_DATE':<20} {'STATUS':<8} {'VALUE':<12} {'SERVICE_TYPE'}")
            print("-" * 90)
            
            fechas_oracle = {}
            for row in oracle_header_results:
                fecha_str = str(row[1])[:10]
                fechas_oracle[fecha_str] = fechas_oracle.get(fecha_str, 0) + 1
                print(f"  {row[0]:<20} {str(row[1])[:19]:<20} {row[2]:<8} {row[3]:<12} {row[4]}")
            
            print(f"\n  📊 DISTRIBUCIÓN POR FECHAS EN ORACLE:")
            for fecha, count in sorted(fechas_oracle.items()):
                print(f"    {fecha}: {count} casos")
        
        # 2. BUSCAR EN S3 MTX_TRANSACTION_HEADER (MÚLTIPLES FECHAS)
        print(f"\n2️⃣ BÚSQUEDA EN S3 MTX_TRANSACTION_HEADER (MÚLTIPLES FECHAS):")
        print("-" * 60)
        
        # Buscar en un rango amplio de fechas S3
        fechas_s3_buscar = [
            '2025/06/10', '2025/06/11', '2025/06/12', '2025/06/13', '2025/06/14',
            '2025/06/15', '2025/06/16', '2025/06/17', '2025/06/18', '2025/06/19',
            '2025/06/20', '2025/06/21'
        ]
        
        s3_resultados_totales = []
        
        for fecha in fechas_s3_buscar:
            try:
                s3_results = conn.execute(f"""
                    SELECT 
                        TRANSFER_ID,
                        TRANSFER_DATE,
                        TRANSFER_STATUS,
                        TRANSFER_VALUE,
                        SERVICE_TYPE
                    FROM read_parquet('s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_HEADER_ORA/{fecha}/*.parquet')
                    WHERE TRANSFER_ID IN ('{casos_str}')
                """).fetchall()
                
                if s3_results:
                    print(f"  📊 {fecha}: {len(s3_results)} casos encontrados")
                    s3_resultados_totales.extend(s3_results)
                    
                    # Mostrar algunos ejemplos
                    for row in s3_results[:3]:
                        print(f"    {row[0]} | {str(row[1])[:19]} | {row[2]} | {row[3]}")
                        
            except Exception as e:
                # Silenciar errores de fechas que no existen
                pass
        
        print(f"\n  📊 Total casos encontrados en S3 HEADER: {len(s3_resultados_totales)}")
        
        if s3_resultados_totales:
            print(f"  📋 RESUMEN S3 HEADER:")
            fechas_s3 = {}
            for row in s3_resultados_totales:
                fecha_str = str(row[1])[:10]
                fechas_s3[fecha_str] = fechas_s3.get(fecha_str, 0) + 1
            
            print(f"  📊 DISTRIBUCIÓN POR FECHAS EN S3:")
            for fecha, count in sorted(fechas_s3.items()):
                print(f"    {fecha}: {count} casos")
        
        # 3. BUSCAR EN ORACLE MTX_TRANSACTION_ITEMS (SIN FILTRO FECHA)
        print(f"\n3️⃣ BÚSQUEDA EN ORACLE MTX_TRANSACTION_ITEMS (SIN FILTRO FECHA):")
        print("-" * 60)
        
        cursor.execute(f"""
            SELECT
                TRANSFER_ID,
                COUNT(*) as ITEM_COUNT
            FROM PDP_PROD10_MAINDBBUS.MTX_TRANSACTION_ITEMS
            WHERE TRANSFER_ID IN ('{casos_str}')
            GROUP BY TRANSFER_ID
            ORDER BY TRANSFER_ID
        """)
        
        oracle_items_results = cursor.fetchall()
        
        print(f"  📊 Casos con items en Oracle: {len(oracle_items_results)}")
        
        if oracle_items_results:
            print(f"  📋 ITEMS EN ORACLE:")
            print(f"  {'TRANSFER_ID':<20} {'ITEMS':<8}")
            print("-" * 30)

            for row in oracle_items_results:
                print(f"  {row[0]:<20} {row[1]:<8}")
        
        # 4. COMPARACIÓN DETALLADA
        print(f"\n4️⃣ COMPARACIÓN DETALLADA:")
        print("-" * 60)
        
        oracle_ids = set([row[0] for row in oracle_header_results])
        s3_ids = set([row[0] for row in s3_resultados_totales])
        oracle_items_ids = set([row[0] for row in oracle_items_results])
        
        print(f"  📊 RESUMEN COMPARATIVO:")
        print(f"    Total casos a buscar: {len(casos_57)}")
        print(f"    Encontrados en Oracle HEADER: {len(oracle_ids)}")
        print(f"    Encontrados en S3 HEADER: {len(s3_ids)}")
        print(f"    Encontrados en Oracle ITEMS: {len(oracle_items_ids)}")
        
        # Casos solo en Oracle
        solo_oracle = oracle_ids - s3_ids
        if solo_oracle:
            print(f"\n  🔍 CASOS SOLO EN ORACLE ({len(solo_oracle)}):")
            for caso in sorted(solo_oracle):
                print(f"    {caso}")
        
        # Casos solo en S3
        solo_s3 = s3_ids - oracle_ids
        if solo_s3:
            print(f"\n  🔍 CASOS SOLO EN S3 ({len(solo_s3)}):")
            for caso in sorted(solo_s3):
                print(f"    {caso}")
        
        # Casos en ambos
        en_ambos = oracle_ids & s3_ids
        if en_ambos:
            print(f"\n  🔍 CASOS EN AMBOS SISTEMAS ({len(en_ambos)}):")
            for caso in sorted(list(en_ambos)[:10]):  # Mostrar primeros 10
                print(f"    {caso}")
        
        # Casos no encontrados en ningún lado
        no_encontrados = set(casos_57) - oracle_ids - s3_ids
        if no_encontrados:
            print(f"\n  ❌ CASOS NO ENCONTRADOS EN NINGÚN LADO ({len(no_encontrados)}):")
            for caso in sorted(list(no_encontrados)[:10]):  # Mostrar primeros 10
                print(f"    {caso}")

            if len(no_encontrados) > 10:
                print(f"    ... y {len(no_encontrados) - 10} casos más")

        # 4.5. BÚSQUEDA EXHAUSTIVA EN OTRAS TABLAS
        print(f"\n4️⃣.5 BÚSQUEDA EXHAUSTIVA EN OTRAS TABLAS:")
        print("-" * 60)

        if no_encontrados:
            # Buscar en USER_DATA_TRX
            casos_no_encontrados_str = "', '".join(list(no_encontrados)[:10])

            try:
                print(f"  🔍 Buscando en USER_DATA_TRX:")
                user_data_results = conn.execute(f"""
                    SELECT USER_ID, O_USER_ID, WALLET_NUMBER
                    FROM read_parquet('s3://prd-datalake-golden-zone-637423440311/LOGS_USUARIOS/USER_DATA_TRX.parquet')
                    WHERE USER_ID IN ('{casos_no_encontrados_str}')
                       OR O_USER_ID IN ('{casos_no_encontrados_str}')
                       OR WALLET_NUMBER IN ('{casos_no_encontrados_str}')
                    LIMIT 10
                """).fetchall()

                print(f"    📊 Casos en USER_DATA_TRX: {len(user_data_results)}")

                if user_data_results:
                    for row in user_data_results:
                        print(f"      {row[0]} | {row[1]} | {row[2]}")

            except Exception as e:
                print(f"    ❌ Error en USER_DATA_TRX: {str(e)[:50]}...")

            # Verificar si son IDs generados por nuestro pipeline
            print(f"\n  🔍 ANÁLISIS DE PATRONES DE IDs:")
            patrones = {}
            for caso in list(no_encontrados)[:20]:
                if caso.startswith('175020514'):
                    patrones['175020514_pattern'] = patrones.get('175020514_pattern', 0) + 1
                elif caso.startswith('175029151'):
                    patrones['175029151_pattern'] = patrones.get('175029151_pattern', 0) + 1
                elif caso.startswith('5000'):
                    patrones['5000_pattern'] = patrones.get('5000_pattern', 0) + 1
                else:
                    patrones['other_pattern'] = patrones.get('other_pattern', 0) + 1

            print(f"    📊 Patrones identificados:")
            for patron, count in patrones.items():
                print(f"      {patron}: {count} casos")

            # Verificar si estos IDs se generan en nuestro pipeline
            print(f"\n  🔍 HIPÓTESIS:")
            print(f"    🤔 Los casos NO existen en tablas origen")
            print(f"    🤔 Se generan durante el procesamiento SP_PRE_LOG_TRX")
            print(f"    🤔 Posible lógica de transformación que crea nuevos IDs")
            print(f"    🤔 O combinación de datos de múltiples tablas")
        
        # 5. ANÁLISIS DE FECHAS
        print(f"\n5️⃣ ANÁLISIS DE FECHAS:")
        print("-" * 60)
        
        if oracle_header_results and s3_resultados_totales:
            print(f"  🔍 COMPARACIÓN DE FECHAS:")
            
            # Verificar si las fechas coinciden para los mismos casos
            oracle_fechas_por_id = {}
            for row in oracle_header_results:
                oracle_fechas_por_id[row[0]] = str(row[1])[:10]
            
            s3_fechas_por_id = {}
            for row in s3_resultados_totales:
                s3_fechas_por_id[row[0]] = str(row[1])[:10]
            
            casos_comunes = set(oracle_fechas_por_id.keys()) & set(s3_fechas_por_id.keys())
            
            if casos_comunes:
                print(f"    📊 Casos comunes con fechas diferentes:")
                print(f"    {'TRANSFER_ID':<20} {'Oracle Fecha':<12} {'S3 Fecha':<12} {'Coincide'}")
                print("-" * 60)
                
                fechas_diferentes = 0
                for caso in sorted(list(casos_comunes)[:10]):
                    oracle_fecha = oracle_fechas_por_id[caso]
                    s3_fecha = s3_fechas_por_id[caso]
                    coincide = "✅" if oracle_fecha == s3_fecha else "❌"
                    
                    if oracle_fecha != s3_fecha:
                        fechas_diferentes += 1
                    
                    print(f"    {caso:<20} {oracle_fecha:<12} {s3_fecha:<12} {coincide}")
                
                print(f"\n    📊 Casos con fechas diferentes: {fechas_diferentes}/{len(casos_comunes)}")
        
        cursor.close()
        connection.close()
        conn.close()
        
    except Exception as e:
        print(f"❌ Error en búsqueda sin fecha: {e}")
        import traceback
        traceback.print_exc()

def main():
    print("🥷 CODE NINJA: BÚSQUEDA SIN RESTRICCIONES DE FECHA")
    print("=" * 80)
    print("MISIÓN: Encontrar los 57 casos en CUALQUIER fecha")
    print()
    
    busqueda_sin_restricciones_fecha()
    
    print("\n🏁 BÚSQUEDA SIN RESTRICCIONES COMPLETADA")

if __name__ == "__main__":
    main()
