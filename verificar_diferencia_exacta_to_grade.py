#!/usr/bin/env python3
"""
Verificar diferencia exacta en To_Grade
"""

import oracledb
import duckdb
import boto3

def verificar_diferencia_exacta():
    """Verifica diferencia exacta en To_Grade"""
    print("🔍 VERIFICACIÓN: DIFERENCIA EXACTA To_Grade")
    print("=" * 80)
    
    transfer_id = '5000074554'
    parquet_path = '/home/<USER>/REPORTE_2025/reports/generate/S3_LOG_TRANSACCIONES/TEMP_LOGS_TRANSACCIONES/20250615/PRE_LOG_TRX.parquet'
    
    try:
        # 1. VERIFICAR VALORES EXACTOS
        print("1️⃣ VERIFICAR VALORES EXACTOS:")
        print("-" * 60)
        
        connection = oracledb.connect(
            user='usr_datalake',
            password='U2024b1mD4t4l4k5',
            dsn='10.240.131.10:1521/MMONEY'
        )
        
        cursor = connection.cursor()
        
        # Oracle
        cursor.execute("""
            SELECT "To_Grade"
            FROM USR_DATALAKE.PRE_LOG_TRX
            WHERE "TransferID" = :transfer_id
        """, {'transfer_id': transfer_id})
        
        oracle_result = cursor.fetchone()
        oracle_value = oracle_result[0] if oracle_result else None
        
        # S3
        conn = duckdb.connect()
        session = boto3.Session()
        credentials = session.get_credentials().get_frozen_credentials()
        
        conn.sql("INSTALL httpfs;")
        conn.sql("LOAD httpfs;")
        conn.sql("SET s3_region='us-east-1';")
        conn.sql("SET s3_use_ssl=true;")
        conn.sql("SET s3_url_style='path';")
        conn.sql(f"SET s3_access_key_id='{credentials.access_key}';")
        conn.sql(f"SET s3_secret_access_key='{credentials.secret_key}';")
        if credentials.token:
            conn.sql(f"SET s3_session_token='{credentials.token}';")
        
        s3_result = conn.execute(f"""
            SELECT "To_Grade"
            FROM read_parquet('{parquet_path}')
            WHERE "TransferID" = '{transfer_id}'
        """).fetchone()
        
        s3_value = s3_result[0] if s3_result else None
        
        print(f"  Oracle: '{oracle_value}'")
        print(f"  S3:     '{s3_value}'")
        print(f"  Longitud Oracle: {len(oracle_value) if oracle_value else 0}")
        print(f"  Longitud S3: {len(s3_value) if s3_value else 0}")
        
        # 2. ANÁLISIS CARÁCTER POR CARÁCTER
        print("\n2️⃣ ANÁLISIS CARÁCTER POR CARÁCTER:")
        print("-" * 60)
        
        if oracle_value and s3_value:
            print(f"  Oracle: {[c for c in oracle_value]}")
            print(f"  S3:     {[c for c in s3_value]}")
            
            # Encontrar diferencias
            min_len = min(len(oracle_value), len(s3_value))
            diferencias = []
            
            for i in range(min_len):
                if oracle_value[i] != s3_value[i]:
                    diferencias.append((i, oracle_value[i], s3_value[i]))
            
            if diferencias:
                print(f"  Diferencias encontradas:")
                for pos, oracle_char, s3_char in diferencias:
                    print(f"    Posición {pos}: Oracle='{oracle_char}' vs S3='{s3_char}'")
            else:
                print(f"  Primeros {min_len} caracteres son idénticos")
                
                if len(oracle_value) != len(s3_value):
                    print(f"  Diferencia en longitud:")
                    if len(oracle_value) > len(s3_value):
                        extra = oracle_value[min_len:]
                        print(f"    Oracle tiene extra: '{extra}'")
                    else:
                        extra = s3_value[min_len:]
                        print(f"    S3 tiene extra: '{extra}'")
        
        # 3. VERIFICAR MAPEO EN CHANNEL_GRADES
        print("\n3️⃣ VERIFICAR MAPEO EN CHANNEL_GRADES:")
        print("-" * 60)
        
        cursor.execute("""
            SELECT 
                GRADE_CODE,
                GRADE_NAME,
                UPPER(GRADE_NAME) as GRADE_NAME_UPPER
            FROM PDP_PROD10_MAINDB.CHANNEL_GRADES
            WHERE GRADE_CODE = 'FUNGAPGRA'
        """)
        
        channel_result = cursor.fetchone()
        if channel_result:
            print(f"  GRADE_CODE: {channel_result[0]}")
            print(f"  GRADE_NAME: '{channel_result[1]}'")
            print(f"  GRADE_NAME_UPPER: '{channel_result[2]}'")
            
            channel_value = channel_result[1]
            channel_upper = channel_result[2]
            
            print(f"\n  Comparaciones:")
            print(f"    Oracle == CHANNEL_GRADES: {oracle_value == channel_value}")
            print(f"    Oracle == CHANNEL_UPPER: {oracle_value == channel_upper}")
            print(f"    S3 == CHANNEL_GRADES: {s3_value == channel_value}")
            print(f"    S3 == CHANNEL_UPPER: {s3_value == channel_upper}")
        
        # 4. VERIFICAR SI HAY TRANSFORMACIÓN ADICIONAL
        print("\n4️⃣ VERIFICAR TRANSFORMACIÓN ADICIONAL:")
        print("-" * 60)
        
        # Buscar si Oracle aplica alguna transformación
        if oracle_value and channel_result:
            channel_value = channel_result[1]
            
            # Verificar transformaciones comunes
            transformaciones = {
                'UPPER': channel_value.upper(),
                'LOWER': channel_value.lower(),
                'TITLE': channel_value.title(),
                'REPLACE_GENERAL': channel_value.replace('General ', ''),
                'TRIM': channel_value.strip()
            }
            
            print(f"  Transformaciones posibles:")
            for nombre, valor in transformaciones.items():
                coincide = "✅" if valor == oracle_value else "❌"
                print(f"    {nombre}: '{valor}' {coincide}")
        
        cursor.close()
        connection.close()
        conn.close()
        
        # 5. CONCLUSIONES
        print("\n5️⃣ CONCLUSIONES:")
        print("-" * 60)
        
        if oracle_value and s3_value:
            if oracle_value == s3_value:
                print("1. ✅ Valores son idénticos")
            else:
                print("1. ❌ Valores son diferentes")
                print(f"2. Oracle: '{oracle_value}'")
                print(f"3. S3: '{s3_value}'")
                
                if channel_result:
                    channel_value = channel_result[1]
                    if s3_value == channel_value:
                        print("4. ✅ S3 usa valor correcto de CHANNEL_GRADES")
                        print("5. ❌ Oracle aplica transformación no identificada")
                    elif oracle_value == channel_value:
                        print("4. ✅ Oracle usa valor correcto de CHANNEL_GRADES")
                        print("5. ❌ S3 aplica transformación incorrecta")
                    else:
                        print("4. ❌ Ninguno usa valor exacto de CHANNEL_GRADES")
        
    except Exception as e:
        print(f"❌ Error en verificación: {e}")
        import traceback
        traceback.print_exc()

def main():
    print("🚀 VERIFICACIÓN DIFERENCIA EXACTA To_Grade")
    print("=" * 80)
    print("OBJETIVO: Identificar diferencia exacta")
    print()
    
    verificar_diferencia_exacta()
    
    print("\n🏁 VERIFICACIÓN COMPLETADA")

if __name__ == "__main__":
    main()
