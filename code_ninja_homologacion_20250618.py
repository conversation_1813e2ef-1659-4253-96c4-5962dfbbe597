#!/usr/bin/env python3
"""
CODE NINJA: Homologación Oracle vs S3 para 2025-06-18
Comparar LOG_TRX_FINAL Oracle vs Parquet generado
"""

import oracledb
import duckdb
import boto3
from pathlib import Path

def homologar_log_trx_final():
    """Homologar cantidad de registros Oracle vs S3"""
    print("🥷 CODE NINJA: HOMOLOGACIÓN LOG_TRX_FINAL 2025-06-18")
    print("=" * 80)
    
    fecha = '2025-06-18'
    parquet_path = '/home/<USER>/REPORTE_2025/reports/generate/S3_LOG_TRANSACCIONES/TEMP_LOGS_TRANSACCIONES/20250618/LOG_TRX_FINAL.parquet'
    
    try:
        # 1. VERIFICAR SI ARCHIVO PARQUET EXISTE
        print("1️⃣ VERIFICANDO ARCHIVO PARQUET:")
        print("-" * 60)
        
        if Path(parquet_path).exists():
            print(f"  ✅ Archivo existe: {parquet_path}")
            file_size = Path(parquet_path).stat().st_size / (1024*1024)  # MB
            print(f"  📊 Tamaño: {file_size:.2f} MB")
        else:
            print(f"  ❌ Archivo NO existe: {parquet_path}")
            print(f"  🔧 Necesitas ejecutar: python3 pipeline_log_transacciones_duckdb.py 2025-06-18")
            return
        
        # 2. CONSULTA ORACLE
        print(f"\n2️⃣ CONSULTANDO ORACLE LOG_TRX_FINAL:")
        print("-" * 60)
        
        connection = oracledb.connect(
            user='usr_datalake',
            password='U2024b1mD4t4l4k5',
            dsn='*************:1521/MMONEY'
        )
        cursor = connection.cursor()
        
        cursor.execute(f"""
            SELECT COUNT(*)
            FROM USR_DATALAKE.LOG_TRX_FINAL
            WHERE TRUNC(TO_DATE("DateTime", 'YYYY-MM-DD HH24:MI:SS')) = TO_DATE('{fecha}', 'YYYY-MM-DD')
        """)
        
        oracle_count = cursor.fetchone()[0]
        print(f"  📊 ORACLE LOG_TRX_FINAL: {oracle_count:,} registros")
        
        # 3. CONSULTA S3 PARQUET
        print(f"\n3️⃣ CONSULTANDO S3 PARQUET:")
        print("-" * 60)
        
        conn = duckdb.connect()
        session = boto3.Session()
        credentials = session.get_credentials().get_frozen_credentials()
        
        conn.sql("INSTALL httpfs;")
        conn.sql("LOAD httpfs;")
        conn.sql("SET s3_region='us-east-1';")
        conn.sql("SET s3_use_ssl=true;")
        conn.sql("SET s3_url_style='path';")
        conn.sql(f"SET s3_access_key_id='{credentials.access_key}';")
        conn.sql(f"SET s3_secret_access_key='{credentials.secret_key}';")
        if credentials.token:
            conn.sql(f"SET s3_session_token='{credentials.token}';")
        
        s3_count_result = conn.execute(f"""
            SELECT COUNT(*)
            FROM read_parquet('{parquet_path}')
        """).fetchone()
        
        s3_count = s3_count_result[0] if s3_count_result else 0
        print(f"  📊 S3 PARQUET: {s3_count:,} registros")
        
        # 4. COMPARACIÓN
        print(f"\n4️⃣ COMPARACIÓN FINAL:")
        print("-" * 60)
        
        diferencia = s3_count - oracle_count
        porcentaje = (diferencia / oracle_count * 100) if oracle_count > 0 else 0
        
        print(f"  📊 ORACLE:     {oracle_count:,} registros")
        print(f"  📊 S3:         {s3_count:,} registros")
        print(f"  📊 DIFERENCIA: {diferencia:+,} registros")
        print(f"  📊 PORCENTAJE: {porcentaje:+.2f}%")
        
        if diferencia == 0:
            print(f"\n  ✅ HOMOLOGACIÓN PERFECTA: Coincidencia exacta")
            print(f"  🎉 CALIDAD: 100% - Sin diferencias")
        elif abs(diferencia) <= 10:
            print(f"\n  ⚠️ DIFERENCIA MÍNIMA: Dentro del margen aceptable")
            print(f"  📊 CALIDAD: 99%+ - Diferencia insignificante")
        elif abs(porcentaje) <= 1:
            print(f"\n  ⚠️ DIFERENCIA MENOR: Menos del 1%")
            print(f"  📊 CALIDAD: 99%+ - Diferencia aceptable")
        else:
            print(f"\n  ❌ DIFERENCIA SIGNIFICATIVA: Requiere investigación")
            print(f"  🔧 ACCIÓN: Revisar lógica de procesamiento")
        
        # 5. MUESTRA DE DATOS
        print(f"\n5️⃣ MUESTRA DE DATOS S3:")
        print("-" * 60)
        
        sample = conn.execute(f"""
            SELECT "TransactionID", "DateTime", "TransactionType", "Amount"
            FROM read_parquet('{parquet_path}')
            LIMIT 5
        """).fetchall()
        
        print(f"  📋 Primeros 5 registros:")
        for row in sample:
            print(f"    {row[0]} | {row[1]} | {row[2]} | {row[3]}")
        
        cursor.close()
        connection.close()
        conn.close()
        
        # 6. CONCLUSIONES
        print(f"\n6️⃣ CONCLUSIONES CODE NINJA:")
        print("-" * 60)
        
        if diferencia == 0:
            print(f"1. ✅ HOMOLOGACIÓN PERFECTA para {fecha}")
            print(f"2. ✅ Pipeline S3 genera datos idénticos a Oracle")
            print(f"3. ✅ Calidad de migración: EXCELENTE")
        else:
            print(f"1. 📊 Diferencia detectada: {diferencia:+,} registros")
            print(f"2. 📊 Porcentaje de diferencia: {porcentaje:+.2f}%")
            if abs(porcentaje) <= 1:
                print(f"3. ✅ Diferencia dentro del margen aceptable")
            else:
                print(f"3. 🔧 Requiere investigación adicional")
        
    except Exception as e:
        print(f"❌ Error en homologación: {e}")
        import traceback
        traceback.print_exc()

def main():
    print("🥷 CODE NINJA: HOMOLOGACIÓN ORACLE vs S3")
    print("=" * 80)
    print("FECHA: 2025-06-18")
    print("TABLA: LOG_TRX_FINAL")
    print()
    
    homologar_log_trx_final()
    
    print("\n🏁 HOMOLOGACIÓN COMPLETADA")

if __name__ == "__main__":
    main()
