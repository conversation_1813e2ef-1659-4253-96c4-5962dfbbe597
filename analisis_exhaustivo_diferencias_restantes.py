#!/usr/bin/env python3
"""
Análisis exhaustivo de las diferencias restantes para lograr 100% perfección
"""

import oracledb
import duckdb
import boto3
import sys
from collections import defaultdict

def analisis_exhaustivo_diferencias():
    """Análisis exhaustivo de las diferencias restantes"""
    print("🔍 ANÁLISIS EXHAUSTIVO: DIFERENCIAS RESTANTES PARA 100% PERFECCIÓN")
    print("=" * 80)
    
    parquet_path = '/home/<USER>/REPORTE_2025/reports/generate/S3_LOG_TRANSACCIONES/TEMP_LOGS_TRANSACCIONES/20250615/PRE_LOG_TRX.parquet'
    fecha = '2025-06-15'
    
    try:
        # 1. CONFIGURAR CONEXIONES
        print("1️⃣ CONFIGURANDO CONEXIONES:")
        print("-" * 60)
        
        connection = oracledb.connect(
            user='usr_datalake',
            password='U2024b1mD4t4l4k5',
            dsn='10.240.131.10:1521/MMONEY'
        )
        cursor = connection.cursor()
        
        conn = duckdb.connect()
        session = boto3.Session()
        credentials = session.get_credentials().get_frozen_credentials()
        
        conn.sql("INSTALL httpfs;")
        conn.sql("LOAD httpfs;")
        conn.sql("SET s3_region='us-east-1';")
        conn.sql("SET s3_use_ssl=true;")
        conn.sql("SET s3_url_style='path';")
        conn.sql(f"SET s3_access_key_id='{credentials.access_key}';")
        conn.sql(f"SET s3_secret_access_key='{credentials.secret_key}';")
        if credentials.token:
            conn.sql(f"SET s3_session_token='{credentials.token}';")
        
        print("  ✅ Conexiones configuradas")
        
        # 2. IDENTIFICAR DIFERENCIAS EXACTAS
        print("\n2️⃣ IDENTIFICANDO DIFERENCIAS EXACTAS:")
        print("-" * 60)
        
        # Obtener combinaciones Oracle
        cursor.execute("""
            SELECT 
                "To_AccountID_Mobiquity",
                "ToID_Mobiquity",
                COUNT(*) as CASOS
            FROM USR_DATALAKE.PRE_LOG_TRX 
            WHERE TRUNC("TransferDate") = TO_DATE(:fecha, 'YYYY-MM-DD')
            GROUP BY "To_AccountID_Mobiquity", "ToID_Mobiquity"
        """, {'fecha': fecha})
        
        oracle_combinations = cursor.fetchall()
        oracle_map = {(str(row[0]), str(row[1])): row[2] for row in oracle_combinations}
        
        # Obtener combinaciones S3
        s3_combinations = conn.execute(f"""
            SELECT 
                "To_AccountID_Mobiquity",
                "ToID_Mobiquity",
                COUNT(*) as CASOS
            FROM read_parquet('{parquet_path}')
            GROUP BY "To_AccountID_Mobiquity", "ToID_Mobiquity"
        """).fetchall()
        
        s3_map = {(str(row[0]), str(row[1])): row[2] for row in s3_combinations}
        
        # Encontrar diferencias
        all_combinations = set(oracle_map.keys()) | set(s3_map.keys())
        diferencias = []
        
        for combination in all_combinations:
            oracle_count = oracle_map.get(combination, 0)
            s3_count = s3_map.get(combination, 0)
            
            if oracle_count != s3_count:
                diferencias.append((combination, oracle_count, s3_count))
        
        print(f"  Total diferencias encontradas: {len(diferencias)}")
        
        # 3. ANÁLISIS DETALLADO DE DIFERENCIAS
        print("\n3️⃣ ANÁLISIS DETALLADO DE DIFERENCIAS:")
        print("-" * 60)
        
        usuarios_problematicos = set()
        patrones_diferencias = defaultdict(list)
        
        print(f"{'Oracle_AccountID':<25} {'S3_AccountID':<25} {'UserID':<15} {'Oracle_Casos':<12} {'S3_Casos':<10}")
        print("-" * 100)
        
        for i, (combination, oracle_count, s3_count) in enumerate(sorted(diferencias, key=lambda x: abs(x[1] - x[2]), reverse=True)[:20]):
            account_id, user_id = combination
            
            # Buscar la contraparte
            contraparte_oracle = None
            contraparte_s3 = None
            
            for other_combination in all_combinations:
                other_account, other_user_id = other_combination
                if other_user_id == user_id and other_account != account_id:
                    if oracle_map.get(other_combination, 0) > 0 and oracle_count == 0:
                        contraparte_oracle = other_account
                    if s3_map.get(other_combination, 0) > 0 and s3_count == 0:
                        contraparte_s3 = other_account
            
            oracle_display = contraparte_oracle if oracle_count == 0 else account_id
            s3_display = contraparte_s3 if s3_count == 0 else account_id
            
            print(f"{oracle_display:<25} {s3_display:<25} {user_id:<15} {oracle_count:<12} {s3_count:<10}")
            
            usuarios_problematicos.add(user_id)
            
            # Clasificar patrones
            if len(oracle_display) > 15 and len(s3_display) <= 15:
                patrones_diferencias['oracle_largo_s3_corto'].append(user_id)
            elif len(oracle_display) <= 15 and len(s3_display) > 15:
                patrones_diferencias['oracle_corto_s3_largo'].append(user_id)
            else:
                patrones_diferencias['otros'].append(user_id)
        
        print(f"\n  Usuarios problemáticos únicos: {len(usuarios_problematicos)}")
        
        # 4. ANÁLISIS DE PATRONES
        print("\n4️⃣ ANÁLISIS DE PATRONES:")
        print("-" * 60)
        
        for patron, usuarios in patrones_diferencias.items():
            print(f"  {patron}: {len(usuarios)} usuarios")
            if usuarios:
                print(f"    Ejemplos: {usuarios[:5]}")
        
        # 5. INVESTIGACIÓN ESPECÍFICA DE USUARIOS PROBLEMÁTICOS
        print("\n5️⃣ INVESTIGACIÓN ESPECÍFICA DE USUARIOS PROBLEMÁTICOS:")
        print("-" * 60)
        
        usuarios_muestra = list(usuarios_problematicos)[:5]
        
        for user_id in usuarios_muestra:
            print(f"\n  📋 INVESTIGANDO USER_ID: {user_id}")
            print("  " + "-" * 50)
            
            # Verificar USER_PROFILE
            cursor.execute("""
                SELECT USER_ID, ATTR8, ATTR7
                FROM PDP_PROD10_MAINDB.USER_PROFILE
                WHERE USER_ID = :user_id
            """, {'user_id': user_id})
            
            user_profile = cursor.fetchone()
            if user_profile:
                print(f"    USER_PROFILE.ATTR8: {user_profile[1]}")
                print(f"    USER_PROFILE.ATTR7: {user_profile[2]}")
            
            # Verificar MTX_WALLET
            cursor.execute("""
                SELECT 
                    WALLET_NUMBER, STATUS, MODIFIED_ON,
                    ROW_NUMBER() OVER (PARTITION BY USER_ID ORDER BY MODIFIED_ON DESC) AS ORDEN
                FROM PDP_PROD10_MAINDBBUS.MTX_WALLET
                WHERE USER_ID = :user_id
                ORDER BY MODIFIED_ON DESC
            """, {'user_id': user_id})
            
            wallets = cursor.fetchall()
            print(f"    MTX_WALLET ({len(wallets)} wallets):")
            for j, wallet in enumerate(wallets[:3]):
                status_icon = "✅" if wallet[1] == 'Y' else "❌"
                print(f"      {j+1}. {wallet[0]} {status_icon} ORDEN={wallet[3]}")
            
            # Verificar USER_DATA_TRX
            cursor.execute("""
                SELECT WALLET_NUMBER
                FROM USR_DATALAKE.USER_DATA_TRX
                WHERE O_USER_ID = :user_id
            """, {'user_id': user_id})
            
            user_data = cursor.fetchone()
            if user_data:
                print(f"    USER_DATA_TRX.WALLET_NUMBER: {user_data[0]}")
            
            # Verificar qué usa Oracle PRE_LOG_TRX
            cursor.execute("""
                SELECT DISTINCT "To_AccountID_Mobiquity"
                FROM USR_DATALAKE.PRE_LOG_TRX
                WHERE "ToID_Mobiquity" = :user_id
                AND TRUNC("TransferDate") = TO_DATE(:fecha, 'YYYY-MM-DD')
            """, {'user_id': user_id, 'fecha': fecha})
            
            oracle_account = cursor.fetchone()
            if oracle_account:
                print(f"    Oracle PRE_LOG_TRX usa: {oracle_account[0]}")
            
            # Verificar qué usa S3
            s3_account = conn.execute(f"""
                SELECT DISTINCT "To_AccountID_Mobiquity"
                FROM read_parquet('{parquet_path}')
                WHERE "ToID_Mobiquity" = '{user_id}'
            """).fetchone()
            
            if s3_account:
                print(f"    S3 PRE_LOG_TRX usa: {s3_account[0]}")
        
        # 6. VERIFICACIÓN DE STORED PROCEDURES ADICIONALES
        print("\n6️⃣ VERIFICACIÓN DE STORED PROCEDURES ADICIONALES:")
        print("-" * 60)
        
        # Verificar si SP_PRE_LOG_TRX_UPDATE afecta los datos
        cursor.execute("""
            SELECT COUNT(*)
            FROM USR_DATALAKE.PRE_LOG_TRX
            WHERE TRUNC("TransferDate") = TO_DATE(:fecha, 'YYYY-MM-DD')
            AND "Context" = 'http-fcompartamos_niubiz_interope'
            AND "TransactionType" = 'REVERSAL'
            AND ("From_Msisdn" = '***********' OR "To_Msisdn" = '***********')
        """, {'fecha': fecha})
        
        update_affected = cursor.fetchone()[0]
        print(f"  Registros afectados por SP_PRE_LOG_TRX_UPDATE: {update_affected}")
        
        # 7. VERIFICACIÓN DE FIELD7 Y ATTR8_NULL
        print("\n7️⃣ VERIFICACIÓN DE FIELD7 Y ATTR8_NULL:")
        print("-" * 60)
        
        # Verificar FIELD7 NULL
        cursor.execute("""
            SELECT COUNT(*)
            FROM PDP_PROD10_MAINDBBUS.MTX_TRANSACTION_HEADER
            WHERE TRANSFER_DATE >= TO_DATE(:fecha, 'YYYY-MM-DD')
            AND TRANSFER_DATE < TO_DATE(:fecha, 'YYYY-MM-DD') + 1
            AND FIELD7 IS NULL
        """, {'fecha': fecha})
        
        field7_null = cursor.fetchone()[0]
        print(f"  MTX_TRANSACTION_HEADER con FIELD7 NULL: {field7_null}")
        
        # Verificar ATTR8 NULL
        cursor.execute("""
            SELECT COUNT(*)
            FROM PDP_PROD10_MAINDB.USER_PROFILE
            WHERE TRUNC(CREATED_ON) = TO_DATE(:fecha, 'YYYY-MM-DD')
            AND ATTR8 IS NULL
        """, {'fecha': fecha})
        
        attr8_null = cursor.fetchone()[0]
        print(f"  USER_PROFILE con ATTR8 NULL: {attr8_null}")
        
        cursor.close()
        connection.close()
        conn.close()
        
        # 8. CONCLUSIONES Y RECOMENDACIONES
        print("\n8️⃣ CONCLUSIONES Y RECOMENDACIONES:")
        print("-" * 60)
        print("1. Patrones identificados en las diferencias")
        print("2. Usuarios específicos que requieren análisis adicional")
        print("3. Verificar si hay lógica adicional en stored procedures")
        print("4. Investigar orden de ejecución y dependencias")
        
        return len(diferencias), usuarios_problematicos, patrones_diferencias
        
    except Exception as e:
        print(f"❌ Error en análisis: {e}")
        import traceback
        traceback.print_exc()
        return 0, set(), {}

def main():
    print("🚀 ANÁLISIS EXHAUSTIVO DIFERENCIAS RESTANTES")
    print("=" * 80)
    print("OBJETIVO: Identificar lógica faltante para 100% perfección")
    print()
    
    total_diferencias, usuarios_problematicos, patrones = analisis_exhaustivo_diferencias()
    
    print(f"\n🏁 ANÁLISIS COMPLETADO")
    print(f"📊 Total diferencias: {total_diferencias}")
    print(f"👥 Usuarios problemáticos: {len(usuarios_problematicos)}")
    print(f"🔍 Patrones identificados: {len(patrones)}")

if __name__ == "__main__":
    main()
