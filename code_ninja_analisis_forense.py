#!/usr/bin/env python3
"""
CODE NINJA: Análisis Forense para Homologación Perfecta
Investigación detallada Oracle vs S3 para identificar diferencias exactas
"""

import oracledb
import duckdb
import boto3
import pandas as pd
from datetime import datetime

def analisis_forense_completo():
    """Análisis forense detallado Oracle vs S3"""
    print("🥷 CODE NINJA: ANÁLISIS FORENSE PARA HOMOLOGACIÓN PERFECTA")
    print("=" * 80)
    
    fecha = '2025-06-18'
    
    try:
        # CONFIGURAR CONEXIONES
        print("🔧 CONFIGURANDO CONEXIONES:")
        print("-" * 60)
        
        # Oracle
        connection = oracledb.connect(
            user='usr_datalake',
            password='U2024b1mD4t4l4k5',
            dsn='10.240.131.10:1521/MMONEY'
        )
        cursor = connection.cursor()
        
        # DuckDB + S3
        conn = duckdb.connect()
        session = boto3.Session()
        credentials = session.get_credentials().get_frozen_credentials()
        
        conn.sql("INSTALL httpfs;")
        conn.sql("LOAD httpfs;")
        conn.sql("SET s3_region='us-east-1';")
        conn.sql("SET s3_use_ssl=true;")
        conn.sql("SET s3_url_style='path';")
        conn.sql(f"SET s3_access_key_id='{credentials.access_key}';")
        conn.sql(f"SET s3_secret_access_key='{credentials.secret_key}';")
        if credentials.token:
            conn.sql(f"SET s3_session_token='{credentials.token}';")
        
        print("  ✅ Conexiones establecidas")
        
        # 1. ANÁLISIS TABLAS FUENTE
        print(f"\n1️⃣ ANÁLISIS TABLAS FUENTE PRINCIPALES:")
        print("-" * 60)
        
        # MTX_TRANSACTION_HEADER
        print("📊 MTX_TRANSACTION_HEADER:")

        # Primero verificar qué esquemas existen
        cursor.execute("""
            SELECT DISTINCT owner, table_name
            FROM all_tables
            WHERE table_name = 'MTX_TRANSACTION_HEADER'
            ORDER BY owner
        """)
        schemas = cursor.fetchall()
        print(f"  Esquemas disponibles: {schemas}")

        # Usar el esquema correcto (probablemente PDP_PROD10_MAINDBBUS)
        schema_header = 'PDP_PROD10_MAINDBBUS'

        cursor.execute(f"""
            SELECT COUNT(*)
            FROM {schema_header}.MTX_TRANSACTION_HEADER
            WHERE CAST(TRANSFER_DATE AS DATE) = TO_DATE('{fecha}', 'YYYY-MM-DD')
            AND TRANSFER_STATUS IN ('TA','TS')
            AND TRANSFER_VALUE <> 0
        """)
        oracle_header = cursor.fetchone()[0]
        
        s3_header = conn.execute(f"""
            SELECT COUNT(*) 
            FROM read_parquet('s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_HEADER_ORA/2025/06/18/*.parquet')
            WHERE TRANSFER_STATUS IN ('TA','TS')
            AND TRANSFER_VALUE <> 0
        """).fetchone()[0]
        
        print(f"  Oracle: {oracle_header:,} | S3: {s3_header:,} | Diff: {s3_header - oracle_header:+,}")
        
        # MTX_TRANSACTION_ITEMS
        print("📊 MTX_TRANSACTION_ITEMS:")

        cursor.execute(f"""
            SELECT COUNT(*)
            FROM {schema_header}.MTX_TRANSACTION_ITEMS MTI
            INNER JOIN {schema_header}.MTX_TRANSACTION_HEADER MTH ON MTI.TRANSFER_ID = MTH.TRANSFER_ID
            WHERE CAST(MTH.TRANSFER_DATE AS DATE) = TO_DATE('{fecha}', 'YYYY-MM-DD')
            AND MTH.TRANSFER_STATUS IN ('TA','TS')
            AND MTH.TRANSFER_VALUE <> 0
        """)
        oracle_items = cursor.fetchone()[0]
        
        s3_items = conn.execute(f"""
            SELECT COUNT(*) 
            FROM read_parquet('s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_ITEMS_ORA/2025/06/18/*.parquet') MTI
            INNER JOIN read_parquet('s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_HEADER_ORA/2025/06/18/*.parquet') MTH 
                ON MTI.TRANSFER_ID = MTH.TRANSFER_ID
            WHERE MTH.TRANSFER_STATUS IN ('TA','TS')
            AND MTH.TRANSFER_VALUE <> 0
        """).fetchone()[0]
        
        print(f"  Oracle: {oracle_items:,} | S3: {s3_items:,} | Diff: {s3_items - oracle_items:+,}")
        
        # 2. ANÁLISIS PRE_LOG_TRX
        print(f"\n2️⃣ ANÁLISIS PRE_LOG_TRX (PASO INTERMEDIO):")
        print("-" * 60)
        
        cursor.execute(f"""
            SELECT COUNT(*) 
            FROM USR_DATALAKE.PRE_LOG_TRX
            WHERE CAST("TransferDate" AS DATE) = TO_DATE('{fecha}', 'YYYY-MM-DD')
        """)
        oracle_pre = cursor.fetchone()[0]
        
        s3_pre = conn.execute(f"""
            SELECT COUNT(*) 
            FROM read_parquet('/home/<USER>/REPORTE_2025/reports/generate/S3_LOG_TRANSACCIONES/TEMP_LOGS_TRANSACCIONES/20250618/PRE_LOG_TRX.parquet')
        """).fetchone()[0]
        
        print(f"  Oracle PRE_LOG_TRX: {oracle_pre:,}")
        print(f"  S3 PRE_LOG_TRX:     {s3_pre:,}")
        print(f"  Diferencia:         {s3_pre - oracle_pre:+,}")
        
        # 3. ANÁLISIS LOG_TRX_FINAL
        print(f"\n3️⃣ ANÁLISIS LOG_TRX_FINAL (RESULTADO FINAL):")
        print("-" * 60)
        
        cursor.execute(f"""
            SELECT COUNT(*) 
            FROM USR_DATALAKE.LOG_TRX_FINAL
            WHERE TRUNC(TO_DATE("DateTime", 'YYYY-MM-DD HH24:MI:SS')) = TO_DATE('{fecha}', 'YYYY-MM-DD')
        """)
        oracle_final = cursor.fetchone()[0]
        
        s3_final = conn.execute(f"""
            SELECT COUNT(*) 
            FROM read_parquet('/home/<USER>/REPORTE_2025/reports/generate/S3_LOG_TRANSACCIONES/TEMP_LOGS_TRANSACCIONES/20250618/LOG_TRX_FINAL.parquet')
        """).fetchone()[0]
        
        print(f"  Oracle LOG_TRX_FINAL: {oracle_final:,}")
        print(f"  S3 LOG_TRX_FINAL:     {s3_final:,}")
        print(f"  Diferencia:           {s3_final - oracle_final:+,}")
        
        # 4. ANÁLISIS DE FILTROS Y CONDICIONES
        print(f"\n4️⃣ ANÁLISIS FILTROS Y CONDICIONES:")
        print("-" * 60)
        
        # Verificar filtros de fecha específicos
        print("🔍 Verificando filtros de fecha:")
        
        cursor.execute(f"""
            SELECT
                TO_CHAR(TRANSFER_DATE, 'YYYY-MM-DD') as fecha,
                COUNT(*) as registros
            FROM {schema_header}.MTX_TRANSACTION_HEADER
            WHERE CAST(TRANSFER_DATE AS DATE) BETWEEN TO_DATE('{fecha}', 'YYYY-MM-DD') - 1
                AND TO_DATE('{fecha}', 'YYYY-MM-DD') + 1
            AND TRANSFER_STATUS IN ('TA','TS')
            AND TRANSFER_VALUE <> 0
            GROUP BY TO_CHAR(TRANSFER_DATE, 'YYYY-MM-DD')
            ORDER BY fecha
        """)
        
        oracle_dates = cursor.fetchall()
        print("  Oracle fechas cercanas:")
        for fecha_str, count in oracle_dates:
            print(f"    {fecha_str}: {count:,} registros")
        
        # 5. ANÁLISIS DE DUPLICADOS
        print(f"\n5️⃣ ANÁLISIS DE DUPLICADOS:")
        print("-" * 60)
        
        # Oracle duplicados
        cursor.execute(f"""
            SELECT COUNT(*) as total_records,
                   COUNT(DISTINCT "TransferID") as unique_transfer_ids
            FROM USR_DATALAKE.PRE_LOG_TRX
            WHERE CAST("TransferDate" AS DATE) = TO_DATE('{fecha}', 'YYYY-MM-DD')
        """)
        oracle_dup = cursor.fetchone()
        
        # S3 duplicados
        s3_dup = conn.execute(f"""
            SELECT COUNT(*) as total_records,
                   COUNT(DISTINCT "TransferID") as unique_transfer_ids
            FROM read_parquet('/home/<USER>/REPORTE_2025/reports/generate/S3_LOG_TRANSACCIONES/TEMP_LOGS_TRANSACCIONES/20250618/PRE_LOG_TRX.parquet')
        """).fetchone()
        
        print(f"  Oracle - Total: {oracle_dup[0]:,}, Únicos: {oracle_dup[1]:,}, Duplicados: {oracle_dup[0] - oracle_dup[1]:,}")
        print(f"  S3     - Total: {s3_dup[0]:,}, Únicos: {s3_dup[1]:,}, Duplicados: {s3_dup[0] - s3_dup[1]:,}")
        
        # 6. ANÁLISIS DE TRANSFORMACIONES ESPECÍFICAS
        print(f"\n6️⃣ ANÁLISIS TRANSFORMACIONES ESPECÍFICAS:")
        print("-" * 60)
        
        # Verificar transformación Context para REFUND
        cursor.execute(f"""
            SELECT 
                COUNT(*) as total_refunds,
                SUM(CASE WHEN "Context" = 'http-xml_awspdp' THEN 1 ELSE 0 END) as xml_awspdp_count
            FROM USR_DATALAKE.LOG_TRX_FINAL
            WHERE TRUNC(TO_DATE("DateTime", 'YYYY-MM-DD HH24:MI:SS')) = TO_DATE('{fecha}', 'YYYY-MM-DD')
            AND "TransactionType" = 'REFUND'
        """)
        oracle_refund = cursor.fetchone()
        
        s3_refund = conn.execute(f"""
            SELECT 
                COUNT(*) as total_refunds,
                SUM(CASE WHEN "Context" = 'http-xml_awspdp' THEN 1 ELSE 0 END) as xml_awspdp_count
            FROM read_parquet('/home/<USER>/REPORTE_2025/reports/generate/S3_LOG_TRANSACCIONES/TEMP_LOGS_TRANSACCIONES/20250618/LOG_TRX_FINAL.parquet')
            WHERE "TransactionType" = 'REFUND'
        """).fetchone()
        
        print(f"  REFUND Oracle - Total: {oracle_refund[0]:,}, xml_awspdp: {oracle_refund[1]:,}")
        print(f"  REFUND S3     - Total: {s3_refund[0]:,}, xml_awspdp: {s3_refund[1]:,}")
        
        cursor.close()
        connection.close()
        conn.close()
        
        # 7. DIAGNÓSTICO Y RECOMENDACIONES
        print(f"\n7️⃣ DIAGNÓSTICO Y RECOMENDACIONES:")
        print("-" * 60)
        
        print("🔍 POSIBLES CAUSAS DE LA DIFERENCIA:")
        
        if s3_header != oracle_header:
            print(f"  1. ❌ Diferencia en MTX_TRANSACTION_HEADER: {s3_header - oracle_header:+,}")
            print(f"     🔧 Revisar filtros de fecha y condiciones WHERE")
        
        if s3_items != oracle_items:
            print(f"  2. ❌ Diferencia en MTX_TRANSACTION_ITEMS: {s3_items - oracle_items:+,}")
            print(f"     🔧 Revisar JOINs entre HEADER e ITEMS")
        
        if s3_pre != oracle_pre:
            print(f"  3. ❌ Diferencia en PRE_LOG_TRX: {s3_pre - oracle_pre:+,}")
            print(f"     🔧 Revisar lógica de SP_PRE_LOG_TRX")
        
        if s3_final != oracle_final:
            print(f"  4. ❌ Diferencia en LOG_TRX_FINAL: {s3_final - oracle_final:+,}")
            print(f"     🔧 Revisar lógica de SP_LOG_TRX")
        
        print(f"\n🎯 RECOMENDACIONES ESPECÍFICAS:")
        print(f"  1. 🔍 Investigar filtros de fecha exactos")
        print(f"  2. 🔍 Verificar manejo de zona horaria")
        print(f"  3. 🔍 Revisar condiciones de JOIN")
        print(f"  4. 🔍 Analizar deduplicación")
        print(f"  5. 🔍 Verificar transformaciones de datos")
        
    except Exception as e:
        print(f"❌ Error en análisis forense: {e}")
        import traceback
        traceback.print_exc()

def main():
    print("🥷 CODE NINJA: ANÁLISIS FORENSE DETALLADO")
    print("=" * 80)
    print("OBJETIVO: Identificar origen exacto de diferencias")
    print("META: Homologación 100% perfecta")
    print()
    
    analisis_forense_completo()
    
    print("\n🏁 ANÁLISIS FORENSE COMPLETADO")

if __name__ == "__main__":
    main()
