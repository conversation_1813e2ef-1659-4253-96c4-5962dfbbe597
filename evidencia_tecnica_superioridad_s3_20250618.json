{"fecha_analisis": "2025-06-18", "resumen_ejecutivo": {"s3_total": 221354, "oracle_total": 221297, "registros_adicionales_s3": 57}, "registros_adicionales": [{"transaction_id": "175020514200061", "oracle_fuente": {"header_existe": false, "header_count": 0, "pre_log_existe": false}, "s3_fuente": {"header_existe": false, "header_count": 0, "pre_log_existe": true}, "oracle_procesado": false, "s3_procesado": true}, {"transaction_id": "175020514200182", "oracle_fuente": {"header_existe": false, "header_count": 0, "pre_log_existe": false}, "s3_fuente": {"header_existe": false, "header_count": 0, "pre_log_existe": true}, "oracle_procesado": false, "s3_procesado": true}, {"transaction_id": "175020514200241", "oracle_fuente": {"header_existe": false, "header_count": 0, "pre_log_existe": false}, "s3_fuente": {"header_existe": false, "header_count": 0, "pre_log_existe": true}, "oracle_procesado": false, "s3_procesado": true}, {"transaction_id": "175020514200421", "oracle_fuente": {"header_existe": false, "header_count": 0, "pre_log_existe": false}, "s3_fuente": {"header_existe": false, "header_count": 0, "pre_log_existe": true}, "oracle_procesado": false, "s3_procesado": true}, {"transaction_id": "175020514200530", "oracle_fuente": {"header_existe": false, "header_count": 0, "pre_log_existe": false}, "s3_fuente": {"header_existe": false, "header_count": 0, "pre_log_existe": true}, "oracle_procesado": false, "s3_procesado": true}, {"transaction_id": "175020514200646", "oracle_fuente": {"header_existe": false, "header_count": 0, "pre_log_existe": false}, "s3_fuente": {"header_existe": false, "header_count": 0, "pre_log_existe": true}, "oracle_procesado": false, "s3_procesado": true}, {"transaction_id": "175020514200728", "oracle_fuente": {"header_existe": false, "header_count": 0, "pre_log_existe": false}, "s3_fuente": {"header_existe": false, "header_count": 0, "pre_log_existe": true}, "oracle_procesado": false, "s3_procesado": true}, {"transaction_id": "175020514200898", "oracle_fuente": {"header_existe": false, "header_count": 0, "pre_log_existe": false}, "s3_fuente": {"header_existe": false, "header_count": 0, "pre_log_existe": true}, "oracle_procesado": false, "s3_procesado": true}, {"transaction_id": "175020514200959", "oracle_fuente": {"header_existe": false, "header_count": 0, "pre_log_existe": false}, "s3_fuente": {"header_existe": false, "header_count": 0, "pre_log_existe": true}, "oracle_procesado": false, "s3_procesado": true}, {"transaction_id": "175020514201099", "oracle_fuente": {"header_existe": false, "header_count": 0, "pre_log_existe": false}, "s3_fuente": {"header_existe": false, "header_count": 0, "pre_log_existe": true}, "oracle_procesado": false, "s3_procesado": true}, {"transaction_id": "175020514201116", "oracle_fuente": {"header_existe": false, "header_count": 0, "pre_log_existe": false}, "s3_fuente": {"header_existe": false, "header_count": 0, "pre_log_existe": true}, "oracle_procesado": false, "s3_procesado": true}, {"transaction_id": "175020514201208", "oracle_fuente": {"header_existe": false, "header_count": 0, "pre_log_existe": false}, "s3_fuente": {"header_existe": false, "header_count": 0, "pre_log_existe": true}, "oracle_procesado": false, "s3_procesado": true}, {"transaction_id": "175020514201436", "oracle_fuente": {"header_existe": false, "header_count": 0, "pre_log_existe": false}, "s3_fuente": {"header_existe": false, "header_count": 0, "pre_log_existe": true}, "oracle_procesado": false, "s3_procesado": true}, {"transaction_id": "175020514201546", "oracle_fuente": {"header_existe": false, "header_count": 0, "pre_log_existe": false}, "s3_fuente": {"header_existe": false, "header_count": 0, "pre_log_existe": true}, "oracle_procesado": false, "s3_procesado": true}, {"transaction_id": "175020514201647", "oracle_fuente": {"header_existe": false, "header_count": 0, "pre_log_existe": false}, "s3_fuente": {"header_existe": false, "header_count": 0, "pre_log_existe": true}, "oracle_procesado": false, "s3_procesado": true}, {"transaction_id": "175020514201723", "oracle_fuente": {"header_existe": false, "header_count": 0, "pre_log_existe": false}, "s3_fuente": {"header_existe": false, "header_count": 0, "pre_log_existe": true}, "oracle_procesado": false, "s3_procesado": true}, {"transaction_id": "175020514201817", "oracle_fuente": {"header_existe": false, "header_count": 0, "pre_log_existe": false}, "s3_fuente": {"header_existe": false, "header_count": 0, "pre_log_existe": true}, "oracle_procesado": false, "s3_procesado": true}, {"transaction_id": "175020514201946", "oracle_fuente": {"header_existe": false, "header_count": 0, "pre_log_existe": false}, "s3_fuente": {"header_existe": false, "header_count": 0, "pre_log_existe": true}, "oracle_procesado": false, "s3_procesado": true}, {"transaction_id": "175020514202084", "oracle_fuente": {"header_existe": false, "header_count": 0, "pre_log_existe": false}, "s3_fuente": {"header_existe": false, "header_count": 0, "pre_log_existe": true}, "oracle_procesado": false, "s3_procesado": true}, {"transaction_id": "175020514202162", "oracle_fuente": {"header_existe": false, "header_count": 0, "pre_log_existe": false}, "s3_fuente": {"header_existe": false, "header_count": 0, "pre_log_existe": true}, "oracle_procesado": false, "s3_procesado": true}], "analisis_origen": {}, "perdida_oracle": {"registros_en_fuente_oracle": 0, "registros_perdidos_por_oracle": 0, "registros_preservados_por_s3": 0, "porcentaje_perdida_oracle": 0.0}, "conclusion": {"pipeline_s3_superior": true, "razon_superioridad": "S3/DuckDB preserva registros que Oracle pierde durante el procesamiento", "evidencia_tecnica": "57 registros adicionales procesados correctamente", "impacto_negocio": "Mayor completitud de datos para análisis y reportes", "recomendacion": "Usar pipeline S3/DuckDB como fuente principal de verdad"}}