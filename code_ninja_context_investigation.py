#!/usr/bin/env python3
"""
CODE NINJA: Investigación exhaustiva columna "Context"
OBJETIVO: Homologación perfecta con Oracle
"""

import oracledb
import duckdb
import boto3
from collections import defaultdict

def code_ninja_context_investigation():
    """Investigación ninja de la columna Context"""
    print("🥷 CODE NINJA: INVESTIGACIÓN COLUMNA 'Context'")
    print("=" * 80)
    print("OBJETIVO: Homologación perfecta con Oracle")
    print()
    
    fecha = '2025-06-15'
    parquet_path_pre = '/home/<USER>/REPORTE_2025/reports/generate/S3_LOG_TRANSACCIONES/TEMP_LOGS_TRANSACCIONES/20250615/PRE_LOG_TRX.parquet'
    
    try:
        # 1. CONFIGURAR CONEXIONES NINJA
        print("1️⃣ CONFIGURANDO ARSENAL NINJA:")
        print("-" * 60)
        
        connection = oracledb.connect(
            user='usr_datalake',
            password='U2024b1mD4t4l4k5',
            dsn='10.240.131.10:1521/MMONEY'
        )
        cursor = connection.cursor()
        
        conn = duckdb.connect()
        session = boto3.Session()
        credentials = session.get_credentials().get_frozen_credentials()
        
        conn.sql("INSTALL httpfs;")
        conn.sql("LOAD httpfs;")
        conn.sql("SET s3_region='us-east-1';")
        conn.sql("SET s3_use_ssl=true;")
        conn.sql("SET s3_url_style='path';")
        conn.sql(f"SET s3_access_key_id='{credentials.access_key}';")
        conn.sql(f"SET s3_secret_access_key='{credentials.secret_key}';")
        if credentials.token:
            conn.sql(f"SET s3_session_token='{credentials.token}';")
        
        print("  ✅ Conexiones ninja activadas")
        
        # 2. ANÁLISIS ORACLE PRE_LOG_TRX
        print(f"\n2️⃣ ANÁLISIS ORACLE PRE_LOG_TRX:")
        print("-" * 60)
        
        cursor.execute(f"""
            SELECT 
                "Context",
                COUNT(*) as COUNT_RECORDS
            FROM USR_DATALAKE.PRE_LOG_TRX
            WHERE TRUNC("TransferDate") = TO_DATE('{fecha}', 'YYYY-MM-DD')
            GROUP BY "Context"
            ORDER BY COUNT(*) DESC
        """)
        
        oracle_context_stats = cursor.fetchall()
        
        print(f"  📊 DISTRIBUCIÓN ORACLE Context:")
        print(f"{'CONTEXT':<40} {'COUNT':<10}")
        print("-" * 55)
        
        oracle_context_map = {}
        for context, count in oracle_context_stats:
            context_str = str(context) if context is not None else 'NULL'
            oracle_context_map[context_str] = count
            print(f"{context_str:<40} {count:<10}")
        
        # 3. ANÁLISIS S3 PRE_LOG_TRX
        print(f"\n3️⃣ ANÁLISIS S3 PRE_LOG_TRX:")
        print("-" * 60)
        
        s3_context_stats = conn.execute(f"""
            SELECT 
                "Context",
                COUNT(*) as COUNT_RECORDS
            FROM read_parquet('{parquet_path_pre}')
            GROUP BY "Context"
            ORDER BY COUNT(*) DESC
        """).fetchall()
        
        print(f"  📊 DISTRIBUCIÓN S3 Context:")
        print(f"{'CONTEXT':<40} {'COUNT':<10}")
        print("-" * 55)
        
        s3_context_map = {}
        for context, count in s3_context_stats:
            context_str = str(context) if context is not None else 'NULL'
            s3_context_map[context_str] = count
            print(f"{context_str:<40} {count:<10}")
        
        # 4. COMPARACIÓN DETALLADA
        print(f"\n4️⃣ COMPARACIÓN DETALLADA:")
        print("-" * 60)
        
        all_contexts = set(oracle_context_map.keys()) | set(s3_context_map.keys())
        
        print(f"{'CONTEXT':<40} {'ORACLE':<10} {'S3':<10} {'DIFERENCIA':<12} {'STATUS'}")
        print("-" * 85)
        
        total_differences = 0
        for context in sorted(all_contexts):
            oracle_count = oracle_context_map.get(context, 0)
            s3_count = s3_context_map.get(context, 0)
            diff = abs(oracle_count - s3_count)
            status = "✅ OK" if diff == 0 else "❌ DIFF"
            
            if diff > 0:
                total_differences += diff
            
            print(f"{context:<40} {oracle_count:<10} {s3_count:<10} {diff:<12} {status}")
        
        print(f"\n  🎯 RESUMEN COMPARACIÓN:")
        print(f"    Total diferencias: {total_differences}")
        print(f"    Contextos únicos: {len(all_contexts)}")
        
        # 5. ANÁLISIS DE CASOS ESPECÍFICOS CON DIFERENCIAS
        print(f"\n5️⃣ ANÁLISIS CASOS ESPECÍFICOS:")
        print("-" * 60)
        
        # Buscar casos donde Context difiere
        cursor.execute(f"""
            SELECT 
                "TransferID",
                "Context",
                "TransactionType",
                "From_Msisdn",
                "To_Msisdn",
                "Canal"
            FROM USR_DATALAKE.PRE_LOG_TRX
            WHERE TRUNC("TransferDate") = TO_DATE('{fecha}', 'YYYY-MM-DD')
            AND ROWNUM <= 20
            ORDER BY "TransferID"
        """)
        
        oracle_samples = cursor.fetchall()
        
        print(f"  📋 MUESTRA ORACLE (primeros 20):")
        print(f"{'TRANSFER_ID':<20} {'CONTEXT':<30} {'TXN_TYPE':<15} {'CANAL'}")
        print("-" * 80)
        
        for row in oracle_samples[:10]:  # Solo primeros 10 para no saturar
            transfer_id = row[0]
            context = str(row[1]) if row[1] is not None else 'NULL'
            txn_type = row[2]
            canal = row[5]
            print(f"{transfer_id:<20} {context:<30} {txn_type:<15} {canal}")
        
        # 6. ANÁLISIS LÓGICA CONTEXT EN ORACLE
        print(f"\n6️⃣ ANÁLISIS LÓGICA CONTEXT ORACLE:")
        print("-" * 60)
        
        # Investigar la lógica de Context en Oracle
        cursor.execute(f"""
            SELECT 
                "Canal",
                "Context",
                COUNT(*) as COUNT_RECORDS
            FROM USR_DATALAKE.PRE_LOG_TRX
            WHERE TRUNC("TransferDate") = TO_DATE('{fecha}', 'YYYY-MM-DD')
            GROUP BY "Canal", "Context"
            ORDER BY "Canal", COUNT(*) DESC
        """)
        
        oracle_canal_context = cursor.fetchall()
        
        print(f"  📊 RELACIÓN CANAL → CONTEXT (Oracle):")
        print(f"{'CANAL':<30} {'CONTEXT':<30} {'COUNT'}")
        print("-" * 70)
        
        canal_context_map = defaultdict(list)
        for canal, context, count in oracle_canal_context:
            canal_str = str(canal) if canal is not None else 'NULL'
            context_str = str(context) if context is not None else 'NULL'
            canal_context_map[canal_str].append((context_str, count))
            print(f"{canal_str:<30} {context_str:<30} {count}")
        
        # 7. ANÁLISIS LÓGICA CONTEXT EN S3
        print(f"\n7️⃣ ANÁLISIS LÓGICA CONTEXT S3:")
        print("-" * 60)
        
        s3_canal_context = conn.execute(f"""
            SELECT 
                "Canal",
                "Context",
                COUNT(*) as COUNT_RECORDS
            FROM read_parquet('{parquet_path_pre}')
            GROUP BY "Canal", "Context"
            ORDER BY "Canal", COUNT(*) DESC
        """).fetchall()
        
        print(f"  📊 RELACIÓN CANAL → CONTEXT (S3):")
        print(f"{'CANAL':<30} {'CONTEXT':<30} {'COUNT'}")
        print("-" * 70)
        
        for canal, context, count in s3_canal_context:
            canal_str = str(canal) if canal is not None else 'NULL'
            context_str = str(context) if context is not None else 'NULL'
            print(f"{canal_str:<30} {context_str:<30} {count}")
        
        # 8. INVESTIGACIÓN FUENTE DE CONTEXT
        print(f"\n8️⃣ INVESTIGACIÓN FUENTE CONTEXT:")
        print("-" * 60)
        
        # Verificar de dónde viene Context en MTX_TRANSACTION_HEADER
        cursor.execute(f"""
            SELECT 
                REQUEST_GATEWAY_TYPE as CANAL,
                CONTEXT,
                COUNT(*) as COUNT_RECORDS
            FROM PDP_PROD10_MAINDBBUS.MTX_TRANSACTION_HEADER
            WHERE TRUNC(TRANSFER_DATE) = TO_DATE('{fecha}', 'YYYY-MM-DD')
            AND TRANSFER_STATUS IN ('TA','TS')
            GROUP BY REQUEST_GATEWAY_TYPE, CONTEXT
            ORDER BY REQUEST_GATEWAY_TYPE, COUNT(*) DESC
        """)
        
        mth_context_source = cursor.fetchall()
        
        print(f"  📊 FUENTE MTX_TRANSACTION_HEADER:")
        print(f"{'CANAL':<30} {'CONTEXT':<30} {'COUNT'}")
        print("-" * 70)
        
        for canal, context, count in mth_context_source[:20]:  # Primeros 20
            canal_str = str(canal) if canal is not None else 'NULL'
            context_str = str(context) if context is not None else 'NULL'
            print(f"{canal_str:<30} {context_str:<30} {count}")
        
        cursor.close()
        connection.close()
        conn.close()
        
        # 9. CONCLUSIONES NINJA
        print(f"\n9️⃣ CONCLUSIONES CODE NINJA:")
        print("-" * 60)
        print(f"1. 📊 Total diferencias encontradas: {total_differences}")
        print(f"2. 🔍 Contextos únicos identificados: {len(all_contexts)}")
        print(f"3. 📋 Relación CANAL → CONTEXT analizada")
        print(f"4. 🎯 Fuente original: MTX_TRANSACTION_HEADER.CONTEXT")
        
        if total_differences == 0:
            print(f"5. ✅ HOMOLOGACIÓN PERFECTA: Context ya coincide 100%")
        else:
            print(f"5. 🔧 CORRECCIÓN NECESARIA: Implementar lógica específica")
        
    except Exception as e:
        print(f"❌ Error en investigación ninja: {e}")
        import traceback
        traceback.print_exc()

def main():
    print("🥷 CODE NINJA: INVESTIGACIÓN CONTEXT")
    print("=" * 80)
    print("MISIÓN: Homologación perfecta columna Context")
    print()
    
    code_ninja_context_investigation()
    
    print("\n🏁 INVESTIGACIÓN NINJA COMPLETADA")

if __name__ == "__main__":
    main()
