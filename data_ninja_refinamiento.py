#!/usr/bin/env python3
"""
DATA NINJA: Refinamiento de lógica para casos específicos
"""

import oracledb

def data_ninja_refinamiento():
    """Refinamiento de lógica Data Ninja"""
    print("🥷 DATA NINJA: REFINAMIENTO DE LÓGICA")
    print("=" * 80)
    
    fecha = '2025-06-15'
    
    # Casos problemáticos actuales
    casos_nuevos = [
        '*************',   # FromAccountID y FromID diferentes
        '***************', # ToID diferente (mi corrección se aplicó mal)
        '*************'    # ToAccountID diferente
    ]
    
    try:
        connection = oracledb.connect(
            user='usr_datalake',
            password='U2024b1mD4t4l4k5',
            dsn='10.240.131.10:1521/MMONEY'
        )
        cursor = connection.cursor()
        
        print("1️⃣ ANÁLISIS REFINADO DE CASOS:")
        print("-" * 60)
        
        for caso in casos_nuevos:
            print(f"\n  📋 CASO: {caso}")
            print(f"  {'-' * 40}")
            
            # Obtener datos completos de PRE_LOG_TRX
            cursor.execute("""
                SELECT 
                    "TransferID",
                    "FromID",
                    "ToID", 
                    "From_AccountID",
                    "To_AccountID",
                    "FromID_Mobiquity",
                    "ToID_Mobiquity",
                    "From_AccountID_Mobiquity",
                    "To_AccountID_Mobiquity",
                    "TransactionType"
                FROM USR_DATALAKE.PRE_LOG_TRX
                WHERE "TransferID" = :caso
            """, {'caso': caso})
            
            pre_data = cursor.fetchone()
            
            # Obtener datos de LOG_TRX_FINAL
            cursor.execute("""
                SELECT 
                    "TransactionID",
                    "FromID",
                    "ToID",
                    "FromAccountID", 
                    "ToAccountID"
                FROM USR_DATALAKE.LOG_TRX_FINAL
                WHERE "TransactionID" = :caso
            """, {'caso': caso})
            
            log_data = cursor.fetchone()
            
            if pre_data and log_data:
                print(f"    PRE_LOG_TRX:")
                print(f"      FromID: {pre_data[1]}")
                print(f"      ToID: {pre_data[2]}")
                print(f"      From_AccountID: {pre_data[3]}")
                print(f"      To_AccountID: {pre_data[4]}")
                print(f"      FromID_Mobiquity: {pre_data[5]}")
                print(f"      ToID_Mobiquity: {pre_data[6]}")
                print(f"      From_AccountID_Mobiquity: {pre_data[7]}")
                print(f"      To_AccountID_Mobiquity: {pre_data[8]}")
                print(f"      TransactionType: {pre_data[9]}")
                
                print(f"\n    LOG_TRX_FINAL (Oracle):")
                print(f"      FromID: {log_data[1]}")
                print(f"      ToID: {log_data[2]}")
                print(f"      FromAccountID: {log_data[3]}")
                print(f"      ToAccountID: {log_data[4]}")
                
                print(f"\n    🧮 ANÁLISIS LÓGICA:")
                
                # Analizar FromID
                if str(log_data[1]) == str(pre_data[5]):  # FromID_Mobiquity
                    print(f"      FromID: Oracle usa FromID_Mobiquity ({pre_data[5]})")
                elif str(log_data[1]) == str(pre_data[1]):  # FromID
                    print(f"      FromID: Oracle usa FromID ({pre_data[1]})")
                else:
                    print(f"      FromID: Oracle usa valor desconocido ({log_data[1]})")
                
                # Analizar ToID
                if str(log_data[2]) == str(pre_data[6]):  # ToID_Mobiquity
                    print(f"      ToID: Oracle usa ToID_Mobiquity ({pre_data[6]})")
                elif str(log_data[2]) == str(pre_data[2]):  # ToID
                    print(f"      ToID: Oracle usa ToID ({pre_data[2]})")
                else:
                    print(f"      ToID: Oracle usa valor desconocido ({log_data[2]})")
                
                # Analizar FromAccountID
                if str(log_data[3]) == str(pre_data[7]):  # From_AccountID_Mobiquity
                    print(f"      FromAccountID: Oracle usa From_AccountID_Mobiquity ({pre_data[7]})")
                elif str(log_data[3]) == str(pre_data[3]):  # From_AccountID
                    print(f"      FromAccountID: Oracle usa From_AccountID ({pre_data[3]})")
                else:
                    print(f"      FromAccountID: Oracle usa valor desconocido ({log_data[3]})")
                
                # Analizar ToAccountID
                if str(log_data[4]) == str(pre_data[8]):  # To_AccountID_Mobiquity
                    print(f"      ToAccountID: Oracle usa To_AccountID_Mobiquity ({pre_data[8]})")
                elif str(log_data[4]) == str(pre_data[4]):  # To_AccountID
                    print(f"      ToAccountID: Oracle usa To_AccountID ({pre_data[4]})")
                else:
                    print(f"      ToAccountID: Oracle usa valor desconocido ({log_data[4]})")
        
        # 2. ANÁLISIS DE PATRÓN GENERAL
        print(f"\n2️⃣ ANÁLISIS DE PATRÓN GENERAL:")
        print("-" * 60)
        
        # Buscar todos los casos donde Oracle usa valores _Mobiquity
        cursor.execute("""
            SELECT 
                PLT."TransferID",
                PLT."FromID",
                PLT."FromID_Mobiquity",
                PLT."ToID",
                PLT."ToID_Mobiquity",
                PLT."From_AccountID",
                PLT."From_AccountID_Mobiquity",
                PLT."To_AccountID",
                PLT."To_AccountID_Mobiquity",
                LTF."FromID" as LOG_FromID,
                LTF."ToID" as LOG_ToID,
                LTF."FromAccountID" as LOG_FromAccountID,
                LTF."ToAccountID" as LOG_ToAccountID
            FROM USR_DATALAKE.PRE_LOG_TRX PLT
            INNER JOIN USR_DATALAKE.LOG_TRX_FINAL LTF ON PLT."TransferID" = LTF."TransactionID"
            WHERE TRUNC(PLT."TransferDate") = TO_DATE(:fecha, 'YYYY-MM-DD')
            AND (
                LTF."FromID" = PLT."FromID_Mobiquity" OR
                LTF."ToID" = PLT."ToID_Mobiquity" OR
                LTF."FromAccountID" = PLT."From_AccountID_Mobiquity" OR
                LTF."ToAccountID" = PLT."To_AccountID_Mobiquity"
            )
            AND ROWNUM <= 20
        """, {'fecha': fecha})
        
        mobiquity_cases = cursor.fetchall()
        
        print(f"  Casos donde Oracle usa valores _Mobiquity (primeros 20):")
        print(f"{'TRANSFER_ID':<20} {'CAMPO':<20} {'VALOR_MOBIQUITY'}")
        print("-" * 70)
        
        for row in mobiquity_cases:
            transfer_id = row[0]
            
            # Verificar qué campo usa _Mobiquity
            if str(row[9]) == str(row[2]):  # FromID usa FromID_Mobiquity
                print(f"{transfer_id:<20} {'FromID':<20} {row[2]}")
            if str(row[10]) == str(row[4]):  # ToID usa ToID_Mobiquity
                print(f"{transfer_id:<20} {'ToID':<20} {row[4]}")
            if str(row[11]) == str(row[6]):  # FromAccountID usa From_AccountID_Mobiquity
                print(f"{transfer_id:<20} {'FromAccountID':<20} {row[6]}")
            if str(row[12]) == str(row[8]):  # ToAccountID usa To_AccountID_Mobiquity
                print(f"{transfer_id:<20} {'ToAccountID':<20} {row[8]}")
        
        cursor.close()
        connection.close()
        
        print(f"\n3️⃣ CONCLUSIONES REFINADAS:")
        print("-" * 60)
        print(f"1. Oracle NO siempre usa _Mobiquity para 945661")
        print(f"2. Hay lógica más compleja que depende del contexto")
        print(f"3. Necesario análisis más granular por TransactionType")
        
    except Exception as e:
        print(f"❌ Error en refinamiento: {e}")
        import traceback
        traceback.print_exc()

def main():
    print("🥷 DATA NINJA: REFINAMIENTO")
    print("=" * 80)
    
    data_ninja_refinamiento()
    
    print("\n🏁 REFINAMIENTO COMPLETADO")

if __name__ == "__main__":
    main()
