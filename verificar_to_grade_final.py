#!/usr/bin/env python3
"""
Verificar To_Grade final después de la corrección
"""

import duckdb
import boto3

def verificar_to_grade_final():
    """Verifica To_Grade final"""
    print("🔍 VERIFICACIÓN: To_Grade FINAL")
    print("=" * 80)
    
    transfer_id = '5000074554'
    parquet_path = '/home/<USER>/REPORTE_2025/reports/generate/S3_LOG_TRANSACCIONES/TEMP_LOGS_TRANSACCIONES/20250615/PRE_LOG_TRX.parquet'
    
    try:
        conn = duckdb.connect()
        session = boto3.Session()
        credentials = session.get_credentials().get_frozen_credentials()
        
        conn.sql("INSTALL httpfs;")
        conn.sql("LOAD httpfs;")
        conn.sql("SET s3_region='us-east-1';")
        conn.sql("SET s3_use_ssl=true;")
        conn.sql("SET s3_url_style='path';")
        conn.sql(f"SET s3_access_key_id='{credentials.access_key}';")
        conn.sql(f"SET s3_secret_access_key='{credentials.secret_key}';")
        if credentials.token:
            conn.sql(f"SET s3_session_token='{credentials.token}';")
        
        # Verificar valor actual
        result = conn.execute(f"""
            SELECT 
                "TransferID",
                "To_Grade",
                "ToID_Mobiquity"
            FROM read_parquet('{parquet_path}')
            WHERE "TransferID" = '{transfer_id}'
        """).fetchone()
        
        if result:
            print(f"  TransferID: {result[0]}")
            print(f"  To_Grade: '{result[1]}'")
            print(f"  ToID_Mobiquity: {result[2]}")
            
            s3_value = result[1]
            oracle_expected = 'NORMAL ACCOUNT PROFILE'
            
            print(f"\n  Comparación:")
            print(f"    S3 actual: '{s3_value}'")
            print(f"    Oracle esperado: '{oracle_expected}'")
            print(f"    ¿Coinciden? {'✅ SÍ' if s3_value == oracle_expected else '❌ NO'}")
            
            if s3_value != oracle_expected:
                print(f"\n  Análisis:")
                print(f"    Longitud S3: {len(s3_value)}")
                print(f"    Longitud Oracle: {len(oracle_expected)}")
                
                # Verificar si contiene "GENERAL"
                if 'GENERAL' in s3_value:
                    print(f"    ❌ S3 aún contiene 'GENERAL'")
                    print(f"    🔧 La corrección REPLACE no se aplicó")
                else:
                    print(f"    ✅ S3 no contiene 'GENERAL'")
                    print(f"    🔍 Diferencia en otro aspecto")
        else:
            print(f"  ❌ TransferID {transfer_id} no encontrado")
        
        # Verificar estadísticas generales de To_Grade
        print(f"\n  📊 ESTADÍSTICAS GENERALES To_Grade:")
        print("-" * 50)
        
        stats = conn.execute(f"""
            SELECT 
                "To_Grade",
                COUNT(*) as CASOS
            FROM read_parquet('{parquet_path}')
            GROUP BY "To_Grade"
            ORDER BY COUNT(*) DESC
            LIMIT 10
        """).fetchall()
        
        print(f"{'TO_GRADE':<40} {'CASOS'}")
        print("-" * 50)
        
        for row in stats:
            print(f"{row[0]:<40} {row[1]}")
        
        # Verificar si hay casos con "GENERAL"
        general_count = conn.execute(f"""
            SELECT COUNT(*)
            FROM read_parquet('{parquet_path}')
            WHERE "To_Grade" LIKE '%GENERAL%'
        """).fetchone()[0]
        
        print(f"\n  📋 CASOS CON 'GENERAL': {general_count}")
        
        if general_count > 0:
            print("  ❌ La corrección REPLACE no se aplicó correctamente")
        else:
            print("  ✅ La corrección REPLACE se aplicó correctamente")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error en verificación: {e}")
        import traceback
        traceback.print_exc()

def main():
    print("🚀 VERIFICACIÓN To_Grade FINAL")
    print("=" * 80)
    
    verificar_to_grade_final()
    
    print("\n🏁 VERIFICACIÓN COMPLETADA")

if __name__ == "__main__":
    main()
