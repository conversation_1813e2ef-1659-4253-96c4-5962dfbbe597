#!/usr/bin/env python3
"""
Investigar criterio específico para transformación To_Grade
"""

import oracledb

def investigar_criterio_transformacion():
    """Investiga el criterio específico para la transformación"""
    print("🔍 INVESTIGACIÓN: CRITERIO TRANSFORMACIÓN To_Grade")
    print("=" * 80)
    
    fecha = '2025-06-15'
    
    try:
        connection = oracledb.connect(
            user='usr_datalake',
            password='U2024b1mD4t4l4k5',
            dsn='10.240.131.10:1521/MMONEY'
        )
        
        cursor = connection.cursor()
        
        print("1️⃣ COMPARAR ATRIBUTOS ENTRE CASOS TRANSFORMADOS Y NO TRANSFORMADOS:")
        print("-" * 80)
        
        # Comparar atributos de casos transformados vs no transformados
        cursor.execute("""
            WITH CASOS_TRANSFORMADOS AS (
                SELECT DISTINCT
                    PLT."ToID_Mobiquity",
                    PLT."TransactionType",
                    PLT."To_Profile",
                    PLT."From_Profile",
                    PLT."To_BankDomain",
                    PLT."From_BankDomain",
                    'TRANSFORMADO' as TIPO
                FROM USR_DATALAKE.PRE_LOG_TRX PLT
                INNER JOIN PDP_PROD10_MAINDBBUS.MTX_WALLET MW ON PLT."ToID_Mobiquity" = MW.USER_ID
                INNER JOIN PDP_PROD10_MAINDB.CHANNEL_GRADES CG ON MW.USER_GRADE = CG.GRADE_CODE
                WHERE TRUNC(PLT."TransferDate") = TO_DATE(:fecha, 'YYYY-MM-DD')
                AND PLT."To_Grade" = 'NORMAL ACCOUNT PROFILE'
                AND CG.GRADE_NAME = 'Normal General Account Profile'
                AND ROWNUM <= 50
            ),
            CASOS_NO_TRANSFORMADOS AS (
                SELECT DISTINCT
                    PLT."ToID_Mobiquity",
                    PLT."TransactionType",
                    PLT."To_Profile",
                    PLT."From_Profile",
                    PLT."To_BankDomain",
                    PLT."From_BankDomain",
                    'NO_TRANSFORMADO' as TIPO
                FROM USR_DATALAKE.PRE_LOG_TRX PLT
                INNER JOIN PDP_PROD10_MAINDBBUS.MTX_WALLET MW ON PLT."ToID_Mobiquity" = MW.USER_ID
                INNER JOIN PDP_PROD10_MAINDB.CHANNEL_GRADES CG ON MW.USER_GRADE = CG.GRADE_CODE
                WHERE TRUNC(PLT."TransferDate") = TO_DATE(:fecha, 'YYYY-MM-DD')
                AND PLT."To_Grade" = 'NORMAL GENERAL ACCOUNT PROFILE'
                AND CG.GRADE_NAME = 'Normal General Account Profile'
                AND ROWNUM <= 50
            )
            SELECT 
                'TransactionType' as ATRIBUTO,
                "TransactionType" as VALOR,
                TIPO,
                COUNT(*) as CASOS
            FROM (
                SELECT "TransactionType", TIPO FROM CASOS_TRANSFORMADOS
                UNION ALL
                SELECT "TransactionType", TIPO FROM CASOS_NO_TRANSFORMADOS
            )
            GROUP BY "TransactionType", TIPO
            ORDER BY ATRIBUTO, VALOR, TIPO
        """, {'fecha': fecha})
        
        transaction_types = cursor.fetchall()
        
        print(f"  TRANSACTION_TYPE:")
        print(f"{'VALOR':<20} {'TIPO':<20} {'CASOS'}")
        print("-" * 50)
        for row in transaction_types:
            print(f"{row[1]:<20} {row[2]:<20} {row[3]}")
        
        # Comparar To_Profile
        cursor.execute("""
            WITH CASOS_TRANSFORMADOS AS (
                SELECT DISTINCT
                    PLT."To_Profile"
                FROM USR_DATALAKE.PRE_LOG_TRX PLT
                INNER JOIN PDP_PROD10_MAINDBBUS.MTX_WALLET MW ON PLT."ToID_Mobiquity" = MW.USER_ID
                INNER JOIN PDP_PROD10_MAINDB.CHANNEL_GRADES CG ON MW.USER_GRADE = CG.GRADE_CODE
                WHERE TRUNC(PLT."TransferDate") = TO_DATE(:fecha, 'YYYY-MM-DD')
                AND PLT."To_Grade" = 'NORMAL ACCOUNT PROFILE'
                AND CG.GRADE_NAME = 'Normal General Account Profile'
            ),
            CASOS_NO_TRANSFORMADOS AS (
                SELECT DISTINCT
                    PLT."To_Profile"
                FROM USR_DATALAKE.PRE_LOG_TRX PLT
                INNER JOIN PDP_PROD10_MAINDBBUS.MTX_WALLET MW ON PLT."ToID_Mobiquity" = MW.USER_ID
                INNER JOIN PDP_PROD10_MAINDB.CHANNEL_GRADES CG ON MW.USER_GRADE = CG.GRADE_CODE
                WHERE TRUNC(PLT."TransferDate") = TO_DATE(:fecha, 'YYYY-MM-DD')
                AND PLT."To_Grade" = 'NORMAL GENERAL ACCOUNT PROFILE'
                AND CG.GRADE_NAME = 'Normal General Account Profile'
            )
            SELECT 'TRANSFORMADOS' as TIPO, "To_Profile" FROM CASOS_TRANSFORMADOS
            UNION ALL
            SELECT 'NO_TRANSFORMADOS' as TIPO, "To_Profile" FROM CASOS_NO_TRANSFORMADOS
            ORDER BY TIPO, "To_Profile"
        """, {'fecha': fecha})
        
        profiles = cursor.fetchall()
        
        print(f"\n  TO_PROFILE:")
        print(f"{'TIPO':<20} {'TO_PROFILE'}")
        print("-" * 60)
        for row in profiles:
            print(f"{row[0]:<20} {row[1]}")
        
        print(f"\n2️⃣ INVESTIGAR DIFERENCIAS EN WALLET:")
        print("-" * 80)
        
        # Verificar si hay diferencias en el wallet usado
        cursor.execute("""
            SELECT 
                PLT."ToID_Mobiquity",
                PLT."To_Grade",
                PLT."To_AccountID_Mobiquity",
                MW.WALLET_NUMBER,
                MW.STATUS,
                MW.MODIFIED_ON,
                ROW_NUMBER() OVER (PARTITION BY MW.USER_ID ORDER BY MW.MODIFIED_ON DESC) as WALLET_ORDER
            FROM USR_DATALAKE.PRE_LOG_TRX PLT
            INNER JOIN PDP_PROD10_MAINDBBUS.MTX_WALLET MW ON PLT."ToID_Mobiquity" = MW.USER_ID
            WHERE PLT."TransferID" IN ('**********', '*************')
            ORDER BY PLT."TransferID", MW.MODIFIED_ON DESC
        """)
        
        wallet_details = cursor.fetchall()
        
        print(f"  Detalles de wallet para casos específicos:")
        print(f"{'USER_ID':<15} {'TO_GRADE':<30} {'TO_ACCOUNT_ID':<20} {'WALLET_NUM':<15} {'STATUS':<8} {'ORDER'}")
        print("-" * 100)
        for row in wallet_details:
            print(f"{row[0]:<15} {row[1]:<30} {row[2]:<20} {row[3]:<15} {row[4]:<8} {row[6]}")
        
        print(f"\n3️⃣ VERIFICAR SI HAY LÓGICA EN SP_PRE_LOG_TRX:")
        print("-" * 80)
        
        # Verificar si hay alguna condición específica en el SP
        print(f"  Revisando stored procedure para lógica condicional...")
        
        # Buscar patrones en los USER_ID
        cursor.execute("""
            SELECT 
                CASE 
                    WHEN PLT."To_Grade" = 'NORMAL ACCOUNT PROFILE' THEN 'TRANSFORMADO'
                    ELSE 'NO_TRANSFORMADO'
                END as TIPO,
                PLT."ToID_Mobiquity",
                LENGTH(PLT."ToID_Mobiquity") as USER_ID_LENGTH,
                CASE 
                    WHEN PLT."ToID_Mobiquity" LIKE 'US.%' THEN 'US_PREFIX'
                    WHEN REGEXP_LIKE(PLT."ToID_Mobiquity", '^[0-9]+$') THEN 'NUMERIC'
                    ELSE 'OTHER'
                END as USER_ID_TYPE
            FROM USR_DATALAKE.PRE_LOG_TRX PLT
            INNER JOIN PDP_PROD10_MAINDBBUS.MTX_WALLET MW ON PLT."ToID_Mobiquity" = MW.USER_ID
            INNER JOIN PDP_PROD10_MAINDB.CHANNEL_GRADES CG ON MW.USER_GRADE = CG.GRADE_CODE
            WHERE TRUNC(PLT."TransferDate") = TO_DATE(:fecha, 'YYYY-MM-DD')
            AND CG.GRADE_NAME = 'Normal General Account Profile'
            AND ROWNUM <= 20
            ORDER BY PLT."ToID_Mobiquity"
        """, {'fecha': fecha})
        
        user_patterns = cursor.fetchall()
        
        print(f"  Patrones en USER_ID:")
        print(f"{'TIPO':<20} {'USER_ID':<20} {'LENGTH':<8} {'TYPE'}")
        print("-" * 60)
        for row in user_patterns:
            print(f"{row[0]:<20} {row[1]:<20} {row[2]:<8} {row[3]}")
        
        cursor.close()
        connection.close()
        
        print(f"\n4️⃣ CONCLUSIONES:")
        print("-" * 80)
        print(f"1. Analizar diferencias en TransactionType, Profile, etc.")
        print(f"2. Verificar si hay patrón en USER_ID (US. prefix vs numeric)")
        print(f"3. Revisar lógica específica en SP_PRE_LOG_TRX")
        print(f"4. Puede ser lógica basada en wallet order o status")
        
    except Exception as e:
        print(f"❌ Error en investigación: {e}")
        import traceback
        traceback.print_exc()

def main():
    print("🚀 INVESTIGACIÓN CRITERIO TRANSFORMACIÓN")
    print("=" * 80)
    print("OBJETIVO: Identificar criterio específico para transformación")
    print()
    
    investigar_criterio_transformacion()
    
    print("\n🏁 INVESTIGACIÓN COMPLETADA")

if __name__ == "__main__":
    main()
