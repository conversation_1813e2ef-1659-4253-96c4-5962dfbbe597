#!/usr/bin/env python3
"""
CODE NINJA: Homologación específica LOG_TRX_FINAL Context
TransactionID = '5000074119'
"""

import oracledb
import duckdb
import boto3

def code_ninja_log_trx_context():
    """Investigación ninja específica LOG_TRX_FINAL Context"""
    print("🥷 CODE NINJA: HOMOLOGACIÓN LOG_TRX_FINAL Context")
    print("=" * 80)
    print("TARGET: TransactionID = '5000074119'")
    print("TABLA: LOG_TRX_FINAL")
    print()
    
    target_txn = '5000074119'
    fecha = '2025-06-15'
    parquet_path_log = '/home/<USER>/REPORTE_2025/reports/generate/S3_LOG_TRANSACCIONES/TEMP_LOGS_TRANSACCIONES/20250615/LOG_TRX_FINAL.parquet'
    
    try:
        # 1. CONFIGURAR CONEXIONES NINJA
        print("1️⃣ CONFIGURANDO ARSENAL NINJA:")
        print("-" * 60)
        
        connection = oracledb.connect(
            user='usr_datalake',
            password='U2024b1mD4t4l4k5',
            dsn='10.240.131.10:1521/MMONEY'
        )
        cursor = connection.cursor()
        
        conn = duckdb.connect()
        session = boto3.Session()
        credentials = session.get_credentials().get_frozen_credentials()
        
        conn.sql("INSTALL httpfs;")
        conn.sql("LOAD httpfs;")
        conn.sql("SET s3_region='us-east-1';")
        conn.sql("SET s3_use_ssl=true;")
        conn.sql("SET s3_url_style='path';")
        conn.sql(f"SET s3_access_key_id='{credentials.access_key}';")
        conn.sql(f"SET s3_secret_access_key='{credentials.secret_key}';")
        if credentials.token:
            conn.sql(f"SET s3_session_token='{credentials.token}';")
        
        print("  ✅ Conexiones ninja activadas")
        
        # 2. CONSULTA ORACLE LOG_TRX_FINAL
        print(f"\n2️⃣ ORACLE LOG_TRX_FINAL - TARGET: {target_txn}")
        print("-" * 60)
        
        cursor.execute(f"""
            SELECT "TransactionID", "Context"
            FROM USR_DATALAKE.LOG_TRX_FINAL
            WHERE TRUNC(TO_DATE("DateTime", 'YYYY-MM-DD HH24:MI:SS')) = TO_DATE('{fecha}', 'YYYY-MM-DD')
            AND "TransactionID" = '{target_txn}'
        """)
        
        oracle_result = cursor.fetchone()
        
        if oracle_result:
            print(f"  📋 ORACLE LOG_TRX_FINAL:")
            print(f"    TransactionID: {oracle_result[0]}")
            print(f"    Context: {oracle_result[1]} ⭐ TARGET VALUE")
        else:
            print(f"  ❌ NO ENCONTRADO en Oracle LOG_TRX_FINAL")
        
        # 3. CONSULTA S3 LOG_TRX_FINAL
        print(f"\n3️⃣ S3 LOG_TRX_FINAL - TARGET: {target_txn}")
        print("-" * 60)
        
        s3_result = conn.execute(f"""
            SELECT "TransactionID", "Context"
            FROM read_parquet('{parquet_path_log}')
            WHERE "TransactionID" = '{target_txn}'
        """).fetchone()
        
        if s3_result:
            print(f"  📋 S3 LOG_TRX_FINAL:")
            print(f"    TransactionID: {s3_result[0]}")
            print(f"    Context: {s3_result[1]} ⭐ CURRENT VALUE")
        else:
            print(f"  ❌ NO ENCONTRADO en S3 LOG_TRX_FINAL")
        
        # 4. COMPARACIÓN ESPECÍFICA
        print(f"\n4️⃣ COMPARACIÓN ESPECÍFICA:")
        print("-" * 60)
        
        if oracle_result and s3_result:
            oracle_context = str(oracle_result[1]) if oracle_result[1] is not None else 'NULL'
            s3_context = str(s3_result[1]) if s3_result[1] is not None else 'NULL'
            
            print(f"  📊 COMPARACIÓN Context:")
            print(f"    Oracle: '{oracle_context}'")
            print(f"    S3:     '{s3_context}'")
            
            if oracle_context == s3_context:
                print(f"    ✅ COINCIDE PERFECTAMENTE")
            else:
                print(f"    ❌ DIFERENCIA DETECTADA")
                print(f"    🔧 CORRECCIÓN NECESARIA")
        
        # 5. ANÁLISIS DISTRIBUCIÓN GENERAL LOG_TRX_FINAL
        print(f"\n5️⃣ ANÁLISIS DISTRIBUCIÓN GENERAL:")
        print("-" * 60)
        
        # Oracle distribución
        cursor.execute(f"""
            SELECT "Context", COUNT(*) as COUNT_RECORDS
            FROM USR_DATALAKE.LOG_TRX_FINAL
            WHERE TRUNC(TO_DATE("DateTime", 'YYYY-MM-DD HH24:MI:SS')) = TO_DATE('{fecha}', 'YYYY-MM-DD')
            GROUP BY "Context"
            ORDER BY COUNT(*) DESC
        """)
        
        oracle_distribution = cursor.fetchall()
        
        print(f"  📊 ORACLE LOG_TRX_FINAL Context:")
        print(f"{'CONTEXT':<40} {'COUNT'}")
        print("-" * 50)
        
        oracle_context_map = {}
        for context, count in oracle_distribution:
            context_str = str(context) if context is not None else 'NULL'
            oracle_context_map[context_str] = count
            print(f"{context_str:<40} {count}")
        
        # S3 distribución
        s3_distribution = conn.execute(f"""
            SELECT "Context", COUNT(*) as COUNT_RECORDS
            FROM read_parquet('{parquet_path_log}')
            GROUP BY "Context"
            ORDER BY COUNT(*) DESC
        """).fetchall()
        
        print(f"\n  📊 S3 LOG_TRX_FINAL Context:")
        print(f"{'CONTEXT':<40} {'COUNT'}")
        print("-" * 50)
        
        s3_context_map = {}
        for context, count in s3_distribution:
            context_str = str(context) if context is not None else 'NULL'
            s3_context_map[context_str] = count
            print(f"{context_str:<40} {count}")
        
        # 6. COMPARACIÓN DISTRIBUCIÓN COMPLETA
        print(f"\n6️⃣ COMPARACIÓN DISTRIBUCIÓN COMPLETA:")
        print("-" * 60)
        
        all_contexts = set(oracle_context_map.keys()) | set(s3_context_map.keys())
        
        print(f"{'CONTEXT':<40} {'ORACLE':<10} {'S3':<10} {'DIFERENCIA':<12} {'STATUS'}")
        print("-" * 85)
        
        total_differences = 0
        for context in sorted(all_contexts):
            oracle_count = oracle_context_map.get(context, 0)
            s3_count = s3_context_map.get(context, 0)
            diff = abs(oracle_count - s3_count)
            status = "✅ OK" if diff == 0 else "❌ DIFF"
            
            if diff > 0:
                total_differences += diff
            
            print(f"{context:<40} {oracle_count:<10} {s3_count:<10} {diff:<12} {status}")
        
        # 7. INVESTIGACIÓN ORIGEN Context EN LOG_TRX_FINAL
        print(f"\n7️⃣ INVESTIGACIÓN ORIGEN Context:")
        print("-" * 60)
        
        # Verificar de dónde viene Context en LOG_TRX_FINAL (debe venir de PRE_LOG_TRX)
        cursor.execute(f"""
            SELECT 
                PLT."TransferID",
                PLT."Context" as PRE_CONTEXT,
                LTF."Context" as LOG_CONTEXT
            FROM USR_DATALAKE.PRE_LOG_TRX PLT
            INNER JOIN USR_DATALAKE.LOG_TRX_FINAL LTF ON PLT."TransferID" = LTF."TransactionID"
            WHERE PLT."TransferID" = '{target_txn}'
        """)
        
        context_origin = cursor.fetchone()
        
        if context_origin:
            print(f"  📋 ORIGEN Context para {target_txn}:")
            print(f"    PRE_LOG_TRX Context: {context_origin[1]}")
            print(f"    LOG_TRX_FINAL Context: {context_origin[2]}")
            
            if str(context_origin[1]) == str(context_origin[2]):
                print(f"    ✅ Context se preserva correctamente PRE → LOG")
            else:
                print(f"    ❌ Context cambia entre PRE → LOG")
        
        cursor.close()
        connection.close()
        conn.close()
        
        # 8. CONCLUSIONES NINJA
        print(f"\n8️⃣ CONCLUSIONES CODE NINJA:")
        print("-" * 60)
        
        if oracle_result and s3_result:
            if oracle_context == s3_context:
                print(f"1. ✅ TransactionID {target_txn}: Context HOMOLOGADO")
            else:
                print(f"1. ❌ TransactionID {target_txn}: Context REQUIERE CORRECCIÓN")
                print(f"   Oracle: '{oracle_context}' → S3: '{s3_context}'")
        
        print(f"2. 📊 Distribución general diferencias: {total_differences}")
        
        if total_differences == 0:
            print(f"3. ✅ LOG_TRX_FINAL Context: HOMOLOGACIÓN PERFECTA")
        else:
            print(f"3. 🔧 LOG_TRX_FINAL Context: CORRECCIÓN NECESARIA")
        
    except Exception as e:
        print(f"❌ Error en investigación ninja: {e}")
        import traceback
        traceback.print_exc()

def main():
    print("🥷 CODE NINJA: HOMOLOGACIÓN LOG_TRX_FINAL Context")
    print("=" * 80)
    print("MISIÓN: Verificar TransactionID específico")
    print()
    
    code_ninja_log_trx_context()
    
    print("\n🏁 INVESTIGACIÓN NINJA COMPLETADA")

if __name__ == "__main__":
    main()
