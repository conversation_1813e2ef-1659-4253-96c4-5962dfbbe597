#!/usr/bin/env python3
"""
CODE NINJA: Ingeniería Inversa Completa
Rastrear casos desde LOG_TRX_FINAL hacia atrás hasta tabla origen
"""

import oracledb
import duckdb
import boto3

def ingenieria_inversa_completa():
    """Ingeniería inversa completa de casos S3 y Oracle"""
    print("🥷 CODE NINJA: INGENIERÍA INVERSA COMPLETA")
    print("=" * 80)
    print("OBJETIVO: Rastrear casos desde resultado final hasta tabla origen")
    print()
    
    # Los 57 casos problemáticos
    casos_57 = [
        '175020514200061', '175020514200182', '175020514200241', '175020514200421', '175020514200530',
        '175020514200646', '175020514200728', '175020514200898', '175020514200959', '175020514201099',
        '175020514201116', '175020514201208', '175020514201436', '175020514201546', '175020514201647',
        '175020514201723', '175020514201817', '175020514201946', '175020514202084', '175020514202162',
        '175020514202275', '175020514202363', '175020514202579', '175020514202681', '175020514202724',
        '175020514202865', '175020514202972', '175020514203090', '175020514203132', '175020514203215',
        '175020514203322', '175020514203490', '175020514203653', '175020514203745', '175020514203909',
        '175020514204059', '175020514204182', '175020514204290', '175020514204351', '175020514204462',
        '175020514204530', '175020514204717', '175020514204983', '175020514205033', '175020514205132',
        '175020514205361', '175020514205488', '175020514205532', '175020514205638', '175020514205826',
        '175020514205920', '175020514206091', '175020514206185', '175020514299909', '175029151203224',
        '175029151206399', '5000077909'
    ]
    
    try:
        # CONFIGURAR CONEXIONES
        print("🔧 CONFIGURANDO CONEXIONES:")
        print("-" * 60)
        
        # Oracle
        connection = oracledb.connect(
            user='usr_datalake',
            password='U2024b1mD4t4l4k5',
            dsn='10.240.131.10:1521/MMONEY'
        )
        cursor = connection.cursor()
        
        # DuckDB + S3
        conn = duckdb.connect()
        session = boto3.Session()
        credentials = session.get_credentials().get_frozen_credentials()
        
        conn.sql("INSTALL httpfs;")
        conn.sql("LOAD httpfs;")
        conn.sql("SET s3_region='us-east-1';")
        conn.sql("SET s3_use_ssl=true;")
        conn.sql("SET s3_url_style='path';")
        conn.sql(f"SET s3_access_key_id='{credentials.access_key}';")
        conn.sql(f"SET s3_secret_access_key='{credentials.secret_key}';")
        if credentials.token:
            conn.sql(f"SET s3_session_token='{credentials.token}';")
        
        print("  ✅ Conexiones establecidas")
        
        # 1. INGENIERÍA INVERSA DE LOS 57 CASOS (S3)
        print(f"\n1️⃣ INGENIERÍA INVERSA DE LOS 57 CASOS (S3):")
        print("-" * 60)
        
        casos_str = "', '".join(casos_57[:10])  # Analizar primeros 10 para ejemplo
        
        print(f"  🔍 PASO 1: Verificar en LOG_TRX_FINAL S3")
        log_final_s3 = conn.execute(f"""
            SELECT "TransactionID", "DateTime", "TransactionType", "FromID", "ToID"
            FROM read_parquet('/home/<USER>/REPORTE_2025/reports/generate/S3_LOG_TRANSACCIONES/TEMP_LOGS_TRANSACCIONES/20250618/LOG_TRX_FINAL.parquet')
            WHERE "TransactionID" IN ('{casos_str}')
            ORDER BY "TransactionID"
        """).fetchall()
        
        print(f"    📊 Casos en LOG_TRX_FINAL S3: {len(log_final_s3)}")
        
        print(f"\n  🔍 PASO 2: Rastrear en PRE_LOG_TRX S3")
        pre_log_s3 = conn.execute(f"""
            SELECT "TransferID", "TransferDate", "TransactionType", "FromID_Mobiquity", "ToID_Mobiquity"
            FROM read_parquet('/home/<USER>/REPORTE_2025/reports/generate/S3_LOG_TRANSACCIONES/TEMP_LOGS_TRANSACCIONES/20250618/PRE_LOG_TRX.parquet')
            WHERE "TransferID" IN ('{casos_str}')
            ORDER BY "TransferID"
        """).fetchall()
        
        print(f"    📊 Casos en PRE_LOG_TRX S3: {len(pre_log_s3)}")
        
        print(f"\n  🔍 PASO 3: Buscar en USER_DATA_TRX (Golden Zone)")
        try:
            user_data_s3 = conn.execute(f"""
                SELECT USER_ID, O_USER_ID, WALLET_NUMBER, PROFILE_TRX
                FROM read_parquet('s3://prd-datalake-golden-zone-637423440311/LOGS_USUARIOS/USER_DATA_TRX.parquet')
                WHERE USER_ID IN ('{casos_str}') OR O_USER_ID IN ('{casos_str}')
                LIMIT 10
            """).fetchall()
            
            print(f"    📊 Casos en USER_DATA_TRX: {len(user_data_s3)}")
            
        except Exception as e:
            print(f"    ❌ Error en USER_DATA_TRX: {str(e)[:50]}...")
        
        print(f"\n  🔍 PASO 4: Buscar en todas las fechas MTX_TRANSACTION_HEADER")
        fechas_buscar = ['2025/06/15', '2025/06/16', '2025/06/17', '2025/06/18', '2025/06/19', '2025/06/20']
        
        total_encontrados_header = 0
        for fecha in fechas_buscar:
            try:
                header_fecha = conn.execute(f"""
                    SELECT COUNT(*), MIN(TRANSFER_DATE), MAX(TRANSFER_DATE)
                    FROM read_parquet('s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_HEADER_ORA/{fecha}/*.parquet')
                    WHERE TRANSFER_ID IN ('{casos_str}')
                """).fetchone()
                
                if header_fecha and header_fecha[0] > 0:
                    total_encontrados_header += header_fecha[0]
                    print(f"    📊 {fecha}: {header_fecha[0]} casos encontrados")
                    
                    # Obtener detalles
                    detalles = conn.execute(f"""
                        SELECT TRANSFER_ID, TRANSFER_DATE, TRANSFER_STATUS, TRANSFER_VALUE
                        FROM read_parquet('s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_HEADER_ORA/{fecha}/*.parquet')
                        WHERE TRANSFER_ID IN ('{casos_str}')
                        LIMIT 3
                    """).fetchall()
                    
                    for detalle in detalles:
                        print(f"      {detalle[0]} | {detalle[1]} | {detalle[2]} | {detalle[3]}")
                        
            except Exception as e:
                print(f"    ❌ Error {fecha}: {str(e)[:30]}...")
        
        print(f"    📊 Total encontrados en HEADER: {total_encontrados_header}")
        
        # 2. INGENIERÍA INVERSA DE CASOS ORACLE (CASOS QUE SÍ EXISTEN)
        print(f"\n2️⃣ INGENIERÍA INVERSA DE CASOS ORACLE (CASOS QUE SÍ EXISTEN):")
        print("-" * 60)
        
        print(f"  🔍 PASO 1: Obtener muestra de casos que SÍ existen en Oracle")
        cursor.execute(f"""
            SELECT "TransactionID", "DateTime", "TransactionType", "FromID", "ToID"
            FROM USR_DATALAKE.LOG_TRX_FINAL
            WHERE TRUNC(TO_DATE("DateTime", 'YYYY-MM-DD HH24:MI:SS')) = TO_DATE('2025-06-18', 'YYYY-MM-DD')
            AND ROWNUM <= 10
            ORDER BY "TransactionID"
        """)
        
        casos_oracle = cursor.fetchall()
        print(f"    📊 Muestra de casos Oracle: {len(casos_oracle)}")
        
        if casos_oracle:
            casos_oracle_ids = [caso[0] for caso in casos_oracle]
            casos_oracle_str = "', '".join(casos_oracle_ids)
            
            print(f"  🔍 PASO 2: Rastrear casos Oracle en PRE_LOG_TRX Oracle")
            cursor.execute(f"""
                SELECT "TransferID", "TransferDate", "TransactionType"
                FROM USR_DATALAKE.PRE_LOG_TRX
                WHERE "TransferID" IN ('{casos_oracle_str}')
                AND CAST("TransferDate" AS DATE) = TO_DATE('2025-06-18', 'YYYY-MM-DD')
            """)
            
            pre_oracle = cursor.fetchall()
            print(f"    📊 Casos en PRE_LOG_TRX Oracle: {len(pre_oracle)}")
            
            print(f"  🔍 PASO 3: Buscar casos Oracle en MTX_TRANSACTION_HEADER Oracle")
            cursor.execute(f"""
                SELECT COUNT(*), MIN(TRANSFER_DATE), MAX(TRANSFER_DATE)
                FROM PDP_PROD10_MAINDBBUS.MTX_TRANSACTION_HEADER
                WHERE TRANSFER_ID IN ('{casos_oracle_str}')
                AND TRANSFER_DATE >= TO_DATE('2025-06-17', 'YYYY-MM-DD')
                AND TRANSFER_DATE <= TO_DATE('2025-06-19', 'YYYY-MM-DD')
            """)
            
            header_oracle = cursor.fetchone()
            print(f"    📊 Casos en HEADER Oracle: {header_oracle[0] if header_oracle else 0}")
            
            print(f"  🔍 PASO 4: Buscar casos Oracle en MTX_TRANSACTION_HEADER S3")
            try:
                header_oracle_s3 = conn.execute(f"""
                    SELECT COUNT(*)
                    FROM read_parquet('s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_HEADER_ORA/2025/06/18/*.parquet')
                    WHERE TRANSFER_ID IN ('{casos_oracle_str}')
                """).fetchone()
                
                print(f"    📊 Casos Oracle en HEADER S3: {header_oracle_s3[0] if header_oracle_s3 else 0}")
                
            except Exception as e:
                print(f"    ❌ Error buscando Oracle en S3: {str(e)[:50]}...")
        
        # 3. ANÁLISIS COMPARATIVO
        print(f"\n3️⃣ ANÁLISIS COMPARATIVO:")
        print("-" * 60)
        
        print(f"  📊 RESUMEN INGENIERÍA INVERSA:")
        print(f"    🔍 57 CASOS PROBLEMÁTICOS:")
        print(f"      - LOG_TRX_FINAL S3: ✅ {len(log_final_s3)} casos")
        print(f"      - PRE_LOG_TRX S3: ✅ {len(pre_log_s3)} casos")
        print(f"      - HEADER S3 (todas fechas): {'✅' if total_encontrados_header > 0 else '❌'} {total_encontrados_header} casos")
        print(f"      - USER_DATA_TRX: {'✅' if 'user_data_s3' in locals() and len(user_data_s3) > 0 else '❌'}")
        print()
        print(f"    🔍 CASOS ORACLE (CONTROL):")
        print(f"      - LOG_TRX_FINAL Oracle: ✅ {len(casos_oracle)} casos")
        print(f"      - PRE_LOG_TRX Oracle: {'✅' if 'pre_oracle' in locals() and len(pre_oracle) > 0 else '❌'}")
        print(f"      - HEADER Oracle: {'✅' if 'header_oracle' in locals() and header_oracle and header_oracle[0] > 0 else '❌'}")
        print(f"      - HEADER S3: {'✅' if 'header_oracle_s3' in locals() and header_oracle_s3 and header_oracle_s3[0] > 0 else '❌'}")
        
        # 4. CONCLUSIONES
        print(f"\n4️⃣ CONCLUSIONES INGENIERÍA INVERSA:")
        print("-" * 60)
        
        if total_encontrados_header > 0:
            print(f"  🎯 ORIGEN ENCONTRADO:")
            print(f"    ✅ Los 57 casos SÍ existen en MTX_TRANSACTION_HEADER S3")
            print(f"    ✅ Están en fechas diferentes a las esperadas")
            print(f"    🔍 Problema: Diferencia en timing de procesamiento")
        else:
            print(f"  🎯 ORIGEN NO ENCONTRADO:")
            print(f"    ❌ Los 57 casos NO existen en MTX_TRANSACTION_HEADER S3")
            print(f"    🤔 Posible origen: USER_DATA_TRX u otra tabla")
            print(f"    🔍 Requiere investigación adicional")
        
        print(f"\n  🎯 COMPARACIÓN CON ORACLE:")
        if 'header_oracle' in locals() and header_oracle and header_oracle[0] > 0:
            print(f"    ✅ Casos Oracle SÍ tienen origen en HEADER")
            print(f"    📊 Diferencia: Oracle procesa casos con origen, S3 procesa casos sin origen claro")
        else:
            print(f"    ❌ Casos Oracle TAMBIÉN sin origen claro en HEADER")
            print(f"    🤔 Problema general de sincronización")
        
        cursor.close()
        connection.close()
        conn.close()
        
    except Exception as e:
        print(f"❌ Error en ingeniería inversa: {e}")
        import traceback
        traceback.print_exc()

def main():
    print("🥷 CODE NINJA: INGENIERÍA INVERSA COMPLETA")
    print("=" * 80)
    print("MISIÓN: Rastrear casos desde resultado final hasta tabla origen")
    print()
    
    ingenieria_inversa_completa()
    
    print("\n🏁 INGENIERÍA INVERSA COMPLETADA")

if __name__ == "__main__":
    main()
