#!/usr/bin/env python3
"""
Investigar específicamente To_Grade para TransferID 5000074554
"""

import oracledb
import duckdb
import boto3

def investigar_to_grade_transferid():
    """Investiga To_Grade para TransferID específico"""
    print("🔍 INVESTIGACIÓN: To_Grade para TransferID 5000074554")
    print("=" * 80)
    
    transfer_id = '5000074554'
    fecha = '2025-06-15'
    parquet_path = '/home/<USER>/REPORTE_2025/reports/generate/S3_LOG_TRANSACCIONES/TEMP_LOGS_TRANSACCIONES/20250615/PRE_LOG_TRX.parquet'
    
    try:
        # 1. VERIFICAR EN ORACLE PRE_LOG_TRX
        print("1️⃣ VERIFICACIÓN EN ORACLE PRE_LOG_TRX:")
        print("-" * 60)
        
        connection = oracledb.connect(
            user='usr_datalake',
            password='U2024b1mD4t4l4k5',
            dsn='10.240.131.10:1521/MMONEY'
        )
        
        cursor = connection.cursor()
        
        cursor.execute("""
            SELECT 
                "TransferID",
                "To_Grade",
                "ToID_Mobiquity",
                "To_Profile",
                "To_AccountID_Mobiquity",
                "TransactionType"
            FROM USR_DATALAKE.PRE_LOG_TRX
            WHERE TRUNC("TransferDate") = TO_DATE(:fecha, 'YYYY-MM-DD')
            AND "TransferID" = :transfer_id
        """, {'fecha': fecha, 'transfer_id': transfer_id})
        
        oracle_result = cursor.fetchone()
        if oracle_result:
            print(f"  TransferID: {oracle_result[0]}")
            print(f"  To_Grade: {oracle_result[1]} ⭐ VALOR ORACLE")
            print(f"  ToID_Mobiquity: {oracle_result[2]}")
            print(f"  To_Profile: {oracle_result[3]}")
            print(f"  To_AccountID_Mobiquity: {oracle_result[4]}")
            print(f"  TransactionType: {oracle_result[5]}")
            
            oracle_to_grade = oracle_result[1]
            oracle_to_user_id = oracle_result[2]
        else:
            print("  ❌ No encontrado en Oracle PRE_LOG_TRX")
            return
        
        # 2. VERIFICAR EN S3 PRE_LOG_TRX
        print("\n2️⃣ VERIFICACIÓN EN S3 PRE_LOG_TRX:")
        print("-" * 60)
        
        conn = duckdb.connect()
        session = boto3.Session()
        credentials = session.get_credentials().get_frozen_credentials()
        
        conn.sql("INSTALL httpfs;")
        conn.sql("LOAD httpfs;")
        conn.sql("SET s3_region='us-east-1';")
        conn.sql("SET s3_use_ssl=true;")
        conn.sql("SET s3_url_style='path';")
        conn.sql(f"SET s3_access_key_id='{credentials.access_key}';")
        conn.sql(f"SET s3_secret_access_key='{credentials.secret_key}';")
        if credentials.token:
            conn.sql(f"SET s3_session_token='{credentials.token}';")
        
        s3_result = conn.execute(f"""
            SELECT 
                "TransferID",
                "To_Grade",
                "ToID_Mobiquity",
                "To_Profile",
                "To_AccountID_Mobiquity",
                "TransactionType"
            FROM read_parquet('{parquet_path}')
            WHERE "TransferID" = '{transfer_id}'
        """).fetchone()
        
        if s3_result:
            print(f"  TransferID: {s3_result[0]}")
            print(f"  To_Grade: {s3_result[1]} ⭐ VALOR S3")
            print(f"  ToID_Mobiquity: {s3_result[2]}")
            print(f"  To_Profile: {s3_result[3]}")
            print(f"  To_AccountID_Mobiquity: {s3_result[4]}")
            print(f"  TransactionType: {s3_result[5]}")
            
            s3_to_grade = s3_result[1]
        else:
            print("  ❌ No encontrado en S3 PRE_LOG_TRX")
            return
        
        # 3. COMPARACIÓN
        print("\n3️⃣ COMPARACIÓN:")
        print("-" * 60)
        
        print(f"  Oracle To_Grade: {oracle_to_grade}")
        print(f"  S3 To_Grade: {s3_to_grade}")
        
        if str(oracle_to_grade) == str(s3_to_grade):
            print("  ✅ COINCIDEN PERFECTAMENTE")
            return
        else:
            print("  ❌ NO COINCIDEN - INVESTIGAR ORIGEN")
        
        # 4. INVESTIGAR ORIGEN DE To_Grade EN ORACLE
        print("\n4️⃣ INVESTIGAR ORIGEN DE To_Grade EN ORACLE:")
        print("-" * 60)
        
        # Verificar en MTX_TRANSACTION_HEADER
        cursor.execute("""
            SELECT
                TRANSFER_ID,
                PAYEE_USER_ID,
                TRANSFER_DATE
            FROM PDP_PROD10_MAINDBBUS.MTX_TRANSACTION_HEADER
            WHERE TRANSFER_ID = :transfer_id
        """, {'transfer_id': transfer_id})
        
        mth_result = cursor.fetchone()
        if mth_result:
            print(f"  MTX_TRANSACTION_HEADER:")
            print(f"    TRANSFER_ID: {mth_result[0]}")
            print(f"    PAYEE_USER_ID: {mth_result[1]}")
            print(f"    TRANSFER_DATE: {mth_result[2]}")

            payee_user_id = mth_result[1]
        else:
            print("  ❌ No encontrado en MTX_TRANSACTION_HEADER")
            return
        
        # 5. VERIFICAR EN MTX_WALLET PARA OBTENER USER_GRADE
        print("\n5️⃣ VERIFICAR EN MTX_WALLET PARA USER_GRADE:")
        print("-" * 60)
        
        cursor.execute("""
            SELECT 
                USER_ID,
                WALLET_NUMBER,
                USER_GRADE,
                STATUS,
                MODIFIED_ON,
                ROW_NUMBER() OVER (PARTITION BY USER_ID ORDER BY MODIFIED_ON DESC) AS ORDEN
            FROM PDP_PROD10_MAINDBBUS.MTX_WALLET
            WHERE USER_ID = :user_id
            ORDER BY MODIFIED_ON DESC
        """, {'user_id': payee_user_id})
        
        wallet_results = cursor.fetchall()
        print(f"  MTX_WALLET para USER_ID {payee_user_id}:")
        print(f"{'#':<3} {'WALLET_NUMBER':<20} {'USER_GRADE':<12} {'STATUS':<8} {'ORDEN':<6}")
        print("-" * 60)
        
        for i, row in enumerate(wallet_results):
            wallet_number = row[1]
            user_grade = row[2]
            status = row[3]
            orden = row[5]
            
            status_icon = "✅" if status == 'Y' else "❌"
            orden_icon = "⭐" if orden == 1 else ""
            
            print(f"{i+1:<3} {wallet_number:<20} {user_grade:<12} {status_icon}{status:<7} {orden:<6} {orden_icon}")
        
        # 6. VERIFICAR LÓGICA EN SP_PRE_LOG_TRX
        print("\n6️⃣ VERIFICAR LÓGICA EN SP_PRE_LOG_TRX:")
        print("-" * 60)
        
        if wallet_results:
            # Oracle usa ORDEN=1 (más reciente por MODIFIED_ON)
            wallet_orden_1 = wallet_results[0]
            oracle_expected_grade = wallet_orden_1[2]
            
            print(f"  Lógica Oracle SP_PRE_LOG_TRX:")
            print(f"    - Usa MTX_WALLET con ORDEN=1 (más reciente)")
            print(f"    - WALLET_NUMBER: {wallet_orden_1[1]}")
            print(f"    - USER_GRADE esperado: {oracle_expected_grade}")
            print(f"    - USER_GRADE Oracle actual: {oracle_to_grade}")
            
            if str(oracle_expected_grade) == str(oracle_to_grade):
                print("    ✅ Oracle usa lógica correcta")
            else:
                print("    ❌ Oracle NO usa lógica esperada")
        
        # 7. VERIFICAR LÓGICA EN S3
        print("\n7️⃣ VERIFICAR LÓGICA EN S3:")
        print("-" * 60)
        
        print(f"  S3 To_Grade actual: {s3_to_grade}")
        if wallet_results:
            print(f"  S3 debería usar: {oracle_expected_grade} (ORDEN=1)")
            
            if str(s3_to_grade) == str(oracle_expected_grade):
                print("  ✅ S3 usa lógica correcta")
            else:
                print("  ❌ S3 NO usa lógica correcta")
                print("  🔧 CORRECCIÓN NECESARIA en pipeline S3")
        
        cursor.close()
        connection.close()
        conn.close()
        
        # 8. CONCLUSIONES
        print("\n8️⃣ CONCLUSIONES:")
        print("-" * 60)
        print(f"1. Oracle To_Grade: {oracle_to_grade}")
        print(f"2. S3 To_Grade: {s3_to_grade}")
        print(f"3. Esperado (MTX_WALLET ORDEN=1): {oracle_expected_grade if wallet_results else 'N/A'}")
        
        if str(oracle_to_grade) != str(s3_to_grade):
            print("4. ❌ DIFERENCIA IDENTIFICADA")
            print("5. 🔧 Revisar lógica de To_Grade en pipeline S3")
        else:
            print("4. ✅ COINCIDENCIA PERFECTA")
        
    except Exception as e:
        print(f"❌ Error en investigación: {e}")
        import traceback
        traceback.print_exc()

def main():
    print("🚀 INVESTIGACIÓN To_Grade TransferID 5000074554")
    print("=" * 80)
    print("OBJETIVO: Homologar To_Grade específico")
    print()
    
    investigar_to_grade_transferid()
    
    print("\n🏁 INVESTIGACIÓN COMPLETADA")

if __name__ == "__main__":
    main()
