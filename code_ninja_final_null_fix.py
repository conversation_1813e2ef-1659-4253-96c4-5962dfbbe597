#!/usr/bin/env python3
"""
CODE NINJA: Corrección final NULL vs '' para 100% absoluto
"""

import oracledb

def investigar_null_vs_empty():
    """Investigar casos NULL vs '' en ToID"""
    print("🥷 CODE NINJA: CORRECCIÓN FINAL NULL vs ''")
    print("=" * 80)
    
    casos_null = ['5000074115', '5000074116', '5000074117', '5000074118', '5000074120']
    
    try:
        connection = oracledb.connect(
            user='usr_datalake',
            password='U2024b1mD4t4l4k5',
            dsn='10.240.131.10:1521/MMONEY'
        )
        cursor = connection.cursor()
        
        print("1️⃣ INVESTIGANDO CASOS NULL:")
        print("-" * 60)
        
        for caso in casos_null:
            cursor.execute("""
                SELECT 
                    PLT."TransferID",
                    PLT."ToID",
                    PLT."ToID_Mobiquity",
                    PLT."TransactionType",
                    LTF."ToID" as LOG_ToID
                FROM USR_DATALAKE.PRE_LOG_TRX PLT
                INNER JOIN USR_DATALAKE.LOG_TRX_FINAL LTF ON PLT."TransferID" = LTF."TransactionID"
                WHERE PLT."TransferID" = :caso
            """, {'caso': caso})
            
            data = cursor.fetchone()
            if data:
                print(f"  📋 {caso}:")
                print(f"    PRE ToID: {data[1]}")
                print(f"    PRE ToID_Mobiquity: {data[2]}")
                print(f"    TransactionType: {data[3]}")
                print(f"    LOG ToID: {data[4]} ({'NULL' if data[4] is None else data[4]})")
        
        cursor.close()
        connection.close()
        
        print(f"\n2️⃣ CONCLUSIÓN:")
        print("-" * 60)
        print(f"Oracle devuelve NULL, S3 devuelve '' (vacío)")
        print(f"Corrección: Convertir '' a NULL en casos específicos")
        
    except Exception as e:
        print(f"❌ Error: {e}")

def main():
    investigar_null_vs_empty()

if __name__ == "__main__":
    main()
