#!/usr/bin/env python3
"""
Verificar tabla CHANNEL_GRADES para mapeo de USER_GRADE
"""

import oracledb

def verificar_channel_grades():
    """Verifica tabla CHANNEL_GRADES"""
    print("🔍 VERIFICACIÓN: TABLA CHANNEL_GRADES")
    print("=" * 80)
    
    try:
        connection = oracledb.connect(
            user='usr_datalake',
            password='U2024b1mD4t4l4k5',
            dsn='10.240.131.10:1521/MMONEY'
        )
        
        cursor = connection.cursor()
        
        print("1️⃣ VERIFICAR MAPEO ESPECÍFICO FUNGAPGRA:")
        print("-" * 60)
        
        # Verificar mapeo específico para FUNGAPGRA
        cursor.execute("""
            SELECT 
                GRADE_CODE,
                GRADE_NAME,
                STATUS,
                CREATED_ON
            FROM PDP_PROD10_MAINDB.CHANNEL_GRADES
            WHERE GRADE_CODE = 'FUNGAPGRA'
        """)
        
        fungapgra_result = cursor.fetchone()
        if fungapgra_result:
            print(f"  GRADE_CODE: {fungapgra_result[0]}")
            print(f"  GRADE_NAME: {fungapgra_result[1]} ⭐ MAPEO ORACLE")
            print(f"  STATUS: {fungapgra_result[2]}")
            print(f"  CREATED_ON: {fungapgra_result[3]}")
        else:
            print("  ❌ FUNGAPGRA no encontrado en CHANNEL_GRADES")
        
        print("\n2️⃣ VERIFICAR TODOS LOS MAPEOS DISPONIBLES:")
        print("-" * 60)
        
        # Verificar todos los mapeos
        cursor.execute("""
            SELECT 
                GRADE_CODE,
                GRADE_NAME,
                STATUS
            FROM PDP_PROD10_MAINDB.CHANNEL_GRADES
            WHERE STATUS = 'Y'
            ORDER BY GRADE_CODE
        """)
        
        all_grades = cursor.fetchall()
        print(f"  Total mapeos activos: {len(all_grades)}")
        print(f"{'GRADE_CODE':<20} {'GRADE_NAME':<40} {'STATUS'}")
        print("-" * 70)
        
        for row in all_grades:
            print(f"{row[0]:<20} {row[1]:<40} {row[2]}")
        
        print("\n3️⃣ VERIFICAR MAPEOS USADOS EN PRE_LOG_TRX:")
        print("-" * 60)
        
        # Verificar qué mapeos se usan realmente
        cursor.execute("""
            SELECT DISTINCT
                "To_Grade",
                COUNT(*) as CASOS
            FROM USR_DATALAKE.PRE_LOG_TRX
            WHERE TRUNC("TransferDate") = TO_DATE('2025-06-15', 'YYYY-MM-DD')
            GROUP BY "To_Grade"
            ORDER BY COUNT(*) DESC
        """)
        
        used_grades = cursor.fetchall()
        print(f"  Grades usados en PRE_LOG_TRX (2025-06-15):")
        print(f"{'TO_GRADE':<40} {'CASOS'}")
        print("-" * 50)
        
        for row in used_grades:
            print(f"{row[0]:<40} {row[1]}")
        
        print("\n4️⃣ VERIFICAR MAPEO INVERSO:")
        print("-" * 60)
        
        # Buscar qué GRADE_CODE mapea a "NORMAL ACCOUNT PROFILE"
        cursor.execute("""
            SELECT 
                GRADE_CODE,
                GRADE_NAME
            FROM PDP_PROD10_MAINDB.CHANNEL_GRADES
            WHERE GRADE_NAME = 'NORMAL ACCOUNT PROFILE'
            AND STATUS = 'Y'
        """)
        
        normal_account_result = cursor.fetchone()
        if normal_account_result:
            print(f"  'NORMAL ACCOUNT PROFILE' viene de:")
            print(f"    GRADE_CODE: {normal_account_result[0]}")
            print(f"    GRADE_NAME: {normal_account_result[1]}")
        
        # Buscar qué GRADE_CODE mapea a "NORMAL GENERAL ACCOUNT PROFILE"
        cursor.execute("""
            SELECT 
                GRADE_CODE,
                GRADE_NAME
            FROM PDP_PROD10_MAINDB.CHANNEL_GRADES
            WHERE GRADE_NAME = 'NORMAL GENERAL ACCOUNT PROFILE'
            AND STATUS = 'Y'
        """)
        
        normal_general_result = cursor.fetchone()
        if normal_general_result:
            print(f"  'NORMAL GENERAL ACCOUNT PROFILE' viene de:")
            print(f"    GRADE_CODE: {normal_general_result[0]}")
            print(f"    GRADE_NAME: {normal_general_result[1]}")
        
        cursor.close()
        connection.close()
        
        print("\n5️⃣ CONCLUSIONES:")
        print("-" * 60)
        
        if fungapgra_result:
            oracle_mapping = fungapgra_result[1]
            print(f"1. FUNGAPGRA → '{oracle_mapping}' (Oracle correcto)")
            print("2. S3 debe usar tabla CHANNEL_GRADES para mapeo")
            print("3. Implementar JOIN con CHANNEL_GRADES en pipeline S3")
        else:
            print("1. FUNGAPGRA no tiene mapeo en CHANNEL_GRADES")
            print("2. Investigar lógica alternativa")
        
    except Exception as e:
        print(f"❌ Error en verificación: {e}")
        import traceback
        traceback.print_exc()

def main():
    print("🚀 VERIFICACIÓN CHANNEL_GRADES")
    print("=" * 80)
    print("OBJETIVO: Entender mapeo USER_GRADE → GRADE_NAME")
    print()
    
    verificar_channel_grades()
    
    print("\n🏁 VERIFICACIÓN COMPLETADA")

if __name__ == "__main__":
    main()
