#!/usr/bin/env python3
"""
DATA NINJA: Investigación láser enfocada en FromID para TransactionID específico
"""

import oracledb
import duckdb
import boto3

def data_ninja_fromid_laser():
    """Investigación láser enfocada en FromID"""
    print("🥷 DATA NINJA: INVESTIGACIÓN LÁSER FromID")
    print("=" * 80)
    print("TARGET: TransactionID = '175003230421458'")
    print("OBJETIVO: Homologación PERFECTA de FromID")
    print()
    
    target_txn = '175003230421458'
    fecha = '2025-06-15'
    parquet_path_pre = '/home/<USER>/REPORTE_2025/reports/generate/S3_LOG_TRANSACCIONES/TEMP_LOGS_TRANSACCIONES/20250615/PRE_LOG_TRX.parquet'
    parquet_path_log = '/home/<USER>/REPORTE_2025/reports/generate/S3_LOG_TRANSACCIONES/TEMP_LOGS_TRANSACCIONES/20250615/LOG_TRX_FINAL.parquet'
    
    try:
        # 1. CONFIGURAR CONEXIONES NINJA
        print("1️⃣ CONFIGURANDO CONEXIONES NINJA:")
        print("-" * 60)
        
        connection = oracledb.connect(
            user='usr_datalake',
            password='U2024b1mD4t4l4k5',
            dsn='10.240.131.10:1521/MMONEY'
        )
        cursor = connection.cursor()
        
        conn = duckdb.connect()
        session = boto3.Session()
        credentials = session.get_credentials().get_frozen_credentials()
        
        conn.sql("INSTALL httpfs;")
        conn.sql("LOAD httpfs;")
        conn.sql("SET s3_region='us-east-1';")
        conn.sql("SET s3_use_ssl=true;")
        conn.sql("SET s3_url_style='path';")
        conn.sql(f"SET s3_access_key_id='{credentials.access_key}';")
        conn.sql(f"SET s3_secret_access_key='{credentials.secret_key}';")
        if credentials.token:
            conn.sql(f"SET s3_session_token='{credentials.token}';")
        
        print("  ✅ Conexiones ninja activadas")
        
        # 2. DATOS ORACLE PRE_LOG_TRX
        print(f"\n2️⃣ ORACLE PRE_LOG_TRX - TARGET: {target_txn}")
        print("-" * 60)
        
        cursor.execute("""
            SELECT 
                "TransferID",
                "FromID",
                "FromID_Mobiquity",
                "TransactionType",
                "From_BankDomain",
                "From_AccountID",
                "From_AccountID_Mobiquity"
            FROM USR_DATALAKE.PRE_LOG_TRX
            WHERE "TransferID" = :target_txn
        """, {'target_txn': target_txn})
        
        oracle_pre = cursor.fetchone()
        if oracle_pre:
            print(f"  📋 ORACLE PRE_LOG_TRX:")
            print(f"    TransferID: {oracle_pre[0]}")
            print(f"    FromID: {oracle_pre[1]} ⭐")
            print(f"    FromID_Mobiquity: {oracle_pre[2]} ⭐")
            print(f"    TransactionType: {oracle_pre[3]}")
            print(f"    From_BankDomain: {oracle_pre[4]}")
            print(f"    From_AccountID: {oracle_pre[5]}")
            print(f"    From_AccountID_Mobiquity: {oracle_pre[6]}")
        
        # 3. DATOS S3 PRE_LOG_TRX
        print(f"\n3️⃣ S3 PRE_LOG_TRX - TARGET: {target_txn}")
        print("-" * 60)
        
        s3_pre = conn.execute(f"""
            SELECT 
                "TransferID",
                "FromID",
                "FromID_Mobiquity",
                "TransactionType",
                "From_BankDomain",
                "From_AccountID",
                "From_AccountID_Mobiquity"
            FROM read_parquet('{parquet_path_pre}')
            WHERE "TransferID" = '{target_txn}'
        """).fetchone()
        
        if s3_pre:
            print(f"  📋 S3 PRE_LOG_TRX:")
            print(f"    TransferID: {s3_pre[0]}")
            print(f"    FromID: {s3_pre[1]} ⭐")
            print(f"    FromID_Mobiquity: {s3_pre[2]} ⭐")
            print(f"    TransactionType: {s3_pre[3]}")
            print(f"    From_BankDomain: {s3_pre[4]}")
            print(f"    From_AccountID: {s3_pre[5]}")
            print(f"    From_AccountID_Mobiquity: {s3_pre[6]}")
        
        # 4. COMPARACIÓN PRE_LOG_TRX
        print(f"\n4️⃣ COMPARACIÓN PRE_LOG_TRX:")
        print("-" * 60)
        
        if oracle_pre and s3_pre:
            campos = ['TransferID', 'FromID', 'FromID_Mobiquity', 'TransactionType', 'From_BankDomain', 'From_AccountID', 'From_AccountID_Mobiquity']
            
            for i, campo in enumerate(campos):
                oracle_val = str(oracle_pre[i]) if oracle_pre[i] is not None else 'NULL'
                s3_val = str(s3_pre[i]) if s3_pre[i] is not None else 'NULL'
                
                if oracle_val == s3_val:
                    print(f"    {campo}: ✅ COINCIDE ({oracle_val})")
                else:
                    print(f"    {campo}: ❌ DIFIERE (Oracle: '{oracle_val}' vs S3: '{s3_val}')")
        
        # 5. DATOS ORACLE LOG_TRX_FINAL
        print(f"\n5️⃣ ORACLE LOG_TRX_FINAL - TARGET: {target_txn}")
        print("-" * 60)
        
        cursor.execute("""
            SELECT 
                "TransactionID",
                "FromID"
            FROM USR_DATALAKE.LOG_TRX_FINAL
            WHERE "TransactionID" = :target_txn
        """, {'target_txn': target_txn})
        
        oracle_log = cursor.fetchone()
        if oracle_log:
            print(f"  📋 ORACLE LOG_TRX_FINAL:")
            print(f"    TransactionID: {oracle_log[0]}")
            print(f"    FromID: {oracle_log[1]} ⭐ TARGET VALUE")
        
        # 6. DATOS S3 LOG_TRX_FINAL
        print(f"\n6️⃣ S3 LOG_TRX_FINAL - TARGET: {target_txn}")
        print("-" * 60)
        
        s3_log = conn.execute(f"""
            SELECT 
                "TransactionID",
                "FromID"
            FROM read_parquet('{parquet_path_log}')
            WHERE "TransactionID" = '{target_txn}'
        """).fetchone()
        
        if s3_log:
            print(f"  📋 S3 LOG_TRX_FINAL:")
            print(f"    TransactionID: {s3_log[0]}")
            print(f"    FromID: {s3_log[1]} ⭐ CURRENT VALUE")
        
        # 7. ANÁLISIS LÓGICA SP_LOG_TRX
        print(f"\n7️⃣ ANÁLISIS LÓGICA SP_LOG_TRX:")
        print("-" * 60)
        
        if oracle_pre and oracle_log and s3_log:
            transaction_type = oracle_pre[3]
            from_account_mobiquity = oracle_pre[6]
            oracle_fromid = oracle_pre[1]
            oracle_fromid_mobiquity = oracle_pre[2]
            oracle_log_fromid = oracle_log[1]
            s3_log_fromid = s3_log[1]
            
            print(f"  📋 DATOS CLAVE:")
            print(f"    TransactionType: {transaction_type}")
            print(f"    From_AccountID_Mobiquity: {from_account_mobiquity}")
            print(f"    Oracle PRE FromID: {oracle_fromid}")
            print(f"    Oracle PRE FromID_Mobiquity: {oracle_fromid_mobiquity}")
            print(f"    Oracle LOG FromID: {oracle_log_fromid}")
            print(f"    S3 LOG FromID: {s3_log_fromid}")
            
            print(f"\n  🧮 SIMULACIÓN LÓGICA SP_LOG_TRX:")
            
            # Verificar condición 1: DEPOSIT/CUSTODY_ACCOUNTS_TRANSFER
            if transaction_type in ('DEPOSIT', 'CUSTODY_ACCOUNTS_TRANSFER'):
                expected_fromid = ''
                print(f"    Condición 1: TransactionType = {transaction_type} → FromID = '' (vacío)")
            else:
                print(f"    Condición 1: TransactionType = {transaction_type} → NO aplica")
                
                # Verificar condición 2: USER_ACCOUNT_HISTORY
                cursor.execute("""
                    SELECT 
                        USER_ID,
                        ACCOUNT_ID,
                        ATTR7_OLD
                    FROM USR_DATALAKE.USER_ACCOUNT_HISTORY
                    WHERE USER_ID = :user_id
                    AND ACCOUNT_ID = :account_id
                """, {'user_id': oracle_fromid_mobiquity, 'account_id': from_account_mobiquity})
                
                h_payer = cursor.fetchone()
                if h_payer:
                    expected_fromid = h_payer[2]  # ATTR7_OLD
                    print(f"    Condición 2: USER_ACCOUNT_HISTORY encontrado → FromID = {expected_fromid}")
                    print(f"      H_PAYER.USER_ID: {h_payer[0]}")
                    print(f"      H_PAYER.ACCOUNT_ID: {h_payer[1]}")
                    print(f"      H_PAYER.ATTR7_OLD: {h_payer[2]}")
                else:
                    expected_fromid = oracle_fromid
                    print(f"    Condición 2: USER_ACCOUNT_HISTORY NO encontrado → FromID = {oracle_fromid} (default)")
            
            print(f"\n  🎯 RESULTADO ANÁLISIS:")
            print(f"    Valor esperado: {expected_fromid}")
            print(f"    Oracle real: {oracle_log_fromid}")
            print(f"    S3 actual: {s3_log_fromid}")
            
            if str(expected_fromid) == str(oracle_log_fromid):
                print(f"    ✅ Lógica esperada coincide con Oracle")
            else:
                print(f"    ❌ Lógica esperada NO coincide con Oracle")
                print(f"    🔍 Oracle usa lógica diferente a la documentada")
            
            if str(s3_log_fromid) == str(oracle_log_fromid):
                print(f"    ✅ S3 coincide con Oracle")
            else:
                print(f"    ❌ S3 NO coincide con Oracle")
                print(f"    🔧 CORRECCIÓN NECESARIA")
        
        # 8. INVESTIGACIÓN PROFUNDA
        print(f"\n8️⃣ INVESTIGACIÓN PROFUNDA:")
        print("-" * 60)
        
        if oracle_pre:
            oracle_fromid_mobiquity = oracle_pre[2]
            
            # Verificar si Oracle usa FromID_Mobiquity en lugar de FromID
            if str(oracle_log[1]) == str(oracle_fromid_mobiquity):
                print(f"  🎯 DESCUBRIMIENTO: Oracle usa FromID_Mobiquity ({oracle_fromid_mobiquity})")
                print(f"  📋 PATRÓN: Para este caso específico, Oracle ignora la lógica estándar")
                print(f"  🔧 CORRECCIÓN: Implementar lógica especial para este patrón")
            else:
                print(f"  🔍 Oracle no usa FromID_Mobiquity directamente")
                print(f"  📋 Investigar otras fuentes de datos")
        
        cursor.close()
        connection.close()
        conn.close()
        
        # 9. CONCLUSIONES NINJA
        print(f"\n9️⃣ CONCLUSIONES DATA NINJA:")
        print("-" * 60)
        
        if oracle_log and s3_log:
            oracle_val = oracle_log[1]
            s3_val = s3_log[1]
            
            print(f"1. Oracle FromID: {oracle_val}")
            print(f"2. S3 FromID: {s3_val}")
            
            if str(oracle_val) == str(oracle_fromid_mobiquity):
                print(f"3. ✅ PATRÓN IDENTIFICADO: Oracle usa FromID_Mobiquity")
                print(f"4. 🔧 ACCIÓN: Implementar lógica especial para este caso")
            else:
                print(f"3. ❓ PATRÓN NO CLARO: Investigar más")
        
    except Exception as e:
        print(f"❌ Error en investigación ninja: {e}")
        import traceback
        traceback.print_exc()

def main():
    print("🥷 DATA NINJA: INVESTIGACIÓN LÁSER FromID")
    print("=" * 80)
    print("MISIÓN: Homologación PERFECTA")
    print()
    
    data_ninja_fromid_laser()
    
    print("\n🏁 INVESTIGACIÓN NINJA COMPLETADA")

if __name__ == "__main__":
    main()
