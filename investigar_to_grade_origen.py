#!/usr/bin/env python3
"""
Investigar el origen de To_Grade en Oracle vs S3
"""

import oracledb

def investigar_to_grade_origen():
    """Investiga el origen de To_Grade"""
    print("🔍 INVESTIGACIÓN: ORIGEN DE To_Grade")
    print("=" * 80)
    
    user_id = '3565256'  # ToID_Mobiquity del caso
    
    try:
        connection = oracledb.connect(
            user='usr_datalake',
            password='U2024b1mD4t4l4k5',
            dsn='10.240.131.10:1521/MMONEY'
        )
        
        cursor = connection.cursor()
        
        print(f"1️⃣ INVESTIGAR USER_ID: {user_id}")
        print("-" * 60)
        
        # Verificar en MTX_WALLET para este usuario
        cursor.execute("""
            SELECT 
                USER_ID,
                WALLET_NUMBER,
                USER_GRADE,
                STATUS,
                MODIFIED_ON,
                ROW_NUMBER() OVER (PARTITION BY USER_ID ORDER BY MODIFIED_ON DESC) AS ORDEN_DESC
            FROM PDP_PROD10_MAINDBBUS.MTX_WALLET
            WHERE USER_ID = :user_id
            ORDER BY MODIFIED_ON DESC
        """, {'user_id': user_id})
        
        wallet_results = cursor.fetchall()
        print(f"  MTX_WALLET para USER_ID {user_id}:")
        print(f"{'#':<3} {'WALLET_NUMBER':<20} {'USER_GRADE':<30} {'STATUS':<8} {'ORDEN':<6}")
        print("-" * 80)
        
        for i, row in enumerate(wallet_results):
            wallet_number = row[1]
            user_grade = row[2]
            status = row[3]
            orden = row[5]
            
            status_icon = "✅" if status == 'Y' else "❌"
            orden_icon = "⭐" if orden == 1 else ""
            
            print(f"{i+1:<3} {wallet_number:<20} {user_grade:<30} {status_icon}{status:<7} {orden:<6} {orden_icon}")
        
        # 2. VERIFICAR LÓGICA EN SP_PRE_LOG_TRX
        print(f"\n2️⃣ LÓGICA EN SP_PRE_LOG_TRX:")
        print("-" * 60)
        
        if wallet_results:
            # Oracle debería usar ORDEN=1 (más reciente por MODIFIED_ON)
            wallet_orden_1 = wallet_results[0]
            oracle_expected_grade = wallet_orden_1[2]
            
            print(f"  Lógica Oracle SP_PRE_LOG_TRX:")
            print(f"    - Debería usar MTX_WALLET con ORDEN=1 (más reciente)")
            print(f"    - WALLET_NUMBER: {wallet_orden_1[1]}")
            print(f"    - USER_GRADE esperado: {oracle_expected_grade}")
            print(f"    - STATUS: {wallet_orden_1[3]}")
        
        # 3. VERIFICAR QUÉ USA REALMENTE ORACLE
        print(f"\n3️⃣ VERIFICAR QUÉ USA REALMENTE ORACLE:")
        print("-" * 60)
        
        cursor.execute("""
            SELECT DISTINCT
                "To_Grade",
                "ToID_Mobiquity",
                COUNT(*) as CASOS
            FROM USR_DATALAKE.PRE_LOG_TRX
            WHERE "ToID_Mobiquity" = :user_id
            AND TRUNC("TransferDate") = TO_DATE('2025-06-15', 'YYYY-MM-DD')
            GROUP BY "To_Grade", "ToID_Mobiquity"
        """, {'user_id': user_id})
        
        oracle_grades = cursor.fetchall()
        print(f"  Oracle PRE_LOG_TRX usa para USER_ID {user_id}:")
        for row in oracle_grades:
            print(f"    To_Grade: '{row[0]}' ({row[2]} casos)")
        
        # 4. COMPARAR CON OTROS USUARIOS SIMILARES
        print(f"\n4️⃣ COMPARAR CON OTROS USUARIOS SIMILARES:")
        print("-" * 60)
        
        # Buscar usuarios que tengan ambos tipos de USER_GRADE
        cursor.execute("""
            SELECT 
                USER_ID,
                USER_GRADE,
                COUNT(*) as WALLETS
            FROM PDP_PROD10_MAINDBBUS.MTX_WALLET
            WHERE USER_GRADE IN ('NORMAL ACCOUNT PROFILE', 'NORMAL GENERAL ACCOUNT PROFILE')
            GROUP BY USER_ID, USER_GRADE
            HAVING COUNT(*) > 0
            ORDER BY USER_ID
        """)
        
        similar_users = cursor.fetchall()
        print(f"  Usuarios con USER_GRADE similar (primeros 10):")
        print(f"{'USER_ID':<15} {'USER_GRADE':<30} {'WALLETS':<8}")
        print("-" * 60)
        
        for i, row in enumerate(similar_users[:10]):
            print(f"{row[0]:<15} {row[1]:<30} {row[2]:<8}")
        
        # 5. VERIFICAR SI HAY PATRÓN EN LOS DATOS
        print(f"\n5️⃣ VERIFICAR PATRÓN EN LOS DATOS:")
        print("-" * 60)
        
        # Verificar si Oracle usa STATUS='Y' vs ORDEN=1
        cursor.execute("""
            SELECT 
                MW.USER_ID,
                MW.USER_GRADE,
                MW.STATUS,
                ROW_NUMBER() OVER (PARTITION BY MW.USER_ID ORDER BY MW.MODIFIED_ON DESC) AS ORDEN
            FROM PDP_PROD10_MAINDBBUS.MTX_WALLET MW
            WHERE MW.USER_ID = :user_id
            ORDER BY MW.MODIFIED_ON DESC
        """, {'user_id': user_id})
        
        detailed_wallets = cursor.fetchall()
        print(f"  Análisis detallado para USER_ID {user_id}:")
        print(f"{'USER_GRADE':<30} {'STATUS':<8} {'ORDEN':<6} {'¿Oracle usa?'}")
        print("-" * 60)
        
        oracle_actual_grade = oracle_grades[0][0] if oracle_grades else None
        
        for row in detailed_wallets:
            user_grade = row[1]
            status = row[2]
            orden = row[3]
            
            oracle_uses = "✅ SÍ" if user_grade == oracle_actual_grade else "❌ NO"
            status_icon = "✅" if status == 'Y' else "❌"
            
            print(f"{user_grade:<30} {status_icon}{status:<7} {orden:<6} {oracle_uses}")
        
        cursor.close()
        connection.close()
        
        # 6. CONCLUSIONES
        print(f"\n6️⃣ CONCLUSIONES:")
        print("-" * 60)
        
        if wallet_results and oracle_grades:
            expected_grade = wallet_results[0][2]  # ORDEN=1
            actual_grade = oracle_grades[0][0]
            
            print(f"1. USER_ID: {user_id}")
            print(f"2. Esperado (ORDEN=1): '{expected_grade}'")
            print(f"3. Oracle actual: '{actual_grade}'")
            
            if expected_grade == actual_grade:
                print("4. ✅ Oracle usa lógica ORDEN=1 (más reciente)")
            else:
                print("4. ❌ Oracle NO usa lógica ORDEN=1")
                print("5. 🔍 Oracle podría usar STATUS='Y' u otra lógica")
                
                # Verificar si Oracle usa STATUS='Y'
                for row in detailed_wallets:
                    if row[2] == 'Y' and row[1] == actual_grade:
                        print("6. ✅ Oracle usa STATUS='Y'")
                        break
                else:
                    print("6. ❓ Oracle usa lógica desconocida")
        
    except Exception as e:
        print(f"❌ Error en investigación: {e}")
        import traceback
        traceback.print_exc()

def main():
    print("🚀 INVESTIGACIÓN ORIGEN To_Grade")
    print("=" * 80)
    print("OBJETIVO: Entender lógica de To_Grade")
    print()
    
    investigar_to_grade_origen()
    
    print("\n🏁 INVESTIGACIÓN COMPLETADA")

if __name__ == "__main__":
    main()
