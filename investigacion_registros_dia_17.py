#!/usr/bin/env python3
"""
Investigación profunda: ¿Por qué hay registros del día 17 en el parquet del día 18?
"""

import duckdb
import boto3

def investigar_registros_dia_17():
    """Investigar por qué hay registros del día 17 cuando ejecutamos para el día 18"""
    print("🥷 CODE NINJA MASTER: INVESTIGACIÓN REGISTROS DÍA 17")
    print("=" * 80)
    print("OBJETIVO: Encontrar por qué hay 55 registros del día 17 en el parquet del día 18")
    print()
    
    try:
        # DuckDB + S3
        conn = duckdb.connect()
        session = boto3.Session()
        credentials = session.get_credentials().get_frozen_credentials()
        
        conn.sql("INSTALL httpfs;")
        conn.sql("LOAD httpfs;")
        conn.sql("SET s3_region='us-east-1';")
        conn.sql("SET s3_use_ssl=true;")
        conn.sql("SET s3_url_style='path';")
        conn.sql(f"SET s3_access_key_id='{credentials.access_key}';")
        conn.sql(f"SET s3_secret_access_key='{credentials.secret_key}';")
        if credentials.token:
            conn.sql(f"SET s3_session_token='{credentials.token}';")
        
        fecha_objetivo = '2025-06-18'
        
        print("1️⃣ ANALIZANDO REGISTROS DEL DÍA 17 EN LOG_TRX_FINAL:")
        print("-" * 70)
        
        # Obtener detalles de los registros del día 17
        registros_dia_17 = conn.execute(f"""
            SELECT 
                "TransactionID",
                "DateTime",
                "TransactionType",
                "Context"
            FROM read_parquet('/home/<USER>/REPORTE_2025/reports/generate/S3_LOG_TRANSACCIONES/TEMP_LOGS_TRANSACCIONES/20250618/LOG_TRX_FINAL.parquet')
            WHERE CAST("DateTime" AS DATE) = CAST('2025-06-17' AS DATE)
            ORDER BY "DateTime"
            LIMIT 10
        """).fetchall()
        
        print(f"  📋 Muestra de registros del día 17 (primeros 10):")
        for i, row in enumerate(registros_dia_17, 1):
            txn_id = row[0]
            datetime_val = row[1]
            txn_type = row[2]
            context = row[3]
            print(f"    {i}. ID: {txn_id} | DateTime: {datetime_val} | Type: {txn_type} | Context: {context}")
        
        print(f"\n2️⃣ VERIFICANDO SI ESTOS REGISTROS ESTÁN EN PRE_LOG_TRX:")
        print("-" * 70)
        
        # Verificar si estos registros también están en PRE_LOG_TRX
        if len(registros_dia_17) > 0:
            txn_ids_dia_17 = [row[0] for row in registros_dia_17]
            txn_ids_str = "', '".join(txn_ids_dia_17[:5])  # Solo los primeros 5 para la query
            
            registros_en_pre_log = conn.execute(f"""
                SELECT 
                    "TransferID",
                    "TransferDate",
                    "TransactionType",
                    "Context"
                FROM read_parquet('/home/<USER>/REPORTE_2025/reports/generate/S3_LOG_TRANSACCIONES/TEMP_LOGS_TRANSACCIONES/20250618/PRE_LOG_TRX.parquet')
                WHERE "TransferID" IN ('{txn_ids_str}')
                ORDER BY "TransferDate"
            """).fetchall()
            
            if len(registros_en_pre_log) > 0:
                print(f"  ✅ SÍ están en PRE_LOG_TRX:")
                for row in registros_en_pre_log:
                    transfer_id = row[0]
                    transfer_date = row[1]
                    txn_type = row[2]
                    context = row[3]
                    print(f"    ID: {transfer_id} | TransferDate: {transfer_date} | Type: {txn_type} | Context: {context}")
                
                print(f"  🔍 PROBLEMA: Los registros del día 17 están en PRE_LOG_TRX del día 18")
                print(f"  🎯 CAUSA: El filtro de fecha en SP_PRE_LOG_TRX no está funcionando correctamente")
            else:
                print(f"  ❌ NO están en PRE_LOG_TRX")
                print(f"  🔍 PROBLEMA: Se agregan en SP_LOG_TRX por algún JOIN")
        
        print(f"\n3️⃣ VERIFICANDO FILTROS DE FECHA EN DATOS FUENTE S3:")
        print("-" * 70)
        
        # Verificar qué hay en los archivos fuente S3 del día 18
        print(f"  📋 Verificando MTX_TRANSACTION_HEADER del día 18:")
        
        try:
            header_fechas = conn.execute(f"""
                SELECT 
                    CAST(TRANSFER_DATE AS DATE) as fecha,
                    COUNT(*) as count,
                    MIN(TRANSFER_DATE) as min_date,
                    MAX(TRANSFER_DATE) as max_date
                FROM read_parquet('s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_HEADER_ORA/2025/06/18/*.parquet', union_by_name=true)
                GROUP BY CAST(TRANSFER_DATE AS DATE)
                ORDER BY fecha
            """).fetchall()
            
            print(f"    📊 Distribución de fechas en MTX_TRANSACTION_HEADER:")
            for row in header_fechas:
                fecha = row[0]
                count = row[1]
                min_date = row[2]
                max_date = row[3]
                
                if str(fecha) == fecha_objetivo:
                    print(f"      📅 {fecha}: {count:,} registros ✅ (fecha objetivo)")
                else:
                    print(f"      📅 {fecha}: {count:,} registros ⚠️ (fecha diferente)")
                print(f"          🕐 Rango: {min_date} - {max_date}")
                
        except Exception as e:
            print(f"    ❌ Error accediendo MTX_TRANSACTION_HEADER: {e}")
        
        print(f"\n4️⃣ ANALIZANDO LÓGICA DE FILTRADO EN NUESTRO PIPELINE:")
        print("-" * 70)
        
        print(f"  📋 Filtros que deberían aplicarse:")
        print(f"    1. Lectura de archivos S3: /2025/06/18/*.parquet")
        print(f"    2. Filtro WHERE en TRX_HEADER (líneas 264-267):")
        print(f"       WHERE MTH.TRANSFER_STATUS IN ('TA','TS')")
        print(f"       AND MTH.TRANSFER_VALUE <> 0")
        print(f"       AND SST.IS_FINANCIAL = 'Y'")
        print(f"    3. Filtro TRX_SERVICE NOT IN (línea 651):")
        print(f"       AND MTH.TRX_SERVICE NOT IN ('MULTIDRCR','FTBOA','MERCHPAY','TXNCORRECT','MULTIDRCR3')")
        
        print(f"\n5️⃣ VERIFICANDO SI HAY PROBLEMA EN LA RUTA S3:")
        print("-" * 70)
        
        # Verificar si los archivos S3 del día 18 contienen datos del día 17
        try:
            # Verificar algunos TransferIDs específicos del día 17 en los archivos del día 18
            if len(registros_dia_17) > 0:
                sample_id = registros_dia_17[0][0]  # Primer TransactionID del día 17
                
                encontrado_en_s3 = conn.execute(f"""
                    SELECT 
                        FIELD7,
                        TRANSFER_DATE,
                        SERVICE_TYPE,
                        SOURCE
                    FROM read_parquet('s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_HEADER_ORA/2025/06/18/*.parquet', union_by_name=true)
                    WHERE FIELD7 = '{sample_id}'
                """).fetchall()
                
                if len(encontrado_en_s3) > 0:
                    print(f"  ⚠️ PROBLEMA ENCONTRADO:")
                    print(f"    El TransactionID {sample_id} del día 17 SÍ está en los archivos S3 del día 18")
                    for row in encontrado_en_s3:
                        field7 = row[0]
                        transfer_date = row[1]
                        service_type = row[2]
                        source = row[3]
                        print(f"    FIELD7: {field7} | TRANSFER_DATE: {transfer_date} | SERVICE_TYPE: {service_type} | SOURCE: {source}")
                    
                    print(f"  🎯 CAUSA RAÍZ:")
                    print(f"    Los archivos S3 particionados por /2025/06/18/ contienen registros del día 17")
                    print(f"    Esto puede deberse a:")
                    print(f"    - Zona horaria en el particionado")
                    print(f"    - Procesamiento tardío de transacciones")
                    print(f"    - Lógica de particionado en el ETL")
                else:
                    print(f"  ✅ El TransactionID {sample_id} NO está en los archivos S3 del día 18")
                    print(f"  🔍 El problema debe estar en otra parte del pipeline")
                    
        except Exception as e:
            print(f"    ❌ Error verificando S3: {e}")
        
        print(f"\n6️⃣ RECOMENDACIONES:")
        print("-" * 70)
        
        print(f"  🔧 SOLUCIONES POSIBLES:")
        print(f"    1. AGREGAR FILTRO EXPLÍCITO DE FECHA en SP_PRE_LOG_TRX:")
        print(f"       AND CAST(MTH.TRANSFER_DATE AS DATE) = CAST('{fecha_objetivo}' AS DATE)")
        print(f"    2. AGREGAR FILTRO EXPLÍCITO DE FECHA en SP_LOG_TRX:")
        print(f"       AND CAST(MTH.\"TransferDate\" AS DATE) = CAST('{fecha_objetivo}' AS DATE)")
        print(f"    3. INVESTIGAR particionado S3 para entender por qué hay datos del día 17")
        
        print(f"  🎯 RECOMENDACIÓN INMEDIATA:")
        print(f"    Agregar filtro de fecha explícito para garantizar que solo se procesen")
        print(f"    registros del día especificado en el argumento del pipeline")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error en investigación: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    investigar_registros_dia_17()
