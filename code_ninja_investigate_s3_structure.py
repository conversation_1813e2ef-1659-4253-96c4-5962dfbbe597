#!/usr/bin/env python3
"""
CODE NINJA: Investigar estructura real de archivos S3 particionados
"""

import boto3
import duckdb

def investigate_s3_structure():
    """Investigar estructura real de particiones S3"""
    print("🥷 CODE NINJA: INVESTIGACIÓN ESTRUCTURA S3")
    print("=" * 80)
    
    try:
        # Configurar DuckDB con S3
        conn = duckdb.connect()
        session = boto3.Session()
        credentials = session.get_credentials().get_frozen_credentials()
        
        conn.sql("INSTALL httpfs;")
        conn.sql("LOAD httpfs;")
        conn.sql("SET s3_region='us-east-1';")
        conn.sql("SET s3_use_ssl=true;")
        conn.sql("SET s3_url_style='path';")
        conn.sql(f"SET s3_access_key_id='{credentials.access_key}';")
        conn.sql(f"SET s3_secret_access_key='{credentials.secret_key}';")
        if credentials.token:
            conn.sql(f"SET s3_session_token='{credentials.token}';")
        
        bucket = 'prd-datalake-silver-zone-637423440311'
        
        # 1. Investigar MTX_TRANSACTION_HEADER_ORA
        print("1️⃣ INVESTIGANDO MTX_TRANSACTION_HEADER_ORA:")
        print("-" * 60)
        
        # Probar diferentes estructuras de partición
        test_paths = [
            f's3://{bucket}/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_HEADER_ORA/year=2025/month=06/day=15/*.parquet',
            f's3://{bucket}/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_HEADER_ORA/2025/06/15/*.parquet',
            f's3://{bucket}/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_HEADER_ORA/20250615/*.parquet',
            f's3://{bucket}/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_HEADER_ORA/*/*/*/*.parquet'
        ]
        
        for i, path in enumerate(test_paths, 1):
            try:
                print(f"  Probando estructura {i}: {path}")
                result = conn.execute(f"SELECT COUNT(*) FROM read_parquet('{path}') LIMIT 1").fetchone()
                if result:
                    print(f"    ✅ FUNCIONA: {result[0]} registros encontrados")
                    
                    # Si funciona, obtener muestra de fechas
                    sample = conn.execute(f"""
                        SELECT DISTINCT CAST(TRANSFER_DATE AS DATE) as fecha, COUNT(*) as registros
                        FROM read_parquet('{path}')
                        WHERE CAST(TRANSFER_DATE AS DATE) = '2025-06-15'
                        GROUP BY CAST(TRANSFER_DATE AS DATE)
                        LIMIT 5
                    """).fetchall()
                    
                    print(f"    📊 Muestra de fechas para 2025-06-15:")
                    for fecha, count in sample:
                        print(f"      {fecha}: {count} registros")
                    break
                else:
                    print(f"    ❌ No funciona")
            except Exception as e:
                print(f"    ❌ Error: {str(e)[:100]}...")
        
        # 2. Investigar MTX_TRANSACTION_ITEMS_ORA
        print(f"\n2️⃣ INVESTIGANDO MTX_TRANSACTION_ITEMS_ORA:")
        print("-" * 60)
        
        test_paths_items = [
            f's3://{bucket}/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_ITEMS_ORA/year=2025/month=06/day=15/*.parquet',
            f's3://{bucket}/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_ITEMS_ORA/2025/06/15/*.parquet',
            f's3://{bucket}/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_ITEMS_ORA/20250615/*.parquet',
            f's3://{bucket}/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_ITEMS_ORA/*/*/*/*.parquet'
        ]
        
        for i, path in enumerate(test_paths_items, 1):
            try:
                print(f"  Probando estructura {i}: {path}")
                result = conn.execute(f"SELECT COUNT(*) FROM read_parquet('{path}') LIMIT 1").fetchone()
                if result:
                    print(f"    ✅ FUNCIONA: {result[0]} registros encontrados")
                    break
                else:
                    print(f"    ❌ No funciona")
            except Exception as e:
                print(f"    ❌ Error: {str(e)[:100]}...")
        
        # 3. Listar estructura real usando boto3
        print(f"\n3️⃣ LISTANDO ESTRUCTURA REAL CON BOTO3:")
        print("-" * 60)
        
        s3_client = boto3.client('s3')
        
        # Listar prefijos en MTX_TRANSACTION_HEADER_ORA
        try:
            response = s3_client.list_objects_v2(
                Bucket=bucket,
                Prefix='PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_HEADER_ORA/',
                Delimiter='/',
                MaxKeys=20
            )
            
            print(f"  📁 Estructura MTX_TRANSACTION_HEADER_ORA:")
            if 'CommonPrefixes' in response:
                for prefix in response['CommonPrefixes']:
                    print(f"    {prefix['Prefix']}")
            
            if 'Contents' in response:
                print(f"  📄 Archivos directos:")
                for obj in response['Contents'][:5]:
                    print(f"    {obj['Key']}")
                    
        except Exception as e:
            print(f"    ❌ Error listando: {e}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error general: {e}")

def main():
    investigate_s3_structure()

if __name__ == "__main__":
    main()
