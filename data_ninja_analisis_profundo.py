#!/usr/bin/env python3
"""
DATA NINJA: Análisis profundo de lógica Oracle para 100% perfección
"""

import oracledb
import duckdb
import boto3

def data_ninja_analisis():
    """Análisis profundo estilo Data Ninja"""
    print("🥷 DATA NINJA: ANÁLISIS PROFUNDO ORACLE")
    print("=" * 80)
    print("OBJETIVO: 100% PERFECCIÓN ABSOLUTA - SIN EXCEPCIONES")
    print()
    
    fecha = '2025-06-15'
    parquet_path_pre = '/home/<USER>/REPORTE_2025/reports/generate/S3_LOG_TRANSACCIONES/TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet'
    parquet_path_log = '/home/<USER>/REPORTE_2025/reports/generate/S3_LOG_TRANSACCIONES/TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet'
    
    # Casos problemáticos identificados
    casos_criticos = [
        '***************',  # FromAccountID y FromID diferentes
        '*************'     # ToAccountID diferente
    ]
    
    try:
        # 1. CONFIGURAR CONEXIONES
        print("1️⃣ CONFIGURANDO CONEXIONES DATA NINJA:")
        print("-" * 60)
        
        connection = oracledb.connect(
            user='usr_datalake',
            password='U2024b1mD4t4l4k5',
            dsn='10.240.131.10:1521/MMONEY'
        )
        cursor = connection.cursor()
        
        conn = duckdb.connect()
        session = boto3.Session()
        credentials = session.get_credentials().get_frozen_credentials()
        
        conn.sql("INSTALL httpfs;")
        conn.sql("LOAD httpfs;")
        conn.sql("SET s3_region='us-east-1';")
        conn.sql("SET s3_use_ssl=true;")
        conn.sql("SET s3_url_style='path';")
        conn.sql(f"SET s3_access_key_id='{credentials.access_key}';")
        conn.sql(f"SET s3_secret_access_key='{credentials.secret_key}';")
        if credentials.token:
            conn.sql(f"SET s3_session_token='{credentials.token}';")
        
        print("  ✅ Conexiones ninja configuradas")
        
        # 2. ANÁLISIS CASO POR CASO
        for caso in casos_criticos:
            print(f"\n2️⃣ ANÁLISIS PROFUNDO CASO: {caso}")
            print("-" * 60)
            
            # 2.1 DATOS ORACLE PRE_LOG_TRX
            print(f"  📋 ORACLE PRE_LOG_TRX:")
            cursor.execute("""
                SELECT 
                    "TransferID",
                    "FromID",
                    "ToID", 
                    "From_AccountID",
                    "To_AccountID",
                    "FromID_Mobiquity",
                    "ToID_Mobiquity",
                    "From_AccountID_Mobiquity",
                    "To_AccountID_Mobiquity",
                    "TransactionType",
                    "From_BankDomain",
                    "To_BankDomain"
                FROM USR_DATALAKE.PRE_LOG_TRX
                WHERE "TransferID" = :caso
            """, {'caso': caso})
            
            oracle_pre = cursor.fetchone()
            if oracle_pre:
                print(f"    TransferID: {oracle_pre[0]}")
                print(f"    FromID: {oracle_pre[1]}")
                print(f"    ToID: {oracle_pre[2]}")
                print(f"    From_AccountID: {oracle_pre[3]}")
                print(f"    To_AccountID: {oracle_pre[4]}")
                print(f"    FromID_Mobiquity: {oracle_pre[5]}")
                print(f"    ToID_Mobiquity: {oracle_pre[6]}")
                print(f"    From_AccountID_Mobiquity: {oracle_pre[7]}")
                print(f"    To_AccountID_Mobiquity: {oracle_pre[8]}")
                print(f"    TransactionType: {oracle_pre[9]}")
                print(f"    From_BankDomain: {oracle_pre[10]}")
                print(f"    To_BankDomain: {oracle_pre[11]}")
            
            # 2.2 DATOS S3 PRE_LOG_TRX
            print(f"\n  📋 S3 PRE_LOG_TRX:")
            s3_pre = conn.execute(f"""
                SELECT 
                    "TransferID",
                    "FromID",
                    "ToID", 
                    "From_AccountID",
                    "To_AccountID",
                    "FromID_Mobiquity",
                    "ToID_Mobiquity",
                    "From_AccountID_Mobiquity",
                    "To_AccountID_Mobiquity",
                    "TransactionType",
                    "From_BankDomain",
                    "To_BankDomain"
                FROM read_parquet('{parquet_path_pre}')
                WHERE "TransferID" = '{caso}'
            """).fetchone()
            
            if s3_pre:
                print(f"    TransferID: {s3_pre[0]}")
                print(f"    FromID: {s3_pre[1]}")
                print(f"    ToID: {s3_pre[2]}")
                print(f"    From_AccountID: {s3_pre[3]}")
                print(f"    To_AccountID: {s3_pre[4]}")
                print(f"    FromID_Mobiquity: {s3_pre[5]}")
                print(f"    ToID_Mobiquity: {s3_pre[6]}")
                print(f"    From_AccountID_Mobiquity: {s3_pre[7]}")
                print(f"    To_AccountID_Mobiquity: {s3_pre[8]}")
                print(f"    TransactionType: {s3_pre[9]}")
                print(f"    From_BankDomain: {s3_pre[10]}")
                print(f"    To_BankDomain: {s3_pre[11]}")
            
            # 2.3 COMPARACIÓN PRE_LOG_TRX
            print(f"\n  🔍 COMPARACIÓN PRE_LOG_TRX:")
            if oracle_pre and s3_pre:
                campos = ['TransferID', 'FromID', 'ToID', 'From_AccountID', 'To_AccountID', 
                         'FromID_Mobiquity', 'ToID_Mobiquity', 'From_AccountID_Mobiquity', 
                         'To_AccountID_Mobiquity', 'TransactionType', 'From_BankDomain', 'To_BankDomain']
                
                for i, campo in enumerate(campos):
                    oracle_val = str(oracle_pre[i]) if oracle_pre[i] is not None else 'NULL'
                    s3_val = str(s3_pre[i]) if s3_pre[i] is not None else 'NULL'
                    
                    if oracle_val == s3_val:
                        print(f"    {campo}: ✅ COINCIDE ({oracle_val})")
                    else:
                        print(f"    {campo}: ❌ DIFIERE (Oracle: '{oracle_val}' vs S3: '{s3_val}')")
            
            # 2.4 DATOS ORACLE LOG_TRX_FINAL
            print(f"\n  📋 ORACLE LOG_TRX_FINAL:")
            cursor.execute("""
                SELECT 
                    "TransactionID",
                    "FromID",
                    "ToID",
                    "FromAccountID", 
                    "ToAccountID"
                FROM USR_DATALAKE.LOG_TRX_FINAL
                WHERE "TransactionID" = :caso
            """, {'caso': caso})
            
            oracle_log = cursor.fetchone()
            if oracle_log:
                print(f"    TransactionID: {oracle_log[0]}")
                print(f"    FromID: {oracle_log[1]}")
                print(f"    ToID: {oracle_log[2]}")
                print(f"    FromAccountID: {oracle_log[3]}")
                print(f"    ToAccountID: {oracle_log[4]}")
            
            # 2.5 DATOS S3 LOG_TRX_FINAL
            print(f"\n  📋 S3 LOG_TRX_FINAL:")
            s3_log = conn.execute(f"""
                SELECT 
                    "TransactionID",
                    "FromID",
                    "ToID",
                    "FromAccountID", 
                    "ToAccountID"
                FROM read_parquet('{parquet_path_log}')
                WHERE "TransactionID" = '{caso}'
            """).fetchone()
            
            if s3_log:
                print(f"    TransactionID: {s3_log[0]}")
                print(f"    FromID: {s3_log[1]}")
                print(f"    ToID: {s3_log[2]}")
                print(f"    FromAccountID: {s3_log[3]}")
                print(f"    ToAccountID: {s3_log[4]}")
            
            # 2.6 COMPARACIÓN LOG_TRX_FINAL
            print(f"\n  🔍 COMPARACIÓN LOG_TRX_FINAL:")
            if oracle_log and s3_log:
                campos_log = ['TransactionID', 'FromID', 'ToID', 'FromAccountID', 'ToAccountID']
                
                for i, campo in enumerate(campos_log):
                    oracle_val = str(oracle_log[i]) if oracle_log[i] is not None else 'NULL'
                    s3_val = str(s3_log[i]) if s3_log[i] is not None else 'NULL'
                    
                    if oracle_val == s3_val:
                        print(f"    {campo}: ✅ COINCIDE ({oracle_val})")
                    else:
                        print(f"    {campo}: ❌ DIFIERE (Oracle: '{oracle_val}' vs S3: '{s3_val}')")
            
            # 2.7 ANÁLISIS USER_ACCOUNT_HISTORY
            print(f"\n  🔍 ANÁLISIS USER_ACCOUNT_HISTORY:")
            if oracle_pre:
                from_id_mobiquity = oracle_pre[5]
                to_id_mobiquity = oracle_pre[6]
                from_account_mobiquity = oracle_pre[7]
                to_account_mobiquity = oracle_pre[8]
                
                # Verificar H_PAYER
                cursor.execute("""
                    SELECT 
                        USER_ID,
                        ACCOUNT_ID,
                        ATTR7_OLD,
                        ATTR8_OLD
                    FROM USR_DATALAKE.USER_ACCOUNT_HISTORY
                    WHERE USER_ID = :user_id
                    AND ACCOUNT_ID = :account_id
                """, {'user_id': from_id_mobiquity, 'account_id': from_account_mobiquity})
                
                h_payer = cursor.fetchone()
                if h_payer:
                    print(f"    H_PAYER encontrado:")
                    print(f"      USER_ID: {h_payer[0]}")
                    print(f"      ACCOUNT_ID: {h_payer[1]}")
                    print(f"      ATTR7_OLD: {h_payer[2]}")
                    print(f"      ATTR8_OLD: {h_payer[3]}")
                else:
                    print(f"    H_PAYER NO encontrado para USER_ID={from_id_mobiquity}, ACCOUNT_ID={from_account_mobiquity}")
                
                # Verificar H_PAYEE
                cursor.execute("""
                    SELECT 
                        USER_ID,
                        ACCOUNT_ID,
                        ATTR7_OLD,
                        ATTR8_OLD
                    FROM USR_DATALAKE.USER_ACCOUNT_HISTORY
                    WHERE USER_ID = :user_id
                    AND ACCOUNT_ID = :account_id
                """, {'user_id': to_id_mobiquity, 'account_id': to_account_mobiquity})
                
                h_payee = cursor.fetchone()
                if h_payee:
                    print(f"    H_PAYEE encontrado:")
                    print(f"      USER_ID: {h_payee[0]}")
                    print(f"      ACCOUNT_ID: {h_payee[1]}")
                    print(f"      ATTR7_OLD: {h_payee[2]}")
                    print(f"      ATTR8_OLD: {h_payee[3]}")
                else:
                    print(f"    H_PAYEE NO encontrado para USER_ID={to_id_mobiquity}, ACCOUNT_ID={to_account_mobiquity}")
            
            # 2.8 SIMULACIÓN LÓGICA ORACLE
            print(f"\n  🧮 SIMULACIÓN LÓGICA ORACLE:")
            if oracle_pre and oracle_log:
                transaction_type = oracle_pre[9]
                from_bank_domain = oracle_pre[10]
                to_bank_domain = oracle_pre[11]
                
                print(f"    TransactionType: {transaction_type}")
                print(f"    From_BankDomain: {from_bank_domain}")
                print(f"    To_BankDomain: {to_bank_domain}")
                
                # Simular FromAccountID
                print(f"\n    📋 SIMULACIÓN FromAccountID:")
                if transaction_type in ('DEPOSIT', 'CUSTODY_ACCOUNTS_TRANSFER'):
                    if from_bank_domain == 'FCOMPARTAMOS':
                        expected_from_account = '1188057'
                        print(f"      Lógica BankDomain: {expected_from_account}")
                    else:
                        expected_from_account = f"BankDomain_{from_bank_domain}"
                        print(f"      Lógica BankDomain: {expected_from_account}")
                elif h_payer:
                    expected_from_account = h_payer[3]  # ATTR8_OLD
                    print(f"      Lógica USER_ACCOUNT_HISTORY: {expected_from_account}")
                else:
                    expected_from_account = oracle_pre[3]  # From_AccountID
                    print(f"      Lógica DEFAULT: {expected_from_account}")
                
                oracle_from_account = oracle_log[3]
                print(f"      Oracle real: {oracle_from_account}")
                print(f"      ¿Coincide? {'✅' if str(expected_from_account) == str(oracle_from_account) else '❌'}")
        
        cursor.close()
        connection.close()
        conn.close()
        
        # 3. CONCLUSIONES DATA NINJA
        print(f"\n3️⃣ CONCLUSIONES DATA NINJA:")
        print("-" * 60)
        print(f"1. Analizar diferencias específicas en PRE_LOG_TRX")
        print(f"2. Verificar lógica USER_ACCOUNT_HISTORY")
        print(f"3. Revisar condiciones CASE en SP_LOG_TRX")
        print(f"4. Identificar lógica oculta no documentada")
        
    except Exception as e:
        print(f"❌ Error en análisis ninja: {e}")
        import traceback
        traceback.print_exc()

def main():
    print("🥷 DATA NINJA: ANÁLISIS PROFUNDO")
    print("=" * 80)
    print("MISIÓN: 100% PERFECCIÓN ABSOLUTA")
    print()
    
    data_ninja_analisis()
    
    print("\n🏁 ANÁLISIS NINJA COMPLETADO")

if __name__ == "__main__":
    main()
