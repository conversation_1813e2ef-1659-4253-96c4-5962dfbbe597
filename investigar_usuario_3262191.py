#!/usr/bin/env python3
"""
Investigar específicamente el usuario 3262191 para entender la lógica exacta de Oracle
"""

import oracledb

def investigar_usuario_3262191():
    """Investiga el usuario 3262191 que causa las diferencias"""
    print("🔍 INVESTIGACIÓN ESPECÍFICA: USER_ID 3262191")
    print("=" * 80)
    
    user_id = '3262191'
    
    try:
        connection = oracledb.connect(
            user='usr_datalake',
            password='U2024b1mD4t4l4k5',
            dsn='10.240.131.10:1521/MMONEY'
        )
        
        cursor = connection.cursor()
        
        print("1️⃣ DATOS DE USER_PROFILE:")
        print("-" * 60)
        
        # Verificar USER_PROFILE
        cursor.execute("""
            SELECT 
                USER_ID,
                ATTR8,
                ATTR7,
                MSISDN,
                STATUS,
                CREATED_ON,
                MODIFIED_ON
            FROM PDP_PROD10_MAINDB.USER_PROFILE
            WHERE USER_ID = :user_id
        """, {'user_id': user_id})
        
        user_profile = cursor.fetchone()
        if user_profile:
            print(f"  USER_ID: {user_profile[0]}")
            print(f"  ATTR8: {user_profile[1]} ⭐ CLAVE")
            print(f"  ATTR7: {user_profile[2]}")
            print(f"  MSISDN: {user_profile[3]}")
            print(f"  STATUS: {user_profile[4]}")
            print(f"  CREATED: {user_profile[5]}")
            print(f"  MODIFIED: {user_profile[6]}")
            
            attr8_value = user_profile[1]
        else:
            print("  ❌ No encontrado en USER_PROFILE")
            attr8_value = None
        
        print("\n2️⃣ DATOS DE MTX_WALLET (TODOS):")
        print("-" * 60)
        
        # Verificar TODOS los wallets
        cursor.execute("""
            SELECT 
                USER_ID,
                WALLET_NUMBER,
                STATUS,
                MODIFIED_ON,
                CREATED_ON,
                ROW_NUMBER() OVER (PARTITION BY USER_ID ORDER BY MODIFIED_ON DESC) AS ORDEN_MODIFIED,
                ROW_NUMBER() OVER (PARTITION BY USER_ID ORDER BY CREATED_ON DESC) AS ORDEN_CREATED,
                ROW_NUMBER() OVER (PARTITION BY USER_ID ORDER BY 
                    CASE WHEN STATUS = 'Y' THEN 1 ELSE 2 END,
                    MODIFIED_ON DESC) AS ORDEN_STATUS_MODIFIED,
                ROW_NUMBER() OVER (PARTITION BY USER_ID ORDER BY 
                    CASE WHEN STATUS = 'Y' THEN 1 ELSE 2 END,
                    LENGTH(WALLET_NUMBER) ASC,
                    MODIFIED_ON DESC) AS ORDEN_STATUS_LENGTH_MODIFIED
            FROM PDP_PROD10_MAINDBBUS.MTX_WALLET
            WHERE USER_ID = :user_id
            ORDER BY MODIFIED_ON DESC
        """, {'user_id': user_id})
        
        wallets = cursor.fetchall()
        print(f"  Total wallets encontrados: {len(wallets)}")
        print()
        print(f"{'#':<3} {'WALLET_NUMBER':<20} {'STATUS':<8} {'MODIFIED_ON':<20} {'ORD_MOD':<8} {'ORD_STAT_MOD':<12} {'ORD_STAT_LEN':<12}")
        print("-" * 100)
        
        for i, row in enumerate(wallets):
            wallet_number = row[1]
            status = row[2]
            modified_on = row[3]
            orden_mod = row[5]
            orden_stat_mod = row[7]
            orden_stat_len = row[8]
            
            status_icon = "✅" if status == 'Y' else "❌"
            length_icon = "🔢" if len(str(wallet_number)) > 15 else "📱"
            
            print(f"{i+1:<3} {wallet_number:<20} {status_icon}{status:<7} {str(modified_on)[:19]:<20} {orden_mod:<8} {orden_stat_mod:<12} {orden_stat_len:<12}")
        
        print("\n3️⃣ LÓGICA DE SP_PRE_LOG_USR:")
        print("-" * 60)
        
        # Simular lógica exacta de SP_PRE_LOG_USR
        print("  📋 PASO 1: CTE WALLETS (ORDEN por MODIFIED_ON DESC)")
        if wallets:
            wallet_orden_1 = wallets[0]  # ORDEN=1 en CTE WALLETS
            print(f"    WALLETS.ORDEN=1: {wallet_orden_1[1]} (STATUS: {wallet_orden_1[2]})")
        
        print("  📋 PASO 2: Aplicar lógica WALLET_NUMBER (líneas 33-37)")
        if attr8_value:
            final_wallet = attr8_value
            logic_used = "ATTR8"
            print(f"    ATTR8 existe: {attr8_value}")
            print(f"    RESULTADO: {final_wallet} (usando ATTR8)")
        elif wallets:
            wallet_number = wallet_orden_1[1]
            wallet_clean = str(wallet_number).replace('UA.', '')
            
            if len(wallet_clean) > 15:
                final_wallet = wallet_clean[-15:]
                logic_used = "SUBSTR últimos 15"
                print(f"    WALLET_NUMBER: {wallet_number}")
                print(f"    Sin 'UA.': {wallet_clean}")
                print(f"    LENGTH > 15: Sí ({len(wallet_clean)})")
                print(f"    RESULTADO: {final_wallet} (últimos 15 dígitos)")
            else:
                final_wallet = wallet_clean
                logic_used = "WALLET_NUMBER completo"
                print(f"    WALLET_NUMBER: {wallet_number}")
                print(f"    Sin 'UA.': {wallet_clean}")
                print(f"    LENGTH > 15: No ({len(wallet_clean)})")
                print(f"    RESULTADO: {final_wallet} (completo)")
        
        print("\n4️⃣ VERIFICACIÓN CON USER_DATA_TRX:")
        print("-" * 60)
        
        # Verificar qué tiene USER_DATA_TRX
        cursor.execute("""
            SELECT 
                USER_ID,
                O_USER_ID,
                WALLET_NUMBER
            FROM USR_DATALAKE.USER_DATA_TRX
            WHERE O_USER_ID = :user_id
        """, {'user_id': user_id})
        
        user_data_trx = cursor.fetchone()
        if user_data_trx:
            print(f"  USER_DATA_TRX.WALLET_NUMBER: {user_data_trx[2]}")
            
            if str(user_data_trx[2]) == str(final_wallet):
                print("  ✅ USER_DATA_TRX coincide con la lógica de SP_PRE_LOG_USR")
            else:
                print("  ❌ USER_DATA_TRX NO coincide con la lógica de SP_PRE_LOG_USR")
                print(f"     Esperado (SP_PRE_LOG_USR): {final_wallet}")
                print(f"     Actual (USER_DATA_TRX): {user_data_trx[2]}")
        else:
            print("  ❌ No encontrado en USER_DATA_TRX")
        
        print("\n5️⃣ VERIFICACIÓN EN PRE_LOG_TRX:")
        print("-" * 60)
        
        # Verificar qué usa PRE_LOG_TRX para este usuario
        cursor.execute("""
            SELECT DISTINCT
                "To_AccountID_Mobiquity",
                "ToID_Mobiquity",
                COUNT(*) as CASOS
            FROM USR_DATALAKE.PRE_LOG_TRX
            WHERE "ToID_Mobiquity" = :user_id
            AND TRUNC("TransferDate") = TO_DATE('2025-06-15', 'YYYY-MM-DD')
            GROUP BY "To_AccountID_Mobiquity", "ToID_Mobiquity"
            ORDER BY COUNT(*) DESC
        """, {'user_id': user_id})
        
        pre_log_trx = cursor.fetchall()
        if pre_log_trx:
            print(f"  PRE_LOG_TRX encontró {len(pre_log_trx)} combinaciones:")
            for row in pre_log_trx:
                print(f"    To_AccountID_Mobiquity: {row[0]} ({row[2]} casos)")
        else:
            print("  ❌ No encontrado en PRE_LOG_TRX")
        
        cursor.close()
        connection.close()
        
        print(f"\n6️⃣ CONCLUSIONES:")
        print("-" * 60)
        print(f"1. ATTR8: {attr8_value}")
        print(f"2. Lógica usada: {logic_used}")
        print(f"3. Resultado esperado: {final_wallet}")
        print(f"4. Oracle PRE_LOG_TRX usa: {pre_log_trx[0][0] if pre_log_trx else 'N/A'}")
        print("5. Identificar por qué hay diferencia")
        
    except Exception as e:
        print(f"❌ Error en investigación: {e}")
        import traceback
        traceback.print_exc()

def main():
    print("🚀 INVESTIGACIÓN USUARIO 3262191")
    print("=" * 80)
    print("OBJETIVO: Entender lógica exacta de Oracle")
    print()
    
    investigar_usuario_3262191()
    
    print("\n🏁 INVESTIGACIÓN COMPLETADA")

if __name__ == "__main__":
    main()
