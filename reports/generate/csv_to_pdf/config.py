import os 

ROUTE_MAIN = "/home/<USER>/generate/csv_to_pdf"
ROUTE_CSV = "/home/<USER>/output/pdf"
## El formato del archivo siempre debe ser {EMISOR}-COBRAR-PDF.csv
EMISORES = ["FCOM<PERSON><PERSON><PERSON><PERSON>","BNA<PERSON><PERSON>","FOCNFIANZA","CRANDES","CCUSCO","FQAPAQ"]
BUCKET_S3 = "prd-datalake-reports-637423440311"
#PRIVATE_KEY_PATH=os.environ.get("PRIVATE_KEY_PATH")
#PUBLIC_KEY_PATH=os.environ.get("PRIVATE_CRT_PATH")
private_key_path = "/home/<USER>/generate/FileSigner/pdp_sign.key"
public_key_path = "/home/<USER>/generate/FileSigner/SignFileNC.crt"
