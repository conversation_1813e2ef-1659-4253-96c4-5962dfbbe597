import csv
import pdfkit
import sys
import logging
import os
import boto3
from collections import defaultdict
from constants import EMISOR_SECTION, AGENTE_SECTION
from config import ROUTE_MAIN, ROUTE_CSV, EMISORES, BUCKET_S3, PRIVATE_KEY_PATH, <PERSON><PERSON><PERSON><PERSON>_KEY_PATH
from datetime import datetime, timed<PERSON>ta
# from config import output_route, private_key_path, public_key_path
from file_signer.file_signer import FileSigner

def traducir_mes(mes):
    meses = {
        "January": "ENERO", "February": "FEBRERO", "March": "MARZO",
        "April": "ABRIL", "May": "MAYO", "June": "JUNIO",
        "July": "JUL<PERSON>", "August": "AGOSTO", "September": "SEPTIEMBRE",
        "October": "OCTUBRE", "November": "NOVIEMBRE", "December": "DICIEMBRE"
    }
    return meses.get(mes, mes)


def upload_s3(file_route, file_name, s3_key):
    print('file_route: ', file_route)
    print('file_name: ', file_name)
    print('s3_key: ', s3_key)
    s3_client = boto3.client('s3')
    s3_client.upload_file(file_route, BUCKET_S3, f"{s3_key}{file_name}")
    logging.info("Archivo: %s subido a: s3://%s", file_name, f"{BUCKET_S3}/{s3_key}{file_name}")


def signed_file(file_name_route, s3_key):
    signer = FileSigner(PRIVATE_KEY_PATH)
    signer.sign_file(file_name_route)
    signature_path = f"{file_name_route}.signature"
    is_valid = signer.verify_signature(file_name_route, signature_path, PUBLIC_KEY_PATH)
    signature_file = signature_path.split("/")[-1]
    print("signature_path: ", signature_path)
    print("signature_file: ", signature_file)
    if is_valid:
        print('soy valido')
        upload_s3(signature_path, signature_file, s3_key)


def read_and_process_csv(file):
    grouped_data = defaultdict(list)
    file_path = os.path.join(ROUTE_CSV, file)

    with open(file_path, mode="r", encoding="utf-8") as csv_file:
        reader = csv.DictReader(csv_file)

        for row in reader:
            emisor = row["Emisor"]
            agente = row["Agente"]
            transaction_type = row["TransactionType"]
            cantidad = int(row["Cantidad"])
            total = float(row["Total"])

            # Buscar si el agente ya existe en la lista del emisor
            agente_data = next((item for item in grouped_data[emisor] if item["Agente"] == agente), None)

            if not agente_data:
                agente_data = {
                    "Agente": agente,
                    "CASH_IN": 0,
                    "REVERSAL": 0,
                    "CASH_OUT": 0,
                    "Total_CASH_IN": 0.0,
                    "Total_REVERSAL": 0.0,
                    "Total_CASH_OUT": 0.0
                }
                grouped_data[emisor].append(agente_data)

            # Sumar valores según el tipo de transacción
            if transaction_type == "CASH_IN":
                agente_data["CASH_IN"] += cantidad
                agente_data["Total_CASH_IN"] += total
            elif transaction_type == "REVERSAL":
                agente_data["REVERSAL"] += cantidad
                agente_data["Total_REVERSAL"] += total
            elif transaction_type == "CASH_OUT":
                agente_data["CASH_OUT"] += cantidad
                agente_data["Total_CASH_OUT"] += total

    return [{"Emisor": emisor, "Data": data} for emisor, data in grouped_data.items()]


def main():
    if len(sys.argv) < 2:
        print("Debe especificar la fecha actual")
        sys.exit(1)
    fecha = sys.argv[1].upper()
    fecha = datetime.strptime(fecha, "%Y/%m/%d")
    fecha_plus_one = fecha + timedelta(days=1)
    fecha_filename = fecha_plus_one.strftime("%Y%m%d")
    month_filename = fecha_plus_one.strftime("%Y%m")
    month_filename_txt = fecha_plus_one.strftime("%B")
    fecha_plus_one = fecha_plus_one.strftime("%Y-%m-%d")
    files = [f for f in os.listdir(ROUTE_CSV) if f.endswith(".csv") and f.split("-")[1] in EMISORES]
    with open(os.path.join(ROUTE_MAIN, "index.html"), "r", encoding="utf-8") as file:
        html_content = file.read()


    for file in files:
        html_data = ""
        csv_content = read_and_process_csv(file)

        for content in csv_content:
            emisor_section = EMISOR_SECTION.replace("[emisor]", content["Emisor"])
            html_content = html_content.replace("[emisor]", content.get("Emisor", "").upper())\
                .replace("[MES]", traducir_mes(month_filename_txt))
            agente_section = ''
            for data in content["Data"]:
                total_transacciones = data["CASH_IN"] + data["CASH_OUT"] - data["REVERSAL"]
                total_pagar = data["Total_CASH_IN"] + data["Total_CASH_OUT"] - data["Total_REVERSAL"]

                # Reemplazo de valores en EMISOR_SECTION
                agente_section += AGENTE_SECTION.replace("[agent_number]", data["Agente"]) \
                    .replace("[cash_out_cantidad]", str(data["CASH_OUT"])) \
                    .replace("[cash_out_comision]", "1") \
                    .replace("[cash_out_total]", str(data["Total_CASH_OUT"])) \
                    .replace("[reversal_cantidad]", str(data["REVERSAL"])) \
                    .replace("[reversal_comision]", "1") \
                    .replace("[reversal_total]", str(data["Total_REVERSAL"])) \
                    .replace("[cash_in_cantidad]", str(data["CASH_IN"])) \
                    .replace("[cash_in_comision]", "1") \
                    .replace("[cash_in_total]", str(data["Total_CASH_IN"])) \
                    .replace("[total_transacciones]", str(total_transacciones)) \
                    .replace("[total_pagar]", str(total_pagar))
            emisor_section = emisor_section.replace("[AGENTE_SECTION_HTML]", agente_section)
            html_data += emisor_section

        file_name = file.split(f"{month_filename}")[0] + f"_{fecha_filename}.pdf"
        # Generación del PDF
        html_generate = html_content.replace("[SECTION]", html_data)
        file_route = os.path.join(ROUTE_MAIN, file_name)
        pdfkit.from_string(html_generate, file_route)
        # Variables necesarias
        emisor = file.split("-")[1]
        tipo = file.split("-")[2]
        s3_key = f"{emisor}/{fecha_plus_one}/INTEROPE-{tipo}/"
        # Firmar archivo
        signed_file(file_route, s3_key)
        # Subir a S3
        upload_s3(file_route, file_name, s3_key)


if __name__ == "__main__":
    main()
