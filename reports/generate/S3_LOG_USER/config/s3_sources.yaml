# Configuración de fuentes S3 para el pipeline de LOG_USUARIOS
s3_bucket: "prd-datalake-silver-zone-************"

# Tablas fuente Oracle convertidas a Parquet
oracle_sources:
  user_profile:
    table_name: "PDP_PROD10_MAINDB.USER_PROFILE"
    s3_path: "s3://prd-datalake-silver-zone-************/PDP_PROD10_MAINDB/USER_PROFILE_ORA/consolidado_puro.parquet"
    
  kyc_details:
    table_name: "PDP_PROD10_MAINDB.KYC_DETAILS"
    s3_path: "s3://prd-datalake-silver-zone-************/PDP_PROD10_MAINDB/KYC_DETAILS_ORA/consolidado_puro.parquet"
    
  issuer_details:
    table_name: "PDP_PROD10_MAINDB.ISSUER_DETAILS"
    s3_path: "s3://prd-datalake-silver-zone-************/PDP_PROD10_MAINDB/ISSUER_DETAILS_ORA/consolidado_puro.parquet"
    
  mtx_categories:
    table_name: "PDP_PROD10_MAINDB.MTX_CATEGORIES"
    s3_path: "s3://prd-datalake-silver-zone-************/PDP_PROD10_MAINDB/MTX_CATEGORIES_ORA/consolidado_puro.parquet"
    
  channel_grades:
    table_name: "PDP_PROD10_MAINDB.CHANNEL_GRADES"
    s3_path: "s3://prd-datalake-silver-zone-************/PDP_PROD10_MAINDB/CHANNEL_GRADES_ORA/consolidado_puro.parquet"
    
  user_modification_history:
    table_name: "PDP_PROD10_MAINDB.USER_MODIFICATION_HISTORY"
    s3_path: "s3://prd-datalake-silver-zone-************/PDP_PROD10_MAINDB/USER_MODIFICATION_HISTORY_ORA/consolidado_puro.parquet"
    
  user_auth_change_history:
    table_name: "PDP_PROD10_MAINDB.USER_AUTH_CHANGE_HISTORY"
    s3_path: "s3://prd-datalake-silver-zone-************/PDP_PROD10_MAINDB/USER_AUTH_CHANGE_HISTORY_ORA/consolidado_puro.parquet"

  user_identifier:
    table_name: "PDP_PROD10_MAINDB.USER_IDENTIFIER"
    s3_path: "s3://prd-datalake-silver-zone-************/PDP_PROD10_MAINDB/USER_IDENTIFIER_ORA/consolidado_puro.parquet"

  mtx_wallet:
    table_name: "PDP_PROD10_MAINDBBUS.MTX_WALLET"
    s3_path: "s3://prd-datalake-silver-zone-************/PDP_PROD10_MAINDBBUS/MTX_WALLET_ORA/consolidado_puro.parquet"

# Tabla fuente MySQL ya en S3
mysql_sources:
  user_account_history:
    table_name: "app_bim_prod_1.user_account_history"
    s3_path: "s3://prd-datalake-silver-zone-************/APP_BIM_PROD_1/USER_ACCOUNT_HISTORY/consolidado_puro.parquet"

# Configuración de archivos temporales
temp_storage:
  base_path: "TEMP_LOGS_USUARIOS"
  files:
    user_data_trx: "USER_DATA_TRX.parquet"
    user_modification_day: "USER_MODIFICATION_DAY.parquet"
    user_auth_change_history: "USER_AUTH_CHANGE_HISTORY.parquet"
    user_account_history: "USER_ACCOUNT_HISTORY.parquet"

# Configuración de salida
output:
  base_path: "output"
  final_file: "LOG_USR.parquet"
  csv_export_path: "csv_exports"
