#!/usr/bin/env python3
"""
Script de retroingeniería Oracle: rastrea 4 registros desde la tabla final LOG_USR hasta la tabla origen.
"""
import oracledb
from datetime import datetime

# Credenciales y DSN ya validados
ORACLE_USER = 'usr_datalake'
ORACLE_PASS = 'U2024b1mD4t4l4k5'
ORACLE_DSN = '*************:1521/MMONEY'

# Datos de los 4 registros a rastrear
REGISTROS = [
    {'userhistid': 'US.1824699228168277', 'createdon': '2025-06-05 22:12:49', 'requesttype': 'AfiliaUser', 'userid': '*************'},
    {'userhistid': 'US.1824699228168277', 'createdon': '2025-06-05 22:12:49', 'requesttype': 'ActivateUser', 'userid': '*************'},
    {'userhistid': 'UM.6548', 'createdon': '2025-06-05 22:12:50', 'requesttype': 'User Modification', 'userid': '*************'},
    {'userhistid': 'UM.6549', 'createdon': '2025-06-05 22:12:51', 'requesttype': 'User Modification', 'userid': '*************'},
]

# Tablas intermedias a revisar (puedes agregar más si identificas otras)
TABLAS_INTERMEDIAS = [
    'USER_DATA_TRX',
    'USER_MODIFICATION_DAY',
    'USER_AUTH_CHANGE_HISTORY',
    'USER_ACCOUNT_HISTORY',
    'USER_PROFILE_TEST',
    'USER_BALANCES'
]

# Función de conexión

def conectar_oracle():
    try:
        conn = oracledb.connect(user=ORACLE_USER, password=ORACLE_PASS, dsn=ORACLE_DSN)
        print('✅ Conexión a Oracle exitosa')
        return conn
    except Exception as e:
        print(f'❌ Error conectando a Oracle: {e}')
        return None

def buscar_en_tabla(cursor, owner, tabla, campo, valor):
    try:
        cursor.execute(f"SELECT COUNT(*) FROM {owner}.{tabla} WHERE {campo} = :valor", {'valor': valor})
        count = cursor.fetchone()[0]
        if count > 0:
            cursor.execute(f"SELECT * FROM {owner}.{tabla} WHERE {campo} = :valor AND ROWNUM <= 3", {'valor': valor})
            rows = cursor.fetchall()
            print(f"   ✅ {count} encontrados en {owner}.{tabla} por {campo}={valor}. Ejemplo: {rows[0] if rows else 'Sin muestra'}")
            return True
        else:
            print(f"   ❌ No encontrado en {owner}.{tabla} por {campo}={valor}")
            return False
    except Exception as e:
        print(f"   ⚠️  Error buscando en {owner}.{tabla}: {e}")
        return False

def buscar_en_tablas_origen(cursor, reg):
    print("   🔄 Buscando en tablas origen MMONEYBCH (barrido exhaustivo de columnas)...")
    try:
        cursor.execute("""
            SELECT TABLE_NAME FROM ALL_TABLES WHERE OWNER = 'MMONEYBCH' AND (
                TABLE_NAME LIKE '%USER%' OR TABLE_NAME LIKE '%PROFILE%' OR TABLE_NAME LIKE '%ACCOUNT%' OR TABLE_NAME LIKE '%AUTH%' OR TABLE_NAME LIKE '%WALLET%'
            )
        """)
        tablas_origen = [row[0] for row in cursor.fetchall()]
        encontrado = False
        for tabla in tablas_origen:
            cursor.execute(f"SELECT COLUMN_NAME FROM ALL_TAB_COLUMNS WHERE TABLE_NAME = :tabla AND OWNER = 'MMONEYBCH'", {'tabla': tabla})
            columnas = [row[0] for row in cursor.fetchall()]
            # Probar todos los valores posibles del registro
            valores = {
                'DOCUMENT': '********',
                'USERID': reg['userid'],
                'MSISDN': '***********',
                'NOMBRE': 'JOSÉ MARÍA',
                'APELLIDO': 'FERNANDEZ',
                'CREATEDON': reg['createdon'],
                'USERHISTID': reg['userhistid']
            }
            for col in columnas:
                for key, valor in valores.items():
                    if key in col or col in valores:
                        try:
                            cursor.execute(f"SELECT * FROM MMONEYBCH.{tabla} WHERE {col} = :valor AND ROWNUM <= 1", {'valor': valor})
                            row = cursor.fetchone()
                            if row:
                                print(f"   ✅ ENCONTRADO en MMONEYBCH.{tabla} por {col}={valor}. Ejemplo: {row}")
                                encontrado = True
                                break
                        except Exception as e:
                            continue
                if encontrado:
                    break
            if encontrado:
                break
        if not encontrado:
            print("   ❌ No se encontró el registro en ninguna tabla de origen MMONEYBCH para ningún campo del registro.")
    except Exception as e:
        print(f"   ⚠️  Error buscando en tablas origen: {e}")

def rastrear_registro(reg):
    conn = conectar_oracle()
    if not conn:
        return
    cursor = conn.cursor()
    print(f"\n🔎 Rastreando registro: {reg}")
    # 1. Buscar en LOG_USR
    buscar_en_tabla(cursor, 'USR_DATALAKE', 'LOG_USR', 'USERHISTID', reg['userhistid'])
    # 2. Buscar en tablas intermedias
    for tabla in TABLAS_INTERMEDIAS:
        try:
            cursor.execute(f"SELECT COUNT(*) FROM ALL_TAB_COLUMNS WHERE TABLE_NAME = :tabla AND OWNER = 'USR_DATALAKE' AND COLUMN_NAME = 'USERHISTID'", {'tabla': tabla})
            tiene_userhistid = cursor.fetchone()[0]
            if tiene_userhistid:
                if buscar_en_tabla(cursor, 'USR_DATALAKE', tabla, 'USERHISTID', reg['userhistid']):
                    continue
            cursor.execute(f"SELECT COUNT(*) FROM ALL_TAB_COLUMNS WHERE TABLE_NAME = :tabla AND OWNER = 'USR_DATALAKE' AND COLUMN_NAME = 'USERID'", {'tabla': tabla})
            tiene_userid = cursor.fetchone()[0]
            if tiene_userid:
                if buscar_en_tabla(cursor, 'USR_DATALAKE', tabla, 'USERID', reg['userid']):
                    continue
        except Exception as e:
            print(f"   ⚠️  Error revisando columnas en {tabla}: {e}")
    # 3. Buscar en tablas origen (MMONEYBCH) mejorado
    buscar_en_tablas_origen(cursor, reg)
    cursor.close()
    conn.close()

def main():
    print("\n🚀 INGENIERÍA INVERSA DE 4 REGISTROS LOG_USR -> ORIGEN")
    for reg in REGISTROS:
        rastrear_registro(reg)
    print("\n✅ Proceso completado. Revisa el rastro de cada registro arriba.")

if __name__ == "__main__":
    main()
