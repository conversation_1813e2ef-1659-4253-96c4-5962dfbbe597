#!/usr/bin/env python3
"""
VALIDADOR DE CONTEOS LOG_USR - Oracle vs S3 Pipeline
Objetivo: Verificar que los conteos coincidan exactamente ('2 gotas de agua')
Autor: Senior Data Engineer Detective
"""

import oracledb
import pandas as pd
import sys
from datetime import datetime
from pathlib import Path
import logging

class ValidadorConteosLogUsr:
    def __init__(self):
        self.setup_logging()
        
    def setup_logging(self):
        """Configurar logging"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('validacion_conteos_log_usr.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def conectar_oracle(self):
        """Conectar a Oracle usando credenciales del usuario"""
        try:
            self.logger.info("🔌 Conectando a Oracle...")
            
            # Credenciales Oracle del usuario
            connection = oracledb.connect(
                user="usr_datalake",
                password="U2024b1mD4t4l4k5",
                dsn="10.240.131.10:1521/MMONEY"
            )
            
            self.logger.info("✅ Conexión exitosa a Oracle")
            return connection
            
        except Exception as e:
            self.logger.error(f"❌ Error conectando a Oracle: {e}")
            raise
    
    def obtener_conteo_oracle(self, fecha: str) -> int:
        """
        Obtener conteo de registros de Oracle LOG_USR para fecha específica
        
        Args:
            fecha: Fecha en formato 'YYYY-MM-DD'
        
        Returns:
            int: Cantidad de registros en Oracle
        """
        try:
            self.logger.info(f"📊 Obteniendo conteo Oracle para fecha: {fecha}")
            
            with self.conectar_oracle() as connection:
                cursor = connection.cursor()
                
                # Query exacta del usuario
                query = """
                SELECT COUNT(*)
                FROM USR_DATALAKE.LOG_USR
                WHERE TRUNC(CREATEDON) = TO_DATE(:fecha, 'YYYY-MM-DD')
                """
                
                cursor.execute(query, {'fecha': fecha})
                resultado = cursor.fetchone()
                conteo_oracle = resultado[0] if resultado else 0
                
                self.logger.info(f"🔢 Conteo Oracle LOG_USR: {conteo_oracle:,} registros")
                return conteo_oracle
                
        except Exception as e:
            self.logger.error(f"❌ Error obteniendo conteo Oracle: {e}")
            raise
    
    def obtener_conteo_s3_pipeline(self, fecha: str) -> int:
        """
        Obtener conteo de registros del pipeline S3 LOG_USR.parquet
        
        Args:
            fecha: Fecha en formato 'YYYY-MM-DD'
        
        Returns:
            int: Cantidad de registros en S3 pipeline
        """
        try:
            # Convertir fecha a formato de carpeta (YYYYMMDD)
            fecha_carpeta = datetime.strptime(fecha, '%Y-%m-%d').strftime('%Y%m%d')
            
            # Ruta del archivo LOG_USR.parquet
            parquet_path = f"/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/output/{fecha_carpeta}/LOG_USR.parquet"
            
            self.logger.info(f"📁 Leyendo archivo: {parquet_path}")
            
            if not Path(parquet_path).exists():
                self.logger.error(f"❌ Archivo no encontrado: {parquet_path}")
                return 0
            
            # Leer parquet y contar registros
            df = pd.read_parquet(parquet_path)
            conteo_s3 = len(df)
            
            self.logger.info(f"🔢 Conteo S3 Pipeline LOG_USR.parquet: {conteo_s3:,} registros")
            return conteo_s3
            
        except Exception as e:
            self.logger.error(f"❌ Error obteniendo conteo S3: {e}")
            raise
    
    def obtener_detalles_oracle(self, fecha: str) -> dict:
        """Obtener detalles adicionales de Oracle para análisis"""
        try:
            with self.conectar_oracle() as connection:
                cursor = connection.cursor()
                
                # Query para obtener estadísticas detalladas
                query_detalles = """
                SELECT 
                    REQUESTTYPE,
                    COUNT(*) as cantidad,
                    MIN(CREATEDON) as min_fecha,
                    MAX(CREATEDON) as max_fecha
                FROM USR_DATALAKE.LOG_USR
                WHERE TRUNC(CREATEDON) = TO_DATE(:fecha, 'YYYY-MM-DD')
                GROUP BY REQUESTTYPE
                ORDER BY cantidad DESC
                """
                
                cursor.execute(query_detalles, {'fecha': fecha})
                resultados = cursor.fetchall()
                
                detalles = {}
                for row in resultados:
                    detalles[row[0]] = {
                        'cantidad': row[1],
                        'min_fecha': row[2],
                        'max_fecha': row[3]
                    }
                
                return detalles
                
        except Exception as e:
            self.logger.error(f"❌ Error obteniendo detalles Oracle: {e}")
            return {}
    
    def obtener_detalles_s3(self, fecha: str) -> dict:
        """Obtener detalles adicionales del S3 pipeline para análisis"""
        try:
            fecha_carpeta = datetime.strptime(fecha, '%Y-%m-%d').strftime('%Y%m%d')
            parquet_path = f"/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/output/{fecha_carpeta}/LOG_USR.parquet"
            
            if not Path(parquet_path).exists():
                return {}
            
            df = pd.read_parquet(parquet_path)
            
            # Agrupar por REQUESTTYPE
            detalles = {}
            if 'REQUESTTYPE' in df.columns:
                agrupado = df.groupby('REQUESTTYPE').agg({
                    'CREATEDON': ['count', 'min', 'max']
                }).round(2)
                
                for request_type in agrupado.index:
                    detalles[request_type] = {
                        'cantidad': int(agrupado.loc[request_type, ('CREATEDON', 'count')]),
                        'min_fecha': agrupado.loc[request_type, ('CREATEDON', 'min')],
                        'max_fecha': agrupado.loc[request_type, ('CREATEDON', 'max')]
                    }
            
            return detalles
            
        except Exception as e:
            self.logger.error(f"❌ Error obteniendo detalles S3: {e}")
            return {}
    
    def validar_conteos(self, fecha: str) -> bool:
        """
        Validación principal: Comparar conteos Oracle vs S3 Pipeline
        
        Args:
            fecha: Fecha en formato 'YYYY-MM-DD'
        
        Returns:
            bool: True si los conteos coinciden exactamente
        """
        try:
            self.logger.info(f"🕵️ INICIANDO VALIDACIÓN DE CONTEOS PARA FECHA: {fecha}")
            self.logger.info("=" * 80)
            
            # Obtener conteos
            conteo_oracle = self.obtener_conteo_oracle(fecha)
            conteo_s3 = self.obtener_conteo_s3_pipeline(fecha)
            
            # Calcular diferencia
            diferencia = abs(conteo_oracle - conteo_s3)
            porcentaje_coincidencia = (min(conteo_oracle, conteo_s3) / max(conteo_oracle, conteo_s3) * 100) if max(conteo_oracle, conteo_s3) > 0 else 0
            
            # Mostrar resultados
            self.logger.info("📊 RESULTADOS DE VALIDACIÓN:")
            self.logger.info(f"   🔸 Oracle LOG_USR:     {conteo_oracle:,} registros")
            self.logger.info(f"   🔸 S3 Pipeline:        {conteo_s3:,} registros")
            self.logger.info(f"   🔸 Diferencia:         {diferencia:,} registros")
            self.logger.info(f"   🔸 Coincidencia:       {porcentaje_coincidencia:.2f}%")
            
            # Verificar coincidencia exacta
            coincidencia_exacta = (conteo_oracle == conteo_s3)
            
            if coincidencia_exacta:
                self.logger.info("✅ ¡ÉXITO! Los conteos coinciden exactamente ('2 gotas de agua')")
            else:
                self.logger.warning("⚠️  Los conteos NO coinciden - Requiere investigación")
                
                # Obtener detalles para análisis
                self.logger.info("\n🔍 ANÁLISIS DETALLADO POR REQUESTTYPE:")
                detalles_oracle = self.obtener_detalles_oracle(fecha)
                detalles_s3 = self.obtener_detalles_s3(fecha)
                
                # Comparar por tipo de request
                todos_tipos = set(detalles_oracle.keys()) | set(detalles_s3.keys())
                
                for tipo in sorted(todos_tipos):
                    oracle_count = detalles_oracle.get(tipo, {}).get('cantidad', 0)
                    s3_count = detalles_s3.get(tipo, {}).get('cantidad', 0)
                    diff = abs(oracle_count - s3_count)
                    
                    status = "✅" if oracle_count == s3_count else "❌"
                    self.logger.info(f"   {status} {tipo}: Oracle={oracle_count:,}, S3={s3_count:,}, Diff={diff:,}")
            
            self.logger.info("=" * 80)
            return coincidencia_exacta
            
        except Exception as e:
            self.logger.error(f"❌ Error en validación: {e}")
            return False

def main():
    """Función principal"""
    if len(sys.argv) != 2:
        print("Uso: python3 validar_conteos_log_usr.py YYYY-MM-DD")
        print("Ejemplo: python3 validar_conteos_log_usr.py 2025-06-10")
        sys.exit(1)
    
    fecha = sys.argv[1]
    
    # Validar formato de fecha
    try:
        datetime.strptime(fecha, '%Y-%m-%d')
    except ValueError:
        print("❌ Formato de fecha inválido. Use YYYY-MM-DD")
        sys.exit(1)
    
    # Ejecutar validación
    validador = ValidadorConteosLogUsr()
    resultado = validador.validar_conteos(fecha)
    
    # Código de salida
    sys.exit(0 if resultado else 1)

if __name__ == "__main__":
    main()
