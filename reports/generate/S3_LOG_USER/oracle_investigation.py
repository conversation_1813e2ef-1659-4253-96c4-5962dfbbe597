#!/usr/bin/env python3
import oracledb
import pandas as pd

# Configuración de conexión Oracle
oracle_config = {
    'user': 'usr_datalake',
    'password': 'U2024b1mD4t4l4k5',
    'host': '*************',
    'port': 1521,
    'service_name': 'MMONEY'
}

try:
    print("🔍 CONECTANDO A ORACLE...")
    
    # Crear conexión
    dsn = f"{oracle_config['host']}:{oracle_config['port']}/{oracle_config['service_name']}"
    connection = oracledb.connect(
        user=oracle_config['user'],
        password=oracle_config['password'],
        dsn=dsn
    )
    
    print("✅ Conexión exitosa a Oracle")
    
    # Buscar documento ******** en LOG_USR
    print(f"\n📄 BUSCANDO DOCUMENTO ******** EN USR_DATALAKE.LOG_USR")
    
    query = """
    SELECT 
        USERHISTID,
        CREATEDON,
        REQUESTTYPE,
        DOCUMENTO,
        MSISDN,
        BANKDOMAIN
    FROM USR_DATALAKE.LOG_USR
    WHERE DOCUMENTO = '********'
    AND TRUNC(CREATEDON) = TO_DATE('2025-06-09', 'YYYY-MM-DD')
    ORDER BY CREATEDON
    """
    
    cursor = connection.cursor()
    cursor.execute(query)
    results = cursor.fetchall()
    
    print(f"Registros encontrados en Oracle LOG_USR: {len(results)}")
    for i, row in enumerate(results):
        print(f"  {i+1}. USERHISTID: {row[0]}, CREATEDON: {row[1]}, REQUESTTYPE: {row[2]}")
    
    cursor.close()
    connection.close()
    
    print(f"\n🎯 CONCLUSIÓN:")
    if len(results) == 1:
        print("Oracle tiene SOLO 1 registro para este documento")
        print("Nuestro pipeline debe estar generando múltiples registros incorrectamente")
    elif len(results) > 1:
        print("Oracle tiene MÚLTIPLES registros para este documento")
        print("Necesitamos revisar la lógica de selección/deduplicación")
    else:
        print("Oracle NO tiene registros para este documento")
        print("Hay un problema en la consulta o los datos")

except Exception as e:
    print(f"❌ Error: {e}")

