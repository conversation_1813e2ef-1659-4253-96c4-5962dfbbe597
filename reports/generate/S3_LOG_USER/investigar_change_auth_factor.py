#!/usr/bin/env python3
"""
INVESTIGADOR CHANGE_AUTH_FACTOR - Encontrar los 38 registros extra
Objetivo: Identificar exactamente qué registros CHANGE_AUTH_FACTOR tiene S3 que Oracle no tiene
"""

import oracledb
import pandas as pd
import sys
from datetime import datetime

def investigar_change_auth_factor():
    """Investigar diferencias en CHANGE_AUTH_FACTOR entre Oracle y S3"""
    
    print("🕵️ INVESTIGANDO CHANGE_AUTH_FACTOR - 38 registros extra en S3")
    print("=" * 80)
    
    try:
        # 1. OBTENER DATOS DE ORACLE
        print("1️⃣ OBTENIENDO CHANGE_AUTH_FACTOR DE ORACLE...")
        connection = oracledb.connect(
            user="usr_datalake",
            password="U2024b1mD4t4l4k5", 
            dsn="10.240.131.10:1521/MMONEY"
        )
        
        cursor = connection.cursor()
        oracle_query = """
        SELECT 
            USERHISTID,
            TO_CHAR(CREATEDON, 'YYYY-MM-DD HH24:MI:SS') as CREATEDON,
            DOCUMENTO,
            MSISDN,
            USERID
        FROM USR_DATALAKE.LOG_USR
        WHERE TRUNC(CREATEDON) = TO_DATE('2025-06-10', 'YYYY-MM-DD')
        AND REQUESTTYPE = 'CHANGE_AUTH_FACTOR'
        ORDER BY CREATEDON, USERHISTID
        """
        
        cursor.execute(oracle_query)
        oracle_results = cursor.fetchall()
        connection.close()
        
        # Convertir a DataFrame
        oracle_df = pd.DataFrame(oracle_results, columns=['USERHISTID', 'CREATEDON', 'DOCUMENTO', 'MSISDN', 'USERID'])
        print(f"✅ Oracle CHANGE_AUTH_FACTOR: {len(oracle_df):,} registros")
        
        # 2. OBTENER DATOS DE S3
        print("\n2️⃣ OBTENIENDO CHANGE_AUTH_FACTOR DE S3...")
        s3_path = "/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/output/20250610/LOG_USR.parquet"
        s3_df_full = pd.read_parquet(s3_path)
        
        # Filtrar solo CHANGE_AUTH_FACTOR
        s3_df = s3_df_full[s3_df_full['REQUESTTYPE'] == 'CHANGE_AUTH_FACTOR'].copy()
        s3_df = s3_df[['USERHISTID', 'CREATEDON', 'DOCUMENTO', 'MSISDN', 'USERID']].copy()
        
        # Convertir CREATEDON a string para comparación
        s3_df['CREATEDON'] = pd.to_datetime(s3_df['CREATEDON']).dt.strftime('%Y-%m-%d %H:%M:%S')
        
        print(f"✅ S3 CHANGE_AUTH_FACTOR: {len(s3_df):,} registros")
        
        # 3. COMPARAR Y ENCONTRAR DIFERENCIAS
        print("\n3️⃣ COMPARANDO Y ENCONTRANDO DIFERENCIAS...")
        
        # Crear claves únicas para comparación
        oracle_df['key'] = oracle_df['USERHISTID'] + '|' + oracle_df['CREATEDON'] + '|' + oracle_df['DOCUMENTO'].astype(str)
        s3_df['key'] = s3_df['USERHISTID'] + '|' + s3_df['CREATEDON'] + '|' + s3_df['DOCUMENTO'].astype(str)
        
        oracle_keys = set(oracle_df['key'])
        s3_keys = set(s3_df['key'])
        
        # Registros que están en S3 pero NO en Oracle (los 38 extra)
        extra_in_s3 = s3_keys - oracle_keys
        missing_in_s3 = oracle_keys - s3_keys
        
        print(f"🔍 Registros EXTRA en S3 (no en Oracle): {len(extra_in_s3):,}")
        print(f"🔍 Registros FALTANTES en S3 (sí en Oracle): {len(missing_in_s3):,}")
        print(f"🔍 Diferencia neta: {len(extra_in_s3) - len(missing_in_s3):,}")
        
        # 4. ANALIZAR REGISTROS EXTRA EN S3
        if extra_in_s3:
            print(f"\n4️⃣ ANALIZANDO LOS {len(extra_in_s3)} REGISTROS EXTRA EN S3:")
            print("-" * 80)
            
            extra_df = s3_df[s3_df['key'].isin(extra_in_s3)].copy()
            
            # Mostrar primeros 10 registros extra
            print("📋 PRIMEROS 10 REGISTROS EXTRA EN S3:")
            for i, row in extra_df.head(10).iterrows():
                print(f"  {i+1:2d}. USERHISTID: {row['USERHISTID']}, CREATEDON: {row['CREATEDON']}, DOC: {row['DOCUMENTO']}")
            
            # Análisis por patrones
            print(f"\n📊 ANÁLISIS DE PATRONES EN REGISTROS EXTRA:")
            
            # Por hora
            extra_df['hora'] = pd.to_datetime(extra_df['CREATEDON']).dt.hour
            horas_extra = extra_df['hora'].value_counts().sort_index()
            print(f"🕐 Distribución por hora:")
            for hora, count in horas_extra.head(5).items():
                print(f"   Hora {hora:02d}: {count} registros")
            
            # Por USERHISTID pattern
            extra_df['userhistid_prefix'] = extra_df['USERHISTID'].str[:5]
            prefix_counts = extra_df['userhistid_prefix'].value_counts()
            print(f"🆔 Patrones de USERHISTID:")
            for prefix, count in prefix_counts.head(5).items():
                print(f"   {prefix}*: {count} registros")
        
        # 5. ANALIZAR REGISTROS FALTANTES EN S3
        if missing_in_s3:
            print(f"\n5️⃣ ANALIZANDO LOS {len(missing_in_s3)} REGISTROS FALTANTES EN S3:")
            print("-" * 80)
            
            missing_df = oracle_df[oracle_df['key'].isin(missing_in_s3)].copy()
            
            # Mostrar primeros 10 registros faltantes
            print("📋 PRIMEROS 10 REGISTROS FALTANTES EN S3:")
            for i, row in missing_df.head(10).iterrows():
                print(f"  {i+1:2d}. USERHISTID: {row['USERHISTID']}, CREATEDON: {row['CREATEDON']}, DOC: {row['DOCUMENTO']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error en investigación: {e}")
        return False

if __name__ == "__main__":
    investigar_change_auth_factor()
