#!/usr/bin/env python3
"""
VALIDADOR LOG_USR - Oracle vs S3 Pipeline
Compara conteos exactos entre Oracle USR_DATALAKE.LOG_USR y LOG_USR.parquet
Objetivo: Verificar coincidencia exacta ('2 gotas de agua')
"""

import oracledb
import pandas as pd
import sys
from datetime import datetime
from pathlib import Path

def conectar_oracle():
    """Conectar a Oracle usando template del usuario"""
    try:
        connection = oracledb.connect(
            user='usr_datalake',
            password='U2024b1mD4t4l4k5',
            dsn='*************:1521/MMONEY'
        )
        print("✅ Conexión a Oracle establecida exitosamente")
        return connection
    except Exception as e:
        print(f"❌ Error conectando a Oracle: {e}")
        raise

def obtener_conteo_oracle(fecha):
    """
    Ejecutar query exacta del usuario en Oracle
    SELECT count(*) FROM USR_DATALAKE.LOG_USR WHERE TRUNC(CREATEDON) = TO_DATE('2025-06-10', 'YYYY-MM-DD')
    """
    try:
        connection = conectar_oracle()
        cursor = connection.cursor()
        
        # Query exacta del usuario
        query = f"""
        SELECT count(*)
        FROM USR_DATALAKE.LOG_USR
        WHERE TRUNC(CREATEDON) = TO_DATE('{fecha}', 'YYYY-MM-DD')
        """
        
        print(f"🔍 Ejecutando query Oracle:")
        print(f"   {query.strip()}")
        
        cursor.execute(query)
        resultado = cursor.fetchone()
        conteo_oracle = resultado[0] if resultado else 0
        
        cursor.close()
        connection.close()
        
        print(f"📊 Resultado Oracle: {conteo_oracle:,} registros")
        return conteo_oracle
        
    except Exception as e:
        print(f"❌ Error ejecutando query Oracle: {e}")
        raise

def obtener_conteo_s3_parquet(fecha):
    """
    Leer archivo LOG_USR.parquet del pipeline S3
    /home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/output/20250610/LOG_USR.parquet
    """
    try:
        # Convertir fecha YYYY-MM-DD a YYYYMMDD para la carpeta
        fecha_carpeta = datetime.strptime(fecha, '%Y-%m-%d').strftime('%Y%m%d')
        
        # Ruta exacta del archivo parquet
        parquet_path = f"/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/output/{fecha_carpeta}/LOG_USR.parquet"
        
        print(f"📁 Leyendo archivo S3 parquet:")
        print(f"   {parquet_path}")
        
        if not Path(parquet_path).exists():
            print(f"❌ Archivo no encontrado: {parquet_path}")
            return 0
        
        # Leer parquet y contar registros
        df = pd.read_parquet(parquet_path)
        conteo_s3 = len(df)
        
        print(f"📊 Resultado S3: {conteo_s3:,} registros")
        return conteo_s3
        
    except Exception as e:
        print(f"❌ Error leyendo archivo S3 parquet: {e}")
        raise

def validar_coincidencia(fecha):
    """Validación principal - Comparar Oracle vs S3"""
    
    print(f"🕵️ VALIDANDO COINCIDENCIA LOG_USR PARA FECHA: {fecha}")
    print("=" * 80)
    
    try:
        # Obtener conteos
        print("\n1️⃣ OBTENIENDO CONTEO ORACLE...")
        conteo_oracle = obtener_conteo_oracle(fecha)
        
        print("\n2️⃣ OBTENIENDO CONTEO S3 PIPELINE...")
        conteo_s3 = obtener_conteo_s3_parquet(fecha)
        
        # Comparar resultados
        print("\n3️⃣ COMPARANDO RESULTADOS...")
        print("=" * 80)
        
        diferencia = abs(conteo_oracle - conteo_s3)
        coincide_exacto = (conteo_oracle == conteo_s3)
        
        print(f"🔸 Oracle USR_DATALAKE.LOG_USR:  {conteo_oracle:,} registros")
        print(f"🔸 S3 Pipeline LOG_USR.parquet:  {conteo_s3:,} registros")
        print(f"🔸 Diferencia absoluta:          {diferencia:,} registros")
        
        if coincide_exacto:
            print("\n✅ ¡ÉXITO TOTAL! Los conteos coinciden EXACTAMENTE")
            print("🎯 Resultado: '2 gotas de agua' - Coincidencia perfecta")
            print("🚀 El pipeline S3 replica Oracle al 100%")
            return True
        else:
            porcentaje = (min(conteo_oracle, conteo_s3) / max(conteo_oracle, conteo_s3) * 100) if max(conteo_oracle, conteo_s3) > 0 else 0
            print(f"\n⚠️  NO COINCIDEN - Coincidencia: {porcentaje:.2f}%")
            print("🔍 Se requiere investigación para lograr coincidencia exacta")
            
            # Sugerencias de investigación
            if conteo_oracle > conteo_s3:
                print(f"📈 Oracle tiene {diferencia:,} registros MÁS que S3")
                print("💡 Posibles causas: Filtros faltantes en pipeline S3")
            else:
                print(f"📉 S3 tiene {diferencia:,} registros MÁS que Oracle")
                print("💡 Posibles causas: Duplicaciones en pipeline S3")
            
            return False
            
    except Exception as e:
        print(f"❌ Error en validación: {e}")
        return False
    
    finally:
        print("=" * 80)

def obtener_detalles_oracle(fecha):
    """Obtener detalles adicionales de Oracle para investigación"""
    try:
        print(f"\n🔍 OBTENIENDO DETALLES ORACLE PARA INVESTIGACIÓN...")
        
        connection = conectar_oracle()
        cursor = connection.cursor()
        
        # Query para detalles por REQUESTTYPE
        query_detalles = f"""
        SELECT 
            REQUESTTYPE,
            COUNT(*) as cantidad,
            MIN(CREATEDON) as primera_fecha,
            MAX(CREATEDON) as ultima_fecha
        FROM USR_DATALAKE.LOG_USR
        WHERE TRUNC(CREATEDON) = TO_DATE('{fecha}', 'YYYY-MM-DD')
        GROUP BY REQUESTTYPE
        ORDER BY cantidad DESC
        """
        
        cursor.execute(query_detalles)
        resultados = cursor.fetchall()
        
        print(f"📊 DETALLES POR REQUESTTYPE (Oracle):")
        print(f"{'REQUESTTYPE':<25} {'CANTIDAD':<10} {'PRIMERA':<20} {'ÚLTIMA':<20}")
        print("-" * 80)
        
        total_detalle = 0
        for row in resultados:
            request_type = row[0] or 'NULL'
            cantidad = row[1]
            primera = row[2].strftime('%H:%M:%S') if row[2] else 'N/A'
            ultima = row[3].strftime('%H:%M:%S') if row[3] else 'N/A'
            
            print(f"{request_type:<25} {cantidad:<10,} {primera:<20} {ultima:<20}")
            total_detalle += cantidad
        
        print("-" * 80)
        print(f"{'TOTAL':<25} {total_detalle:<10,}")
        
        cursor.close()
        connection.close()
        
    except Exception as e:
        print(f"❌ Error obteniendo detalles Oracle: {e}")

def main():
    """Función principal"""
    
    if len(sys.argv) != 2:
        print("❌ Uso incorrecto")
        print("📖 Uso: python3 validar_log_usr_oracle_vs_s3.py YYYY-MM-DD")
        print("📖 Ejemplo: python3 validar_log_usr_oracle_vs_s3.py 2025-06-10")
        sys.exit(1)
    
    fecha = sys.argv[1]
    
    # Validar formato de fecha
    try:
        datetime.strptime(fecha, '%Y-%m-%d')
    except ValueError:
        print("❌ Formato de fecha inválido")
        print("📖 Use formato: YYYY-MM-DD (ejemplo: 2025-06-10)")
        sys.exit(1)
    
    # Ejecutar validación principal
    resultado = validar_coincidencia(fecha)
    
    # Si no coinciden, mostrar detalles para investigación
    if not resultado:
        obtener_detalles_oracle(fecha)
        print("\n💡 RECOMENDACIONES:")
        print("   1. Revisar filtros de fecha en pipeline S3")
        print("   2. Verificar lógica de deduplicación")
        print("   3. Comparar estructura de datos Oracle vs S3")
        print("   4. Validar transformaciones de datos")
    
    # Código de salida
    sys.exit(0 if resultado else 1)

if __name__ == "__main__":
    main()
