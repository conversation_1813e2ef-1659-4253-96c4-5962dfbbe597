#!/usr/bin/env python3
"""
BUSCAR CASOS FALTANTES EN S3 - Retroingeniería desde pipeline hasta fuentes S3
Busca los casos del archivo casos_faltantes_26.csv en las fuentes S3 para identificar el origen
"""
import pandas as pd
import duckdb
import boto3
import sys
from datetime import datetime
import logging

class BuscadorCasosFaltantesS3:
    def __init__(self):
        self.setup_logging()
        self.conn = duckdb.connect()
        self.setup_s3_credentials()
        
        # Configuración de rutas S3 (igual que en pipeline_log_usuarios_duckdb.py)
        self.s3_bucket = "prd-datalake-silver-zone-637423440311"
        self.s3_sources = {
            'user_profile': f's3://{self.s3_bucket}/PDP_PROD10_MAINDB/USER_PROFILE_ORA/consolidado_puro.parquet',
            'user_identifier': f's3://{self.s3_bucket}/PDP_PROD10_MAINDB/USER_IDENTIFIER_ORA/consolidado_puro.parquet',
            'kyc_details': f's3://{self.s3_bucket}/PDP_PROD10_MAINDB/KYC_DETAILS_ORA/consolidado_puro.parquet',
            'issuer_details': f's3://{self.s3_bucket}/PDP_PROD10_MAINDB/ISSUER_DETAILS_ORA/consolidado_puro.parquet',
            'mtx_categories': f's3://{self.s3_bucket}/PDP_PROD10_MAINDB/MTX_CATEGORIES_ORA/consolidado_puro.parquet',
            'channel_grades': f's3://{self.s3_bucket}/PDP_PROD10_MAINDB/CHANNEL_GRADES_ORA/consolidado_puro.parquet',
            'user_modification_history': f's3://{self.s3_bucket}/PDP_PROD10_MAINDB/USER_MODIFICATION_HISTORY_ORA/consolidado_puro.parquet',
            'user_auth_change_history': f's3://{self.s3_bucket}/PDP_PROD10_MAINDB/USER_AUTH_CHANGE_HISTORY_ORA/consolidado_puro.parquet',
            'mtx_wallet': f's3://{self.s3_bucket}/PDP_PROD10_MAINDBBUS/MTX_WALLET_ORA/consolidado_puro.parquet',
            'user_account_history': f's3://{self.s3_bucket}/APP_BIM_PROD_1/USER_ACCOUNT_HISTORY/consolidado_puro.parquet'
        }
        
    def setup_logging(self):
        logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
        self.logger = logging.getLogger(__name__)
        
    def setup_s3_credentials(self):
        """Configurar credenciales S3 en DuckDB"""
        try:
            self.logger.info("Configurando credenciales S3...")
            
            # Obtener credenciales activas de AWS CLI
            session = boto3.Session()
            credentials = session.get_credentials().get_frozen_credentials()
            
            # Cargar soporte para S3 en DuckDB
            self.conn.sql("INSTALL httpfs;")
            self.conn.sql("LOAD httpfs;")
            self.conn.sql("SET s3_region='us-east-1';")
            self.conn.sql("SET s3_use_ssl=true;")
            self.conn.sql("SET s3_url_style='path';")
            
            # Pasar credenciales explícitamente
            self.conn.sql(f"SET s3_access_key_id='{credentials.access_key}';")
            self.conn.sql(f"SET s3_secret_access_key='{credentials.secret_key}';")
            if credentials.token:
                self.conn.sql(f"SET s3_session_token='{credentials.token}';")
            
            self.logger.info("✅ Credenciales S3 configuradas exitosamente")
            
        except Exception as e:
            self.logger.error(f"❌ Error configurando credenciales S3: {e}")
            raise
    
    def cargar_casos_faltantes(self, archivo_csv='casos_faltantes_26.csv'):
        """Cargar casos faltantes desde CSV"""
        try:
            df = pd.read_csv(archivo_csv)
            self.logger.info(f"📊 Casos faltantes cargados: {len(df):,}")
            
            # Extraer documentos únicos para búsqueda
            documentos_unicos = df['Documento'].str.replace(r'X.*', '', regex=True).unique()
            msisdns_unicos = df['MSISDN'].unique()
            
            self.logger.info(f"🔍 Documentos únicos a buscar: {len(documentos_unicos)}")
            self.logger.info(f"🔍 MSISDNs únicos a buscar: {len(msisdns_unicos)}")
            
            return df, documentos_unicos, msisdns_unicos
            
        except Exception as e:
            self.logger.error(f"❌ Error cargando casos faltantes: {e}")
            return None, None, None
    
    def buscar_en_tabla_s3(self, tabla_nombre, tabla_url, documentos, msisdns):
        """Buscar documentos/MSISDNs en una tabla específica de S3"""
        try:
            self.logger.info(f"🔎 Buscando en {tabla_nombre}...")
            
            # Intentar cargar la tabla
            try:
                # Verificar estructura de la tabla primero
                result = self.conn.sql(f"DESCRIBE SELECT * FROM '{tabla_url}' LIMIT 1").fetchall()
                columnas = [row[0] for row in result]
                self.logger.info(f"   📋 Columnas disponibles: {columnas[:10]}...")  # Mostrar primeras 10
                
            except Exception as e:
                self.logger.warning(f"   ⚠️  Error accediendo a {tabla_nombre}: {e}")
                return {}
            
            resultados = {}
            
            # Buscar por campos comunes de documento
            campos_documento = ['DOCUMENT', 'DOCUMENT_ID', 'DOCUMENT_NUMBER', 'IDENTIFICATION_NUMBER']
            for campo in campos_documento:
                if campo in columnas:
                    try:
                        # Crear lista de documentos para query IN
                        docs_str = "', '".join(documentos[:50])  # Limitar a 50 para evitar queries muy largas
                        
                        result = self.conn.sql(f"""
                            SELECT {campo}, COUNT(*) as count
                            FROM '{tabla_url}'
                            WHERE {campo} IN ('{docs_str}')
                            GROUP BY {campo}
                            LIMIT 10
                        """).fetchall()
                        
                        if result:
                            resultados[f'{campo}_matches'] = len(result)
                            self.logger.info(f"   ✅ {len(result)} documentos encontrados en {campo}")
                            for doc, count in result[:3]:  # Mostrar primeros 3 ejemplos
                                self.logger.info(f"      📄 {doc}: {count} registros")
                        
                    except Exception as e:
                        continue
            
            # Buscar por campos de MSISDN
            campos_msisdn = ['MSISDN', 'MOBILE_NUMBER', 'PHONE_NUMBER', 'MOBILE_PHONE']
            for campo in campos_msisdn:
                if campo in columnas:
                    try:
                        msisdns_str = "', '".join(msisdns[:50])  # Limitar a 50
                        
                        result = self.conn.sql(f"""
                            SELECT {campo}, COUNT(*) as count
                            FROM '{tabla_url}'
                            WHERE {campo} IN ('{msisdns_str}')
                            GROUP BY {campo}
                            LIMIT 10
                        """).fetchall()
                        
                        if result:
                            resultados[f'{campo}_matches'] = len(result)
                            self.logger.info(f"   ✅ {len(result)} MSISDNs encontrados en {campo}")
                            for msisdn, count in result[:3]:  # Mostrar primeros 3 ejemplos
                                self.logger.info(f"      📱 {msisdn}: {count} registros")
                    
                    except Exception as e:
                        continue
            
            # Buscar por USER_ID si existe
            if 'USER_ID' in columnas:
                try:
                    # Extraer USER_IDs de algunos casos faltantes si los hay en el nombre del archivo
                    result = self.conn.sql(f"""
                        SELECT COUNT(*) as total_records
                        FROM '{tabla_url}'
                        LIMIT 1
                    """).fetchone()
                    
                    if result:
                        resultados['total_records'] = result[0]
                        self.logger.info(f"   📊 Total de registros en tabla: {result[0]:,}")
                
                except Exception as e:
                    pass
            
            if not resultados:
                self.logger.info(f"   ❌ No se encontraron matches en {tabla_nombre}")
            
            return resultados
            
        except Exception as e:
            self.logger.error(f"   ⚠️  Error procesando {tabla_nombre}: {e}")
            return {}
    
    def buscar_en_todas_las_fuentes(self, documentos, msisdns):
        """Buscar en todas las fuentes S3 configuradas"""
        self.logger.info("🔍 Iniciando búsqueda en todas las fuentes S3...")
        
        resultados_totales = {}
        
        for tabla_nombre, tabla_url in self.s3_sources.items():
            resultados = self.buscar_en_tabla_s3(tabla_nombre, tabla_url, documentos, msisdns)
            if resultados:
                resultados_totales[tabla_nombre] = resultados
        
        return resultados_totales
    
    def generar_reporte(self, casos_df, resultados):
        """Generar reporte de hallazgos"""
        self.logger.info("\n" + "="*80)
        self.logger.info("📋 REPORTE DE HALLAZGOS EN FUENTES S3")
        self.logger.info("="*80)
        
        # Resumen de casos faltantes
        tipos_transaccion = casos_df['TipoTransaccion'].value_counts()
        self.logger.info(f"\n📊 CASOS FALTANTES POR TIPO:")
        for tipo, count in tipos_transaccion.items():
            self.logger.info(f"   {tipo}: {count}")
        
        # Hallazgos por fuente
        self.logger.info(f"\n🔍 HALLAZGOS POR FUENTE S3:")
        fuentes_con_datos = 0
        for fuente, datos in resultados.items():
            if datos:
                fuentes_con_datos += 1
                self.logger.info(f"\n   ✅ {fuente.upper()}:")
                if isinstance(datos, dict):
                    for campo, valor in datos.items():
                        if isinstance(valor, int):
                            self.logger.info(f"      {campo}: {valor:,}")
                        else:
                            self.logger.info(f"      {campo}: {valor}")
                elif isinstance(datos, list):
                    self.logger.info(f"      registros_encontrados: {len(datos)}")
                    # Mostrar algunos ejemplos
                    for i, registro in enumerate(datos[:3]):
                        self.logger.info(f"      ejemplo_{i+1}: {registro}")
        
        self.logger.info(f"\n📈 RESUMEN:")
        self.logger.info(f"   • Casos faltantes analizados: {len(casos_df):,}")
        self.logger.info(f"   • Fuentes S3 verificadas: {len(self.s3_sources)}")
        self.logger.info(f"   • Fuentes con datos encontrados: {fuentes_con_datos}")
        
        if fuentes_con_datos == 0:
            self.logger.warning("   ⚠️  NO se encontraron datos en ninguna fuente S3 para los casos faltantes")
        else:
            self.logger.info(f"   ✅ Datos encontrados en {fuentes_con_datos} fuentes")
    
    def buscar_casos_especificos(self, documentos, msisdns):
        """Buscar casos específicos usando las columnas correctas de cada tabla"""
        self.logger.info("🔍 BÚSQUEDA ESPECÍFICA POR CASOS")
        
        resultados = {}
        
        # 1. Buscar en USER_PROFILE por MSISDN
        try:
            self.logger.info("🔎 Buscando MSISDNs en USER_PROFILE...")
            msisdns_str = "', '".join([str(m) for m in msisdns[:100]])  # Convertir a string
            
            result = self.conn.sql(f"""
                SELECT MSISDN, USER_ID, FIRST_NAME, LAST_NAME, COUNT(*) as count
                FROM '{self.s3_sources['user_profile']}'
                WHERE MSISDN IN ('{msisdns_str}')
                GROUP BY MSISDN, USER_ID, FIRST_NAME, LAST_NAME
                LIMIT 20
            """).fetchall()
            
            if result:
                resultados['user_profile_msisdn'] = result
                self.logger.info(f"   ✅ {len(result)} usuarios encontrados por MSISDN")
                for msisdn, user_id, first_name, last_name, count in result[:5]:
                    self.logger.info(f"      📱 {msisdn} -> USER_ID: {user_id}, Nombre: {first_name} {last_name}")
            
        except Exception as e:
            self.logger.warning(f"   ⚠️  Error buscando en USER_PROFILE: {e}")
        
        # 2. Buscar en USER_IDENTIFIER por IDENTIFIER_VALUE (documentos)
        try:
            self.logger.info("🔎 Buscando documentos en USER_IDENTIFIER...")
            docs_str = "', '".join(documentos[:100])
            
            result = self.conn.sql(f"""
                SELECT IDENTIFIER_VALUE, USER_ID, IDENTIFIER_TYPE, COUNT(*) as count
                FROM '{self.s3_sources['user_identifier']}'
                WHERE IDENTIFIER_VALUE IN ('{docs_str}')
                AND IDENTIFIER_TYPE = 'DOCUMENT'
                GROUP BY IDENTIFIER_VALUE, USER_ID, IDENTIFIER_TYPE
                LIMIT 20
            """).fetchall()
            
            if result:
                resultados['user_identifier_docs'] = result
                self.logger.info(f"   ✅ {len(result)} documentos encontrados")
                for doc, user_id, id_type, count in result[:5]:
                    self.logger.info(f"      📄 {doc} -> USER_ID: {user_id}, Tipo: {id_type}")
            
        except Exception as e:
            self.logger.warning(f"   ⚠️  Error buscando en USER_IDENTIFIER: {e}")
        
        # 3. Buscar en KYC_DETAILS por ID_VALUE (documentos)
        try:
            self.logger.info("🔎 Buscando documentos en KYC_DETAILS...")
            docs_str = "', '".join(documentos[:100])
            
            result = self.conn.sql(f"""
                SELECT ID_VALUE, KYC_ID, ID_TYPE, COUNT(*) as count
                FROM '{self.s3_sources['kyc_details']}'
                WHERE ID_VALUE IN ('{docs_str}')
                GROUP BY ID_VALUE, KYC_ID, ID_TYPE
                LIMIT 20
            """).fetchall()
            
            if result:
                resultados['kyc_details_docs'] = result
                self.logger.info(f"   ✅ {len(result)} documentos encontrados en KYC")
                for doc, kyc_id, id_type, count in result[:5]:
                    self.logger.info(f"      📄 {doc} -> KYC_ID: {kyc_id}, Tipo: {id_type}")
            
        except Exception as e:
            self.logger.warning(f"   ⚠️  Error buscando en KYC_DETAILS: {e}")
        
        # 4. Buscar en USER_MODIFICATION_HISTORY por USER_ID de los usuarios encontrados
        if 'user_profile_msisdn' in resultados:
            try:
                self.logger.info("🔎 Buscando historiales de modificación...")
                user_ids = [str(row[1]) for row in resultados['user_profile_msisdn']]
                user_ids_str = "', '".join(user_ids[:50])
                
                result = self.conn.sql(f"""
                    SELECT USER_ID, REQUEST_TYPE, CREATED_ON, COUNT(*) as count
                    FROM '{self.s3_sources['user_modification_history']}'
                    WHERE USER_ID IN ('{user_ids_str}')
                    AND CREATED_ON >= '2025-06-08 00:00:00'
                    AND CREATED_ON <= '2025-06-08 23:59:59'
                    GROUP BY USER_ID, REQUEST_TYPE, CREATED_ON
                    ORDER BY CREATED_ON DESC
                    LIMIT 20
                """).fetchall()
                
                if result:
                    resultados['modification_history'] = result
                    self.logger.info(f"   ✅ {len(result)} modificaciones encontradas para 2025-06-08")
                    for user_id, request_type, created_on, count in result[:5]:
                        self.logger.info(f"      🔄 USER_ID: {user_id}, Tipo: {request_type}, Fecha: {created_on}")
                else:
                    self.logger.info("   ❌ No se encontraron modificaciones para 2025-06-08")
                
                # Buscar también en todo el historial (sin filtro de fecha)
                result_all = self.conn.sql(f"""
                    SELECT USER_ID, REQUEST_TYPE, COUNT(*) as total_changes
                    FROM '{self.s3_sources['user_modification_history']}'
                    WHERE USER_ID IN ('{user_ids_str}')
                    GROUP BY USER_ID, REQUEST_TYPE
                    ORDER BY total_changes DESC
                    LIMIT 10
                """).fetchall()
                
                if result_all:
                    resultados['modification_history_all'] = result_all
                    self.logger.info(f"   📊 Historial completo: {len(result_all)} tipos de cambios")
                    for user_id, request_type, total in result_all[:3]:
                        self.logger.info(f"      📈 USER_ID: {user_id}, Tipo: {request_type}, Total: {total}")
                
            except Exception as e:
                self.logger.warning(f"   ⚠️  Error buscando en USER_MODIFICATION_HISTORY: {e}")
        
        return resultados
    
    def ejecutar_busqueda(self):
        """Método principal para ejecutar la búsqueda completa"""
        try:
            self.logger.info("🚀 INICIANDO BÚSQUEDA DE CASOS FALTANTES EN S3")
            
            # Cargar casos faltantes
            casos_df, documentos, msisdns = self.cargar_casos_faltantes()
            if casos_df is None:
                return
            
            # Buscar casos específicos primero
            resultados_especificos = self.buscar_casos_especificos(documentos, msisdns)
            
            # Buscar en todas las fuentes (búsqueda general)
            resultados = self.buscar_en_todas_las_fuentes(documentos, msisdns)
            
            # Combinar resultados
            resultados.update(resultados_especificos)
            
            # Generar reporte
            self.generar_reporte(casos_df, resultados)
            
            self.logger.info("\n✅ BÚSQUEDA COMPLETADA")
            
        except Exception as e:
            self.logger.error(f"❌ Error en búsqueda: {e}")
            raise

def main():
    buscador = BuscadorCasosFaltantesS3()
    buscador.ejecutar_busqueda()

if __name__ == "__main__":
    main()
