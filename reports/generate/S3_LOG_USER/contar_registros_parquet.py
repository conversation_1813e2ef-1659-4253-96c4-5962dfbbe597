#!/usr/bin/env python3
"""
CONTADOR DE REGISTROS - LOG_USR.parquet
Valida la cantidad exacta de registros en el archivo parquet
"""

import pandas as pd
import sys
from pathlib import Path

def contar_registros_parquet(archivo_path):
    """Contar registros en archivo parquet"""
    
    print(f"📁 Contando registros en: {archivo_path}")
    print("=" * 80)
    
    try:
        # Verificar que el archivo existe
        if not Path(archivo_path).exists():
            print(f"❌ Archivo no encontrado: {archivo_path}")
            return None
        
        # Leer parquet
        print("📊 Leyendo archivo parquet...")
        df = pd.read_parquet(archivo_path)
        
        # Contar registros
        total_registros = len(df)
        
        print(f"✅ Archivo leído exitosamente")
        print(f"🔢 Total de registros: {total_registros:,}")
        
        # Mostrar información adicional
        print(f"📋 Columnas en el archivo: {len(df.columns)}")
        print(f"💾 Tamaño en memoria: {df.memory_usage(deep=True).sum() / 1024 / 1024:.2f} MB")
        
        # Mostrar primeras columnas
        print(f"📝 Primeras columnas: {list(df.columns[:5])}")
        
        return total_registros
        
    except Exception as e:
        print(f"❌ Error leyendo archivo: {e}")
        return None

def main():
    """Función principal"""
    
    # Ruta fija del archivo que mencionaste
    archivo_path = "/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/output/20250610/LOG_USR.parquet"
    
    print("🔍 VALIDANDO CANTIDAD DE REGISTROS EN LOG_USR.parquet")
    print("=" * 80)
    
    # Contar registros
    total = contar_registros_parquet(archivo_path)
    
    if total is not None:
        print("=" * 80)
        print("📊 RESULTADO FINAL:")
        print(f"   🔸 Archivo: LOG_USR.parquet")
        print(f"   🔸 Registros: {total:,}")
        
        # Comparar con tu conteo manual
        tu_conteo = 21862
        print(f"   🔸 Tu conteo manual: {tu_conteo:,}")
        
        if total == tu_conteo:
            print("   ✅ ¡COINCIDE! Tu conteo manual es correcto")
        else:
            diferencia = abs(total - tu_conteo)
            print(f"   ⚠️  Diferencia: {diferencia:,} registros")
            if total > tu_conteo:
                print(f"   📈 El archivo tiene {diferencia:,} registros MÁS")
            else:
                print(f"   📉 El archivo tiene {diferencia:,} registros MENOS")
        
        return True
    else:
        print("❌ No se pudo contar los registros")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
