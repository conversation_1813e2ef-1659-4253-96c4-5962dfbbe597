#!/usr/bin/env python3
"""
VALIDACIÓN RÁPIDA - Conteos LOG_USR Oracle vs S3
Uso: python3 check_log_usr_count.py 2025-06-10
"""

import oracledb
import pandas as pd
import sys
from datetime import datetime
from pathlib import Path

def check_counts(fecha):
    """Validación rápida de conteos"""
    
    print(f"🔍 Validando conteos LOG_USR para fecha: {fecha}")
    print("=" * 60)
    
    # 1. CONTEO ORACLE
    try:
        print("📊 Conectando a Oracle...")
        connection = oracledb.connect(
            user="usr_datalake",
            password="U2024b1mD4t4l4k5", 
            dsn="10.240.131.10:1521/MMONEY"
        )
        
        cursor = connection.cursor()
        query = """
        SELECT COUNT(*)
        FROM USR_DATALAKE.LOG_USR
        WHERE TRUNC(CREATEDON) = TO_DATE(:fecha, 'YYYY-MM-DD')
        """
        
        cursor.execute(query, {'fecha': fecha})
        conteo_oracle = cursor.fetchone()[0]
        connection.close()
        
        print(f"✅ Oracle LOG_USR: {conteo_oracle:,} registros")
        
    except Exception as e:
        print(f"❌ Error Oracle: {e}")
        return False
    
    # 2. CONTEO S3 PIPELINE
    try:
        fecha_carpeta = datetime.strptime(fecha, '%Y-%m-%d').strftime('%Y%m%d')
        parquet_path = f"/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/output/{fecha_carpeta}/LOG_USR.parquet"
        
        print(f"📁 Leyendo: {parquet_path}")
        
        if not Path(parquet_path).exists():
            print(f"❌ Archivo no encontrado: {parquet_path}")
            return False
        
        df = pd.read_parquet(parquet_path)
        conteo_s3 = len(df)
        
        print(f"✅ S3 Pipeline: {conteo_s3:,} registros")
        
    except Exception as e:
        print(f"❌ Error S3: {e}")
        return False
    
    # 3. COMPARACIÓN
    print("=" * 60)
    diferencia = abs(conteo_oracle - conteo_s3)
    coincide = (conteo_oracle == conteo_s3)
    
    print(f"🔸 Oracle:     {conteo_oracle:,}")
    print(f"🔸 S3:         {conteo_s3:,}")
    print(f"🔸 Diferencia: {diferencia:,}")
    
    if coincide:
        print("✅ ¡PERFECTO! Los conteos coinciden exactamente ('2 gotas de agua')")
        return True
    else:
        porcentaje = (min(conteo_oracle, conteo_s3) / max(conteo_oracle, conteo_s3) * 100)
        print(f"⚠️  NO coinciden - Coincidencia: {porcentaje:.2f}%")
        print("🔍 Se requiere investigación adicional")
        return False

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Uso: python3 check_log_usr_count.py YYYY-MM-DD")
        print("Ejemplo: python3 check_log_usr_count.py 2025-06-10")
        sys.exit(1)
    
    fecha = sys.argv[1]
    
    try:
        datetime.strptime(fecha, '%Y-%m-%d')
    except ValueError:
        print("❌ Formato de fecha inválido. Use YYYY-MM-DD")
        sys.exit(1)
    
    resultado = check_counts(fecha)
    sys.exit(0 if resultado else 1)
