#!/usr/bin/env python3
"""
INVESTIGADOR DE DUPLICADOS INTERNOS - S3 Pipeline
Objetivo: Encontrar duplicados internos en el archivo LOG_USR.parquet de S3
"""

import pandas as pd
import sys

def investigar_duplicados_internos():
    """Investigar duplicados internos en S3 LOG_USR.parquet"""
    
    print("🕵️ INVESTIGANDO DUPLICADOS INTERNOS EN S3 LOG_USR.parquet")
    print("=" * 80)
    
    try:
        # 1. CARGAR DATOS S3
        print("1️⃣ CARGANDO DATOS S3...")
        s3_path = "/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/output/20250610/LOG_USR.parquet"
        df = pd.read_parquet(s3_path)
        
        print(f"✅ Datos cargados: {len(df):,} registros")
        
        # 2. BUSCAR DUPLICADOS POR USERHISTID + REQUESTTYPE
        print("\n2️⃣ BUSCANDO DUPLICADOS POR USERHISTID + REQUESTTYPE...")
        
        # Contar combinaciones
        duplicates_check = df.groupby(['USERHISTID', 'REQUESTTYPE']).size().reset_index(name='count')
        duplicates = duplicates_check[duplicates_check['count'] > 1]
        
        print(f"🔍 Combinaciones únicas USERHISTID+REQUESTTYPE: {len(duplicates_check):,}")
        print(f"🔍 Combinaciones duplicadas: {len(duplicates):,}")
        
        if len(duplicates) > 0:
            total_duplicated_records = duplicates['count'].sum() - len(duplicates)
            print(f"🔍 Total registros duplicados: {total_duplicated_records:,}")
            
            # Mostrar ejemplos de duplicados
            print(f"\n📋 EJEMPLOS DE DUPLICADOS (Top 10):")
            print("-" * 80)
            for i, row in duplicates.head(10).iterrows():
                userhistid = row['USERHISTID']
                requesttype = row['REQUESTTYPE']
                count = row['count']
                
                # Obtener registros duplicados específicos
                dup_records = df[(df['USERHISTID'] == userhistid) & (df['REQUESTTYPE'] == requesttype)]
                
                print(f"  {i+1:2d}. USERHISTID: {userhistid}, REQUESTTYPE: {requesttype}, Count: {count}")
                for j, dup_row in dup_records.iterrows():
                    createdon = dup_row['CREATEDON']
                    documento = dup_row['DOCUMENTO']
                    print(f"      - CREATEDON: {createdon}, DOCUMENTO: {documento}")
                print()
        
        # 3. BUSCAR DUPLICADOS EXACTOS (todas las columnas)
        print("\n3️⃣ BUSCANDO DUPLICADOS EXACTOS (todas las columnas)...")
        
        # Identificar duplicados exactos
        duplicated_mask = df.duplicated(keep=False)
        exact_duplicates = df[duplicated_mask]
        
        print(f"🔍 Registros con duplicados exactos: {len(exact_duplicates):,}")
        
        if len(exact_duplicates) > 0:
            # Agrupar duplicados exactos
            exact_dup_groups = exact_duplicates.groupby(list(exact_duplicates.columns)).size().reset_index(name='count')
            exact_dup_groups = exact_dup_groups[exact_dup_groups['count'] > 1]
            
            print(f"🔍 Grupos de duplicados exactos: {len(exact_dup_groups):,}")
            
            # Mostrar ejemplos
            print(f"\n📋 EJEMPLOS DE DUPLICADOS EXACTOS (Top 5):")
            print("-" * 80)
            for i, row in exact_dup_groups.head(5).iterrows():
                userhistid = row['USERHISTID']
                requesttype = row['REQUESTTYPE']
                count = row['count']
                createdon = row['CREATEDON']
                documento = row['DOCUMENTO']
                
                print(f"  {i+1}. USERHISTID: {userhistid}")
                print(f"     REQUESTTYPE: {requesttype}")
                print(f"     CREATEDON: {createdon}")
                print(f"     DOCUMENTO: {documento}")
                print(f"     DUPLICADOS: {count} veces")
                print()
        
        # 4. ANÁLISIS ESPECÍFICO DE CHANGE_AUTH_FACTOR
        print("\n4️⃣ ANÁLISIS ESPECÍFICO DE CHANGE_AUTH_FACTOR...")
        
        change_auth_df = df[df['REQUESTTYPE'] == 'CHANGE_AUTH_FACTOR'].copy()
        print(f"🔍 Total CHANGE_AUTH_FACTOR: {len(change_auth_df):,}")
        
        # Duplicados en CHANGE_AUTH_FACTOR
        change_auth_dups = change_auth_df.groupby(['USERHISTID', 'REQUESTTYPE']).size().reset_index(name='count')
        change_auth_dups = change_auth_dups[change_auth_dups['count'] > 1]
        
        print(f"🔍 CHANGE_AUTH_FACTOR duplicados: {len(change_auth_dups):,}")
        
        if len(change_auth_dups) > 0:
            total_change_auth_extra = change_auth_dups['count'].sum() - len(change_auth_dups)
            print(f"🔍 Registros CHANGE_AUTH_FACTOR extra por duplicación: {total_change_auth_extra:,}")
            
            # Mostrar ejemplos
            print(f"\n📋 EJEMPLOS CHANGE_AUTH_FACTOR DUPLICADOS:")
            print("-" * 80)
            for i, row in change_auth_dups.head(5).iterrows():
                userhistid = row['USERHISTID']
                count = row['count']
                
                # Obtener registros específicos
                specific_records = change_auth_df[change_auth_df['USERHISTID'] == userhistid]
                
                print(f"  {i+1}. USERHISTID: {userhistid} ({count} duplicados)")
                for j, spec_row in specific_records.iterrows():
                    createdon = spec_row['CREATEDON']
                    documento = spec_row['DOCUMENTO']
                    print(f"     - CREATEDON: {createdon}, DOCUMENTO: {documento}")
                print()
        
        # 5. RESUMEN FINAL
        print("\n5️⃣ RESUMEN FINAL:")
        print("=" * 80)
        print(f"📊 Total registros en S3: {len(df):,}")
        print(f"📊 Registros únicos por USERHISTID+REQUESTTYPE: {len(duplicates_check):,}")
        print(f"📊 Diferencia (duplicados): {len(df) - len(duplicates_check):,}")
        print(f"📊 CHANGE_AUTH_FACTOR total: {len(change_auth_df):,}")
        
        if len(change_auth_dups) > 0:
            print(f"📊 CHANGE_AUTH_FACTOR duplicados: {total_change_auth_extra:,}")
            print(f"📊 CHANGE_AUTH_FACTOR únicos: {len(change_auth_df) - total_change_auth_extra:,}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error en investigación: {e}")
        return False

if __name__ == "__main__":
    investigar_duplicados_internos()
