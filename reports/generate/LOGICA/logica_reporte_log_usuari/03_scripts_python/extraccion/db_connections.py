import mysql.connector
import oracledb
from config import *

def get_mysql_connection():
    try:
        connection = mysql.connector.connect(
            host=MYSQL_HOST,
            port=MYSQL_PORT,
            user=MYSQL_USER,
            password=MYSQL_PASS,
            database=MYSQL_DB
        )
        return connection
    except Exception as e:
        raise Exception(f"Error connecting to MySQL: {str(e)}")

def get_oracle_connection():
    try:
        dsn = oracledb.makedsn(
            ORACLE_HOST,
            ORACLE_PORT,
            service_name=ORACLE_SERVICE
        )
        connection = oracledb.connect(
            user=ORACLE_USER,
            password=ORACLE_PASS,
            dsn=dsn
            #callTimeout=1500000 #espera 25 min antes de cerrar la conexion
        )
        return connection
    except Exception as e:
        raise Exception(f"Error connecting to Oracle: {str(e)}")
