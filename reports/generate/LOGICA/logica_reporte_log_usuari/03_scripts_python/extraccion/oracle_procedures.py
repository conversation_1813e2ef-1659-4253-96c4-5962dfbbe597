from db_connections import get_oracle_connection
import oracledb
import logging


#Configura el logger
logger = logging.getLogger(__name__)

def execute_sp_field7(fecha):
    try:
        with get_oracle_connection() as connection:
            cursor = connection.cursor()
            fecha_str = str(fecha)
            logger.info("Ejecutando SP_FIELD7")
            out_resultado = cursor.var(oracledb.STRING)
            cursor.callproc('SP_FIELD7', [fecha_str, out_resultado])
            logger.info(f"Resultado de SP_FIELD7: {out_resultado.getvalue()}")
            connection.commit()
    except oracledb.DatabaseError as db_err:
        logger.error(f"Error de Oracle al ejecutar SP_FIELD7: {db_err}")
        raise
    except Exception as e:
        logger.error(f"Error inesperado al ejecutar SP_FIELD7: {e}")
        raise
    else:
        logger.info("SP_FIELD7 ejecutado correctamente.")

def execute_sp_attr7(fecha):
    try:
        with get_oracle_connection() as connection:
            cursor = connection.cursor()
            fecha_str = str(fecha)
            logger.info("Ejecutando SP_ATTR7_NULL")
            out_resultado = cursor.var(oracledb.STRING)
            cursor.callproc('SP_ATTR7_NULL', [fecha_str,out_resultado])
            logger.info(f"Resultado de SP_ATTR7_NULL: {out_resultado.getvalue()}")
            connection.commit()
    except oracledb.DatabaseError as db_err:
        logger.error(f"Error de Oracle al ejecutar SP_ATTR7_NULL: {db_err}")
    except Exception as e:
        logger.error(f"Error inesperado al ejecutar SP_ATTR7_NULL: {e}")
        raise
    else:
        logger.info("SP_ATTR7_NULL ejecutado correctamente.")

def execute_sp_attr8(fecha):
    try:
        with get_oracle_connection() as connection:
            cursor = connection.cursor()
            fecha_str = str(fecha)
            logger.info("Ejecutando SP_ATTR8_NULL")
            out_resultado = cursor.var(oracledb.STRING)
            cursor.callproc('SP_ATTR8_NULL', [fecha_str,out_resultado])
            logger.info(f"Resultado de SP_ATTR8_NULL: {out_resultado.getvalue()}")
            connection.commit()
    except oracledb.DatabaseError as db_err:
        logger.error(f"Error de Oracle al ejecutar SP_ATTR8_NULL: {db_err}")
    except Exception as e:
        logger.error(f"Error inesperado al ejecutar SP_ATTR8_NULL: {e}")
        raise
    else:
        logger.info("SP_ATTR8_NULL ejecutado correctamente.")

def execute_sp_pre_log_usuarios(fecha):
    with get_oracle_connection() as connection:
        cursor = connection.cursor()
        try:
            fecha_str = str(fecha)
            logger.info("Ejecutando SP_PRE_LOG_USR")
            #out_resultado = cursor.var(oracledb.STRING)
            cursor.callproc('SP_PRE_LOG_USR', [fecha_str])
            logger.info("Finalizado SP_PRE_LOG_USR")
            #logger.info(f"Resultado de SP_LOG_USR: {out_resultado.getvalue()}")
            connection.commit()
        except Exception as e:
            connection.rollback()
            raise Exception(f"Error executing SP_LOG_USR: {str(e)}")

def execute_sp_user_modification(fecha):
    with get_oracle_connection() as connection:
        cursor = connection.cursor()
        try:
            fecha_str = str(fecha)
            logger.info("Ejecutando SP_USER_MODIFICATION")
            cursor.callproc('SP_USER_MODIFICATION', [fecha_str])
            connection.commit()
        except Exception as e:
            connection.rollback()
            raise Exception(f"Error executing SP_USER_MODIFICATION: {str(e)}")

def execute_sp_log_usuarios(fecha):
    with get_oracle_connection() as connection:
        cursor = connection.cursor()
        try:
            fecha_str = str(fecha)
            logger.info("Ejecutando SP_LOG_USR")
            out_resultado = cursor.var(oracledb.STRING)
            cursor.callproc('SP_LOG_USR', [fecha_str, out_resultado])
            logger.info(f"Resultado de SP_LOG_USR: {out_resultado.getvalue()}")
            connection.commit()
        except Exception as e:
            connection.rollback()
            raise Exception(f"Error executing SP_LOG_USR: {str(e)}")


def execute_sp_user_auth_day(fecha):
    with get_oracle_connection() as connection:
        cursor = connection.cursor()
        try:
            fecha_str = str(fecha)
            logger.info("Ejecutando SP_USER_AUTH_DAY")
            #out_resultado = cursor.var(oracledb.STRING)
            cursor.callproc('SP_USER_AUTH_DAY', [fecha_str])
            #logger.info(f"Resultado de SP_ID_LOG_USR: {out_resultado.getvalue()}")
            connection.commit()
        except Exception as e:
            connection.rollback()
            raise Exception(f"Error executing SP_USER_AUTH_DAY: {str(e)}")


def execute_sp_previo_log_transacciones(fecha):
    with get_oracle_connection() as connection:
        cursor = connection.cursor()
        try:
            fecha_str = str(fecha)
            logger.info("Ejecutando SP_PRE_LOG_TRX")
            out_resultado=cursor.var(oracledb.STRING)
            cursor.callproc('SP_PRE_LOG_TRX', [fecha_str,out_resultado])
            logger.info(f"Resultado de SP_PRE_LOG_TRX: {out_resultado.getvalue()}") 
            connection.commit()
        except Exception as e:
            connection.rollback()
            raise Exception(f"Error executing SP_PRE_LOG_TRX: {str(e)}")


def execute_sp_log_transacciones(fecha):
    with get_oracle_connection() as connection:
        cursor = connection.cursor()
        try:
            fecha_str = str(fecha)
            logger.info("Ejecutando SP_LOG_TRX")
            cursor.callproc('SP_LOG_TRX', [fecha_str])
            logger.info("Finalizado SP_LOG_TRX")
            connection.commit()
        except Exception as e:
            connection.rollback()
            raise Exception(f"Error executing SP_LOG_TRX: {str(e)}")


def execute_sp_usr_balances(fecha):
    with get_oracle_connection() as connection:
        cursor = connection.cursor()
        try:
            fecha_str = str(fecha)
            print(fecha_str)
            logger.info("Ejecutando SP_PRE_BAL")
            cursor.callproc('SP_PRE_BAL', [fecha_str])
            connection.commit()
        except Exception as e:
            connection.rollback()
            raise Exception(f"Error executing SP_PRE_BAL: {str(e)}")
