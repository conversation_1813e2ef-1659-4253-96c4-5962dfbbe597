import pandas as pd
from db_connections import get_mysql_connection
from config import OUTPUT_DIR, FILE_NAME
import os

def extract_user_modifications(fecha):
    query = """
    SELECT 
        USER_ID,
        ACCOUNT_ID,
        CREATED_AT,
        ATTR7_OLD,
        ATTR8_OLD,
        CATEGORY_OLD,
        ISSUER_OLD,
        GRADE_OLD
    FROM app_bim_prod_1.user_account_history
    WHERE DATE(CREATED_AT) >= %s
    """
    
    try:
        connection = get_mysql_connection()
        df = pd.read_sql(query, connection, params=[fecha])
        os.makedirs(OUTPUT_DIR, exist_ok=True)
        output_path = os.path.join(OUTPUT_DIR, FILE_NAME)
        
        df.to_csv(output_path, index=False)
        return output_path
        
    except Exception as e:
        raise Exception(f"Error extracting data from MySQL: {str(e)}")
    finally:
        connection.close()
