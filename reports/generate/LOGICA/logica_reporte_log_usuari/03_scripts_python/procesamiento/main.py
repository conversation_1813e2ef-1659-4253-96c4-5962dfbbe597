from mysql_extract import extract_user_modifications
from oracle_load import load_user_data
from oracle_procedures import *
import logging
import sys
import threading
from datetime import datetime, timedelta

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

execution_log_file = "execution_status.log"

def log_execution_status(part, status):
    with open(execution_log_file, "a") as log_file:
        log_file.write(f"{part}: {status}\n")
    logger.info(f"{part}: {status}")

input_date = sys.argv[1]
data_day = datetime.strptime(input_date, '%Y/%m/%d') + timedelta(days=0)
data_day = data_day.strftime('%Y-%m-%d')

process_part = sys.argv[2] if len(sys.argv) > 2 else None

def execute_sp_async(func, *args):
    thread = threading.Thread(target=func, args=args)
    thread.start()
    return thread


def main():
    try:
        with open(execution_log_file, "w") as log_file:
            log_file.write(f"Execution started at: {datetime.now()}\n")

        if not process_part or process_part == "POBLAR-NULL":
            #Bloque 1: Ejecutar procedimientos en paralelo (SP_FIELD7, SP_ATTR7_NULL, SP_ATTR8_NULL)
            logger.info("Executing Oracle procedures in parallel (SP_FIELD7, SP_ATTR7_NULL, SP_ATTR8_NULL)")
            try:
                thread_sp_field7 = execute_sp_async(execute_sp_field7, data_day)
                thread_sp_attr7 = execute_sp_async(execute_sp_attr7, data_day)
                thread_sp_attr8 = execute_sp_async(execute_sp_attr8, data_day)
            
                #Ejecucion en paralelo
                thread_sp_field7.join()
                thread_sp_attr7.join()
                thread_sp_attr8.join()
            
                logger.info("Parallel Oracle procedures completed")
                logger.info("Executing Oracle procedures (SP_PRE_LOG_USR)")
                execute_sp_pre_log_usuarios(data_day)
                logger.info("SP_PRE_LOG_USR: Oracle procedures completed")
                log_execution_status("POBLAR-NULL", "OK")
            except Exception as e:
                log_execution_status("POBLAR-NULL", "PENDIENTE")
                logger.error(f"Error executing Parte 1: {str(e)}")

        if not process_part or process_part == "USER-MODIFY":
            #Bloque 2: Ejecución de carga de datos (MySQL -> Oracle)
            logger.info("Starting data extraction from MySQL")
            try:
                output_path = extract_user_modifications(data_day)
                logger.info(f"Loading data to Oracle, File: {output_path}")
                load_user_data(output_path)

                log_execution_status("USER-MODIFY", "OK")
            except Exception as e:
                log_execution_status("USER-MODIFY", "PENDIENTE")
                logger.error(f"Error executing Parte 2: {str(e)}")

        if not process_part or process_part == "BALANCE":
            #Bloque 3: Ejecutar SP para balances
            logger.info("Executing Oracle procedure SP_PRE_BAL")
            try:
                execute_sp_usr_balances(data_day)

                log_execution_status("BALANCE", "OK")
            except Exception as e:
                log_execution_status("BALANCE", "PENDIENTE")
                logger.error(f"Error executing Parte 3: {str(e)}")

        if not process_part or process_part == "LOG-TRX":
            #Bloque 4: Ejecutar transacciones
            logger.info("Executing Oracle procedures (SP_PRE_LOG_TRX, SP_LOG_TRX)")
            try:
                #execute_sp_pre_log_usuarios(data_day)
                execute_sp_previo_log_transacciones(data_day)
                execute_sp_log_transacciones(data_day)

                log_execution_status("LOG-TRX", "OK")
            except Exception as e:
                log_execution_status("LOG-TRX", "PENDIENTE")
                logger.error(f"Error executing Parte 4: {str(e)}")

        if not process_part or process_part == "PRE-LOG-USR":
            #Bloque 5: Ejecutar los SPs con dependencias (Pre y Log)
            logger.info("Executing Oracle procedures (SP_PRE_LOG_USR, SP_USER_MODIFICATION, SP_USER_AUTH_DAY)")
            try:
                #execute_sp_pre_log_usuarios(data_day)
                execute_sp_user_modification(data_day)
                execute_sp_user_auth_day(data_day)
                #logger.info("Executing Oracle procedures (SP_LOG_USR)")
                #execute_sp_log_usuarios(data_day)

                log_execution_status("PRE-LOG-USR", "OK")
            except Exception as e:
                log_execution_status("PRE-LOG-USR", "PENDIENTE")
                logger.error(f"Error executing Parte 5: {str(e)}")
        
        if not process_part or process_part == "LOG-USR":
            #Bloque 5: Ejecutar los SPs con dependencias (Solo Log)
            logger.info("Executing Oracle procedures (SP_LOG_USR)")
            try:
                execute_sp_log_usuarios(data_day)
                log_execution_status("LOG-USR", "OK")
            except Exception as e:
                log_execution_status("LOG-USR", "PENDIENTE")
                logger.error(f"Error executing Parte 5: {str(e)}")

        logger.info("Process completed successfull")

    except Exception as e:
        logger.error(f"Error in main process: {str(e)}")
        with open(execution_log_file, "a") as log_file:
            log_file.write(f"Execution failed at: {datetime.now()} - Error: {str(e)}\n")
        raise

if __name__ == "__main__":
    main()
