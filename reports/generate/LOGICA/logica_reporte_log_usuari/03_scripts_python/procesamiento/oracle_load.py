import pandas as pd
from db_connections import get_oracle_connection
from config import OUTPUT_DIR, FILE_NAME
import os
def load_user_data(file_path):
    cursor = None
    connection = None
    try:
        df = pd.read_csv(file_path)
        
        columns_needed = ['USER_ID', 'ACCOUNT_ID', 'CREATED_AT', 'ATTR7_OLD', 'ATTR8_OLD', 'CATEGORY_OLD', 'ISSUER_OLD', 'GRADE_OLD']
        df = df.dropna(subset=columns_needed)

        if df.empty:
            print("El archivo no tiene registros válidos después de limpiar los valores nulos.")
            return

        df['CREATED_AT'] = pd.to_datetime(df['CREATED_AT']).dt.strftime('%Y-%m-%d %H:%M:%S')
        
        df = df.dropna(subset=['CREATED_AT'])

        if df.empty:
            print("El archivo no tiene registros válidos después de limpiar las fechas inválidas.")
            return

        print(df) 

        connection = get_oracle_connection()
        cursor = connection.cursor()
        data = [tuple(x) for x in df.to_numpy()]
        cursor.executemany(
            """
            INSERT INTO USR_DATALAKE.USER_ACCOUNT_HISTORY 
            (USER_ID, ACCOUNT_ID, CREATED_AT, ATTR7_OLD, ATTR8_OLD, 
             CATEGORY_OLD, ISSUER_OLD, GRADE_OLD)
            VALUES (:1, :2, TO_DATE(:3, 'YYYY-MM-DD HH24:MI:SS'), :4, :5, :6, :7, :8)
            """,
            data
        )
        
        connection.commit()
        print(f"{len(data)} registros insertados exitosamente.")
        
    except Exception as e:
        if connection:
            connection.rollback()
        raise Exception(f"Error loading data to Oracle: {str(e)}")
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()
