# src/main.py

import sys
from infrastructure.adapters.oracle_adapter import OracleAdapter
from core.use_cases.export_sql_to_csv import ExportSqlToCsv
from datetime import datetime, timedelta
from config import apps_with_date, emisores

def main():
    # Obtener el parámetro de la línea de comandos
    if len(sys.argv) < 2:
        print("Debe especificar un parámetro, como 'UNIQUE' o 'MOVISTAR'.")
        sys.exit(1)

    param = sys.argv[1].upper()  # Convertimos el parámetro a mayúsculas para asegurar consistencia
    #emisor = sys.argv[2].upper()
    if len(sys.argv) < 3:
        print("Debe especificar una fecha, en el formato: 'YYYY/MM/DD'")
        sys.exit(1)
    fecha = sys.argv[2].upper()
    fecha = datetime.strptime(fecha, "%Y/%m/%d")
    fecha_minus_one = fecha + timedelta(days=1)
    current_time = fecha_minus_one.strftime("%Y%m%d")
    current_month = fecha_minus_one.strftime("%Y%m")
    current_time_complete = fecha_minus_one.strftime("%Y%m%d%H%M%S")
    
    # Instanciar el adaptador de Oracle y el caso de uso de exportación a CSV
    oracle_adapter = OracleAdapter()
    # oracle_adapter = ''
    year = fecha_minus_one.strftime("%Y")
    month = fecha_minus_one.strftime("%m")
    day = fecha_minus_one.strftime("%d")
    s3_key = f"{year}-{month}-{day}/{param}/"
    export_sql_to_csv = ExportSqlToCsv(oracle_adapter)
    try:
        with open(f"./queries/{param}.sql", "r", encoding="utf-8") as file:
            query = file.read()
    except FileNotFoundError:
        query = ""
    query = query.replace('{fecha}', str(fecha))
    
    # Definir el nombre del archivo según el parámetro
    if param == "LOG-USUARIOS":
        file_name = f"data-{current_time}.csv"
        export_sql_to_csv.execute(query, file_name, "PDP_INTERNO/" + s3_key, param)
    else:
        print("Parámetro no reconocido.")
        sys.exit(1)

if __name__ == "__main__":
    main()
