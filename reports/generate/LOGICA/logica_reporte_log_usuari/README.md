# Documentación ETL Log Usuarios

Este repositorio contiene la documentación del proceso ETL para la generación de logs de usuarios. El proceso extrae datos de MySQL y Oracle, los procesa y genera archivos CSV con información de logs de usuarios por banco/dominio.

## Estructura del Proyecto

```
📁 documentacion_etl_log_usuarios/
├── 01_stored_procedures/
│   ├── sp_pre_log_usr.sql                → prepara datos para log de usuarios
│   ├── sp_user_modification.sql          → procesa modificaciones de usuarios
│   ├── sp_user_auth_day.sql              → procesa autenticaciones por día
│   ├── sp_log_usr.sql                    → genera log final de usuarios
│   ├── sp_pre_log_trx.sql                → prepara datos para log de transacciones
│   └── sp_log_trx.sql                    → genera log final de transacciones
├── 02_queries/
│   ├── log_usuarios.sql                  → consulta tabla LOG_USR
│   └── log_transacciones.sql             → consulta tabla LOG_TRX_FINAL
├── 03_scripts_python/
│   ├── extraccion/
│   │   ├── mysql_extract.py              → extrae datos de MySQL
│   │   ├── oracle_procedures.py          → ejecuta SPs en Oracle
│   │   └── db_connections.py             → gestiona conexiones a bases de datos
│   ├── procesamiento/
│   │   ├── main.py                       → orquesta ejecución de procesos
│   │   └── oracle_load.py                → carga datos en Oracle
│   └── exportacion/
│       ├── exports_csv_main.py           → exporta datos a CSV
│       └── procesar.py                   → procesa logs de usuarios
├── 04_shell_scripts/
│   ├── ejecuta_log_user.sh               → orquesta todo el flujo
│   └── log_user.sh                       → procesa y distribuye logs
└── 05_configuracion/
    ├── config.py                         → configuración general
    └── requirements.txt                  → dependencias
```

## Flujo de Ejecución

1. **ejecuta_log_user.sh**: Script principal que orquesta todo el flujo.
   - Ejecuta `prepare/main.py` con el parámetro "PRE-LOG-USR" para preparar los datos.
   - Ejecuta `prepare/main.py` con el parámetro "LOG-USR" para generar el log de usuarios.
   - Ejecuta `log_user.sh` para procesar y distribuir los logs generados.

2. **log_user.sh**: Procesa y distribuye los logs generados.
   - Ejecuta `exports_csv/main.py` para exportar datos a CSV.
   - Ejecuta `log_usuarios/procesar.py` para procesar los logs de usuarios.

3. **prepare/main.py**: Orquesta la ejecución de procesos.
   - Ejecuta stored procedures en Oracle.
   - Extrae datos de MySQL.
   - Carga datos en Oracle.

4. **log_usuarios/procesar.py**: Procesa los logs de usuarios.
   - Lee el archivo CSV generado por `exports_csv/main.py`.
   - Procesa los datos y genera archivos CSV por banco/dominio.
   - Firma los archivos y los sube a S3.

## Dependencias entre Componentes

1. **Stored Procedures (Oracle)**:
   - `SP_PRE_LOG_USR`: Prepara datos para el log de usuarios.
   - `SP_USER_MODIFICATION`: Procesa modificaciones de usuarios.
   - `SP_USER_AUTH_DAY`: Procesa autenticaciones de usuarios por día.
   - `SP_LOG_USR`: Genera el log final de usuarios.

2. **Queries**:
   - `queries/LOG-USUARIOS.sql`: Consulta datos de la tabla `USR_DATALAKE.LOG_USR`.
   - `queries/LOG-TRANSACCIONES.sql`: Consulta datos de la tabla `USR_DATALAKE.LOG_TRX_FINAL`.

3. **Scripts Python**:
   - `mysql_extract.py`: Extrae datos de la tabla `user_account_history` de MySQL.
   - `oracle_procedures.py`: Ejecuta stored procedures en Oracle.
   - `main.py`: Orquesta la ejecución de procesos.
   - `oracle_load.py`: Carga datos en Oracle.
   - `exports_csv_main.py`: Exporta datos a CSV.
   - `procesar.py`: Procesa logs de usuarios.

## Configuración

La configuración del proyecto se encuentra en el archivo `config.py`. Este archivo contiene:

- Parámetros de conexión a MySQL y Oracle.
- Rutas de salida para los archivos generados.
- Configuración para la firma de archivos y subida a S3.

## Requisitos

Los requisitos del proyecto se encuentran en el archivo `requirements.txt`. Para instalarlos, ejecute:

```bash
pip install -r requirements.txt
```

## Ejecución

Para ejecutar el proceso completo, ejecute:

```bash
./ejecuta_log_user.sh [FECHA]
```

Donde `[FECHA]` es la fecha en formato `YYYY/MM/DD`. Si no se proporciona, se utilizará la fecha de ayer.

## Salidas

El proceso genera los siguientes archivos:

- Archivos CSV con logs de usuarios por banco/dominio.
- Archivos de firma para los archivos CSV.
- Logs de ejecución en la carpeta `logs/`.

Los archivos CSV y sus firmas se suben a S3 en la ruta `{banco}/{fecha}/LOG_USUARIOS/`.
