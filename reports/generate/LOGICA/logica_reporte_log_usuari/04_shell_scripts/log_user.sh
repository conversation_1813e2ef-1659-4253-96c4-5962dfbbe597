#!/bin/bash

# Variables de entorno
export PRIVATE_KEY_PATH="/home/<USER>/generate/FileSigner/private_key.key"
export PRIVATE_CRT_PATH="/home/<USER>/generate/FileSigner/private_key.crt"
export OUTPUT_ROUTE="/home/<USER>/output/excel/"
export OUTPUT_ROUTE_CSV="/home/<USER>/output/csv/"

# Definir el array con los valores
valuescsv=("LOG-USUARIOS")
export REPORTS_NO_S3="LOG-USUARIOS"

ROUTE_CSV="/home/<USER>/output/csv"

if [ -z "$1" ]; then
    fecha=$(date -d "yesterday" +"%Y/%m/%d")
else
    fecha=$1
fi

fecha_path=$(date -d "$fecha + 1 day" +"%Y%m%d")


#Nos posicionamos en la raiz
cd /home/<USER>/generate/

for value in "${valuescsv[@]}"; do
    python3 exports_csv/main.py "$value" "$fecha"> "logs/csv/${value}.log" 2>&1 &
done
wait

cd /home/<USER>/generate/log_usuarios/
python3 procesar.py "$fecha" > "/home/<USER>/generate/logs/log_usuarios/LOG-USUARIOS.log" 2>&1 &
wait
