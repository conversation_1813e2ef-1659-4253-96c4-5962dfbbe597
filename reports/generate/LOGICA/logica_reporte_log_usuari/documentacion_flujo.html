<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Documentación del Flujo ETL de Logs de Usuarios</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            color: #333;
        }
        h1, h2, h3, h4 {
            color: #2c3e50;
        }
        h1 {
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        h2 {
            border-bottom: 1px solid #bdc3c7;
            padding-bottom: 5px;
            margin-top: 30px;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 20px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .metadata {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-top: 30px;
        }
        .flow-step {
            margin-bottom: 20px;
            padding-left: 20px;
            border-left: 3px solid #3498db;
        }
        .flow-step h3 {
            margin-bottom: 5px;
            color: #3498db;
        }
        code {
            background-color: #f8f9fa;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: Consolas, Monaco, 'Andale Mono', monospace;
        }
        .diagram {
            margin: 20px 0;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
            border: 1px solid #ddd;
        }
    </style>
</head>
<body>
    <h1>Documentación del Flujo ETL de Logs de Usuarios</h1>
    
    <div class="metadata">
        <h2>Metadatos del Documento</h2>
        <table>
            <tr>
                <th>Atributo</th>
                <th>Valor</th>
            </tr>
            <tr>
                <td>Autor</td>
                <td>Giancarlos Cardenas Galarza</td>
            </tr>
            <tr>
                <td>Fecha de Creación</td>
                <td>5 de Mayo de 2024</td>
            </tr>
            <tr>
                <td>Versión del Documento</td>
                <td>1.0</td>
            </tr>
            <tr>
                <td>Última Actualización</td>
                <td>5 de Mayo de 2024</td>
            </tr>
            <tr>
                <td>Propósito</td>
                <td>Documentar el flujo ETL para la generación de logs de usuarios</td>
            </tr>
        </table>
    </div>

    <h2>Descripción General del Flujo</h2>
    <p>
        Este proceso ETL (Extracción, Transformación y Carga) genera logs de usuarios y transacciones a partir de datos extraídos de MySQL y Oracle. 
        El proceso está orquestado por scripts de shell que ejecutan procedimientos almacenados y scripts Python para procesar y exportar los datos.
        El resultado final son archivos CSV con información de logs de usuarios por banco/dominio que se firman y suben a S3.
    </p>

    <div class="diagram">
        <h3>Diagrama de Flujo Simplificado</h3>
        <pre>
ejecuta_log_user.sh
    ├── prepare/main.py (PRE-LOG-USR)
    │   ├── SP_PRE_LOG_USR
    │   ├── mysql_extract.py
    │   ├── oracle_load.py
    │   ├── SP_USER_MODIFICATION
    │   └── SP_USER_AUTH_DAY
    ├── prepare/main.py (LOG-USR)
    │   └── SP_LOG_USR
    └── log_user.sh
        ├── exports_csv/main.py
        │   └── LOG-USUARIOS.sql
        └── log_usuarios/procesar.py
            ├── Procesamiento de datos
            ├── Firma de archivos
            └── Subida a S3
        </pre>
    </div>

    <h2>Flujo Detallado de Ejecución</h2>
    
    <div class="flow-step">
        <h3>Paso 1: Inicio del Proceso (ejecuta_log_user.sh)</h3>
        <p>
            El script <code>ejecuta_log_user.sh</code> inicia todo el proceso. Recibe como parámetro opcional una fecha (por defecto usa el día anterior).
            Este script implementa un mecanismo de reintentos y control de tiempo máximo de ejecución para garantizar la robustez del proceso.
        </p>
    </div>

    <div class="flow-step">
        <h3>Paso 2: Preparación de Datos (PRE-LOG-USR)</h3>
        <p>
            Se ejecuta <code>prepare/main.py</code> con el parámetro "PRE-LOG-USR" que realiza las siguientes acciones:
        </p>
        <ul>
            <li>Ejecuta el stored procedure <code>SP_PRE_LOG_USR</code> que crea una tabla temporal <code>USER_DATA_TRX</code> con datos de usuarios.</li>
            <li>Extrae datos de modificaciones de usuarios de MySQL mediante <code>mysql_extract.py</code>.</li>
            <li>Carga estos datos en Oracle mediante <code>oracle_load.py</code>.</li>
            <li>Ejecuta el stored procedure <code>SP_USER_MODIFICATION</code> que procesa las modificaciones de usuarios.</li>
            <li>Ejecuta el stored procedure <code>SP_USER_AUTH_DAY</code> que procesa las autenticaciones de usuarios por día.</li>
        </ul>
    </div>

    <div class="flow-step">
        <h3>Paso 3: Generación de Logs de Usuarios (LOG-USR)</h3>
        <p>
            Se ejecuta <code>prepare/main.py</code> con el parámetro "LOG-USR" que realiza las siguientes acciones:
        </p>
        <ul>
            <li>Ejecuta el stored procedure <code>SP_LOG_USR</code> que genera el log final de usuarios en la tabla <code>USR_DATALAKE.LOG_USR</code>.</li>
            <li>Este procedimiento combina los datos preparados en los pasos anteriores y genera un registro consolidado de actividades de usuarios.</li>
        </ul>
    </div>

    <div class="flow-step">
        <h3>Paso 4: Exportación y Procesamiento (log_user.sh)</h3>
        <p>
            Se ejecuta <code>log_user.sh</code> que realiza las siguientes acciones:
        </p>
        <ul>
            <li>Ejecuta <code>exports_csv/main.py</code> que exporta los datos de la tabla <code>USR_DATALAKE.LOG_USR</code> a un archivo CSV utilizando la consulta <code>LOG-USUARIOS.sql</code>.</li>
            <li>Ejecuta <code>log_usuarios/procesar.py</code> que procesa el archivo CSV generado, lo transforma según los tipos de transacción definidos en <code>tipos_transaccion_map.py</code>.</li>
            <li>Genera archivos CSV separados por banco/dominio.</li>
            <li>Firma los archivos generados utilizando una clave privada.</li>
            <li>Sube los archivos firmados a S3 en la ruta correspondiente.</li>
        </ul>
    </div>

    <h2>Diccionario de Datos</h2>
    
    <h3>Tabla: USR_DATALAKE.LOG_USR</h3>
    <table>
        <tr>
            <th>Campo</th>
            <th>Tipo de Dato</th>
            <th>Descripción</th>
            <th>Reglas de Validación</th>
        </tr>
        <tr>
            <td>USERHISTID</td>
            <td>VARCHAR2(50)</td>
            <td>Identificador único del historial de usuario</td>
            <td>No nulo</td>
        </tr>
        <tr>
            <td>USERID</td>
            <td>VARCHAR2(50)</td>
            <td>Identificador del usuario</td>
            <td>No nulo</td>
        </tr>
        <tr>
            <td>USERIDOLD</td>
            <td>VARCHAR2(50)</td>
            <td>Identificador anterior del usuario (en caso de cambio)</td>
            <td>Puede ser nulo</td>
        </tr>
        <tr>
            <td>ACCOUNTID</td>
            <td>VARCHAR2(50)</td>
            <td>Identificador de la cuenta</td>
            <td>No nulo</td>
        </tr>
        <tr>
            <td>ACCOUNTIDOLD</td>
            <td>VARCHAR2(50)</td>
            <td>Identificador anterior de la cuenta (en caso de cambio)</td>
            <td>Puede ser nulo</td>
        </tr>
        <tr>
            <td>CREATEDON</td>
            <td>DATE</td>
            <td>Fecha y hora de creación del registro</td>
            <td>No nulo, formato YYYY-MM-DD HH24:MI:SS</td>
        </tr>
        <tr>
            <td>BANKDOMAIN</td>
            <td>VARCHAR2(50)</td>
            <td>Dominio del banco</td>
            <td>No nulo</td>
        </tr>
        <tr>
            <td>REQUESTTYPE</td>
            <td>VARCHAR2(100)</td>
            <td>Tipo de solicitud (ej. ActivateUser, AfiliaUser, etc.)</td>
            <td>No nulo</td>
        </tr>
        <tr>
            <td>OLDDATA</td>
            <td>CLOB</td>
            <td>Datos anteriores en formato JSON</td>
            <td>Puede ser nulo</td>
        </tr>
        <tr>
            <td>NEWDATA</td>
            <td>CLOB</td>
            <td>Datos nuevos en formato JSON</td>
            <td>Puede ser nulo</td>
        </tr>
        <tr>
            <td>TIPODOCUMENTO</td>
            <td>VARCHAR2(50)</td>
            <td>Tipo de documento de identidad</td>
            <td>Puede ser nulo</td>
        </tr>
        <tr>
            <td>DOCUMENTO</td>
            <td>VARCHAR2(50)</td>
            <td>Número de documento de identidad</td>
            <td>Puede ser nulo</td>
        </tr>
        <tr>
            <td>MSISDN</td>
            <td>VARCHAR2(50)</td>
            <td>Número de teléfono móvil</td>
            <td>Puede ser nulo</td>
        </tr>
        <tr>
            <td>NOMBRE</td>
            <td>VARCHAR2(100)</td>
            <td>Nombre del usuario</td>
            <td>Puede ser nulo</td>
        </tr>
        <tr>
            <td>APELLIDO</td>
            <td>VARCHAR2(100)</td>
            <td>Apellido del usuario</td>
            <td>Puede ser nulo</td>
        </tr>
        <tr>
            <td>PERFILA</td>
            <td>VARCHAR2(100)</td>
            <td>Perfil actual del usuario</td>
            <td>Puede ser nulo</td>
        </tr>
        <tr>
            <td>PERFILB</td>
            <td>VARCHAR2(100)</td>
            <td>Perfil anterior del usuario (en caso de cambio)</td>
            <td>Puede ser nulo</td>
        </tr>
    </table>

    <h3>Tabla: USR_DATALAKE.LOG_TRX_FINAL</h3>
    <table>
        <tr>
            <th>Campo</th>
            <th>Tipo de Dato</th>
            <th>Descripción</th>
            <th>Reglas de Validación</th>
        </tr>
        <tr>
            <td>TransactionID</td>
            <td>VARCHAR2(50)</td>
            <td>Identificador único de la transacción</td>
            <td>No nulo</td>
        </tr>
        <tr>
            <td>FinancialTransactionID</td>
            <td>VARCHAR2(50)</td>
            <td>Identificador financiero de la transacción</td>
            <td>No nulo</td>
        </tr>
        <tr>
            <td>ExternalTransactionID</td>
            <td>VARCHAR2(100)</td>
            <td>Identificador externo de la transacción</td>
            <td>Puede ser nulo</td>
        </tr>
        <tr>
            <td>DateTime</td>
            <td>VARCHAR2(50)</td>
            <td>Fecha y hora de la transacción</td>
            <td>No nulo, formato YYYY-MM-DD HH24:MI:SS</td>
        </tr>
        <tr>
            <td>TransactionType</td>
            <td>VARCHAR2(50)</td>
            <td>Tipo de transacción (ej. CASH_IN, CASH_OUT, TRANSFER, etc.)</td>
            <td>No nulo</td>
        </tr>
        <tr>
            <td>Amount</td>
            <td>NUMBER</td>
            <td>Monto de la transacción</td>
            <td>No nulo</td>
        </tr>
        <tr>
            <td>Currency</td>
            <td>VARCHAR2(10)</td>
            <td>Moneda de la transacción</td>
            <td>No nulo, valor por defecto: PEN</td>
        </tr>
        <tr>
            <td>TransactionStatus</td>
            <td>VARCHAR2(50)</td>
            <td>Estado de la transacción</td>
            <td>No nulo, valor por defecto: COMMITTED</td>
        </tr>
        <tr>
            <td>Context</td>
            <td>VARCHAR2(200)</td>
            <td>Contexto de la transacción</td>
            <td>Puede ser nulo</td>
        </tr>
        <tr>
            <td>Comment</td>
            <td>VARCHAR2(200)</td>
            <td>Comentario adicional sobre la transacción</td>
            <td>Puede ser nulo</td>
        </tr>
    </table>

    <h2>Notas Adicionales</h2>
    <ul>
        <li>El proceso se ejecuta diariamente para procesar los datos del día anterior.</li>
        <li>Los archivos generados se almacenan en S3 en la ruta <code>{banco}/{fecha}/LOG_USUARIOS/</code>.</li>
        <li>Los archivos se firman digitalmente para garantizar su integridad.</li>
        <li>Se mantiene un historial de logs de ejecución en la carpeta <code>logs/</code>.</li>
    </ul>

    <h2>Dependencias</h2>
    <ul>
        <li>Base de datos MySQL: Contiene la tabla <code>user_account_history</code> con información de cambios en usuarios.</li>
        <li>Base de datos Oracle: Contiene las tablas y stored procedures necesarios para el procesamiento de datos.</li>
        <li>Bibliotecas Python: pandas, oracledb, boto3, cryptography, entre otras (ver <code>requirements.txt</code>).</li>
        <li>Claves de firma: Se requieren claves privadas y certificados para la firma de archivos.</li>
        <li>Acceso a S3: Se requiere acceso para subir los archivos generados.</li>
    </ul>

    <footer>
        <p>2025 Giancarlos Cardenas Galarza.</p>
    </footer>
</body>
</html>
