[general]
output_dir = REPORTE_CONCILIACION_FULLCARGAS
log_dir = logs

[fecha]
# Si rango=true, usar rango_inicio y rango_fin
# Si rango=false, usar dias_atras
rango = true
dias_atras = 1
rango_inicio = 2025-05-06
rango_fin = 2025-05-06

[s3_sources]
# Formato: bucket, prefix, region
# Cada fuente genera automáticamente las siguientes variables en el SQL:
# - {nombre_fuente}_bucket: El bucket de S3
# - {nombre_fuente}_prefix: El prefijo dentro del bucket
# - {nombre_fuente}_region: La región de AWS
# - {nombre_fuente}_path: La ruta completa (s3://{bucket}/{prefix}/{year}/{month}/{day}/*)

# Tabla principal con transacciones exitosas (status = 'SUCCESSFUL')
main = prd-datalake-bronze-zone-637423440311, DBMDWPROD/PRE_CONCILIACION_PROD, us-east-1

# Tabla con información de transacciones reversadas (status = 'REVERSAL')
cashin_prod = prd-datalake-bronze-zone-637423440311, DBMDWPROD/CASHIN_PROD, us-east-1

[queries]
query_list = fullcarga_all
