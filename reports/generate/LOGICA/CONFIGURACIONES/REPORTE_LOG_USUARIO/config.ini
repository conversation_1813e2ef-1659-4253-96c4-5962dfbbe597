[general]
output_dir = REPORTE_LOG_USUARIOS
log_dir = logs

[fecha]
# Si rango=true, usar rango_inicio y rango_fin
# Si rango=false, usar dias_atras
rango = true
dias_atras = 1
rango_inicio = 2025-05-05
rango_fin = 2025-05-05

[s3_sources]
# Formato: bucket, prefix, region
# Cada fuente genera automáticamente las siguientes variables en el SQL:
# - {nombre_fuente}_bucket: El bucket de S3
# - {nombre_fuente}_prefix: El prefijo dentro del bucket
# - {nombre_fuente}_region: La región de AWS
# - {nombre_fuente}_path: La ruta completa

# Tablas de referencia (sin particionamiento por fecha específica)
user_profile = prd-datalake-bronze-zone-************, PDP_PROD10_REPORTING_MOBIQUITY/USER_PROFILE, us-east-1
kyc_details = prd-datalake-bronze-zone-************, PDP_PROD10_REPORTING_MOBIQUITY/KYC_DETAILS, us-east-1
issuer_details = prd-datalake-bronze-zone-************, PDP_PROD10_REPORTING_MOBIQUITY/ISSUER_DETAILS, us-east-1
mtx_categories = prd-datalake-bronze-zone-************, PDP_PROD10_REPORTING_MOBIQUITY/MTX_CATEGORIES, us-east-1
channel_grades = prd-datalake-bronze-zone-************, PDP_PROD10_REPORTING_MOBIQUITY/CHANNEL_GRADES, us-east-1
user_identifier = prd-datalake-bronze-zone-************, PDP_PROD10_REPORTING_MOBIQUITY/USER_IDENTIFIER, us-east-1
mtx_wallet = prd-datalake-bronze-zone-************, PDP_PROD10_REPORTING_MOBIQUITY/MTX_WALLET, us-east-1

# Tablas transaccionales (con particionamiento por fecha)
user_modification_history = prd-datalake-bronze-zone-************, PDP_PROD10_REPORTING_MOBIQUITY/USER_MODIFICATION_HISTORY, us-east-1
user_auth_change_history = prd-datalake-bronze-zone-************, PDP_PROD10_REPORTING_MOBIQUITY/USER_AUTH_CHANGE_HISTORY, us-east-1
user_account_history = prd-datalake-bronze-zone-************, APP_BIM_PROD_1/USER_ACCOUNT_HISTORY, us-east-1

[queries]
query_list = log_usuarios_all

[post_processing]
# Configuración para scripts de post-procesamiento
# Formato: script_path
# script_path: Ruta al script de post-procesamiento relativa a la raíz del proyecto
#
# Variables disponibles:
# - {fecha}: La fecha que se está procesando (YYYY-MM-DD)
# - {output_file}: Ruta completa al archivo Parquet generado
# - {output_dir}: Directorio de salida para el archivo Parquet
#
# Ejemplo: REPORTES/REPORTE_LOG_USUARIO/post_process.py
scripts = REPORTES/REPORTE_LOG_USUARIO/post_process.py
