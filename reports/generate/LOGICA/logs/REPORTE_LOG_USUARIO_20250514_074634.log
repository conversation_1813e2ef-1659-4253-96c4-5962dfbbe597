2025-05-14 07:46:34,083 - INFO - Procesando datos para REPORTE_LOG_USUARIO desde 2025-05-05 hasta 2025-05-05
2025-05-14 07:46:34,083 - INFO - Procesando fecha: 2025-05-05
2025-05-14 07:46:34,109 - INFO - Found credentials from IAM Role: ec-landing-process-01-EC2-Role
2025-05-14 07:46:34,159 - INFO - Ejecutando query: log_usuarios_all
2025-05-14 07:48:38,949 - WARNING - Error al ejecutar la query original: Conversion Error: Malformed JSON at byte 0 of input: unexpected character.  Input: CIERRE POR APP BIM
2025-05-14 07:48:38,949 - INFO - Aplicando solución para manejar JSON como texto (VARCHAR)...
2025-05-14 07:50:50,094 - INFO - Solución para tratar JSON como texto aplicada con éxito. Se encontraron 13001 registros para procesar
2025-05-14 07:50:50,094 - INFO - Eliminando carpeta existente para evitar duplicados: REPORTE_LOG_USUARIOS/2025/05/05
2025-05-14 07:50:50,235 - INFO - Resultados exportados a REPORTE_LOG_USUARIOS/2025/05/05/REPORTE_LOG_USUARIO_2025-05-05_074634.parquet
2025-05-14 07:50:50,259 - INFO - Muestra de datos procesados:
  userHistId   createdOn TipoDocumento Documento       Msisdn  MsisdnB BankDomain        created_by         userId   accountType  ... perfilCuentaB TipoDocumentoA TipoDocumentoB  DocumentoB  NumDocumentoB        requestType                                            oldData                                            newData  userIdOld accountIdOld
0    UM.4416  2025-05-05          None      None  ***********     <NA>       0231  ****************  *************  MOBILE_MONEY  ...          <NA>           None           <NA>        None           <NA>  User Modification  "{\"profileDetails\":[{\"identifierValue\":\"5...  "{\"profileDetails\":[{\"identifierValue\":\"5...       None         None
1    UM.4820  2025-05-05          None      None  ***********     <NA>    BNACION           1840101        1840101  MOBILE_MONEY  ...          <NA>           None           <NA>        None           <NA>  User Modification  "{\"profileDetails\":[{\"identifierValue\":\"5...  "{\"profileDetails\":[{\"identifierValue\":\"5...    1840101      2745666
2    UM.2210  2025-05-05           DNI  ********  ***********     <NA>       0231           2803764        2803764  MOBILE_MONEY  ...          <NA>            DNI           <NA>    ********           <NA>        Delete User  "{\"userStatus\":\"Active\",\"profileDetails\"...  "{\"userStatus\":\"Deleted\",\"profileDetails\...       None         None
3    UM.2211  2025-05-05           DNI  44167572  51979384410     <NA>       0231           3092561        3092561  MOBILE_MONEY  ...          <NA>            DNI           <NA>    44167572           <NA>        Delete User  "{\"userStatus\":\"Active\",\"profileDetails\"...  "{\"userStatus\":\"Deleted\",\"profileDetails\...       None         None
4       UM.5  2025-05-05           DNI  42411990  51924141045     <NA>       0231  ****************  4471644124598  MOBILE_MONEY  ...          <NA>            DNI           <NA>    42411990           <NA>  User Modification  "{\"notificationEndpointRequests\":[{\"notific...  "{\"notificationEndpointRequests\":[{\"notific...       None         None

[5 rows x 34 columns]
2025-05-14 07:50:50,259 - INFO - Ejecutando script de post-procesamiento: REPORTES/REPORTE_LOG_USUARIO/post_process.py 
2025-05-14 07:50:50,614 - ERROR - Error al ejecutar el script de post-procesamiento: REPORTES/REPORTE_LOG_USUARIO/post_process.py
2025-05-14 07:50:50,614 - ERROR - Error: 2025-05-14 07:50:50,584 - ERROR - Uso: python post_process.py YYYY-MM-DD

2025-05-14 07:50:50,614 - INFO - Ejecutando script de post-procesamiento: {fecha} 
2025-05-14 07:50:50,662 - ERROR - Error al ejecutar el script de post-procesamiento: {fecha}
2025-05-14 07:50:50,662 - ERROR - Error: /usr/bin/python3: can't open file '/home/<USER>/aws/app/Gian/Flow_ETL_Landing/duck/LOGICA/{fecha}': [Errno 2] No such file or directory

2025-05-14 07:50:50,730 - INFO - Proceso completado
