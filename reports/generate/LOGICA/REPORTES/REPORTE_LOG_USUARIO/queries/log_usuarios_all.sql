-- Consulta para generar el reporte de logs de usuarios
--
-- Esta consulta combina la lógica de los stored procedures originales:
-- - SP_PRE_LOG_USR
-- - SP_USER_MODIFICATION
-- - SP_USER_AUTH_DAY
-- - SP_LOG_USR
--
-- Adaptada para funcionar con DuckDB y datos de S3
-- Modificada para manejar tipos de datos inconsistentes y simplificar JOINs
--
-- NOTA IMPORTANTE: Para MTX_CATEGORIES se utiliza específicamente la partición de 2024
-- porque contiene el registro con category_code = 'SUBS' (Final User) que no está presente
-- en la partición de 2025. Este registro es esencial para mapear correctamente los perfiles
-- de usuario, ya que la mayoría de los usuarios tienen category_id = 'SUBS'.
--
-- Adem<PERSON>, se han agregado valores predeterminados usando COALESCE para los siguientes campos:
-- - ID.ISSUER_CODE: Se usa '0231' como valor predeterminado cuando es NULL
-- - MC.CATEGORY_NAME: Se usa 'USUARIO FINAL' como valor predeterminado cuando es NULL
-- - DA.GRADE_NAME_OLD: Se usa 'NORMAL' como valor predeterminado cuando es NULL
-- - P.PerfilCuenta y P.PerfilCuentaA: Se usa '0231 NORMAL' como valor predeterminado cuando son NULL
--
-- Estos cambios aseguran que los campos perfilA, PerfilCuenta y PerfilCuentaA siempre tengan valores
-- válidos, incluso cuando hay problemas con los JOINs entre las tablas.

-- Paso 1: Preparar datos de usuarios (equivalente a SP_PRE_LOG_USR)
WITH USER_DATA_TRX AS (
    WITH WALLETS AS (
        SELECT
            MW.USER_ID,
            MW.WALLET_NUMBER,
            MW.ISSUER_ID,
            MW.USER_GRADE,
            ROW_NUMBER() OVER (PARTITION BY MW.USER_ID ORDER BY MW.MODIFIED_ON DESC) AS ORDEN
        FROM read_parquet('s3://{mtx_wallet_bucket}/{mtx_wallet_prefix}/2025/05/06/*.parquet', union_by_name=true) MW
    )
    SELECT
        CASE
            WHEN UP.ATTR7 IS NOT NULL THEN UP.ATTR7
            ELSE UP.USER_ID
        END AS USER_ID,
        UP.USER_ID AS O_USER_ID,
        CASE
            WHEN UP.ATTR7 IS NOT NULL THEN UP.ATTR7
            WHEN LENGTH(REPLACE(UP.USER_ID,'US.',''))>15 THEN SUBSTR(REPLACE(UP.user_id,'US.',''),-15)
            ELSE REPLACE(UP.user_id,'US.','')
        END AS USER_ID_M,
        UK.ID_TYPE,
        UK.ID_VALUE,
        CASE
            WHEN UP.ATTR8 IS NOT NULL THEN UP.ATTR8
            WHEN LENGTH(REPLACE(MW.WALLET_NUMBER,'UA.',''))>15 THEN SUBSTR(REPLACE(MW.WALLET_NUMBER,'UA.',''),-15)
            ELSE REPLACE(MW.WALLET_NUMBER,'UA.','')
        END AS WALLET_NUMBER,
        UP.STATUS,
        CASE
            WHEN UP.MSISDN IN ('***********','***********','***********','***********') THEN REPLACE(REPLACE(ID.ISSUER_CODE,'0144',''),'0231','') || ' MERCHANT GENERAL ACCOUNT PROFILE'
            WHEN UP.MSISDN IN ('***********','***********','***********','***********','***********','***********','***********','***********','***********','***********','***********','***********') THEN UPPER(CG.GRADE_NAME) || ' PROFILE'
            ELSE REPLACE(REPLACE(ID.ISSUER_CODE,'0144',''),'0231','') || ' ' || UPPER(CG.GRADE_NAME)
        END AS GRADE_NAME,
        UP.MSISDN,
        UP.CREATED_ON,
        UP.CREATED_BY,
        UP.REMARKS,
        UP.MODIFIED_ON AS STATUS_CHANGE_ON,
        REPLACE(COALESCE(ID.ISSUER_CODE, '0231'),'0144','') AS ISSUER_CODE,
        UP.FIRST_NAME,
        UP.LAST_NAME,
        MC.CATEGORY_NAME,
        CASE
            WHEN MC.CATEGORY_NAME = 'Final User' THEN COALESCE(ID.ISSUER_CODE, '0231') || ' ' || 'USUARIO FINAL'
            WHEN MC.CATEGORY_NAME = 'BIMER User' THEN COALESCE(ID.ISSUER_CODE, '0231') || ' ' || 'BIMER'
            WHEN MC.CATEGORY_NAME = 'Virtual Agent' THEN COALESCE(ID.ISSUER_CODE, '0231') || ' ' || 'AGENTE VIRTUAL'
            WHEN MC.CATEGORY_NAME = 'Agent' THEN COALESCE(ID.ISSUER_CODE, '0231') || ' ' || 'AGENTE'
            WHEN MC.CATEGORY_NAME = 'Agencia' THEN COALESCE(ID.ISSUER_CODE, '0231') || ' ' || 'AGENCIA'
            WHEN MC.CATEGORY_NAME = 'Super Agent' THEN COALESCE(ID.ISSUER_CODE, '0231') || ' ' || 'SUPER AGENTE'
            WHEN UPPER(MC.CATEGORY_NAME) = 'REMESAS WU' THEN COALESCE(ID.ISSUER_CODE, '0231') || ' ' || 'COMERCIO'
            WHEN MC.CATEGORY_NAME = 'Dispersor' THEN COALESCE(ID.ISSUER_CODE, '0231') || ' ' || 'SUPER AGENTE'
            WHEN MC.CATEGORY_NAME = 'Biller' THEN
                CASE
                    WHEN UP.MSISDN IN ('***********','***********','***********','***********') THEN COALESCE(ID.ISSUER_CODE, '0231') || ' ' || 'COMERCIO'
                    ELSE
                        CASE
                            WHEN UP.MSISDN = '***********' THEN 'LINDLEY PROVEEDOR DE SERVICIOS'
                            WHEN UP.MSISDN = '***********' THEN 'BACKUS PROVEEDOR DE SERVICIOS'
                            WHEN UP.MSISDN IN ('***********','***********') THEN 'BITEL PROVEEDOR DE SERVICIOS'
                            WHEN UP.MSISDN IN ('***********','***********') THEN 'CLARO PROVEEDOR DE SERVICIOS'
                            WHEN UP.MSISDN = '***********' THEN 'ENTEL DE SERVICIOS'
                            WHEN UP.MSISDN = '***********' THEN 'GLORIA PROVEEDOR DE SERVICIOS'
                            WHEN UP.MSISDN = '***********' THEN 'MOVISTAR PROVEEDOR DE SERVICIOS'
                            WHEN UP.MSISDN = '***********' THEN 'UNIQUE PROVEEDOR DE SERVICIOS'
                            WHEN UP.MSISDN = '***********' THEN 'DIGIFLOW PROVEEDOR DE SERVICIOS'
                            WHEN UP.MSISDN = '***********' THEN 'SUNAT PROVEEDOR DE SERVICIOS'
                        END
                END
            ELSE COALESCE(ID.ISSUER_CODE, '0231') || ' ' || MC.CATEGORY_NAME
        END AS PROFILE,
        CASE
            WHEN MC.CATEGORY_NAME = 'Final User' THEN 'USUARIO FINAL'
            WHEN MC.CATEGORY_NAME = 'BIMER User' THEN 'BIMER'
            WHEN MC.CATEGORY_NAME = 'Virtual Agent' THEN 'AGENTE VIRTUAL'
            WHEN MC.CATEGORY_NAME = 'Agent' THEN 'AGENTE'
            WHEN MC.CATEGORY_NAME = 'Agencia' THEN 'AGENCIA'
            WHEN MC.CATEGORY_NAME = 'Super Agent' THEN 'SUPER AGENTE'
            WHEN UPPER(MC.CATEGORY_NAME) = 'REMESAS WU' THEN 'COMERCIO'
            WHEN MC.CATEGORY_NAME = 'Dispersor' THEN 'SUPER AGENTE'
            WHEN MC.CATEGORY_NAME = 'Biller' THEN
                CASE
                    WHEN UP.MSISDN IN ('***********','***********','***********','***********') THEN 'COMERCIO'
                    ELSE
                        CASE
                            WHEN UP.MSISDN = '***********' THEN 'LINDLEY PROVEEDOR DE SERVICIOS'
                            WHEN UP.MSISDN = '***********' THEN 'BACKUS PROVEEDOR DE SERVICIOS'
                            WHEN UP.MSISDN IN ('***********','***********') THEN 'BITEL PROVEEDOR DE SERVICIOS'
                            WHEN UP.MSISDN IN ('***********','***********') THEN 'CLARO PROVEEDOR DE SERVICIOS'
                            WHEN UP.MSISDN = '***********' THEN 'ENTEL DE SERVICIOS'
                            WHEN UP.MSISDN = '***********' THEN 'GLORIA PROVEEDOR DE SERVICIOS'
                            WHEN UP.MSISDN = '***********' THEN 'MOVISTAR PROVEEDOR DE SERVICIOS'
                            WHEN UP.MSISDN = '***********' THEN 'UNIQUE PROVEEDOR DE SERVICIOS'
                            WHEN UP.MSISDN = '***********' THEN 'DIGIFLOW PROVEEDOR DE SERVICIOS'
                            WHEN UP.MSISDN = '***********' THEN 'SUNAT PROVEEDOR DE SERVICIOS'
                            WHEN UP.MSISDN = '51946594070' THEN 'SENTINEL PROVEEDOR DE SERVICIOS'
                        END
                END
            ELSE COALESCE(MC.CATEGORY_NAME, 'USUARIO FINAL')
        END AS PROFILE_TRX,
        UP.ATTR1,
        UP.PREFERRED_LANG,
        UP.USER_CODE,
        UP.LOGIN_ID,
        UP.WORKSPACE_ID
    FROM (
        -- Optimización: Filtrar USER_PROFILE para obtener solo registros únicos por usuario con la fecha más reciente
        SELECT * FROM (
            SELECT
                *,
                ROW_NUMBER() OVER (PARTITION BY USER_ID ORDER BY data_lake_partition_date DESC) AS rn
            FROM read_parquet('s3://{user_profile_bucket}/{user_profile_prefix}/2025/*/*/*.parquet', union_by_name=true)
        ) WHERE rn = 1
    ) UP
    LEFT JOIN read_parquet('s3://{kyc_details_bucket}/{kyc_details_prefix}/2025/05/*/*.parquet', union_by_name=true) UK ON UP.KYC_ID = UK.KYC_ID
    LEFT JOIN WALLETS MW ON UP.USER_ID = MW.USER_ID AND ORDEN=1
    LEFT JOIN read_parquet('s3://{issuer_details_bucket}/{issuer_details_prefix}/2025/05/06/*.parquet', union_by_name=true) ID ON MW.ISSUER_ID = ID.ISSUER_ID
    -- Utilizamos específicamente la partición de 2024 para MTX_CATEGORIES porque contiene el registro 'SUBS'
    LEFT JOIN read_parquet('s3://{mtx_categories_bucket}/{mtx_categories_prefix}/2024/*/*/*.parquet', union_by_name=true) MC ON UP.CATEGORY_ID = MC.CATEGORY_CODE
    LEFT JOIN read_parquet('s3://{channel_grades_bucket}/{channel_grades_prefix}/2025/*/*/*.parquet', union_by_name=true) CG ON MW.USER_GRADE = CG.GRADE_CODE
    WHERE
        -- Convertir fechas de manera segura
        CASE
            WHEN TRY_CAST(UP.CREATED_ON AS DATE) IS NOT NULL THEN TRY_CAST(UP.CREATED_ON AS DATE) <= '{fecha_inicio}'::DATE
            ELSE FALSE
        END
),

-- Paso 2: Obtener modificaciones de usuarios (equivalente a SP_USER_MODIFICATION)
USER_MODIFICATION_DAY AS (
    SELECT
        UMH.REQUEST_TYPE,
        -- Validar que OLD_DATA sea un JSON válido o NULL
        CASE
            WHEN TRY_CAST(UMH.OLD_DATA AS JSON) IS NOT NULL THEN UMH.OLD_DATA
            ELSE NULL
        END AS OLD_DATA,
        -- Validar que NEW_DATA sea un JSON válido o NULL
        CASE
            WHEN TRY_CAST(UMH.NEW_DATA AS JSON) IS NOT NULL THEN UMH.NEW_DATA
            ELSE NULL
        END AS NEW_DATA,
        -- Extraer RAZON de manera segura
        CASE
            -- Solo intentar extraer JSON si el campo es un JSON válido
            WHEN TRY_CAST(UMH.NEW_DATA AS JSON) IS NOT NULL AND TRY_CAST(JSON_EXTRACT(UMH.NEW_DATA, '$.profileDetails.remarks') AS VARCHAR) IS NOT NULL
            THEN JSON_EXTRACT(UMH.NEW_DATA, '$.profileDetails.remarks')
            WHEN TRY_CAST(UMH.NEW_DATA AS JSON) IS NOT NULL AND TRY_CAST(JSON_EXTRACT(UMH.NEW_DATA, '$.profileDetails[0].remarks') AS VARCHAR) IS NOT NULL
            THEN JSON_EXTRACT(UMH.NEW_DATA, '$.profileDetails[0].remarks')
            -- Si no es JSON válido pero contiene "CIERRE POR APP BIM", usar ese texto como razón
            WHEN UMH.NEW_DATA LIKE '%CIERRE POR APP BIM%' THEN 'CIERRE POR APP BIM'
            ELSE NULL
        END AS RAZON,
        UMH.USER_ID,
        UMH.CREATED_BY,
        UMH.CREATED_ON
    FROM read_parquet('s3://{user_modification_history_bucket}/{user_modification_history_prefix}/{year}/{month}/{day}/*.parquet', union_by_name=true) UMH
    WHERE
        -- Convertir fechas de manera segura
        CASE
            WHEN TRY_CAST(UMH.CREATED_ON AS DATE) IS NOT NULL THEN TRY_CAST(UMH.CREATED_ON AS DATE) = '{fecha_inicio}'::DATE
            ELSE FALSE
        END
),

-- Paso 3: Obtener cambios de autenticación (equivalente a SP_USER_AUTH_DAY)
USER_AUTH_CHANGE_HISTORY AS (
    SELECT
        UACH.MODIFIED_ON,
        UACH.MODIFICATION_TYPE,
        UACH.MODIFIED_BY,
        UACH.AUTHENTICATION_ID
    FROM read_parquet('s3://{user_auth_change_history_bucket}/{user_auth_change_history_prefix}/{year}/{month}/{day}/*.parquet', union_by_name=true) UACH
    WHERE
        -- Convertir fechas de manera segura
        CASE
            WHEN TRY_CAST(UACH.MODIFIED_ON AS DATE) IS NOT NULL THEN TRY_CAST(UACH.MODIFIED_ON AS DATE) = '{fecha_inicio}'::DATE
            ELSE FALSE
        END
        AND COALESCE(UACH.AUTHENTICATION_TYPE, '') = 'PIN'
),

-- Paso 4: Obtener historial de cuentas de usuario
WALLET_OLD AS (
    SELECT
        UD.USER_ID,
        UD.ATTR7_OLD,
        UD.ATTR8_OLD,
        UD.CREATED_AT,
        ID.ISSUER_CODE,
        UD.GRADE_OLD AS GRADE_NAME_OLD,
        ROW_NUMBER() OVER(PARTITION BY UD.USER_ID ORDER BY UD.CREATED_AT DESC) AS ORDEN
    FROM read_parquet('s3://{user_account_history_bucket}/{user_account_history_prefix}/{year}/{month}/{day}/*.parquet', union_by_name=true) UD
    LEFT JOIN read_parquet('s3://{issuer_details_bucket}/{issuer_details_prefix}/2025/*/*/*.parquet', union_by_name=true) ID ON UD.ISSUER_OLD = ID.ISSUER_ID
),

-- Paso 5: Procesar datos para el log de usuarios (equivalente a SP_LOG_USR)
PROCESS AS (
    -- Modificaciones de usuarios
    SELECT
        'UM.' || ROW_NUMBER() OVER() AS userHistId,
        UMH.CREATED_ON AS createdOn,
        UD.ID_TYPE AS TipoDocumento,
        UD.ID_VALUE AS Documento,
        UD.MSISDN AS Msisdn,
        NULL AS MsisdnB,
        UD.ISSUER_CODE AS BankDomain,
        CASE
            WHEN UMH.REQUEST_TYPE IN ('Resume User','Unlock Wallet') THEN 'ID:awspdp/ADMIN'
            ELSE REPLACE(REPLACE(UMH.CREATED_BY,'US.',''),'SELF','ID:unknown/SERVICE')
        END AS created_by,
        UD.USER_ID_M AS userId,
        'MOBILE_MONEY' AS accountType,
        UD.WALLET_NUMBER AS accountId,
        UD.FIRST_NAME AS Nombre,
        UD.LAST_NAME AS Apellido,
        NULL AS NNombre,
        NULL AS NApellido,
        REPLACE(REPLACE(UD.PROFILE,'0231',''),'0144','') as perfilA,
        NULL AS perfilB,
        UD.PREFERRED_LANG AS IdiomaA,
        NULL AS IdiomaB,
        UD.ATTR1 AS TelcoA,
        NULL AS TelcoB,
        UMH.RAZON,
        UD.GRADE_NAME as PerfilCuenta,
        UD.GRADE_NAME as PerfilCuentaA,
        NULL AS perfilCuentaB,
        UD.ID_TYPE AS TipoDocumentoA,
        NULL AS TipoDocumentoB,
        UD.ID_VALUE AS DocumentoB,
        NULL AS NumDocumentoB,
        UMH.REQUEST_TYPE AS requestType,
        UMH.OLD_DATA AS oldData,
        UMH.NEW_DATA AS newData,
        UD.O_USER_ID
    FROM USER_MODIFICATION_DAY UMH
    INNER JOIN USER_DATA_TRX UD ON UMH.USER_ID = UD.O_USER_ID

    UNION ALL

    -- Cambios de autenticación
    SELECT
        UACH.AUTHENTICATION_ID AS userHistId,
        UACH.MODIFIED_ON AS createdOn,
        UD.ID_TYPE AS TipoDocumento,
        UD.ID_VALUE AS Documento,
        UD.MSISDN AS Msisdn,
        NULL AS MsisdnB,
        UD.ISSUER_CODE AS BankDomain,
        CASE
            WHEN UACH.MODIFICATION_TYPE = 'RESET_AUTH_VALUE' THEN 'ID:unknown/SERVICE'
            ELSE REPLACE(REPLACE(UACH.MODIFIED_BY,'US.',''),'SELF','ID:unknown/SERVICE')
        END AS created_by,
        UD.USER_ID_M AS userId,
        'MOBILE_MONEY' AS accountType,
        UD.WALLET_NUMBER AS accountId,
        UD.FIRST_NAME AS Nombre,
        UD.LAST_NAME AS Apellido,
        NULL AS NNombre,
        NULL AS NApellido,
        REPLACE(REPLACE(UD.PROFILE,'0231',''),'0144','') as perfilA,
        NULL AS perfilB,
        UD.PREFERRED_LANG AS IdiomaA,
        NULL AS IdiomaB,
        UD.ATTR1 AS TelcoA,
        NULL AS TelcoB,
        NULL AS Razon,
        UD.GRADE_NAME as PerfilCuenta,
        UD.GRADE_NAME as PerfilCuentaA,
        NULL AS perfilCuentaB,
        UD.ID_TYPE AS TipoDocumentoA,
        NULL AS TipoDocumentoB,
        UD.ID_VALUE AS DocumentoB,
        NULL AS NumDocumentoB,
        UACH.MODIFICATION_TYPE AS requestType,
        NULL AS oldData,
        NULL AS newData,
        UD.O_USER_ID
    FROM USER_AUTH_CHANGE_HISTORY UACH
    LEFT JOIN read_parquet('s3://{user_identifier_bucket}/{user_identifier_prefix}/2025/*/*/*.parquet', union_by_name=true) UI ON UACH.AUTHENTICATION_ID = UI.AUTHENTICATION_ID
    LEFT JOIN USER_DATA_TRX UD ON UI.USER_ID = UD.O_USER_ID
    WHERE UD.O_USER_ID IS NOT NULL

    UNION ALL

    -- Activaciones de usuarios
    SELECT
        UD.O_USER_ID AS userHistId,
        UD.CREATED_ON AS createdOn,
        CASE WHEN UD.PROFILE LIKE '%PROVEEDOR%' THEN '' ELSE UD.ID_TYPE END AS TipoDocumento,
        CASE WHEN UD.PROFILE LIKE '%PROVEEDOR%' THEN '' ELSE UD.ID_VALUE END AS Documento,
        UD.MSISDN AS Msisdn,
        NULL AS MsisdnB,
        UD.ISSUER_CODE AS BankDomain,
        REPLACE(REPLACE(UD.CREATED_BY,'US.',''),'SELF','ID:unknown/SERVICE') AS created_by,
        UD.USER_ID_M AS userId,
        'MOBILE_MONEY' AS accountType,
        UD.WALLET_NUMBER AS accountId,
        UD.FIRST_NAME AS Nombre,
        UD.LAST_NAME AS Apellido,
        NULL AS NNombre,
        NULL AS NApellido,
        REPLACE(REPLACE(UD.PROFILE,'0231',''),'0144','') as perfilA,
        NULL AS perfilB,
        UD.PREFERRED_LANG AS IdiomaA,
        NULL AS IdiomaB,
        UD.ATTR1 AS TelcoA,
        NULL AS TelcoB,
        NULL AS Razon,
        CASE WHEN UD.GRADE_NAME LIKE '%GENERAL%' THEN REPLACE(UD.GRADE_NAME,'NORMAL GENERAL','NORMAL') ELSE UD.GRADE_NAME END as PerfilCuenta,
        CASE WHEN UD.GRADE_NAME LIKE '%GENERAL%' THEN REPLACE(UD.GRADE_NAME,'NORMAL GENERAL','NORMAL') ELSE UD.GRADE_NAME END as PerfilCuentaA,
        NULL AS perfilCuentaB,
        UD.ID_TYPE AS TipoDocumentoA,
        NULL AS TipoDocumentoB,
        UD.ID_VALUE AS DocumentoB,
        NULL AS NumDocumentoB,
        'ActivateUser' AS requestType,
        NULL AS oldData,
        NULL AS newData,
        UD.O_USER_ID
    FROM USER_DATA_TRX UD
    WHERE
        -- Convertir fechas de manera segura
        CASE
            WHEN TRY_CAST(UD.CREATED_ON AS DATE) IS NOT NULL THEN TRY_CAST(UD.CREATED_ON AS DATE) = '{fecha_inicio}'::DATE
            ELSE FALSE
        END

    UNION ALL

    -- Cierres de cuentas
    SELECT
        UD.O_USER_ID AS userHistId,
        UD.STATUS_CHANGE_ON AS createdOn,
        UD.ID_TYPE AS TipoDocumento,
        UD.ID_VALUE || 'X' || UD.USER_ID_M AS Documento,
        UD.MSISDN AS Msisdn,
        NULL AS MsisdnB,
        UD.ISSUER_CODE AS BankDomain,
        REPLACE(REPLACE(UD.CREATED_BY,'US.',''),'SELF','ID:unknown/SERVICE') AS created_by,
        UD.USER_ID_M AS userId,
        'MOBILE_MONEY' AS accountType,
        UD.WALLET_NUMBER AS accountId,
        UD.FIRST_NAME AS Nombre,
        UD.LAST_NAME AS Apellido,
        NULL AS NNombre,
        NULL AS NApellido,
        REPLACE(REPLACE(UD.PROFILE,'0231',''),'0144','') as perfilA,
        NULL AS perfilB,
        UD.PREFERRED_LANG AS IdiomaA,
        NULL AS IdiomaB,
        UD.ATTR1 AS TelcoA,
        NULL AS TelcoB,
        -- Manejar el caso especial "CIERRE POR APP BIM"
        CASE
            WHEN UD.REMARKS = 'CIERRE POR APP BIM' THEN 'CIERRE POR APP BIM'
            WHEN TRY_CAST(UD.REMARKS AS VARCHAR) IS NOT NULL THEN UD.REMARKS
            ELSE NULL
        END AS Razon,
        UD.GRADE_NAME as PerfilCuenta,
        UD.GRADE_NAME as PerfilCuentaA,
        NULL AS perfilCuentaB,
        UD.ID_TYPE AS TipoDocumentoA,
        NULL AS TipoDocumentoB,
        UD.ID_VALUE AS DocumentoB,
        NULL AS NumDocumentoB,
        'ClosedAccount' AS requestType,
        NULL AS oldData,
        NULL AS newData,
        UD.O_USER_ID
    FROM USER_DATA_TRX UD
    WHERE COALESCE(UD.STATUS, '') = 'N'
    AND
        -- Convertir fechas de manera segura
        CASE
            WHEN TRY_CAST(UD.STATUS_CHANGE_ON AS DATE) IS NOT NULL THEN TRY_CAST(UD.STATUS_CHANGE_ON AS DATE) = '{fecha_inicio}'::DATE
            ELSE FALSE
        END
)

-- Resultado final
SELECT
    userHistId AS userHistId,
    P.createdOn,
    P.TipoDocumento,
    P.Documento,
    P.Msisdn,
    P.MsisdnB,
    CASE
        WHEN DA.USER_ID IS NOT NULL THEN DA.ISSUER_CODE
        ELSE P.BankDomain
    END AS BankDomain,
    P.created_by,
    CASE
        WHEN DA.USER_ID IS NOT NULL THEN DA.ATTR7_OLD
        ELSE P.userId
    END AS userId,
    P.accountType,
    CASE
        WHEN DA.USER_ID IS NOT NULL THEN DA.ATTR8_OLD
        ELSE P.accountId
    END AS accountId,
    P.Nombre,
    P.Apellido,
    P.NNombre,
    P.NApellido,
    COALESCE(
        CASE
            WHEN DA.USER_ID IS NOT NULL AND P.perfilA IS NOT NULL THEN COALESCE(DA.ISSUER_CODE, '0231') || ' ' || SUBSTR(P.perfilA, POSITION(' ' IN P.perfilA))
            WHEN P.perfilA IS NOT NULL THEN P.perfilA
            ELSE P.PerfilCuenta
        END,
        'USUARIO FINAL'
    ) AS perfilA,
    P.perfilB,
    P.IdiomaA,
    P.IdiomaB,
    P.TelcoA,
    P.TelcoB,
    -- Manejar el campo Razon de manera segura
    CASE
        WHEN P.Razon = 'CIERRE POR APP BIM' THEN 'CIERRE POR APP BIM'
        WHEN TRY_CAST(P.Razon AS VARCHAR) IS NOT NULL THEN P.Razon
        ELSE NULL
    END AS Razon,
    CASE
        WHEN DA.USER_ID IS NOT NULL THEN COALESCE(DA.ISSUER_CODE, '0231') || ' ' || UPPER(COALESCE(DA.GRADE_NAME_OLD, 'NORMAL'))
        ELSE COALESCE(P.PerfilCuenta, '0231 NORMAL')
    END AS PerfilCuenta,
    CASE
        WHEN DA.USER_ID IS NOT NULL THEN COALESCE(DA.ISSUER_CODE, '0231') || ' ' || UPPER(COALESCE(DA.GRADE_NAME_OLD, 'NORMAL'))
        ELSE COALESCE(P.PerfilCuentaA, '0231 NORMAL')
    END AS PerfilCuentaA,
    P.perfilCuentaB,
    P.TipoDocumentoA,
    P.TipoDocumentoB,
    P.DocumentoB,
    P.NumDocumentoB,
    P.requestType,
    -- Asegurar que oldData y newData sean JSON válidos o NULL
    CASE
        WHEN TRY_CAST(P.oldData AS JSON) IS NOT NULL THEN P.oldData
        ELSE NULL
    END AS oldData,
    CASE
        WHEN TRY_CAST(P.newData AS JSON) IS NOT NULL THEN P.newData
        ELSE NULL
    END AS newData,
    DA2.ATTR7_OLD AS userIdOld,
    DA2.ATTR8_OLD AS accountIdOld
FROM PROCESS P
LEFT JOIN WALLET_OLD DA ON P.O_USER_ID = DA.USER_ID
    AND (
        -- Manejar fechas de manera segura
        CASE
            WHEN TRY_CAST(P.createdOn AS TIMESTAMP) IS NOT NULL AND TRY_CAST(DA.CREATED_AT AS TIMESTAMP) IS NOT NULL
            THEN TRY_CAST(P.createdOn AS TIMESTAMP) < TRY_CAST(DA.CREATED_AT AS TIMESTAMP)
            ELSE FALSE
        END
    )
LEFT JOIN WALLET_OLD DA2 ON P.O_USER_ID = DA2.USER_ID
WHERE P.createdOn IS NOT NULL  -- Asegurar que solo se incluyan registros con fecha válida
ORDER BY
    -- Ordenar de manera segura
    CASE
        WHEN TRY_CAST(P.createdOn AS TIMESTAMP) IS NOT NULL THEN 1
        ELSE 2
    END,
    P.createdOn
