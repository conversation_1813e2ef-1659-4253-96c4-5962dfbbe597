#!/usr/bin/env python3
"""
Investigar la lógica específica para EXTERNAL_PAYMENT en To_Profile
"""

import oracledb
import sys

def investigar_external_payment():
    """Investiga la lógica de To_Profile para EXTERNAL_PAYMENT"""
    print("🔍 INVESTIGACIÓN: EXTERNAL_PAYMENT To_Profile")
    print("=" * 80)
    
    try:
        connection = oracledb.connect(
            user='usr_datalake',
            password='U2024b1mD4t4l4k5',
            dsn='10.240.131.10:1521/MMONEY'
        )
        
        cursor = connection.cursor()
        
        print("1️⃣ ANÁLISIS DE CASOS EXTERNAL_PAYMENT:")
        print("-" * 60)
        
        # Analizar casos específicos
        transfer_ids = ['**********', '**********', '**********', '**********']
        
        for transfer_id in transfer_ids:
            cursor.execute("""
                SELECT 
                    "TransferID",
                    "ToID_Mobiquity",
                    "To_Profile",
                    "To_BankDomain",
                    "TransactionType"
                FROM USR_DATALAKE.PRE_LOG_TRX 
                WHERE "TransferID" = :transfer_id
            """, {'transfer_id': transfer_id})
            
            oracle_data = cursor.fetchone()
            if oracle_data:
                print(f"\n  📋 TransferID: {oracle_data[0]}")
                print(f"    ToID_Mobiquity: {oracle_data[1]}")
                print(f"    To_Profile: '{oracle_data[2]}'")
                print(f"    To_BankDomain: {oracle_data[3]}")
                print(f"    TransactionType: {oracle_data[4]}")
                
                # Buscar datos del usuario en USER_DATA_TRX
                cursor.execute("""
                    SELECT 
                        USER_ID,
                        O_USER_ID,
                        PROFILE_TRX,
                        LOGIN_ID,
                        MSISDN
                    FROM USR_DATALAKE.USER_DATA_TRX
                    WHERE O_USER_ID = :user_id
                """, {'user_id': oracle_data[1]})
                
                user_data = cursor.fetchone()
                if user_data:
                    print(f"    USER_DATA_TRX:")
                    print(f"      PROFILE_TRX: {user_data[2]}")
                    print(f"      LOGIN_ID: {user_data[3]} ⭐ CLAVE")
                    print(f"      MSISDN: {user_data[4]}")
                    
                    # Analizar cómo Oracle construye To_Profile
                    oracle_profile = oracle_data[2]
                    login_id = user_data[3]
                    profile_trx = user_data[2]
                    
                    if login_id and login_id.upper() in oracle_profile:
                        print(f"    🎯 PATRÓN: Oracle usa LOGIN_ID '{login_id}' en To_Profile")
                        print(f"    📝 FÓRMULA: '{login_id.upper()} PROVEEDOR DE SERVICIOS'")
                    else:
                        print(f"    🤔 PATRÓN NO CLARO: LOGIN_ID no coincide directamente")
        
        print(f"\n2️⃣ ANÁLISIS GENERAL DE EXTERNAL_PAYMENT:")
        print("-" * 60)
        
        # Obtener patrón general para EXTERNAL_PAYMENT
        cursor.execute("""
            SELECT 
                P."ToID_Mobiquity",
                P."To_Profile",
                U.LOGIN_ID,
                U.PROFILE_TRX,
                COUNT(*) as CASOS
            FROM USR_DATALAKE.PRE_LOG_TRX P
            LEFT JOIN USR_DATALAKE.USER_DATA_TRX U ON P."ToID_Mobiquity" = U.O_USER_ID
            WHERE P."TransactionType" = 'EXTERNAL_PAYMENT'
            AND ROWNUM <= 20
            GROUP BY P."ToID_Mobiquity", P."To_Profile", U.LOGIN_ID, U.PROFILE_TRX
            ORDER BY COUNT(*) DESC
        """)
        
        external_patterns = cursor.fetchall()
        
        print(f"  Patrones encontrados para EXTERNAL_PAYMENT:")
        print(f"{'ToID':<15} {'To_Profile':<35} {'LOGIN_ID':<15} {'PROFILE_TRX':<15} {'CASOS'}")
        print("-" * 95)
        
        for row in external_patterns:
            to_id = row[0]
            to_profile = row[1]
            login_id = row[2]
            profile_trx = row[3]
            casos = row[4]
            
            print(f"{to_id:<15} {to_profile:<35} {login_id:<15} {profile_trx:<15} {casos}")
        
        print(f"\n3️⃣ ANÁLISIS DE LÓGICA DE CONSTRUCCIÓN:")
        print("-" * 60)
        
        # Verificar si hay patrón en la construcción
        unique_profiles = set()
        for row in external_patterns:
            to_profile = row[1]
            login_id = row[2]
            
            if login_id and to_profile:
                unique_profiles.add((login_id, to_profile))
        
        print(f"  Patrones únicos LOGIN_ID → To_Profile:")
        for login_id, to_profile in sorted(unique_profiles):
            print(f"    '{login_id}' → '{to_profile}'")
            
            # Verificar si sigue el patrón LOGIN_ID + " PROVEEDOR DE SERVICIOS"
            expected_pattern = f"{login_id.upper()} PROVEEDOR DE SERVICIOS"
            if to_profile == expected_pattern:
                print(f"      ✅ SIGUE PATRÓN: LOGIN_ID.upper() + ' PROVEEDOR DE SERVICIOS'")
            else:
                print(f"      ❌ NO SIGUE PATRÓN ESPERADO: '{expected_pattern}'")
        
        print(f"\n4️⃣ VERIFICACIÓN EN USER_PROFILE ORIGINAL:")
        print("-" * 60)
        
        # Verificar datos en USER_PROFILE original
        cursor.execute("""
            SELECT 
                UP.USER_ID,
                UP.LOGIN_ID,
                UP.PROFILE_CODE,
                UP.MSISDN
            FROM PDP_PROD10_MAINDB.USER_PROFILE UP
            WHERE UP.USER_ID IN ('3461446', '3507575')
            ORDER BY UP.USER_ID
        """)
        
        profile_data = cursor.fetchall()
        
        print(f"  USER_PROFILE original:")
        print(f"{'USER_ID':<15} {'LOGIN_ID':<15} {'PROFILE_CODE':<15} {'MSISDN'}")
        print("-" * 65)
        
        for row in profile_data:
            user_id = row[0]
            login_id = row[1]
            profile_code = row[2]
            msisdn = row[3]
            
            print(f"{user_id:<15} {login_id:<15} {profile_code:<15} {msisdn}")
        
        cursor.close()
        connection.close()
        
        print(f"\n5️⃣ CONCLUSIONES:")
        print("-" * 60)
        print("1. Para EXTERNAL_PAYMENT, Oracle usa LOGIN_ID específico del usuario")
        print("2. Patrón: LOGIN_ID.upper() + ' PROVEEDOR DE SERVICIOS'")
        print("3. Nuestro S3 está usando solo 'FCOMPARTAMOS Biller' genérico")
        print("4. Necesitamos corregir la lógica para EXTERNAL_PAYMENT")
        
        print(f"\n💡 CORRECCIÓN REQUERIDA:")
        print("En To_Profile, para TransactionType = 'EXTERNAL_PAYMENT':")
        print("- Usar: UPAYEE.LOGIN_ID.upper() + ' PROVEEDOR DE SERVICIOS'")
        print("- En lugar de: To_BankDomain + ' ' + PROFILE genérico")
        
    except Exception as e:
        print(f"❌ Error en investigación: {e}")
        import traceback
        traceback.print_exc()

def main():
    print("🚀 INVESTIGACIÓN EXTERNAL_PAYMENT")
    print("=" * 80)
    print("Identificando lógica específica para To_Profile")
    print()
    
    investigar_external_payment()
    
    print("\n🏁 INVESTIGACIÓN COMPLETADA")

if __name__ == "__main__":
    main()
