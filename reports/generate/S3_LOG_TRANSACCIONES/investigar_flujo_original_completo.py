#!/usr/bin/env python3
"""
Investigación exhaustiva del flujo original de Oracle
para entender la lógica general correcta de From_AccountID_Mobiquity
"""

import oracledb
import sys

def investigar_flujo_original():
    """Investiga el flujo original de Oracle paso a paso"""
    print("🔍 INVESTIGACIÓN EXHAUSTIVA DEL FLUJO ORIGINAL ORACLE")
    print("=" * 80)
    
    # Casos para analizar
    casos = [
        {'transfer_id': '***************', 'from_id': '945661'},  # Caso que funciona
        {'transfer_id': '**********', 'from_id': 'US.****************'}  # Caso que falla
    ]
    
    try:
        connection = oracledb.connect(
            user='usr_datalake',
            password='U2024b1mD4t4l4k5',
            dsn='10.240.131.10:1521/MMONEY'
        )
        
        cursor = connection.cursor()
        
        for i, caso in enumerate(casos):
            print(f"\n{i+1}️⃣ ANÁLISIS CASO: TransferID = {caso['transfer_id']}")
            print("-" * 60)
            
            transfer_id = caso['transfer_id']
            from_id = caso['from_id']
            
            # 1. Datos en Oracle PRE_LOG_TRX
            cursor.execute("""
                SELECT 
                    "TransferID",
                    "FromID_Mobiquity",
                    "From_AccountID_Mobiquity"
                FROM USR_DATALAKE.PRE_LOG_TRX
                WHERE "TransferID" = :transfer_id
            """, {'transfer_id': transfer_id})
            
            pre_log_data = cursor.fetchone()
            if pre_log_data:
                print(f"  Oracle PRE_LOG_TRX:")
                print(f"    TransferID: {pre_log_data[0]}")
                print(f"    FromID_Mobiquity: {pre_log_data[1]}")
                print(f"    From_AccountID_Mobiquity: {pre_log_data[2]} ⭐ VALOR ORACLE")
                
                oracle_from_account = pre_log_data[2]
            else:
                print(f"  ❌ No encontrado en Oracle PRE_LOG_TRX")
                continue
            
            # 2. Datos en USER_DATA_TRX
            cursor.execute("""
                SELECT 
                    USER_ID,
                    O_USER_ID,
                    WALLET_NUMBER
                FROM USR_DATALAKE.USER_DATA_TRX
                WHERE O_USER_ID = :from_id
            """, {'from_id': from_id})
            
            user_data_result = cursor.fetchone()
            if user_data_result:
                print(f"  USER_DATA_TRX:")
                print(f"    USER_ID: {user_data_result[0]}")
                print(f"    O_USER_ID: {user_data_result[1]}")
                print(f"    WALLET_NUMBER: {user_data_result[2]} ⭐ VALOR USER_DATA_TRX")
                
                user_data_wallet = user_data_result[2]
            else:
                print(f"  ❌ No encontrado en USER_DATA_TRX")
                continue
            
            # 3. Comparación
            print(f"  COMPARACIÓN:")
            print(f"    Oracle PRE_LOG_TRX: {oracle_from_account}")
            print(f"    USER_DATA_TRX: {user_data_wallet}")
            
            if str(oracle_from_account) == str(user_data_wallet):
                print(f"    ✅ COINCIDEN: Oracle usa USER_DATA_TRX.WALLET_NUMBER")
            else:
                print(f"    ❌ DIFIEREN: Oracle usa lógica diferente")
                
                # 4. Investigar de dónde viene el valor de Oracle
                print(f"  INVESTIGANDO ORIGEN DEL VALOR ORACLE:")
                
                # Buscar en USER_PROFILE
                cursor.execute("""
                    SELECT 
                        USER_ID,
                        ATTR7,
                        ATTR8,
                        MSISDN
                    FROM PDP_PROD10_MAINDB.USER_PROFILE
                    WHERE USER_ID = :from_id
                """, {'from_id': from_id})
                
                profile_data = cursor.fetchone()
                if profile_data:
                    print(f"    USER_PROFILE:")
                    print(f"      USER_ID: {profile_data[0]}")
                    print(f"      ATTR7: {profile_data[1]}")
                    print(f"      ATTR8: {profile_data[2]}")
                    print(f"      MSISDN: {profile_data[3]}")
                    
                    if str(profile_data[2]) == str(oracle_from_account):
                        print(f"    ✅ MATCH: Oracle usa USER_PROFILE.ATTR8")
                    elif str(profile_data[1]) == str(oracle_from_account):
                        print(f"    ✅ MATCH: Oracle usa USER_PROFILE.ATTR7")
                
                # Buscar en MTX_WALLET
                cursor.execute("""
                    SELECT 
                        USER_ID,
                        WALLET_NUMBER,
                        STATUS,
                        MODIFIED_ON,
                        ROW_NUMBER() OVER (PARTITION BY USER_ID ORDER BY MODIFIED_ON DESC) as RN
                    FROM PDP_PROD10_MAINDBBUS.MTX_WALLET
                    WHERE USER_ID = :from_id
                    ORDER BY MODIFIED_ON DESC
                """, {'from_id': from_id})
                
                wallet_data = cursor.fetchall()
                if wallet_data:
                    print(f"    MTX_WALLET:")
                    for j, row in enumerate(wallet_data[:3]):
                        status_desc = "ACTIVA" if row[2] == 'Y' else "INACTIVA"
                        print(f"      [{j+1}] WALLET: {row[1]}, STATUS: {row[2]} ({status_desc}), RN: {row[4]}")
                        
                        if str(row[1]) == str(oracle_from_account):
                            print(f"          ✅ MATCH: Oracle usa esta wallet (RN={row[4]}, STATUS={row[2]})")
                
                # Buscar en USER_ACCOUNT_HISTORY
                cursor.execute("""
                    SELECT 
                        USER_ID,
                        ACCOUNT_ID,
                        ATTR7_OLD,
                        ATTR8_OLD
                    FROM USER_ACCOUNT_HISTORY
                    WHERE USER_ID = :from_id
                    AND (ACCOUNT_ID = :oracle_val OR ATTR7_OLD = :oracle_val OR ATTR8_OLD = :oracle_val)
                """, {'from_id': from_id, 'oracle_val': oracle_from_account})
                
                history_data = cursor.fetchall()
                if history_data:
                    print(f"    USER_ACCOUNT_HISTORY:")
                    for row in history_data:
                        print(f"      USER_ID: {row[0]}, ACCOUNT_ID: {row[1]}")
                        print(f"      ATTR7_OLD: {row[2]}, ATTR8_OLD: {row[3]}")
                        
                        if str(row[1]) == str(oracle_from_account):
                            print(f"      ✅ MATCH: Oracle usa ACCOUNT_ID")
                        elif str(row[2]) == str(oracle_from_account):
                            print(f"      ✅ MATCH: Oracle usa ATTR7_OLD")
                        elif str(row[3]) == str(oracle_from_account):
                            print(f"      ✅ MATCH: Oracle usa ATTR8_OLD")
                else:
                    print(f"    ❌ No encontrado en USER_ACCOUNT_HISTORY")
        
        print(f"\n🔍 ANÁLISIS DE PATRÓN GENERAL:")
        print("-" * 60)
        
        # Buscar patrón general en múltiples casos
        cursor.execute("""
            SELECT 
                P."FromID_Mobiquity",
                P."From_AccountID_Mobiquity",
                U.WALLET_NUMBER as USER_DATA_WALLET,
                CASE 
                    WHEN P."From_AccountID_Mobiquity" = U.WALLET_NUMBER THEN 'USER_DATA_TRX'
                    ELSE 'OTRA_FUENTE'
                END as FUENTE
            FROM (
                SELECT DISTINCT "FromID_Mobiquity", "From_AccountID_Mobiquity"
                FROM USR_DATALAKE.PRE_LOG_TRX
                WHERE ROWNUM <= 20
            ) P
            LEFT JOIN USR_DATALAKE.USER_DATA_TRX U ON P."FromID_Mobiquity" = U.O_USER_ID
            ORDER BY FUENTE, P."FromID_Mobiquity"
        """)
        
        pattern_data = cursor.fetchall()
        
        user_data_matches = 0
        other_source_matches = 0
        
        print(f"  Análisis de 20 casos aleatorios:")
        for row in pattern_data:
            if row[3] == 'USER_DATA_TRX':
                user_data_matches += 1
            else:
                other_source_matches += 1
                print(f"    OTRA_FUENTE: FromID={row[0]}, Oracle={row[1]}, UserData={row[2]}")
        
        print(f"  📊 ESTADÍSTICAS:")
        print(f"    Casos que usan USER_DATA_TRX: {user_data_matches}")
        print(f"    Casos que usan OTRA_FUENTE: {other_source_matches}")
        
        cursor.close()
        connection.close()
        
        print(f"\n💡 CONCLUSIONES:")
        print("-" * 60)
        print("1. Oracle NO siempre usa USER_DATA_TRX.WALLET_NUMBER")
        print("2. Hay lógica adicional que selecciona diferentes fuentes")
        print("3. Puede usar USER_PROFILE.ATTR7/ATTR8 o MTX_WALLET específicas")
        print("4. Necesitamos identificar la lógica exacta de selección")
        
    except Exception as e:
        print(f"❌ Error en investigación: {e}")
        import traceback
        traceback.print_exc()

def main():
    print("🚀 INVESTIGACIÓN EXHAUSTIVA DEL FLUJO ORIGINAL")
    print("=" * 80)
    print("Analizando casos que funcionan vs casos que fallan")
    print()
    
    investigar_flujo_original()
    
    print("\n🏁 INVESTIGACIÓN COMPLETADA")

if __name__ == "__main__":
    main()
