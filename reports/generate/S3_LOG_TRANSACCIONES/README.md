# Pipeline LOG_TRANSACCIONES Modernizado

## 🎯 Descripción

Pipeline ETL modernizado que replica exactamente el flujo de `log_transacciones` original de Oracle, pero usando **DuckDB + Parquet** como arquitectura de datos moderna.

### 🔄 Migración Completa
- **Origen**: Oracle Database + Stored Procedures
- **Destino**: DuckDB + Archivos Parquet (S3)
- **Funcionalidad**: 100% idéntica al flujo original

## 📁 Estructura del Proyecto

```
S3_LOG_TRANSACCIONES/
├── pipeline_log_transacciones_duckdb.py  # Pipeline principal
├── test_pipeline.py                      # Script de pruebas
├── README.md                            # Este archivo
├── Tablas_PDP_Datalake.md              # Documentación de fuentes S3
├── TEMP_LOGS_TRANSACCIONES/            # Archivos temporales
├── output/                             # Archivos CSV finales
└── logs/                              # Logs de ejecución
```

## 🚀 Uso Rápido

### 1. Ejecutar Pruebas (Recomendado)
```bash
cd /home/<USER>/REPORTE_2025/reports/generate/S3_LOG_TRANSACCIONES
python3 test_pipeline.py
```

### 2. Ejecutar Pipeline Completo
```bash
# Para una fecha específica
python3 pipeline_log_transacciones_duckdb.py 2025-06-15

# Para ayer (por defecto)
python3 pipeline_log_transacciones_duckdb.py
```

## 📊 Flujo del Pipeline

### Etapas de Procesamiento:

1. **SP_PRE_LOG_TRX** 
   - Procesa transacciones desde MTX_TRANSACTION_HEADER/ITEMS
   - Aplica lógica de negocio compleja (TRX_SERVICE, CONTEXT, etc.)
   - Genera: `PRE_LOG_TRX.parquet`

2. **SP_LOG_TRX**
   - Transforma PRE_LOG_TRX al formato final
   - Aplica mapeos de USER_ACCOUNT_HISTORY
   - Genera: `LOG_TRX_FINAL.parquet`

3. **Extracción CSV**
   - Extrae datos filtrados por fecha
   - Genera: `TR-{YYYYMMDD}.csv`

## 🗄️ Fuentes de Datos

### Tablas PDP (Silver Zone)
- `MTX_TRANSACTION_HEADER` - Cabeceras de transacciones (particionada)
- `MTX_TRANSACTION_ITEMS` - Detalles de transacciones (particionada)
- `MTX_WALLET` - Información de billeteras
- `SYS_SERVICE_TYPES` - Tipos de servicios
- `CHANNEL_GRADES` - Grados de canales
- `MARKETING_PROFILE` - Perfiles de marketing
- `MTX_CATEGORIES` - Categorías de transacciones
- `ISSUER_DETAILS` - Detalles de emisores
- `SYS_SERVICE_PROVIDER` - Proveedores de servicios

### Tablas Datalake (Golden Zone)
- `USER_DATA_TRX` - Datos consolidados de usuarios
- `USER_ACCOUNT_HISTORY` - Historial de cuentas

## ⚙️ Configuración

### Credenciales AWS
El pipeline usa las credenciales configuradas en AWS CLI:
```bash
aws configure list
```

### Rutas S3
Configuradas en `pipeline_log_transacciones_duckdb.py`:
- **Silver Zone**: `s3://prd-datalake-silver-zone-************/`
- **Golden Zone**: `s3://prd-datalake-golden-zone-************/`

## 📈 Monitoreo y Logs

### Archivos de Log
- `pipeline_log_transacciones.log` - Log detallado del pipeline
- `execution_status.log` - Estado de ejecución por etapas
- `logs/pipeline_results_{YYYYMMDD}.json` - Resultados en JSON

### Métricas de Rendimiento
El pipeline registra:
- Duración por etapa
- Número de registros procesados
- Archivos generados
- Errores encontrados

## 🔧 Solución de Problemas

### Errores Comunes

1. **Error de credenciales S3**
   ```
   Error configurando credenciales S3
   ```
   **Solución**: Verificar `aws configure` y permisos

2. **Tabla particionada no encontrada**
   ```
   Error de acceso - No files found
   ```
   **Solución**: Verificar que existan datos para la fecha solicitada

3. **Error de memoria DuckDB**
   ```
   Out of memory
   ```
   **Solución**: Procesar fechas con menos volumen de datos

### Verificación de Datos
```bash
# Verificar datos disponibles para una fecha
python3 -c "
from pipeline_log_transacciones_duckdb import LogTransaccionesPipeline
p = LogTransaccionesPipeline()
path = p.get_partitioned_path(p.s3_sources['mtx_transaction_header'], '2025-05-02')
result = p.conn.execute(f'SELECT COUNT(*) FROM read_parquet(\"{path}\")').fetchone()
print(f'Registros disponibles: {result[0]:,}')
"
```

## 🎯 Comparación con Flujo Original

| Aspecto | Oracle Original | DuckDB Modernizado |
|---------|----------------|-------------------|
| **Motor** | Oracle Database | DuckDB + Parquet |
| **Almacenamiento** | Tablas relacionales | Archivos columnar S3 |
| **Rendimiento** | Depende de BD | Ultra-rápido (columnar) |
| **Escalabilidad** | Limitada | Ilimitada (S3) |
| **Mantenimiento** | Complejo (DBA) | Simple (archivos) |
| **Costos** | Licencias Oracle | Solo S3 + compute |
| **Lógica** | **100% idéntica** | **100% idéntica** |

## 📋 Validación de Resultados

### Comparar con Flujo Original
```bash
# Generar archivo con pipeline modernizado
python3 pipeline_log_transacciones_duckdb.py 2025-05-02

# Comparar con archivo original
diff output/TR-20250502.csv /ruta/archivo/original/TR-20250502.csv
```

### Verificar Integridad
```bash
# Contar registros
wc -l output/TR-20250502.csv

# Verificar estructura
head -5 output/TR-20250502.csv
```

## 🚀 Ventajas del Pipeline Modernizado

1. **✅ Rendimiento Superior**: DuckDB es 10-100x más rápido que Oracle para análisis
2. **✅ Escalabilidad Ilimitada**: S3 puede manejar petabytes de datos
3. **✅ Costos Reducidos**: Sin licencias Oracle, solo pago por uso S3
4. **✅ Mantenimiento Simplificado**: Código Python vs. Stored Procedures Oracle
5. **✅ Flexibilidad**: Fácil modificación y extensión del pipeline
6. **✅ Compatibilidad 100%**: Mismos resultados que el flujo original

## 📞 Soporte

Para problemas o mejoras, contactar al equipo de Ingeniería de Datos.
