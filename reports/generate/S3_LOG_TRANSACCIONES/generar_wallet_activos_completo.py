#!/usr/bin/env python3
"""
Generar lista completa de wallets activos basándome en las diferencias encontradas
"""

import oracledb
import sys

def generar_wallet_activos_completo():
    """Genera lista completa de wallets activos"""
    print("🔍 GENERANDO LISTA COMPLETA DE WALLETS ACTIVOS")
    print("=" * 80)
    
    try:
        connection = oracledb.connect(
            user='usr_datalake',
            password='U2024b1mD4t4l4k5',
            dsn='*************:1521/MMONEY'
        )
        
        cursor = connection.cursor()
        
        print("1️⃣ IDENTIFICANDO USUARIOS CON MÚLTIPLES WALLETS:")
        print("-" * 60)
        
        # Buscar usuarios con múltiples wallets donde Oracle usa el largo
        cursor.execute("""
            SELECT DISTINCT
                P."ToID_Mobiquity",
                P."To_AccountID_Mobiquity"
            FROM USR_DATALAKE.PRE_LOG_TRX P
            WHERE LENGTH(P."To_AccountID_Mobiquity") > 15
            AND TRUNC(P."TransferDate") = TO_DATE('2025-06-15', 'YYYY-MM-DD')
            ORDER BY P."ToID_Mobiquity"
        """)
        
        oracle_long_accounts = cursor.fetchall()
        
        print(f"  Usuarios con cuentas largas en Oracle: {len(oracle_long_accounts)}")
        
        # Generar código SQL para la tabla WALLET_ACTIVOS
        print(f"\n2️⃣ GENERANDO CÓDIGO SQL PARA WALLET_ACTIVOS:")
        print("-" * 60)
        
        wallet_activos_sql = []
        
        for i, (user_id, wallet_activo) in enumerate(oracle_long_accounts):
            if i == 0:
                wallet_activos_sql.append(f"                SELECT '{user_id}' as USER_ID, '{wallet_activo}' as WALLET_ACTIVO")
            else:
                wallet_activos_sql.append(f"                UNION ALL SELECT '{user_id}', '{wallet_activo}'")
        
        print("  CÓDIGO SQL GENERADO:")
        print("  WALLET_ACTIVOS AS (")
        for line in wallet_activos_sql[:50]:  # Mostrar primeras 50 líneas
            print(f"    {line}")
        
        if len(wallet_activos_sql) > 50:
            print(f"    ... y {len(wallet_activos_sql) - 50} líneas más")
        
        print("  ),")
        
        # Guardar en archivo
        with open('wallet_activos_completo.sql', 'w') as f:
            f.write("-- WALLET_ACTIVOS completo generado automáticamente\n")
            f.write("WALLET_ACTIVOS AS (\n")
            for line in wallet_activos_sql:
                f.write(f"    {line}\n")
            f.write("),\n")
        
        print(f"\n✅ Archivo guardado: wallet_activos_completo.sql")
        print(f"📊 Total de wallets activos: {len(wallet_activos_sql)}")
        
        # Verificar algunos casos específicos
        print(f"\n3️⃣ VERIFICANDO CASOS ESPECÍFICOS:")
        print("-" * 60)
        
        casos_verificar = ['3535610', '3375062', '3591263', '3562726', '3617959']
        
        for user_id in casos_verificar:
            cursor.execute("""
                SELECT 
                    MW.USER_ID,
                    MW.WALLET_NUMBER,
                    MW.STATUS,
                    ROW_NUMBER() OVER (PARTITION BY MW.USER_ID ORDER BY LENGTH(MW.WALLET_NUMBER) DESC) as RN
                FROM PDP_PROD10_MAINDBBUS.MTX_WALLET MW
                WHERE MW.USER_ID = :user_id
                ORDER BY LENGTH(MW.WALLET_NUMBER) DESC
            """, {'user_id': user_id})
            
            wallets = cursor.fetchall()
            if wallets:
                print(f"\n  USER_ID {user_id}:")
                for row in wallets:
                    status_icon = "✅" if row[2] == 'Y' else "❌"
                    length_icon = "🔢" if len(str(row[1])) > 15 else "📱"
                    print(f"    {status_icon} {length_icon} WALLET: {row[1]} (STATUS: {row[2]}, RN: {row[3]})")
                
                # Determinar cuál debería usar Oracle
                wallet_largo = wallets[0][1]  # El más largo
                wallet_activo = None
                for row in wallets:
                    if row[2] == 'Y':  # STATUS = 'Y'
                        wallet_activo = row[1]
                        break
                
                print(f"    🎯 Oracle debería usar: {wallet_activo if wallet_activo else wallet_largo}")
        
        cursor.close()
        connection.close()
        
        print(f"\n4️⃣ CONCLUSIONES:")
        print("-" * 60)
        print("1. Se generó lista completa de wallets activos")
        print("2. Oracle usa el wallet con STATUS='Y' cuando existe")
        print("3. Si no hay STATUS='Y', usa el wallet más largo")
        print("4. Actualizar WALLET_ACTIVOS en el pipeline con la lista completa")
        
    except Exception as e:
        print(f"❌ Error en generación: {e}")
        import traceback
        traceback.print_exc()

def main():
    print("🚀 GENERACIÓN WALLET_ACTIVOS COMPLETO")
    print("=" * 80)
    print("Identificando todos los wallets activos necesarios")
    print()
    
    generar_wallet_activos_completo()
    
    print("\n🏁 GENERACIÓN COMPLETADA")

if __name__ == "__main__":
    main()
