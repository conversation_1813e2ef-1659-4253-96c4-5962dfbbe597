#!/usr/bin/env python3
"""
Script para probar el pipeline completo con la lógica corregida
Verifica que ahora From_AccountID coincida exactamente con Oracle
"""

import sys
import os
sys.path.append('/home/<USER>/REPORTE_2025/reports/generate/S3_LOG_TRANSACCIONES')

from pipeline_log_transacciones_duckdb import LogTransaccionesPipeline
import oracledb

def test_pipeline_completo():
    """Prueba el pipeline completo con la lógica corregida"""
    print("🧪 PROBANDO PIPELINE COMPLETO CON LÓGICA CORREGIDA")
    print("=" * 70)
    
    fecha = '2025-06-15'
    transfer_id = '***************'
    
    try:
        # Ejecutar pipeline completo
        pipeline = LogTransaccionesPipeline()
        
        print("1️⃣ Ejecutando pipeline completo...")
        result = pipeline.run_pipeline(fecha)
        
        print(f"✅ Pipeline completado:")
        for key, value in result.items():
            print(f"  {key}: {value}")
        
        # Consultar el resultado final en LOG_TRX_FINAL
        print("\n2️⃣ Consultando resultado final en S3...")

        # Buscar el archivo LOG_TRX_FINAL en los archivos generados
        log_trx_path = None
        for archivo in result.get('archivos_generados', []):
            if 'LOG_TRX_FINAL' in archivo:
                log_trx_path = archivo
                break

        if log_trx_path:
            result = pipeline.conn.execute(f"""
                SELECT
                    "TransactionID",
                    "FromID",
                    "FromAccountID"
                FROM read_parquet('{log_trx_path}')
                WHERE "TransactionID" = '{transfer_id}'
            """).fetchall()
            
            if result:
                row = result[0]
                s3_transaction_id = row[0]
                s3_from_id = row[1]
                s3_from_account_id = row[2]

                print(f"  TransactionID: {s3_transaction_id}")
                print(f"  FromID: {s3_from_id}")
                print(f"  FromAccountID: {s3_from_account_id}")
            else:
                print("  ❌ No se encontró el TransferID en LOG_TRX_FINAL")
                return
        else:
            print("  ❌ No se generó LOG_TRX_FINAL")
            return
        
        # Consultar Oracle LOG_TRX para comparar (si existe)
        print("\n3️⃣ Consultando Oracle LOG_TRX para comparar...")
        try:
            connection = oracledb.connect(
                user='usr_datalake',
                password='U2024b1mD4t4l4k5',
                dsn='10.240.131.10:1521/MMONEY'
            )
            
            cursor = connection.cursor()
            cursor.execute("""
                SELECT 
                    "TransactionID",
                    "FromID",
                    "FromAccountID",
                    "TransactionDate"
                FROM USR_DATALAKE.LOG_TRX 
                WHERE "TransactionID" = :transfer_id
            """, {'transfer_id': transfer_id})
            
            oracle_result = cursor.fetchall()
            if oracle_result:
                oracle_row = oracle_result[0]
                oracle_transaction_id = oracle_row[0]
                oracle_from_id = oracle_row[1]
                oracle_from_account_id = oracle_row[2]
                oracle_date = oracle_row[3]
                
                print(f"  TransactionID: {oracle_transaction_id}")
                print(f"  FromID: {oracle_from_id}")
                print(f"  FromAccountID: {oracle_from_account_id}")
                print(f"  TransactionDate: {oracle_date}")
                
                # Comparar resultados
                print("\n4️⃣ COMPARACIÓN FINAL:")
                print("-" * 40)
                print(f"S3 FromAccountID: {s3_from_account_id}")
                print(f"Oracle FromAccountID: {oracle_from_account_id}")
                
                if str(s3_from_account_id) == str(oracle_from_account_id):
                    print("✅ ¡ÉXITO TOTAL! Los valores ahora coinciden perfectamente")
                    print("🎉 La migración Oracle → S3/DuckDB es 100% exitosa")
                else:
                    print("❌ Los valores aún difieren")
                    print("🔧 Se necesita más investigación")
                
            else:
                print("  ❌ No se encontró el TransferID en Oracle LOG_TRX")
                print("  ℹ️  Comparando con valor esperado de mapeo Bank_Domain")
                
                # Valor esperado según mapeo CRANDES → 1414519
                expected_value = '1414519'
                if str(s3_from_account_id) == expected_value:
                    print(f"✅ ¡ÉXITO! S3 usa correctamente el mapeo Bank_Domain: {expected_value}")
                    print("🎉 La lógica de SP_LOG_TRX está funcionando correctamente")
                else:
                    print(f"❌ S3 no usa el mapeo esperado: {expected_value}")
            
            cursor.close()
            connection.close()
            
        except Exception as e:
            print(f"  ❌ Error consultando Oracle: {e}")
            print("  ℹ️  Verificando con valor esperado de mapeo Bank_Domain")
            
            # Valor esperado según mapeo CRANDES → 1414519
            expected_value = '1414519'
            if str(s3_from_account_id) == expected_value:
                print(f"✅ ¡ÉXITO! S3 usa correctamente el mapeo Bank_Domain: {expected_value}")
                print("🎉 La lógica de SP_LOG_TRX está funcionando correctamente")
            else:
                print(f"❌ S3 no usa el mapeo esperado: {expected_value}")
        
    except Exception as e:
        print(f"❌ Error en la prueba: {e}")
        import traceback
        traceback.print_exc()

def main():
    print("🚀 PRUEBA COMPLETA DEL PIPELINE CORREGIDO")
    print("=" * 80)
    print("Verificando que toda la lógica Oracle esté replicada correctamente")
    print()
    
    test_pipeline_completo()
    
    print("\n🏁 PRUEBA COMPLETADA")

if __name__ == "__main__":
    main()
