#!/usr/bin/env python3
"""
Aná<PERSON>is Detective - Comparación Detallada PRE_LOG_TRX
Modo 'detective master' para encontrar diferencias exactas
"""

import oracledb
import duckdb
import pandas as pd
import sys
from datetime import datetime

class DetectiveAnalysis:
    def __init__(self):
        self.fecha = '2025-06-15'
        self.setup_connections()
        
    def setup_connections(self):
        """Configura conexiones"""
        try:
            # Oracle
            self.oracle_conn = oracledb.connect(
                user='usr_datalake',
                password='U2024b1mD4t4l4k5',
                dsn='10.240.131.10:1521/MMONEY'
            )
            
            # DuckDB
            self.duck_conn = duckdb.connect()
            self.duck_conn.sql("INSTALL httpfs;")
            self.duck_conn.sql("LOAD httpfs;")
            
            print("✅ Conexiones establecidas")
            
        except Exception as e:
            print(f"❌ Error en conexiones: {e}")
            raise

    def create_unique_keys(self):
        """Crea claves únicas para comparación exacta"""
        print("🔍 CREANDO CLAVES ÚNICAS PARA COMPARACIÓN")
        print("=" * 60)
        
        # Crear clave única Oracle
        print("📊 Creando claves Oracle...")
        cursor = self.oracle_conn.cursor()
        cursor.execute(f"""
            SELECT 
                "TransferID" || '|' || "TransferID_Mob" || '|' || 
                NVL("FromID_Mobiquity", 'NULL') || '|' || 
                NVL("ToID_Mobiquity", 'NULL') AS unique_key,
                "TransferID",
                "From_Msisdn",
                "To_Msisdn",
                "Remarks"
            FROM USR_DATALAKE.PRE_LOG_TRX
            WHERE TRUNC("TransferDate") = TO_DATE('{self.fecha}', 'YYYY-MM-DD')
            ORDER BY "TransferID"
        """)
        
        oracle_data = cursor.fetchall()
        oracle_keys = set(row[0] for row in oracle_data)
        cursor.close()
        
        print(f"✅ Oracle: {len(oracle_keys):,} claves únicas")
        
        # Crear clave única Parquet
        print("📊 Creando claves Parquet...")
        parquet_path = f"TEMP_LOGS_TRANSACCIONES/20250615/PRE_LOG_TRX.parquet"
        
        parquet_result = self.duck_conn.execute(f"""
            SELECT 
                "TransferID" || '|' || "TransferID_Mob" || '|' || 
                COALESCE("FromID_Mobiquity", 'NULL') || '|' || 
                COALESCE("ToID_Mobiquity", 'NULL') AS unique_key,
                "TransferID",
                "From_Msisdn",
                "To_Msisdn",
                "Remarks"
            FROM read_parquet('{parquet_path}')
            WHERE CAST("TransferDate" AS DATE) = CAST('{self.fecha}' AS DATE)
            ORDER BY "TransferID"
        """).fetchall()
        
        parquet_keys = set(row[0] for row in parquet_result)
        
        print(f"✅ Parquet: {len(parquet_keys):,} claves únicas")
        
        return oracle_data, parquet_result, oracle_keys, parquet_keys

    def find_missing_records(self, oracle_keys, parquet_keys, oracle_data, parquet_data):
        """Encuentra registros faltantes específicos"""
        print(f"\n🔍 ANÁLISIS DE REGISTROS FALTANTES")
        print("=" * 60)
        
        # Registros en Oracle pero no en Parquet
        missing_in_parquet = oracle_keys - parquet_keys
        if missing_in_parquet:
            print(f"❌ Registros en Oracle pero NO en Parquet: {len(missing_in_parquet)}")
            for i, key in enumerate(list(missing_in_parquet)[:3], 1):
                # Encontrar el registro completo
                oracle_record = next((r for r in oracle_data if r[0] == key), None)
                if oracle_record:
                    print(f"  {i}. TransferID: {oracle_record[1]}")
                    print(f"     From_Msisdn: {oracle_record[2]}")
                    print(f"     To_Msisdn: {oracle_record[3]}")
                    print(f"     Remarks: {oracle_record[4][:50]}...")
        else:
            print("✅ Todos los registros Oracle están en Parquet")
        
        # Registros en Parquet pero no en Oracle
        extra_in_parquet = parquet_keys - oracle_keys
        if extra_in_parquet:
            print(f"⚠️  Registros en Parquet pero NO en Oracle: {len(extra_in_parquet)}")
            for i, key in enumerate(list(extra_in_parquet)[:3], 1):
                # Encontrar el registro completo
                parquet_record = next((r for r in parquet_data if r[0] == key), None)
                if parquet_record:
                    print(f"  {i}. TransferID: {parquet_record[1]}")
                    print(f"     From_Msisdn: {parquet_record[2]}")
                    print(f"     To_Msisdn: {parquet_record[3]}")
                    print(f"     Remarks: {parquet_record[4][:50]}...")
        else:
            print("✅ No hay registros extra en Parquet")
        
        return missing_in_parquet, extra_in_parquet

    def compare_specific_records(self, oracle_data, parquet_data):
        """Compara registros específicos para encontrar diferencias"""
        print(f"\n🔍 COMPARACIÓN REGISTRO POR REGISTRO")
        print("=" * 60)
        
        # Convertir a diccionarios para comparación fácil
        oracle_dict = {row[1]: row for row in oracle_data}  # TransferID como clave
        parquet_dict = {row[1]: row for row in parquet_data}
        
        # Tomar los primeros 5 TransferIDs comunes
        common_transfer_ids = set(oracle_dict.keys()) & set(parquet_dict.keys())
        sample_ids = list(sorted(common_transfer_ids))[:5]
        
        print(f"📋 Comparando {len(sample_ids)} registros específicos:")
        
        differences_found = 0
        for i, transfer_id in enumerate(sample_ids, 1):
            oracle_record = oracle_dict[transfer_id]
            parquet_record = parquet_dict[transfer_id]
            
            print(f"\n{i}. TransferID: {transfer_id}")
            
            # Comparar campo por campo
            fields = ['unique_key', 'TransferID', 'From_Msisdn', 'To_Msisdn', 'Remarks']
            record_differences = 0
            
            for j, field in enumerate(fields):
                oracle_val = oracle_record[j]
                parquet_val = parquet_record[j]
                
                if oracle_val != parquet_val:
                    print(f"   ❌ {field}:")
                    print(f"      Oracle:  {oracle_val}")
                    print(f"      Parquet: {parquet_val}")
                    record_differences += 1
                else:
                    print(f"   ✅ {field}: IDÉNTICO")
            
            if record_differences == 0:
                print(f"   🎯 REGISTRO PERFECTO - Sin diferencias")
            else:
                print(f"   ⚠️  {record_differences} diferencias encontradas")
                differences_found += 1
        
        print(f"\n📊 RESUMEN COMPARACIÓN:")
        print(f"   Registros comparados: {len(sample_ids)}")
        print(f"   Registros con diferencias: {differences_found}")
        print(f"   Registros perfectos: {len(sample_ids) - differences_found}")
        
        return differences_found == 0

    def analyze_order_differences(self, oracle_data, parquet_data):
        """Analiza diferencias de orden"""
        print(f"\n🔍 ANÁLISIS DE ORDEN DE REGISTROS")
        print("=" * 60)
        
        # Comparar los primeros 10 TransferIDs en orden
        oracle_order = [row[1] for row in oracle_data[:10]]
        parquet_order = [row[1] for row in parquet_data[:10]]
        
        print("📋 Primeros 10 TransferIDs en orden:")
        print(f"Oracle:  {oracle_order}")
        print(f"Parquet: {parquet_order}")
        
        order_match = oracle_order == parquet_order
        print(f"🎯 Orden: {'✅ IDÉNTICO' if order_match else '❌ DIFERENTE'}")
        
        return order_match

    def run_detective_analysis(self):
        """Ejecuta el análisis detective completo"""
        print("🕵️ INICIANDO ANÁLISIS DETECTIVE MASTER")
        print("=" * 80)
        print(f"📅 Fecha: {self.fecha}")
        print(f"🎯 Objetivo: Encontrar diferencias exactas")
        
        try:
            # 1. Crear claves únicas
            oracle_data, parquet_data, oracle_keys, parquet_keys = self.create_unique_keys()
            
            # 2. Encontrar registros faltantes
            missing_in_parquet, extra_in_parquet = self.find_missing_records(
                oracle_keys, parquet_keys, oracle_data, parquet_data
            )
            
            # 3. Comparar registros específicos
            records_match = self.compare_specific_records(oracle_data, parquet_data)
            
            # 4. Analizar orden
            order_match = self.analyze_order_differences(oracle_data, parquet_data)
            
            # 5. Resultado final
            print(f"\n{'='*20} RESULTADO DETECTIVE {'='*20}")
            
            keys_match = len(missing_in_parquet) == 0 and len(extra_in_parquet) == 0
            
            print(f"🔑 Claves únicas: {'✅ PERFECTAS' if keys_match else '❌ DIFERENTES'}")
            print(f"📊 Contenido registros: {'✅ IDÉNTICO' if records_match else '❌ DIFERENTE'}")
            print(f"📋 Orden registros: {'✅ IDÉNTICO' if order_match else '❌ DIFERENTE'}")
            
            perfect_match = keys_match and records_match and order_match
            print(f"\n🎯 HOMOLOGACIÓN DETECTIVE: {'✅ 100% PERFECTA' if perfect_match else '❌ REQUIERE AJUSTES'}")
            
            if perfect_match:
                print("🎉 ¡FELICITACIONES! Homologación 'dos gotas de agua' confirmada")
            else:
                print("🔧 Diferencias identificadas - revisar lógica de generación")
            
            return perfect_match
            
        except Exception as e:
            print(f"❌ Error en análisis detective: {e}")
            raise
        
        finally:
            if hasattr(self, 'oracle_conn'):
                self.oracle_conn.close()
            if hasattr(self, 'duck_conn'):
                self.duck_conn.close()

def main():
    """Función principal"""
    try:
        detective = DetectiveAnalysis()
        perfect_match = detective.run_detective_analysis()
        
        sys.exit(0 if perfect_match else 1)
        
    except Exception as e:
        print(f"❌ Error crítico: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
