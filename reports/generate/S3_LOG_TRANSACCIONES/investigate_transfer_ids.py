#!/usr/bin/env python3
"""
Investigación de TransferIDs - Análisis de Diferencias
Compara los TransferIDs entre Oracle y nuestro pipeline
"""

import oracledb
import duckdb
import pandas as pd
import sys
from datetime import datetime

class TransferIDInvestigation:
    def __init__(self):
        self.fecha = '2025-06-15'
        self.setup_connections()
        
    def setup_connections(self):
        """Configura conexiones"""
        try:
            # Oracle
            self.oracle_conn = oracledb.connect(
                user='usr_datalake',
                password='U2024b1mD4t4l4k5',
                dsn='*************:1521/MMONEY'
            )
            
            # DuckDB
            self.duck_conn = duckdb.connect()
            self.duck_conn.sql("INSTALL httpfs;")
            self.duck_conn.sql("LOAD httpfs;")
            
            print("✅ Conexiones establecidas")
            
        except Exception as e:
            print(f"❌ Error en conexiones: {e}")
            raise

    def analyze_oracle_source_data(self):
        """Analiza los datos fuente en Oracle MTX_TRANSACTION_HEADER"""
        print("🔍 ANALIZANDO DATOS FUENTE ORACLE")
        print("=" * 60)
        
        cursor = self.oracle_conn.cursor()
        
        # Verificar datos en MTX_TRANSACTION_HEADER para la fecha
        cursor.execute(f"""
            SELECT COUNT(*), MIN(TRANSFER_ID), MAX(TRANSFER_ID)
            FROM PDP_PROD10_MAINDBBUS.MTX_TRANSACTION_HEADER
            WHERE TRUNC(TRANSFER_DATE) = TO_DATE('{self.fecha}', 'YYYY-MM-DD')
            AND TRANSFER_STATUS IN ('TA','TS')
            AND TRANSFER_VALUE <> 0
        """)
        
        result = cursor.fetchone()
        print(f"📊 MTX_TRANSACTION_HEADER ({self.fecha}):")
        print(f"   Registros: {result[0]:,}")
        print(f"   Min TransferID: {result[1]}")
        print(f"   Max TransferID: {result[2]}")
        
        # Muestra de TransferIDs de MTX_TRANSACTION_HEADER
        cursor.execute(f"""
            SELECT TRANSFER_ID, TRANSFER_DATE, TRANSFER_STATUS, TRANSFER_VALUE
            FROM PDP_PROD10_MAINDBBUS.MTX_TRANSACTION_HEADER
            WHERE TRUNC(TRANSFER_DATE) = TO_DATE('{self.fecha}', 'YYYY-MM-DD')
            AND TRANSFER_STATUS IN ('TA','TS')
            AND TRANSFER_VALUE <> 0
            AND ROWNUM <= 5
            ORDER BY TRANSFER_ID
        """)
        
        mtx_sample = cursor.fetchall()
        print(f"\n📋 Muestra MTX_TRANSACTION_HEADER:")
        for i, row in enumerate(mtx_sample, 1):
            print(f"  {i}. TransferID: {row[0]}, Date: {row[1]}, Status: {row[2]}, Value: {row[3]}")
        
        cursor.close()
        return result[0], result[1], result[2]

    def analyze_oracle_pre_log_trx(self):
        """Analiza los datos en Oracle PRE_LOG_TRX"""
        print(f"\n🔍 ANALIZANDO ORACLE PRE_LOG_TRX")
        print("=" * 60)
        
        cursor = self.oracle_conn.cursor()
        
        # Verificar datos en PRE_LOG_TRX
        cursor.execute(f"""
            SELECT COUNT(*), MIN("TransferID"), MAX("TransferID")
            FROM USR_DATALAKE.PRE_LOG_TRX
            WHERE TRUNC("TransferDate") = TO_DATE('{self.fecha}', 'YYYY-MM-DD')
        """)
        
        result = cursor.fetchone()
        print(f"📊 Oracle PRE_LOG_TRX ({self.fecha}):")
        print(f"   Registros: {result[0]:,}")
        print(f"   Min TransferID: {result[1]}")
        print(f"   Max TransferID: {result[2]}")
        
        # Muestra de TransferIDs de PRE_LOG_TRX
        cursor.execute(f"""
            SELECT "TransferID", "TransferDate", "TransferStatus", "Amount"
            FROM USR_DATALAKE.PRE_LOG_TRX
            WHERE TRUNC("TransferDate") = TO_DATE('{self.fecha}', 'YYYY-MM-DD')
            AND ROWNUM <= 5
            ORDER BY "TransferID"
        """)
        
        pre_log_sample = cursor.fetchall()
        print(f"\n📋 Muestra Oracle PRE_LOG_TRX:")
        for i, row in enumerate(pre_log_sample, 1):
            print(f"  {i}. TransferID: {row[0]}, Date: {row[1]}, Status: {row[2]}, Amount: {row[3]}")
        
        cursor.close()
        return result[0], result[1], result[2]

    def analyze_our_source_data(self):
        """Analiza nuestros datos fuente S3"""
        print(f"\n🔍 ANALIZANDO NUESTROS DATOS FUENTE S3")
        print("=" * 60)
        
        # Verificar datos en S3 MTX_TRANSACTION_HEADER
        mtx_header_path = 's3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_HEADER_ORA/*/*/*/*.parquet'
        
        result = self.duck_conn.execute(f"""
            SELECT COUNT(*), MIN(TRANSFER_ID), MAX(TRANSFER_ID)
            FROM read_parquet('{mtx_header_path}', union_by_name=true)
            WHERE CAST(TRANSFER_DATE AS TIMESTAMP) >= CAST('{self.fecha}' AS TIMESTAMP)
                AND CAST(TRANSFER_DATE AS TIMESTAMP) < CAST('{self.fecha}' AS TIMESTAMP) + INTERVAL '1 day'
                AND TRANSFER_STATUS IN ('TA','TS')
                AND TRANSFER_VALUE <> 0
        """).fetchone()
        
        print(f"📊 S3 MTX_TRANSACTION_HEADER ({self.fecha}):")
        print(f"   Registros: {result[0]:,}")
        print(f"   Min TransferID: {result[1]}")
        print(f"   Max TransferID: {result[2]}")
        
        # Muestra de TransferIDs de S3
        sample_result = self.duck_conn.execute(f"""
            SELECT TRANSFER_ID, TRANSFER_DATE, TRANSFER_STATUS, TRANSFER_VALUE
            FROM read_parquet('{mtx_header_path}', union_by_name=true)
            WHERE CAST(TRANSFER_DATE AS TIMESTAMP) >= CAST('{self.fecha}' AS TIMESTAMP)
                AND CAST(TRANSFER_DATE AS TIMESTAMP) < CAST('{self.fecha}' AS TIMESTAMP) + INTERVAL '1 day'
                AND TRANSFER_STATUS IN ('TA','TS')
                AND TRANSFER_VALUE <> 0
            ORDER BY TRANSFER_ID
            LIMIT 5
        """).fetchall()
        
        print(f"\n📋 Muestra S3 MTX_TRANSACTION_HEADER:")
        for i, row in enumerate(sample_result, 1):
            print(f"  {i}. TransferID: {row[0]}, Date: {row[1]}, Status: {row[2]}, Value: {row[3]}")
        
        return result[0], result[1], result[2]

    def analyze_our_parquet_output(self):
        """Analiza nuestro archivo Parquet generado"""
        print(f"\n🔍 ANALIZANDO NUESTRO PARQUET GENERADO")
        print("=" * 60)
        
        parquet_path = f"TEMP_LOGS_TRANSACCIONES/20250615/PRE_LOG_TRX.parquet"
        
        result = self.duck_conn.execute(f"""
            SELECT COUNT(*), MIN("TransferID"), MAX("TransferID")
            FROM read_parquet('{parquet_path}')
            WHERE CAST("TransferDate" AS DATE) = CAST('{self.fecha}' AS DATE)
        """).fetchone()
        
        print(f"📊 Nuestro PRE_LOG_TRX ({self.fecha}):")
        print(f"   Registros: {result[0]:,}")
        print(f"   Min TransferID: {result[1]}")
        print(f"   Max TransferID: {result[2]}")
        
        # Muestra de nuestros TransferIDs
        sample_result = self.duck_conn.execute(f"""
            SELECT "TransferID", "TransferDate", "TransferStatus", "Amount"
            FROM read_parquet('{parquet_path}')
            WHERE CAST("TransferDate" AS DATE) = CAST('{self.fecha}' AS DATE)
            ORDER BY "TransferID"
            LIMIT 5
        """).fetchall()
        
        print(f"\n📋 Muestra Nuestro PRE_LOG_TRX:")
        for i, row in enumerate(sample_result, 1):
            print(f"  {i}. TransferID: {row[0]}, Date: {row[1]}, Status: {row[2]}, Amount: {row[3]}")
        
        return result[0], result[1], result[2]

    def compare_transfer_id_ranges(self, oracle_mtx, oracle_pre, s3_mtx, our_parquet):
        """Compara los rangos de TransferIDs"""
        print(f"\n🔍 COMPARACIÓN DE RANGOS DE TRANSFERID")
        print("=" * 60)
        
        print(f"📊 CONTEOS:")
        print(f"   Oracle MTX_HEADER:     {oracle_mtx[0]:,} registros")
        print(f"   Oracle PRE_LOG_TRX:    {oracle_pre[0]:,} registros")
        print(f"   S3 MTX_HEADER:         {s3_mtx[0]:,} registros")
        print(f"   Nuestro PRE_LOG_TRX:   {our_parquet[0]:,} registros")
        
        print(f"\n📊 RANGOS MIN TransferID:")
        print(f"   Oracle MTX_HEADER:     {oracle_mtx[1]}")
        print(f"   Oracle PRE_LOG_TRX:    {oracle_pre[1]}")
        print(f"   S3 MTX_HEADER:         {s3_mtx[1]}")
        print(f"   Nuestro PRE_LOG_TRX:   {our_parquet[1]}")
        
        print(f"\n📊 RANGOS MAX TransferID:")
        print(f"   Oracle MTX_HEADER:     {oracle_mtx[2]}")
        print(f"   Oracle PRE_LOG_TRX:    {oracle_pre[2]}")
        print(f"   S3 MTX_HEADER:         {s3_mtx[2]}")
        print(f"   Nuestro PRE_LOG_TRX:   {our_parquet[2]}")
        
        # Análisis de coincidencias
        print(f"\n🎯 ANÁLISIS:")
        oracle_mtx_match_s3 = oracle_mtx[0] == s3_mtx[0]
        oracle_pre_match_ours = oracle_pre[0] == our_parquet[0]
        
        print(f"   Oracle MTX vs S3 MTX:        {'✅ COINCIDE' if oracle_mtx_match_s3 else '❌ DIFERENTE'}")
        print(f"   Oracle PRE vs Nuestro PRE:   {'✅ COINCIDE' if oracle_pre_match_ours else '❌ DIFERENTE'}")
        
        return oracle_mtx_match_s3, oracle_pre_match_ours

    def run_investigation(self):
        """Ejecuta la investigación completa"""
        print("🕵️ INICIANDO INVESTIGACIÓN DE TRANSFERIDS")
        print("=" * 80)
        print(f"📅 Fecha: {self.fecha}")
        print(f"🎯 Objetivo: Identificar por qué los TransferIDs son diferentes")
        
        try:
            # 1. Analizar datos fuente Oracle
            oracle_mtx = self.analyze_oracle_source_data()
            
            # 2. Analizar Oracle PRE_LOG_TRX
            oracle_pre = self.analyze_oracle_pre_log_trx()
            
            # 3. Analizar nuestros datos fuente S3
            s3_mtx = self.analyze_our_source_data()
            
            # 4. Analizar nuestro Parquet generado
            our_parquet = self.analyze_our_parquet_output()
            
            # 5. Comparar rangos
            mtx_match, pre_match = self.compare_transfer_id_ranges(
                oracle_mtx, oracle_pre, s3_mtx, our_parquet
            )
            
            # 6. Conclusión
            print(f"\n{'='*20} CONCLUSIÓN INVESTIGACIÓN {'='*20}")
            
            if mtx_match and pre_match:
                print("✅ Los datos fuente coinciden - problema en lógica de procesamiento")
            elif mtx_match and not pre_match:
                print("⚠️  Datos fuente coinciden pero resultado final no - revisar SP_PRE_LOG_TRX")
            elif not mtx_match:
                print("❌ Los datos fuente son diferentes - revisar filtros de fecha/criterios")
            
            return mtx_match and pre_match
            
        except Exception as e:
            print(f"❌ Error en investigación: {e}")
            raise
        
        finally:
            if hasattr(self, 'oracle_conn'):
                self.oracle_conn.close()
            if hasattr(self, 'duck_conn'):
                self.duck_conn.close()

def main():
    """Función principal"""
    try:
        investigator = TransferIDInvestigation()
        success = investigator.run_investigation()
        
        sys.exit(0 if success else 1)
        
    except Exception as e:
        print(f"❌ Error crítico: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
