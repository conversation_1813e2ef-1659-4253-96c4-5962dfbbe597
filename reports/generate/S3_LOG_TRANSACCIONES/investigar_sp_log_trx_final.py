#!/usr/bin/env python3
"""
Script para investigar la lógica final de SP_LOG_TRX
En SP_LOG_TRX línea 57 Oracle usa USER_ACCOUNT_HISTORY.ATTR8_OLD para el valor final
"""

import oracledb
import sys

def investigar_sp_log_trx_logica():
    """Investiga la lógica exacta de SP_LOG_TRX"""
    print("🔍 INVESTIGANDO LÓGICA DE SP_LOG_TRX")
    print("=" * 60)
    
    try:
        connection = oracledb.connect(
            user='usr_datalake',
            password='U2024b1mD4t4l4k5',
            dsn='*************:1521/MMONEY'
        )
        
        cursor = connection.cursor()
        transfer_id = '***************'
        from_id = '945661'
        
        print("1️⃣ DATOS EN PRE_LOG_TRX:")
        print("-" * 40)
        
        # Obtener datos de PRE_LOG_TRX
        cursor.execute("""
            SELECT 
                "TransferID",
                "FromID_Mobiquity",
                "From_AccountID_Mobiquity",
                "From_BankDomain"
            FROM USR_DATALAKE.PRE_LOG_TRX
            WHERE "TransferID" = :transfer_id
        """, {'transfer_id': transfer_id})
        
        pre_log_data = cursor.fetchall()
        if pre_log_data:
            row = pre_log_data[0]
            print(f"  TransferID: {row[0]}")
            print(f"  FromID_Mobiquity: {row[1]}")
            print(f"  From_AccountID_Mobiquity: {row[2]} ⭐ VALOR EN PRE_LOG_TRX")
            print(f"  From_BankDomain: {row[3]}")
            
            pre_log_account = row[2]
            from_bank_domain = row[3]
        else:
            print("  ❌ No encontrado en PRE_LOG_TRX")
            return
        
        print("\n2️⃣ LÓGICA DE SP_LOG_TRX (líneas 32-59):")
        print("-" * 40)
        print("Oracle usa esta lógica para FromAccountID:")
        print("  CASE")
        print("    WHEN MTH.\"TransactionType\" IN ('CUSTODY_ACCOUNTS_TRANSFER','EXTERNAL_PAYMENT','TRANSFER_TO_ANY_BANK_ACCOUNT') THEN ''")
        print("    WHEN MTH.\"From_BankDomain\" = 'BNACION' THEN '1334853'")
        print("    WHEN MTH.\"From_BankDomain\" = 'CCUSCO' THEN '1464437'")
        print("    WHEN MTH.\"From_BankDomain\" = 'CRANDES' THEN '1414519'")
        print("    WHEN MTH.\"From_BankDomain\" = '0231FCONFIANZA' THEN '1882233'")
        print("    WHEN MTH.\"From_BankDomain\" = '0144QAPAQ' THEN '1131834'")
        print("    WHEN MTH.\"From_BankDomain\" = 'FCOMPARTAMOS' THEN '1188057'")
        print("    WHEN H_PAYER.USER_ID IS NOT NULL AND H_PAYER.ACCOUNT_ID = MTH.\"From_AccountID_Mobiquity\" THEN H_PAYER.ATTR8_OLD")
        print("    ELSE MTH.\"From_AccountID\"")
        print("  END AS FromAccountID")
        
        print("\n3️⃣ VERIFICACIÓN DE BANK_DOMAIN:")
        print("-" * 40)
        
        # Verificar si aplica alguna regla de Bank_Domain
        bank_domain_rules = {
            'BNACION': '1334853',
            'CCUSCO': '1464437', 
            'CRANDES': '1414519',
            '0231FCONFIANZA': '1882233',
            '0144QAPAQ': '1131834',
            'FCOMPARTAMOS': '1188057'
        }
        
        if from_bank_domain in bank_domain_rules:
            expected_value = bank_domain_rules[from_bank_domain]
            print(f"  ✅ REGLA BANK_DOMAIN APLICADA:")
            print(f"     From_BankDomain: {from_bank_domain}")
            print(f"     Valor mapeado: {expected_value}")
            print(f"  🎯 ESTA ES LA EXPLICACIÓN: Oracle usa mapeo hardcodeado por Bank_Domain")
        else:
            print(f"  ❌ No aplica regla de Bank_Domain: {from_bank_domain}")
            
            # Verificar USER_ACCOUNT_HISTORY
            print("\n4️⃣ VERIFICACIÓN DE USER_ACCOUNT_HISTORY:")
            print("-" * 40)
            
            cursor.execute("""
                SELECT 
                    USER_ID,
                    ACCOUNT_ID,
                    ATTR8_OLD
                FROM USER_ACCOUNT_HISTORY
                WHERE USER_ID = :from_id
                AND ACCOUNT_ID = :pre_log_account
            """, {'from_id': from_id, 'pre_log_account': pre_log_account})
            
            history_data = cursor.fetchall()
            if history_data:
                row = history_data[0]
                print(f"  ✅ MATCH EN USER_ACCOUNT_HISTORY:")
                print(f"     USER_ID: {row[0]}")
                print(f"     ACCOUNT_ID: {row[1]}")
                print(f"     ATTR8_OLD: {row[2]} ⭐ ESTE SERÍA EL VALOR FINAL")
            else:
                print(f"  ❌ No hay match en USER_ACCOUNT_HISTORY")
                print(f"     Buscando USER_ID={from_id} AND ACCOUNT_ID={pre_log_account}")
        
        print("\n5️⃣ RESULTADO EN LOG_TRX:")
        print("-" * 40)
        
        # Verificar qué tiene LOG_TRX
        cursor.execute("""
            SELECT 
                "TransferID",
                "FromID",
                "FromAccountID"
            FROM USR_DATALAKE.LOG_TRX
            WHERE "TransferID" = :transfer_id
        """, {'transfer_id': transfer_id})
        
        log_trx_data = cursor.fetchall()
        if log_trx_data:
            row = log_trx_data[0]
            print(f"  TransferID: {row[0]}")
            print(f"  FromID: {row[1]}")
            print(f"  FromAccountID: {row[2]} ⭐ VALOR FINAL EN LOG_TRX")
        else:
            print("  ❌ No encontrado en LOG_TRX")
        
        cursor.close()
        connection.close()
        
    except Exception as e:
        print(f"❌ Error investigando SP_LOG_TRX: {e}")

def main():
    print("🚀 INVESTIGACIÓN FINAL: LÓGICA DE SP_LOG_TRX")
    print("=" * 80)
    print("Analizando por qué Oracle produce el valor final diferente")
    print()
    
    investigar_sp_log_trx_logica()
    
    print("\n💡 CONCLUSIÓN:")
    print("=" * 60)
    print("1. Oracle usa lógica de mapeo hardcodeado por Bank_Domain en SP_LOG_TRX")
    print("2. Si no aplica Bank_Domain, usa USER_ACCOUNT_HISTORY.ATTR8_OLD")
    print("3. Tu implementación S3 NO incluye esta lógica de SP_LOG_TRX")
    print("4. Necesitas replicar TODA la lógica de SP_LOG_TRX, no solo SP_PRE_LOG_TRX")

if __name__ == "__main__":
    main()
