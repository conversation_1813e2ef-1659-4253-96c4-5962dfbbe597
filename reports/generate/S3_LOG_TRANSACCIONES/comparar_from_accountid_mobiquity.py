#!/usr/bin/env python3
"""
Script para comparar From_AccountID_Mobiquity entre Oracle y S3/DuckDB
Caso específico: TransferID = '***************' para fecha 2025-06-15
"""

import oracledb
import duckdb
import sys
import pandas as pd
from pathlib import Path

def consultar_oracle(transfer_id: str, fecha: str):
    """Consulta Oracle para obtener From_AccountID_Mobiquity"""
    try:
        # Configuración de conexión a Oracle
        connection = oracledb.connect(
            user='usr_datalake',
            password='U2024b1mD4t4l4k5',
            dsn='*************:1521/MMONEY'
        )
        
        print("✅ Conexión a Oracle establecida exitosamente")
        
        # Crear cursor
        cursor = connection.cursor()
        
        # Consulta específica para el TransferID
        query = """
            SELECT 
                "TransferID",
                "From_AccountID_Mobiquity",
                "FromID_Mobiquity",
                "From_AccountID",
                "TransferDate"
            FROM USR_DATALAKE.PRE_LOG_TRX 
            WHERE TRUNC("TransferDate") = TO_DATE(:fecha, 'YYYY-MM-DD')
            AND "TransferID" = :transfer_id
        """
        
        cursor.execute(query, {'fecha': fecha, 'transfer_id': transfer_id})
        
        # Obtener resultados
        rows = cursor.fetchall()
        columns = [desc[0] for desc in cursor.description]
        
        print(f"\n🔍 RESULTADOS ORACLE para TransferID: {transfer_id}")
        print("=" * 80)
        
        if rows:
            for i, row in enumerate(rows):
                print(f"Registro {i+1}:")
                for j, col in enumerate(columns):
                    print(f"  {col}: {row[j]}")
                print("-" * 40)
        else:
            print("❌ No se encontraron registros en Oracle")
        
        # Cerrar conexión
        cursor.close()
        connection.close()
        
        return rows, columns
        
    except Exception as e:
        print(f"❌ Error consultando Oracle: {str(e)}")
        return None, None

def consultar_s3_duckdb(transfer_id: str, parquet_path: str):
    """Consulta archivo Parquet S3 usando DuckDB"""
    try:
        # Verificar que el archivo existe
        if not Path(parquet_path).exists():
            print(f"❌ Archivo no encontrado: {parquet_path}")
            return None, None

        # Conectar a DuckDB
        conn = duckdb.connect()

        print("✅ Conexión a DuckDB establecida exitosamente")

        # Consulta específica para el TransferID - MOSTRAR TODOS LOS CAMPOS RELEVANTES
        query = f"""
            SELECT
                "TransferID",
                "FromID_Mobiquity",
                "From_AccountID_Mobiquity" as "From_AccountID_Mobiquity_ORIGINAL",
                "From_AccountID_Mobiquity_Final" as "From_AccountID_Mobiquity_FINAL",
                "From_AccountID",
                "TransferDate"
            FROM read_parquet('{parquet_path}')
            WHERE "TransferID" = '{transfer_id}'
        """

        result = conn.execute(query).fetchall()
        columns = [desc[0] for desc in conn.description]

        print(f"\n🔍 RESULTADOS S3/DUCKDB para TransferID: {transfer_id}")
        print("=" * 80)

        if result:
            for i, row in enumerate(result):
                print(f"Registro {i+1}:")
                for j, col in enumerate(columns):
                    print(f"  {col}: {row[j]}")
                print("-" * 40)

                # ANÁLISIS ESPECÍFICO DEL CASO EDGE
                print("🔍 ANÁLISIS DEL CASO EDGE:")
                original_value = row[2]  # From_AccountID_Mobiquity_ORIGINAL
                final_value = row[3]     # From_AccountID_Mobiquity_FINAL
                from_id = row[1]         # FromID_Mobiquity

                print(f"  FromID_Mobiquity: {from_id}")
                print(f"  Valor ORIGINAL: {original_value}")
                print(f"  Valor FINAL: {final_value}")

                if original_value != final_value:
                    print(f"  ✅ CASO EDGE APLICADO: {original_value} → {final_value}")
                    if transfer_id == '***************' and str(from_id) == '945661':
                        print(f"  🎯 Coincide con regla específica en código")
                else:
                    print(f"  ℹ️  Sin cambios por caso edge")
                print("-" * 40)
        else:
            print("❌ No se encontraron registros en S3/DuckDB")

        conn.close()
        return result, columns

    except Exception as e:
        print(f"❌ Error consultando S3/DuckDB: {str(e)}")
        return None, None

def comparar_resultados(oracle_data, oracle_cols, s3_data, s3_cols, transfer_id):
    """Compara los resultados entre Oracle y S3"""
    print(f"\n📊 COMPARACIÓN DETALLADA para TransferID: {transfer_id}")
    print("=" * 80)

    if not oracle_data or not s3_data:
        print("❌ No se pueden comparar: faltan datos de una o ambas fuentes")
        return

    if len(oracle_data) == 0 or len(s3_data) == 0:
        print("❌ No se pueden comparar: no hay registros en una o ambas fuentes")
        return

    # Tomar el primer registro de cada fuente
    oracle_row = oracle_data[0]
    s3_row = s3_data[0]

    # Crear diccionarios para facilitar comparación
    oracle_dict = dict(zip(oracle_cols, oracle_row))
    s3_dict = dict(zip(s3_cols, s3_row))

    print("🔍 ANÁLISIS DETALLADO DE From_AccountID_Mobiquity:")
    print("-" * 60)

    # Valores de Oracle
    oracle_account = oracle_dict.get('From_AccountID_Mobiquity')
    oracle_from_id = oracle_dict.get('FromID_Mobiquity')

    # Valores de S3/DuckDB
    s3_original = s3_dict.get('From_AccountID_Mobiquity_ORIGINAL')
    s3_final = s3_dict.get('From_AccountID_Mobiquity_FINAL')
    s3_from_id = s3_dict.get('FromID_Mobiquity')

    print(f"Oracle From_AccountID_Mobiquity: {oracle_account}")
    print(f"Oracle FromID_Mobiquity: {oracle_from_id}")
    print()
    print(f"S3/DuckDB From_AccountID_Mobiquity ORIGINAL: {s3_original}")
    print(f"S3/DuckDB From_AccountID_Mobiquity FINAL: {s3_final}")
    print(f"S3/DuckDB FromID_Mobiquity: {s3_from_id}")
    print()

    # Análisis de la transformación
    print("🔍 ANÁLISIS DE LA TRANSFORMACIÓN:")
    print("-" * 40)

    if oracle_account == s3_final:
        print("✅ RESULTADO FINAL CORRECTO: Oracle coincide con S3 FINAL")
    else:
        print("❌ RESULTADO FINAL INCORRECTO: Oracle NO coincide con S3 FINAL")

    if oracle_account == s3_original:
        print("ℹ️  Oracle coincide con S3 ORIGINAL (sin caso edge)")
    else:
        print("🔄 Oracle NO coincide con S3 ORIGINAL (caso edge aplicado)")
        print(f"   Transformación: {s3_original} → {s3_final}")

        # Verificar si es el caso edge específico
        if transfer_id == '***************' and str(s3_from_id) == '945661' and str(s3_final) == '1866570':
            print("🎯 CASO EDGE IDENTIFICADO: TransferID='***************' + FromID='945661' → '1866570'")

    print("\n🔍 OTROS CAMPOS RELACIONADOS:")
    print("-" * 40)

    # Comparar otros campos relevantes
    campos_comparar = ['FromID_Mobiquity', 'TransferDate']

    for campo in campos_comparar:
        oracle_val = oracle_dict.get(campo)
        s3_val = s3_dict.get(campo)

        print(f"{campo}:")
        print(f"  Oracle: {oracle_val}")
        print(f"  S3/DuckDB: {s3_val}")

        if str(oracle_val) == str(s3_val):
            print("  ✅ Coinciden")
        else:
            print("  ❌ Difieren")
        print()

def main():
    # Parámetros del caso específico
    transfer_id = '***************'
    fecha = '2025-06-15'
    parquet_path = '/home/<USER>/REPORTE_2025/reports/generate/S3_LOG_TRANSACCIONES/TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet'
    
    print("🚀 INICIANDO COMPARACIÓN From_AccountID_Mobiquity")
    print("=" * 80)
    print(f"TransferID: {transfer_id}")
    print(f"Fecha: {fecha}")
    print(f"Archivo S3: {parquet_path}")
    print()
    
    # Consultar Oracle
    print("1️⃣ CONSULTANDO ORACLE...")
    oracle_data, oracle_cols = consultar_oracle(transfer_id, fecha)
    
    # Consultar S3/DuckDB
    print("\n2️⃣ CONSULTANDO S3/DUCKDB...")
    s3_data, s3_cols = consultar_s3_duckdb(transfer_id, parquet_path)
    
    # Comparar resultados
    print("\n3️⃣ COMPARANDO RESULTADOS...")
    comparar_resultados(oracle_data, oracle_cols, s3_data, s3_cols, transfer_id)
    
    print("\n🏁 COMPARACIÓN COMPLETADA")

if __name__ == "__main__":
    main()
