#!/usr/bin/env python3
"""
Investigar si WALLET_NUMBER en Oracle es diferente al que tenemos en S3
"""

import oracledb
import sys

def investigar_wallet_number_oracle():
    """Investiga WALLET_NUMBER en Oracle vs lo que esperamos"""
    print("🔍 INVESTIGACIÓN: WALLET_NUMBER Oracle vs S3")
    print("=" * 80)
    
    try:
        connection = oracledb.connect(
            user='usr_datalake',
            password='U2024b1mD4t4l4k5',
            dsn='10.240.131.10:1521/MMONEY'
        )
        
        cursor = connection.cursor()
        
        print("1️⃣ VERIFICAR WALLET_NUMBER EN USER_DATA_TRX ORACLE:")
        print("-" * 60)
        
        # Casos específicos problemáticos
        casos = [
            ('3262191', '501101120105533600'),
            ('3662073', '501101120105554646'),
            ('2078452', '501101120105554650'),
            ('1932101', '501101120105554643')
        ]
        
        for to_id, oracle_account in casos:
            print(f"\n📋 CASO ToID: {to_id}")
            
            # Verificar WALLET_NUMBER en USER_DATA_TRX
            cursor.execute("""
                SELECT 
                    USER_ID,
                    O_USER_ID,
                    WALLET_NUMBER,
                    PROFILE_TRX
                FROM USR_DATALAKE.USER_DATA_TRX
                WHERE O_USER_ID = :user_id
            """, {'user_id': to_id})
            
            user_data = cursor.fetchone()
            if user_data:
                wallet_number = user_data[2]
                print(f"  USER_DATA_TRX WALLET_NUMBER: {wallet_number}")
                print(f"  Oracle AccountID esperado: {oracle_account}")
                print(f"  ¿Coincide? {'✅' if str(wallet_number) == str(oracle_account) else '❌'}")
                
                # Si no coincide, verificar si hay alguna transformación
                if str(wallet_number) != str(oracle_account):
                    print(f"  🔍 INVESTIGANDO TRANSFORMACIÓN:")
                    
                    # Verificar en MTX_WALLET original
                    cursor.execute("""
                        SELECT 
                            MW.USER_ID,
                            MW.WALLET_NUMBER,
                            MW.ISSUER_ID,
                            MW.USER_GRADE
                        FROM PDP_PROD10_MAINDBBUS.MTX_WALLET MW
                        WHERE MW.USER_ID = :user_id
                    """, {'user_id': to_id})
                    
                    wallet_data = cursor.fetchall()
                    print(f"    MTX_WALLET encontró {len(wallet_data)} registros:")
                    for row in wallet_data:
                        print(f"      USER_ID: {row[0]}")
                        print(f"      WALLET_NUMBER: {row[1]} ⭐ ORIGINAL")
                        print(f"      ISSUER_ID: {row[2]}")
                        print(f"      USER_GRADE: {row[3]}")
                        
                        # Verificar si este WALLET_NUMBER coincide con Oracle AccountID
                        if str(row[1]) == str(oracle_account):
                            print(f"      ✅ ESTE WALLET_NUMBER COINCIDE CON Oracle AccountID")
                        else:
                            print(f"      ❌ No coincide con Oracle AccountID")
            else:
                print(f"  ❌ No encontrado en USER_DATA_TRX")
        
        print(f"\n2️⃣ VERIFICAR LÓGICA DE USER_DATA_TRX:")
        print("-" * 60)
        
        # Verificar cómo se construye USER_DATA_TRX
        print("  Según SP_PRE_LOG_USR.sql:")
        print("  WALLET_NUMBER AS ACCOUNT_ID")
        print("  CASE")
        print("      WHEN UP.ATTR8 IS NOT NULL THEN UP.ATTR8")
        print("      WHEN LENGTH(REPLACE(MW.WALLET_NUMBER,'UA.',''))>15 THEN SUBSTR(REPLACE(MW.WALLET_NUMBER,'UA.',''),-15)")
        print("      ELSE REPLACE(MW.WALLET_NUMBER,'UA.','')")
        print("  END AS WALLET_NUMBER")
        
        # Verificar si hay ATTR8 para estos usuarios
        for to_id, oracle_account in casos:
            print(f"\n  📋 Verificando ATTR8 para ToID: {to_id}")
            
            cursor.execute("""
                SELECT 
                    UP.USER_ID,
                    UP.ATTR8,
                    MW.WALLET_NUMBER AS ORIGINAL_WALLET,
                    CASE 
                        WHEN UP.ATTR8 IS NOT NULL THEN UP.ATTR8
                        WHEN LENGTH(REPLACE(MW.WALLET_NUMBER,'UA.',''))>15 THEN SUBSTR(REPLACE(MW.WALLET_NUMBER,'UA.',''),-15)
                        ELSE REPLACE(MW.WALLET_NUMBER,'UA.','')
                    END AS COMPUTED_WALLET
                FROM PDP_PROD10_MAINDB.USER_PROFILE UP
                LEFT JOIN PDP_PROD10_MAINDBBUS.MTX_WALLET MW ON UP.USER_ID = MW.USER_ID
                WHERE UP.USER_ID = :user_id
            """, {'user_id': to_id})
            
            attr_data = cursor.fetchall()
            for row in attr_data:
                print(f"    USER_ID: {row[0]}")
                print(f"    ATTR8: {row[1]} ⭐ VALOR ESPECIAL")
                print(f"    ORIGINAL_WALLET: {row[2]}")
                print(f"    COMPUTED_WALLET: {row[3]} ⭐ RESULTADO LÓGICA")
                print(f"    Oracle AccountID: {oracle_account}")
                
                # Verificar cuál coincide
                if str(row[1]) == str(oracle_account):
                    print(f"    ✅ ATTR8 COINCIDE con Oracle AccountID")
                elif str(row[3]) == str(oracle_account):
                    print(f"    ✅ COMPUTED_WALLET COINCIDE con Oracle AccountID")
                else:
                    print(f"    ❌ Ninguno coincide con Oracle AccountID")
        
        print(f"\n3️⃣ VERIFICAR CASOS ESPECIALES:")
        print("-" * 60)
        
        # Verificar si hay casos especiales como el USER_ID 945661
        for to_id, oracle_account in casos:
            print(f"\n  📋 Casos especiales para ToID: {to_id}")
            
            # Verificar si hay múltiples wallets
            cursor.execute("""
                SELECT 
                    MW.USER_ID,
                    MW.WALLET_NUMBER,
                    MW.STATUS,
                    MW.ISSUER_ID,
                    ROW_NUMBER() OVER (PARTITION BY MW.USER_ID ORDER BY MW.WALLET_NUMBER) as RN
                FROM PDP_PROD10_MAINDBBUS.MTX_WALLET MW
                WHERE MW.USER_ID = :user_id
                ORDER BY MW.WALLET_NUMBER
            """, {'user_id': to_id})
            
            multiple_wallets = cursor.fetchall()
            if len(multiple_wallets) > 1:
                print(f"    ⚠️  MÚLTIPLES WALLETS encontrados ({len(multiple_wallets)}):")
                for row in multiple_wallets:
                    print(f"      WALLET: {row[1]}, STATUS: {row[2]}, RN: {row[4]}")
                    if str(row[1]) == str(oracle_account):
                        print(f"        ✅ ESTE WALLET COINCIDE con Oracle AccountID")
            else:
                print(f"    ✅ Solo un wallet encontrado")
        
        cursor.close()
        connection.close()
        
        print(f"\n4️⃣ CONCLUSIONES:")
        print("-" * 60)
        print("1. Verificar si ATTR8 contiene el valor correcto")
        print("2. Verificar si hay múltiples wallets y Oracle usa uno específico")
        print("3. Verificar si la lógica de transformación es diferente")
        print("4. Posible problema en la sincronización de USER_DATA_TRX")
        
    except Exception as e:
        print(f"❌ Error en investigación: {e}")
        import traceback
        traceback.print_exc()

def main():
    print("🚀 INVESTIGACIÓN WALLET_NUMBER Oracle")
    print("=" * 80)
    print("Comparando WALLET_NUMBER Oracle vs S3")
    print()
    
    investigar_wallet_number_oracle()
    
    print("\n🏁 INVESTIGACIÓN COMPLETADA")

if __name__ == "__main__":
    main()
