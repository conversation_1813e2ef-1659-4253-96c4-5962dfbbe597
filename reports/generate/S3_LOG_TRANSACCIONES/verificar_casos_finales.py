#!/usr/bin/env python3
"""
Verificar los 2 casos finales para lograr 100% perfección absoluta
"""

import oracledb

def verificar_casos_finales():
    """Verifica los 2 casos finales que impiden 100% perfección"""
    print("🔍 VERIFICACIÓN CASOS FINALES PARA 100% PERFECCIÓN")
    print("=" * 80)
    
    casos_problematicos = [
        ('3212666', '4091684', '501101130003212666'),
        ('1608372', '2521275', '501101130001608372')
    ]
    
    try:
        connection = oracledb.connect(
            user='usr_datalake',
            password='U2024b1mD4t4l4k5',
            dsn='10.240.131.10:1521/MMONEY'
        )
        
        cursor = connection.cursor()
        
        for user_id, oracle_wallet, s3_wallet in casos_problematicos:
            print(f"\n📋 CASO: USER_ID {user_id}")
            print("-" * 60)
            
            # Verificar en MTX_WALLET
            cursor.execute("""
                SELECT 
                    MW.USER_ID,
                    MW.WALLET_NUMBER,
                    MW.STATUS,
                    MW.MODIFIED_ON,
                    LENGTH(MW.WALLET_NUMBER) as WALLET_LENGTH
                FROM PDP_PROD10_MAINDBBUS.MTX_WALLET MW
                WHERE MW.USER_ID = :user_id
                ORDER BY MW.MODIFIED_ON DESC
            """, {'user_id': user_id})
            
            wallets = cursor.fetchall()
            print(f"  MTX_WALLET encontró {len(wallets)} wallets:")
            
            for i, row in enumerate(wallets):
                status_icon = "✅" if row[2] == 'Y' else "❌"
                length_icon = "🔢" if row[4] > 15 else "📱"
                print(f"    {i+1}. {status_icon} {length_icon} {row[1]} (STATUS: {row[2]}, LENGTH: {row[4]})")
            
            # Verificar en USER_PROFILE para ATTR8
            cursor.execute("""
                SELECT 
                    UP.USER_ID,
                    UP.ATTR8,
                    LENGTH(UP.ATTR8) as ATTR8_LENGTH
                FROM PDP_PROD10_MAINDBBUS.USER_PROFILE UP
                WHERE UP.USER_ID = :user_id
            """, {'user_id': user_id})
            
            profile_data = cursor.fetchone()
            if profile_data:
                print(f"  USER_PROFILE ATTR8: {profile_data[1]} (LENGTH: {profile_data[2]})")
            else:
                print(f"  USER_PROFILE: No encontrado")
            
            # Verificar en USER_DATA_TRX (S3)
            print(f"  Oracle usa: {oracle_wallet}")
            print(f"  S3 usa: {s3_wallet}")
            
            # Analizar patrón
            if oracle_wallet in s3_wallet:
                print(f"  🔍 PATRÓN: Oracle wallet está contenido en S3 wallet")
            elif s3_wallet.endswith(oracle_wallet):
                print(f"  🔍 PATRÓN: S3 wallet termina con Oracle wallet")
            else:
                print(f"  🔍 PATRÓN: Wallets completamente diferentes")
            
            # Verificar lógica de SP_PRE_LOG_USR
            print(f"  📋 LÓGICA ORACLE SP_PRE_LOG_USR:")
            if profile_data and profile_data[1]:
                print(f"    ATTR8 existe: {profile_data[1]} → Debería usar ATTR8")
            else:
                print(f"    ATTR8 no existe → Debería usar WALLET_NUMBER procesado")
                
                # Encontrar wallet con STATUS='Y'
                wallet_activo = None
                for row in wallets:
                    if row[2] == 'Y':
                        wallet_activo = row[1]
                        break
                
                if wallet_activo:
                    print(f"    Wallet STATUS='Y': {wallet_activo}")
                    if len(str(wallet_activo)) > 15:
                        wallet_procesado = str(wallet_activo)[-15:]
                        print(f"    Wallet procesado (últimos 15): {wallet_procesado}")
                    else:
                        print(f"    Wallet procesado: {wallet_activo}")
        
        cursor.close()
        connection.close()
        
        print(f"\n🎯 CONCLUSIONES:")
        print("-" * 60)
        print("1. Verificar si estos usuarios tienen ATTR8 especial")
        print("2. Verificar si la lógica de STATUS='Y' es correcta")
        print("3. Implementar corrección específica para estos 2 casos")
        print("4. Lograr 100% PERFECCIÓN ABSOLUTA")
        
    except Exception as e:
        print(f"❌ Error en verificación: {e}")
        import traceback
        traceback.print_exc()

def main():
    print("🚀 VERIFICACIÓN CASOS FINALES")
    print("=" * 80)
    print("OBJETIVO: 100% PERFECCIÓN ABSOLUTA")
    print()
    
    verificar_casos_finales()
    
    print("\n🏁 VERIFICACIÓN COMPLETADA")

if __name__ == "__main__":
    main()
