#!/usr/bin/env python3
"""
Investigación exhaustiva de TODA la lógica oculta de Oracle
para lograr homologación 100% perfecta
"""

import oracledb
import sys

def investigar_logica_completa():
    """Investiga TODA la lógica oculta de Oracle paso a paso"""
    print("🔍 INVESTIGACIÓN EXHAUSTIVA DE LÓGICA OCULTA ORACLE")
    print("=" * 80)
    
    transfer_id = '175003230421458'
    from_id = '945661'
    to_id = '945661'
    
    try:
        connection = oracledb.connect(
            user='usr_datalake',
            password='U2024b1mD4t4l4k5',
            dsn='10.240.131.10:1521/MMONEY'
        )
        
        cursor = connection.cursor()
        
        print("1️⃣ ANÁLISIS DE MTX_TRANSACTION_HEADER ORIGINAL:")
        print("-" * 60)
        
        # Obtener datos originales de MTX_TRANSACTION_HEADER
        cursor.execute("""
            SELECT
                TRANSFER_ID,
                PAYER_USER_ID,
                PAYEE_USER_ID,
                SERVICE_TYPE,
                TRANSFER_STATUS,
                FIELD7,
                REMARKS
            FROM PDP_PROD10_MAINDBBUS.MTX_TRANSACTION_HEADER
            WHERE TRANSFER_ID = :transfer_id
        """, {'transfer_id': transfer_id})
        
        mth_data = cursor.fetchall()
        if mth_data:
            row = mth_data[0]
            print(f"  TRANSFER_ID: {row[0]}")
            print(f"  PAYER_USER_ID: {row[1]} (FromID)")
            print(f"  PAYEE_USER_ID: {row[2]} (ToID)")
            print(f"  SERVICE_TYPE: {row[3]}")
            print(f"  TRANSFER_STATUS: {row[4]}")
            print(f"  FIELD7: {row[5]} (Comment)")
            print(f"  REMARKS: {row[6]}")

            field7_comment = row[5]
        else:
            print("  ❌ No encontrado en MTX_TRANSACTION_HEADER")
            return
        
        print("\n2️⃣ ANÁLISIS DE USER_DATA_TRX (LÓGICA ATTR8):")
        print("-" * 60)
        
        # Verificar USER_DATA_TRX para ambos usuarios
        cursor.execute("""
            SELECT 
                USER_ID,
                O_USER_ID,
                WALLET_NUMBER
            FROM USR_DATALAKE.USER_DATA_TRX
            WHERE O_USER_ID IN (:from_id, :to_id)
            ORDER BY O_USER_ID
        """, {'from_id': from_id, 'to_id': to_id})
        
        user_data_results = cursor.fetchall()
        user_data_map = {}
        
        for row in user_data_results:
            user_data_map[row[1]] = row[2]  # O_USER_ID -> WALLET_NUMBER
            print(f"  O_USER_ID {row[1]}: WALLET_NUMBER = {row[2]}")
        
        print("\n3️⃣ ANÁLISIS DE USER_ACCOUNT_HISTORY (LÓGICA OCULTA):")
        print("-" * 60)
        
        # Buscar en USER_ACCOUNT_HISTORY con diferentes criterios
        print("  🔍 Buscando matches en USER_ACCOUNT_HISTORY...")
        
        # Criterio 1: USER_ID = FromID_Mobiquity AND ACCOUNT_ID = From_AccountID_Mobiquity
        cursor.execute("""
            SELECT 
                USER_ID,
                ACCOUNT_ID,
                ATTR7_OLD,
                ATTR8_OLD,
                'MATCH_FROM_CRITERIA' as MATCH_TYPE
            FROM USER_ACCOUNT_HISTORY
            WHERE USER_ID = :from_id
        """, {'from_id': from_id})
        
        history_results = cursor.fetchall()
        
        if history_results:
            print(f"  ✅ Encontrados {len(history_results)} registros para USER_ID {from_id}:")
            for i, row in enumerate(history_results):
                print(f"    [{i+1}] USER_ID: {row[0]}, ACCOUNT_ID: {row[1]}")
                print(f"        ATTR7_OLD: {row[2]}, ATTR8_OLD: {row[3]}")
                print(f"        MATCH_TYPE: {row[4]}")
        else:
            print(f"  ❌ No se encontraron registros en USER_ACCOUNT_HISTORY para USER_ID {from_id}")
        
        # Criterio 2: Buscar por diferentes ACCOUNT_ID
        print(f"\n  🔍 Buscando por ACCOUNT_ID específicos...")
        
        account_ids_to_check = [
            user_data_map.get(from_id),  # USER_DATA_TRX.WALLET_NUMBER
            '1866570',  # Valor que aparece en Oracle PRE_LOG_TRX
            '501101120105612302',  # Otro valor observado
            '*************'  # Valor de S3
        ]
        
        for account_id in account_ids_to_check:
            if account_id:
                cursor.execute("""
                    SELECT 
                        USER_ID,
                        ACCOUNT_ID,
                        ATTR7_OLD,
                        ATTR8_OLD
                    FROM USER_ACCOUNT_HISTORY
                    WHERE ACCOUNT_ID = :account_id
                """, {'account_id': account_id})
                
                account_results = cursor.fetchall()
                if account_results:
                    print(f"    ✅ ACCOUNT_ID {account_id}:")
                    for row in account_results:
                        print(f"      USER_ID: {row[0]}, ATTR7_OLD: {row[2]}, ATTR8_OLD: {row[3]}")
                else:
                    print(f"    ❌ ACCOUNT_ID {account_id}: No encontrado")
        
        print("\n4️⃣ ANÁLISIS DE LÓGICA DE COMMENT (FIELD7):")
        print("-" * 60)
        
        print(f"  MTX_TRANSACTION_HEADER.FIELD7: {field7_comment}")
        print(f"  Oracle PRE_LOG_TRX.Comment: NULL (según homologación)")
        print(f"  S3 PRE_LOG_TRX.Comment: '' (string vacío)")
        
        # Verificar si SP_PRE_LOG_TRX_UPDATE afecta este TransferID
        cursor.execute("""
            SELECT 
                "TransferID",
                "Comment",
                "TransactionType",
                "From_Msisdn",
                "To_Msisdn"
            FROM USR_DATALAKE.PRE_LOG_TRX
            WHERE "TransferID" = :transfer_id
        """, {'transfer_id': transfer_id})
        
        pre_log_data = cursor.fetchall()
        if pre_log_data:
            row = pre_log_data[0]
            print(f"  Oracle PRE_LOG_TRX.Comment actual: {row[1]}")
            print(f"  TransactionType: {row[2]}")
            print(f"  From_Msisdn: {row[3]}")
            print(f"  To_Msisdn: {row[4]}")
            
            # Verificar si aplica lógica de SP_PRE_LOG_TRX_UPDATE
            if row[2] == 'REVERSAL' and (row[3] == '***********' or row[4] == '***********'):
                print("  🔍 APLICA lógica de SP_PRE_LOG_TRX_UPDATE (REVERSAL + MSISDN específico)")
            else:
                print("  ℹ️  NO aplica lógica de SP_PRE_LOG_TRX_UPDATE")
        
        print("\n5️⃣ ANÁLISIS DE LÓGICA DE ACCOUNT_ID MAPPING:")
        print("-" * 60)
        
        # Verificar si hay lógica especial para este usuario
        cursor.execute("""
            SELECT 
                USER_ID,
                ATTR7,
                ATTR8,
                MSISDN,
                STATUS
            FROM PDP_PROD10_MAINDB.USER_PROFILE
            WHERE USER_ID = :user_id
        """, {'user_id': from_id})
        
        profile_data = cursor.fetchall()
        if profile_data:
            row = profile_data[0]
            print(f"  USER_PROFILE.ATTR7: {row[1]}")
            print(f"  USER_PROFILE.ATTR8: {row[2]} ⭐ VALOR CLAVE")
            print(f"  USER_PROFILE.MSISDN: {row[3]}")
            print(f"  USER_PROFILE.STATUS: {row[4]}")
        
        # Verificar MTX_WALLET para este usuario
        cursor.execute("""
            SELECT 
                USER_ID,
                WALLET_NUMBER,
                STATUS,
                MODIFIED_ON,
                ROW_NUMBER() OVER (PARTITION BY USER_ID ORDER BY MODIFIED_ON DESC) as RN
            FROM PDP_PROD10_MAINDBBUS.MTX_WALLET
            WHERE USER_ID = :user_id
            ORDER BY MODIFIED_ON DESC
        """, {'user_id': from_id})
        
        wallet_data = cursor.fetchall()
        if wallet_data:
            print(f"\n  MTX_WALLET para USER_ID {from_id}:")
            for i, row in enumerate(wallet_data[:3]):
                status_marker = " ⭐ ACTIVA" if row[2] == 'Y' else " ❌ INACTIVA"
                orden_marker = " (ORDEN=1)" if row[4] == 1 else f" (ORDEN={row[4]})"
                print(f"    [{i+1}] WALLET: {row[1]}, STATUS: {row[2]}{status_marker}{orden_marker}")
                print(f"        MODIFIED: {row[3]}")
        
        cursor.close()
        connection.close()
        
        print("\n6️⃣ CONCLUSIONES DE LA INVESTIGACIÓN:")
        print("-" * 60)
        print("1. Oracle usa lógica compleja de USER_ACCOUNT_HISTORY")
        print("2. Hay mapeo especial entre ATTR7/ATTR8 y WALLET_NUMBER")
        print("3. Comment usa FIELD7 vs NULL vs string vacío")
        print("4. Necesitamos replicar EXACTAMENTE esta lógica en S3")
        
    except Exception as e:
        print(f"❌ Error en investigación: {e}")
        import traceback
        traceback.print_exc()

def main():
    print("🚀 INVESTIGACIÓN EXHAUSTIVA DE LÓGICA OCULTA")
    print("=" * 80)
    print("Identificando TODA la lógica que causa las diferencias")
    print()
    
    investigar_logica_completa()
    
    print("\n🏁 INVESTIGACIÓN COMPLETADA")

if __name__ == "__main__":
    main()
