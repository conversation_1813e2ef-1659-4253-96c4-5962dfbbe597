2025-06-17 03:57:06,006 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-17 03:57:06,018 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-17 03:57:06,067 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-17 03:57:06,068 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-15
2025-06-17 03:57:06,068 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 03:57:06,068 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 03:57:06,116 - LogTransaccionesPipeline - ERROR - <PERSON><PERSON>r completando SP_PRE_LOG_TRX: IO Error: No files found that match the pattern "s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_HEADER_ORA/year=2025/month=06/day=15/*.parquet"
2025-06-17 03:57:06,116 - LogTransaccionesPipeline - ERROR - Error en SP_PRE_LOG_TRX: IO Error: No files found that match the pattern "s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_HEADER_ORA/year=2025/month=06/day=15/*.parquet"
2025-06-17 03:57:06,116 - LogTransaccionesPipeline - ERROR - Error en SP_PRE_LOG_TRX: IO Error: No files found that match the pattern "s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_HEADER_ORA/year=2025/month=06/day=15/*.parquet"
2025-06-17 03:57:06,116 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: ERROR: Error en SP_PRE_LOG_TRX: IO Error: No files found that match the pattern "s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_HEADER_ORA/year=2025/month=06/day=15/*.parquet"
2025-06-17 03:57:06,116 - LogTransaccionesPipeline - ERROR - ❌ Pipeline LOG_TRANSACCIONES falló después de 0.05 segundos: IO Error: No files found that match the pattern "s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_HEADER_ORA/year=2025/month=06/day=15/*.parquet"
2025-06-17 03:57:06,116 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: ERROR: IO Error: No files found that match the pattern "s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_HEADER_ORA/year=2025/month=06/day=15/*.parquet"
2025-06-17 03:57:41,922 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-17 03:57:41,936 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-17 03:57:41,985 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-17 03:57:41,986 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-15
2025-06-17 03:57:41,986 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 03:57:41,986 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 03:57:42,037 - LogTransaccionesPipeline - ERROR - Error completando SP_PRE_LOG_TRX: IO Error: No files found that match the pattern "s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_HEADER_ORA/year=2025/month=06/day=15/*.parquet"
2025-06-17 03:57:42,038 - LogTransaccionesPipeline - ERROR - Error en SP_PRE_LOG_TRX: IO Error: No files found that match the pattern "s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_HEADER_ORA/year=2025/month=06/day=15/*.parquet"
2025-06-17 03:57:42,038 - LogTransaccionesPipeline - ERROR - Error en SP_PRE_LOG_TRX: IO Error: No files found that match the pattern "s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_HEADER_ORA/year=2025/month=06/day=15/*.parquet"
2025-06-17 03:57:42,038 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: ERROR: Error en SP_PRE_LOG_TRX: IO Error: No files found that match the pattern "s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_HEADER_ORA/year=2025/month=06/day=15/*.parquet"
2025-06-17 03:57:42,038 - LogTransaccionesPipeline - ERROR - ❌ Pipeline LOG_TRANSACCIONES falló después de 0.05 segundos: IO Error: No files found that match the pattern "s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_HEADER_ORA/year=2025/month=06/day=15/*.parquet"
2025-06-17 03:57:42,038 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: ERROR: IO Error: No files found that match the pattern "s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_HEADER_ORA/year=2025/month=06/day=15/*.parquet"
2025-06-17 03:57:55,560 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-17 03:57:55,578 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-17 03:57:55,626 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-17 03:58:12,140 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-17 03:58:12,152 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-17 03:58:12,202 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-17 03:59:04,732 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-17 03:59:04,744 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-17 03:59:04,793 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-17 03:59:04,794 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-15
2025-06-17 03:59:04,794 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 03:59:04,794 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 03:59:05,126 - LogTransaccionesPipeline - ERROR - Error completando SP_PRE_LOG_TRX: Binder Error: No function matches the given name and argument types 'replace(INTEGER, STRING_LITERAL, STRING_LITERAL)'. You might need to add explicit type casts.
	Candidate functions:
	replace(VARCHAR, VARCHAR, VARCHAR) -> VARCHAR


LINE 6:                     REPLACE(MTH.PAYEE_IDENTIFIER_VALUE,'1','') AS NEW_PAYEE_IDE...
                            ^
2025-06-17 03:59:05,126 - LogTransaccionesPipeline - ERROR - Error en SP_PRE_LOG_TRX: Binder Error: No function matches the given name and argument types 'replace(INTEGER, STRING_LITERAL, STRING_LITERAL)'. You might need to add explicit type casts.
	Candidate functions:
	replace(VARCHAR, VARCHAR, VARCHAR) -> VARCHAR


LINE 6:                     REPLACE(MTH.PAYEE_IDENTIFIER_VALUE,'1','') AS NEW_PAYEE_IDE...
                            ^
2025-06-17 03:59:05,126 - LogTransaccionesPipeline - ERROR - Error en SP_PRE_LOG_TRX: Binder Error: No function matches the given name and argument types 'replace(INTEGER, STRING_LITERAL, STRING_LITERAL)'. You might need to add explicit type casts.
	Candidate functions:
	replace(VARCHAR, VARCHAR, VARCHAR) -> VARCHAR


LINE 6:                     REPLACE(MTH.PAYEE_IDENTIFIER_VALUE,'1','') AS NEW_PAYEE_IDE...
                            ^
2025-06-17 03:59:05,126 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: ERROR: Error en SP_PRE_LOG_TRX: Binder Error: No function matches the given name and argument types 'replace(INTEGER, STRING_LITERAL, STRING_LITERAL)'. You might need to add explicit type casts.
	Candidate functions:
	replace(VARCHAR, VARCHAR, VARCHAR) -> VARCHAR


LINE 6:                     REPLACE(MTH.PAYEE_IDENTIFIER_VALUE,'1','') AS NEW_PAYEE_IDE...
                            ^
2025-06-17 03:59:05,126 - LogTransaccionesPipeline - ERROR - ❌ Pipeline LOG_TRANSACCIONES falló después de 0.33 segundos: Binder Error: No function matches the given name and argument types 'replace(INTEGER, STRING_LITERAL, STRING_LITERAL)'. You might need to add explicit type casts.
	Candidate functions:
	replace(VARCHAR, VARCHAR, VARCHAR) -> VARCHAR


LINE 6:                     REPLACE(MTH.PAYEE_IDENTIFIER_VALUE,'1','') AS NEW_PAYEE_IDE...
                            ^
2025-06-17 03:59:05,126 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: ERROR: Binder Error: No function matches the given name and argument types 'replace(INTEGER, STRING_LITERAL, STRING_LITERAL)'. You might need to add explicit type casts.
	Candidate functions:
	replace(VARCHAR, VARCHAR, VARCHAR) -> VARCHAR


LINE 6:                     REPLACE(MTH.PAYEE_IDENTIFIER_VALUE,'1','') AS NEW_PAYEE_IDE...
                            ^
2025-06-17 03:59:48,664 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-17 03:59:48,678 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-17 03:59:48,732 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-17 03:59:48,733 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-15
2025-06-17 03:59:48,733 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 03:59:48,733 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 03:59:50,911 - LogTransaccionesPipeline - ERROR - Error completando SP_PRE_LOG_TRX: Binder Error: Cannot mix values of type VARCHAR and INTEGER in CASE expression - an explicit cast is required

LINE 86:                     CASE WHEN MTH.PAYER_USER_ID = 'IND012' THEN MTH.PAYER_IDENT...
                             ^
2025-06-17 03:59:50,912 - LogTransaccionesPipeline - ERROR - Error en SP_PRE_LOG_TRX: Binder Error: Cannot mix values of type VARCHAR and INTEGER in CASE expression - an explicit cast is required

LINE 86:                     CASE WHEN MTH.PAYER_USER_ID = 'IND012' THEN MTH.PAYER_IDENT...
                             ^
2025-06-17 03:59:50,912 - LogTransaccionesPipeline - ERROR - Error en SP_PRE_LOG_TRX: Binder Error: Cannot mix values of type VARCHAR and INTEGER in CASE expression - an explicit cast is required

LINE 86:                     CASE WHEN MTH.PAYER_USER_ID = 'IND012' THEN MTH.PAYER_IDENT...
                             ^
2025-06-17 03:59:50,912 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: ERROR: Error en SP_PRE_LOG_TRX: Binder Error: Cannot mix values of type VARCHAR and INTEGER in CASE expression - an explicit cast is required

LINE 86:                     CASE WHEN MTH.PAYER_USER_ID = 'IND012' THEN MTH.PAYER_IDENT...
                             ^
2025-06-17 03:59:50,912 - LogTransaccionesPipeline - ERROR - ❌ Pipeline LOG_TRANSACCIONES falló después de 2.18 segundos: Binder Error: Cannot mix values of type VARCHAR and INTEGER in CASE expression - an explicit cast is required

LINE 86:                     CASE WHEN MTH.PAYER_USER_ID = 'IND012' THEN MTH.PAYER_IDENT...
                             ^
2025-06-17 03:59:50,912 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: ERROR: Binder Error: Cannot mix values of type VARCHAR and INTEGER in CASE expression - an explicit cast is required

LINE 86:                     CASE WHEN MTH.PAYER_USER_ID = 'IND012' THEN MTH.PAYER_IDENT...
                             ^
2025-06-17 04:00:29,341 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-17 04:00:29,359 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-17 04:00:29,408 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-17 04:00:29,409 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-15
2025-06-17 04:00:29,409 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 04:00:29,409 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 04:00:50,847 - LogTransaccionesPipeline - ERROR - Error completando SP_PRE_LOG_TRX: Conversion Error: In Parquet reader of file "s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_HEADER_ORA/2025/06/15/PDP_PROD10_MAINDBBUS-MTX_TRANSACTION_HEADER_ORA_20250616-145051_chunk_0.parquet": failed to cast column "RECONCILIATION_BY" from type VARCHAR to INTEGER: Could not convert string '5902506151015052360380' to INT32

In file "s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_HEADER_ORA/2025/06/15/PDP_PROD10_MAINDBBUS-MTX_TRANSACTION_HEADER_ORA_20250616-145051_chunk_0.parquet" the column "RECONCILIATION_BY" has type VARCHAR, but we are trying to read it as type INTEGER.
This can happen when reading multiple Parquet files. The schema information is taken from the first Parquet file by default. Possible solutions:
* Enable the union_by_name=True option to combine the schema of all Parquet files (duckdb.org/docs/data/multiple_files/combining_schemas)
* Use a COPY statement to automatically derive types from an existing table.
2025-06-17 04:00:50,847 - LogTransaccionesPipeline - ERROR - Error en SP_PRE_LOG_TRX: Conversion Error: In Parquet reader of file "s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_HEADER_ORA/2025/06/15/PDP_PROD10_MAINDBBUS-MTX_TRANSACTION_HEADER_ORA_20250616-145051_chunk_0.parquet": failed to cast column "RECONCILIATION_BY" from type VARCHAR to INTEGER: Could not convert string '5902506151015052360380' to INT32

In file "s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_HEADER_ORA/2025/06/15/PDP_PROD10_MAINDBBUS-MTX_TRANSACTION_HEADER_ORA_20250616-145051_chunk_0.parquet" the column "RECONCILIATION_BY" has type VARCHAR, but we are trying to read it as type INTEGER.
This can happen when reading multiple Parquet files. The schema information is taken from the first Parquet file by default. Possible solutions:
* Enable the union_by_name=True option to combine the schema of all Parquet files (duckdb.org/docs/data/multiple_files/combining_schemas)
* Use a COPY statement to automatically derive types from an existing table.
2025-06-17 04:00:50,847 - LogTransaccionesPipeline - ERROR - Error en SP_PRE_LOG_TRX: Conversion Error: In Parquet reader of file "s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_HEADER_ORA/2025/06/15/PDP_PROD10_MAINDBBUS-MTX_TRANSACTION_HEADER_ORA_20250616-145051_chunk_0.parquet": failed to cast column "RECONCILIATION_BY" from type VARCHAR to INTEGER: Could not convert string '5902506151015052360380' to INT32

In file "s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_HEADER_ORA/2025/06/15/PDP_PROD10_MAINDBBUS-MTX_TRANSACTION_HEADER_ORA_20250616-145051_chunk_0.parquet" the column "RECONCILIATION_BY" has type VARCHAR, but we are trying to read it as type INTEGER.
This can happen when reading multiple Parquet files. The schema information is taken from the first Parquet file by default. Possible solutions:
* Enable the union_by_name=True option to combine the schema of all Parquet files (duckdb.org/docs/data/multiple_files/combining_schemas)
* Use a COPY statement to automatically derive types from an existing table.
2025-06-17 04:00:50,847 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: ERROR: Error en SP_PRE_LOG_TRX: Conversion Error: In Parquet reader of file "s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_HEADER_ORA/2025/06/15/PDP_PROD10_MAINDBBUS-MTX_TRANSACTION_HEADER_ORA_20250616-145051_chunk_0.parquet": failed to cast column "RECONCILIATION_BY" from type VARCHAR to INTEGER: Could not convert string '5902506151015052360380' to INT32

In file "s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_HEADER_ORA/2025/06/15/PDP_PROD10_MAINDBBUS-MTX_TRANSACTION_HEADER_ORA_20250616-145051_chunk_0.parquet" the column "RECONCILIATION_BY" has type VARCHAR, but we are trying to read it as type INTEGER.
This can happen when reading multiple Parquet files. The schema information is taken from the first Parquet file by default. Possible solutions:
* Enable the union_by_name=True option to combine the schema of all Parquet files (duckdb.org/docs/data/multiple_files/combining_schemas)
* Use a COPY statement to automatically derive types from an existing table.
2025-06-17 04:00:50,847 - LogTransaccionesPipeline - ERROR - ❌ Pipeline LOG_TRANSACCIONES falló después de 21.44 segundos: Conversion Error: In Parquet reader of file "s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_HEADER_ORA/2025/06/15/PDP_PROD10_MAINDBBUS-MTX_TRANSACTION_HEADER_ORA_20250616-145051_chunk_0.parquet": failed to cast column "RECONCILIATION_BY" from type VARCHAR to INTEGER: Could not convert string '5902506151015052360380' to INT32

In file "s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_HEADER_ORA/2025/06/15/PDP_PROD10_MAINDBBUS-MTX_TRANSACTION_HEADER_ORA_20250616-145051_chunk_0.parquet" the column "RECONCILIATION_BY" has type VARCHAR, but we are trying to read it as type INTEGER.
This can happen when reading multiple Parquet files. The schema information is taken from the first Parquet file by default. Possible solutions:
* Enable the union_by_name=True option to combine the schema of all Parquet files (duckdb.org/docs/data/multiple_files/combining_schemas)
* Use a COPY statement to automatically derive types from an existing table.
2025-06-17 04:00:50,847 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: ERROR: Conversion Error: In Parquet reader of file "s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_HEADER_ORA/2025/06/15/PDP_PROD10_MAINDBBUS-MTX_TRANSACTION_HEADER_ORA_20250616-145051_chunk_0.parquet": failed to cast column "RECONCILIATION_BY" from type VARCHAR to INTEGER: Could not convert string '5902506151015052360380' to INT32

In file "s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_HEADER_ORA/2025/06/15/PDP_PROD10_MAINDBBUS-MTX_TRANSACTION_HEADER_ORA_20250616-145051_chunk_0.parquet" the column "RECONCILIATION_BY" has type VARCHAR, but we are trying to read it as type INTEGER.
This can happen when reading multiple Parquet files. The schema information is taken from the first Parquet file by default. Possible solutions:
* Enable the union_by_name=True option to combine the schema of all Parquet files (duckdb.org/docs/data/multiple_files/combining_schemas)
* Use a COPY statement to automatically derive types from an existing table.
2025-06-17 04:01:17,392 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-17 04:01:17,406 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-17 04:01:17,456 - LogTransaccionesPipeline - ERROR - Error configurando credenciales S3: Catalog Error: unrecognized configuration parameter "union_by_name"

Did you mean: "username"
2025-06-17 04:03:51,508 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-17 04:03:51,521 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-17 04:03:51,569 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-17 04:03:51,569 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-15
2025-06-17 04:03:51,569 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 04:03:51,569 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 04:03:51,570 - LogTransaccionesPipeline - WARNING - Error verificando si PRE_LOG_TRX fue procesado: Invalid Input Error: File 'TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet' too small to be a Parquet file
2025-06-17 04:03:55,038 - LogTransaccionesPipeline - ERROR - Error completando SP_PRE_LOG_TRX: Binder Error: Cannot compare values of type VARCHAR and type TIMESTAMP - an explicit cast is required

LINE 11:                 WHERE MTH.TRANSFER_DATE >= CAST('2025-06-15' AS TIMESTAMP)
                                                 ^
2025-06-17 04:03:55,038 - LogTransaccionesPipeline - ERROR - Error en SP_PRE_LOG_TRX: Binder Error: Cannot compare values of type VARCHAR and type TIMESTAMP - an explicit cast is required

LINE 11:                 WHERE MTH.TRANSFER_DATE >= CAST('2025-06-15' AS TIMESTAMP)
                                                 ^
2025-06-17 04:03:55,038 - LogTransaccionesPipeline - ERROR - Error en SP_PRE_LOG_TRX: Binder Error: Cannot compare values of type VARCHAR and type TIMESTAMP - an explicit cast is required

LINE 11:                 WHERE MTH.TRANSFER_DATE >= CAST('2025-06-15' AS TIMESTAMP)
                                                 ^
2025-06-17 04:03:55,038 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: ERROR: Error en SP_PRE_LOG_TRX: Binder Error: Cannot compare values of type VARCHAR and type TIMESTAMP - an explicit cast is required

LINE 11:                 WHERE MTH.TRANSFER_DATE >= CAST('2025-06-15' AS TIMESTAMP)
                                                 ^
2025-06-17 04:03:55,038 - LogTransaccionesPipeline - ERROR - ❌ Pipeline LOG_TRANSACCIONES falló después de 3.47 segundos: Binder Error: Cannot compare values of type VARCHAR and type TIMESTAMP - an explicit cast is required

LINE 11:                 WHERE MTH.TRANSFER_DATE >= CAST('2025-06-15' AS TIMESTAMP)
                                                 ^
2025-06-17 04:03:55,038 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: ERROR: Binder Error: Cannot compare values of type VARCHAR and type TIMESTAMP - an explicit cast is required

LINE 11:                 WHERE MTH.TRANSFER_DATE >= CAST('2025-06-15' AS TIMESTAMP)
                                                 ^
2025-06-17 04:04:17,257 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-17 04:04:17,270 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-17 04:04:17,319 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-17 04:04:17,319 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-15
2025-06-17 04:04:17,319 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 04:04:17,319 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 04:04:17,320 - LogTransaccionesPipeline - WARNING - Error verificando si PRE_LOG_TRX fue procesado: Invalid Input Error: File 'TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet' too small to be a Parquet file
2025-06-17 04:06:12,235 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX completado: 104116 registros -> TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-17 04:06:12,236 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: COMPLETADO
2025-06-17 04:06:12,236 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: INICIANDO
2025-06-17 04:06:12,236 - LogTransaccionesPipeline - INFO - Ejecutando SP_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 04:06:12,317 - LogTransaccionesPipeline - ERROR - Error en SP_LOG_TRX: Binder Error: Table "MTH" does not have a column named "FromID_Mobiquity"

Candidate bindings: : "ID_TYPE"

LINE 112:                 ON MTH."FromID_Mobiquity" = H_PAYER.USER_ID
                             ^
2025-06-17 04:06:12,317 - LogTransaccionesPipeline - ERROR - Error en SP_LOG_TRX: Binder Error: Table "MTH" does not have a column named "FromID_Mobiquity"

Candidate bindings: : "ID_TYPE"

LINE 112:                 ON MTH."FromID_Mobiquity" = H_PAYER.USER_ID
                             ^
2025-06-17 04:06:12,317 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: ERROR: Error en SP_LOG_TRX: Binder Error: Table "MTH" does not have a column named "FromID_Mobiquity"

Candidate bindings: : "ID_TYPE"

LINE 112:                 ON MTH."FromID_Mobiquity" = H_PAYER.USER_ID
                             ^
2025-06-17 04:06:12,317 - LogTransaccionesPipeline - ERROR - ❌ Pipeline LOG_TRANSACCIONES falló después de 115.00 segundos: Binder Error: Table "MTH" does not have a column named "FromID_Mobiquity"

Candidate bindings: : "ID_TYPE"

LINE 112:                 ON MTH."FromID_Mobiquity" = H_PAYER.USER_ID
                             ^
2025-06-17 04:06:12,317 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: ERROR: Binder Error: Table "MTH" does not have a column named "FromID_Mobiquity"

Candidate bindings: : "ID_TYPE"

LINE 112:                 ON MTH."FromID_Mobiquity" = H_PAYER.USER_ID
                             ^
2025-06-17 04:11:01,782 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-17 04:11:01,795 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-17 04:11:01,844 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-17 04:11:01,845 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-15
2025-06-17 04:11:01,845 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 04:11:01,845 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 04:11:01,846 - LogTransaccionesPipeline - WARNING - Error verificando si PRE_LOG_TRX fue procesado: Binder Error: Referenced column "TransferDate" not found in FROM clause!
Candidate bindings: "FTXN_ID"

LINE 3:                     WHERE CAST("TransferDate" AS DATE) = CAST('2025-06-15' AS DATE)
                                       ^
2025-06-17 04:12:55,543 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX completado: 104116 registros -> TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-17 04:12:55,544 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: COMPLETADO
2025-06-17 04:12:55,544 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: INICIANDO
2025-06-17 04:12:55,544 - LogTransaccionesPipeline - INFO - Ejecutando SP_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 04:13:31,436 - LogTransaccionesPipeline - INFO - SP_LOG_TRX completado: 104116 registros -> TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-17 04:13:31,436 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: COMPLETADO
2025-06-17 04:13:31,436 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: INICIANDO
2025-06-17 04:13:31,436 - LogTransaccionesPipeline - INFO - Extrayendo CSV final para fecha: 2025-06-15
2025-06-17 04:13:31,690 - LogTransaccionesPipeline - INFO - CSV final generado: 104116 registros -> output/TR-********.csv
2025-06-17 04:13:31,690 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: COMPLETADO
2025-06-17 04:13:31,690 - LogTransaccionesPipeline - INFO - ✅ Pipeline LOG_TRANSACCIONES completado exitosamente en 149.85 segundos
2025-06-17 04:13:31,690 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: EXITOSO
2025-06-17 04:26:55,510 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-17 04:26:55,524 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-17 04:26:55,573 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-17 04:26:55,574 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 04:27:08,252 - LogTransaccionesPipeline - ERROR - Error completando SP_PRE_LOG_TRX: Binder Error: Values list "MTH" does not have a column named "NEW_PAYER_USER_ID"

LINE 171:                 MTH.NEW_PAYER_USER_ID AS "FromID_Mobiquity",
                          ^
2025-06-17 04:27:08,252 - LogTransaccionesPipeline - ERROR - Error en SP_PRE_LOG_TRX: Binder Error: Values list "MTH" does not have a column named "NEW_PAYER_USER_ID"

LINE 171:                 MTH.NEW_PAYER_USER_ID AS "FromID_Mobiquity",
                          ^
2025-06-17 04:27:37,937 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-17 04:27:37,950 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-17 04:27:37,997 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-17 04:27:37,997 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 04:29:29,332 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX completado: 104116 registros -> TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-17 04:41:26,712 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-17 04:41:26,726 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-17 04:41:26,777 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-17 04:41:26,777 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 04:43:17,363 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX completado: 104116 registros -> TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-17 04:45:55,479 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-17 04:45:55,492 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-17 04:45:55,541 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-17 04:45:55,541 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 04:47:42,258 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX completado: 104116 registros -> TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-17 05:17:48,423 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-17 05:17:48,436 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-17 05:17:48,484 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-17 05:17:48,484 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 05:19:40,955 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX completado: 104122 registros -> TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-17 05:29:11,682 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-17 05:29:11,696 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-17 05:29:11,744 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-17 05:29:11,745 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 05:31:15,770 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX completado: 104122 registros -> TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-17 05:32:11,079 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-17 05:32:11,092 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-17 05:32:11,140 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-17 05:32:11,140 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 05:34:07,633 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX completado: 104118 registros -> TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-17 05:44:31,640 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-17 05:44:31,653 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-17 05:44:31,702 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-17 05:44:31,703 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 05:46:53,755 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX completado: 101575 registros -> TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-17 05:48:20,569 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-17 05:48:20,582 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-17 05:48:20,631 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-17 05:48:20,631 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 05:50:15,718 - LogTransaccionesPipeline - ERROR - Error completando SP_PRE_LOG_TRX: IO Error: Could not write file "TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet": No space left on device
2025-06-17 05:50:15,718 - LogTransaccionesPipeline - ERROR - Error en SP_PRE_LOG_TRX: IO Error: Could not write file "TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet": No space left on device
2025-06-17 05:52:05,557 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-17 05:52:05,573 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-17 05:52:05,622 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-17 05:52:05,623 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 05:52:05,623 - LogTransaccionesPipeline - ERROR - Error en SP_PRE_LOG_TRX: [Errno 13] Permission denied: '/reporting/TEMP_LOGS_TRANSACCIONES'
2025-06-17 06:05:03,812 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-17 06:05:03,825 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-17 06:05:03,873 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-17 06:05:03,873 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 06:07:08,428 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX completado: 104118 registros -> TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-17 06:09:57,087 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-17 06:09:57,101 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-17 06:09:57,150 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-17 06:09:57,150 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-15
2025-06-17 06:09:57,150 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 06:09:57,150 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 06:11:56,201 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX completado: 104118 registros -> TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-17 06:11:56,202 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: COMPLETADO
2025-06-17 06:11:56,202 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: INICIANDO
2025-06-17 06:11:56,202 - LogTransaccionesPipeline - INFO - Ejecutando SP_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 06:11:56,291 - LogTransaccionesPipeline - ERROR - Error en SP_LOG_TRX: Binder Error: Table "MTH" does not have a column named "NEW_PAYER_USER_ID"

Candidate bindings: : "TransferID"

LINE 112:                 ON MTH.NEW_PAYER_USER_ID = H_PAYER.USER_ID
                             ^
2025-06-17 06:11:56,291 - LogTransaccionesPipeline - ERROR - Error en SP_LOG_TRX: Binder Error: Table "MTH" does not have a column named "NEW_PAYER_USER_ID"

Candidate bindings: : "TransferID"

LINE 112:                 ON MTH.NEW_PAYER_USER_ID = H_PAYER.USER_ID
                             ^
2025-06-17 06:11:56,291 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: ERROR: Error en SP_LOG_TRX: Binder Error: Table "MTH" does not have a column named "NEW_PAYER_USER_ID"

Candidate bindings: : "TransferID"

LINE 112:                 ON MTH.NEW_PAYER_USER_ID = H_PAYER.USER_ID
                             ^
2025-06-17 06:11:56,291 - LogTransaccionesPipeline - ERROR - ❌ Pipeline LOG_TRANSACCIONES falló después de 119.14 segundos: Binder Error: Table "MTH" does not have a column named "NEW_PAYER_USER_ID"

Candidate bindings: : "TransferID"

LINE 112:                 ON MTH.NEW_PAYER_USER_ID = H_PAYER.USER_ID
                             ^
2025-06-17 06:11:56,291 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: ERROR: Binder Error: Table "MTH" does not have a column named "NEW_PAYER_USER_ID"

Candidate bindings: : "TransferID"

LINE 112:                 ON MTH.NEW_PAYER_USER_ID = H_PAYER.USER_ID
                             ^
2025-06-17 06:12:48,970 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-17 06:12:48,984 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-17 06:12:49,034 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-17 06:12:49,034 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-15
2025-06-17 06:12:49,034 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 06:12:49,034 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 06:14:46,230 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX completado: 104118 registros -> TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-17 06:14:46,230 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: COMPLETADO
2025-06-17 06:14:46,230 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: INICIANDO
2025-06-17 06:14:46,230 - LogTransaccionesPipeline - INFO - Ejecutando SP_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 06:14:46,313 - LogTransaccionesPipeline - ERROR - Error en SP_LOG_TRX: Binder Error: Table "MTH" does not have a column named "NEW_PAYER_USER_ID"

Candidate bindings: : "TransferID"

LINE 112:                 ON MTH.NEW_PAYER_USER_ID = H_PAYER.USER_ID
                             ^
2025-06-17 06:14:46,313 - LogTransaccionesPipeline - ERROR - Error en SP_LOG_TRX: Binder Error: Table "MTH" does not have a column named "NEW_PAYER_USER_ID"

Candidate bindings: : "TransferID"

LINE 112:                 ON MTH.NEW_PAYER_USER_ID = H_PAYER.USER_ID
                             ^
2025-06-17 06:14:46,313 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: ERROR: Error en SP_LOG_TRX: Binder Error: Table "MTH" does not have a column named "NEW_PAYER_USER_ID"

Candidate bindings: : "TransferID"

LINE 112:                 ON MTH.NEW_PAYER_USER_ID = H_PAYER.USER_ID
                             ^
2025-06-17 06:14:46,314 - LogTransaccionesPipeline - ERROR - ❌ Pipeline LOG_TRANSACCIONES falló después de 117.28 segundos: Binder Error: Table "MTH" does not have a column named "NEW_PAYER_USER_ID"

Candidate bindings: : "TransferID"

LINE 112:                 ON MTH.NEW_PAYER_USER_ID = H_PAYER.USER_ID
                             ^
2025-06-17 06:14:46,314 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: ERROR: Binder Error: Table "MTH" does not have a column named "NEW_PAYER_USER_ID"

Candidate bindings: : "TransferID"

LINE 112:                 ON MTH.NEW_PAYER_USER_ID = H_PAYER.USER_ID
                             ^
2025-06-17 06:19:35,694 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-17 06:19:35,707 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-17 06:19:35,755 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-17 06:19:35,756 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-15
2025-06-17 06:19:35,756 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 06:19:35,756 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 06:19:35,759 - LogTransaccionesPipeline - INFO - Proceso PRE_LOG_TRX ya ejecutado para 2025-06-15: 104118 registros
2025-06-17 06:19:35,759 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX ya fue procesado para esta fecha
2025-06-17 06:19:35,759 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: COMPLETADO
2025-06-17 06:19:35,759 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: INICIANDO
2025-06-17 06:19:35,759 - LogTransaccionesPipeline - INFO - Ejecutando SP_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 06:19:35,960 - LogTransaccionesPipeline - ERROR - Error en SP_LOG_TRX: Binder Error: Referenced column "TRANSFER_ID" not found in FROM clause!
Candidate bindings: "H_PAYEE.USER_ID", "H_PAYER.USER_ID", "H_PAYER.GRADE_OLD", "H_PAYEE.GRADE_OLD", "H_PAYEE.ACCOUNT_ID"

LINE 4:                 SELECT TRANSFER_ID, CONTEXT
                               ^
2025-06-17 06:19:35,960 - LogTransaccionesPipeline - ERROR - Error en SP_LOG_TRX: Binder Error: Referenced column "TRANSFER_ID" not found in FROM clause!
Candidate bindings: "H_PAYEE.USER_ID", "H_PAYER.USER_ID", "H_PAYER.GRADE_OLD", "H_PAYEE.GRADE_OLD", "H_PAYEE.ACCOUNT_ID"

LINE 4:                 SELECT TRANSFER_ID, CONTEXT
                               ^
2025-06-17 06:19:35,960 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: ERROR: Error en SP_LOG_TRX: Binder Error: Referenced column "TRANSFER_ID" not found in FROM clause!
Candidate bindings: "H_PAYEE.USER_ID", "H_PAYER.USER_ID", "H_PAYER.GRADE_OLD", "H_PAYEE.GRADE_OLD", "H_PAYEE.ACCOUNT_ID"

LINE 4:                 SELECT TRANSFER_ID, CONTEXT
                               ^
2025-06-17 06:19:35,960 - LogTransaccionesPipeline - ERROR - ❌ Pipeline LOG_TRANSACCIONES falló después de 0.20 segundos: Binder Error: Referenced column "TRANSFER_ID" not found in FROM clause!
Candidate bindings: "H_PAYEE.USER_ID", "H_PAYER.USER_ID", "H_PAYER.GRADE_OLD", "H_PAYEE.GRADE_OLD", "H_PAYEE.ACCOUNT_ID"

LINE 4:                 SELECT TRANSFER_ID, CONTEXT
                               ^
2025-06-17 06:19:35,960 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: ERROR: Binder Error: Referenced column "TRANSFER_ID" not found in FROM clause!
Candidate bindings: "H_PAYEE.USER_ID", "H_PAYER.USER_ID", "H_PAYER.GRADE_OLD", "H_PAYEE.GRADE_OLD", "H_PAYEE.ACCOUNT_ID"

LINE 4:                 SELECT TRANSFER_ID, CONTEXT
                               ^
2025-06-17 06:20:05,538 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-17 06:20:05,551 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-17 06:20:05,603 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-17 06:20:05,603 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-15
2025-06-17 06:20:05,603 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 06:20:05,603 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 06:20:05,606 - LogTransaccionesPipeline - INFO - Proceso PRE_LOG_TRX ya ejecutado para 2025-06-15: 104118 registros
2025-06-17 06:20:05,606 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX ya fue procesado para esta fecha
2025-06-17 06:20:05,607 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: COMPLETADO
2025-06-17 06:20:05,607 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: INICIANDO
2025-06-17 06:20:05,607 - LogTransaccionesPipeline - INFO - Ejecutando SP_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 06:20:13,888 - LogTransaccionesPipeline - INFO - SP_LOG_TRX completado: 104118 registros -> TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-17 06:20:13,889 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: COMPLETADO
2025-06-17 06:20:13,889 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: INICIANDO
2025-06-17 06:20:13,889 - LogTransaccionesPipeline - INFO - Extrayendo CSV final para fecha: 2025-06-15
2025-06-17 06:20:13,959 - LogTransaccionesPipeline - INFO - CSV final generado: 104118 registros -> output/TR-********.csv
2025-06-17 06:20:13,959 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: COMPLETADO
2025-06-17 06:20:13,959 - LogTransaccionesPipeline - INFO - ✅ Pipeline LOG_TRANSACCIONES completado exitosamente en 8.36 segundos
2025-06-17 06:20:13,959 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: EXITOSO
2025-06-17 07:02:22,347 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-17 07:02:22,360 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-17 07:02:22,411 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-17 07:02:22,411 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-15
2025-06-17 07:02:22,411 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 07:02:22,411 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 07:02:22,412 - LogTransaccionesPipeline - ERROR - Error completando SP_PRE_LOG_TRX: Parser Error: syntax error at or near "WITH"
2025-06-17 07:02:22,412 - LogTransaccionesPipeline - ERROR - Error en SP_PRE_LOG_TRX: Parser Error: syntax error at or near "WITH"
2025-06-17 07:02:22,412 - LogTransaccionesPipeline - ERROR - Error en SP_PRE_LOG_TRX: Parser Error: syntax error at or near "WITH"
2025-06-17 07:02:22,412 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: ERROR: Error en SP_PRE_LOG_TRX: Parser Error: syntax error at or near "WITH"
2025-06-17 07:02:22,412 - LogTransaccionesPipeline - ERROR - ❌ Pipeline LOG_TRANSACCIONES falló después de 0.00 segundos: Parser Error: syntax error at or near "WITH"
2025-06-17 07:02:22,412 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: ERROR: Parser Error: syntax error at or near "WITH"
2025-06-17 07:03:04,285 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-17 07:03:04,298 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-17 07:03:04,345 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-17 07:03:04,346 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-15
2025-06-17 07:03:04,346 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 07:03:04,346 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 07:03:04,346 - LogTransaccionesPipeline - ERROR - Error completando SP_PRE_LOG_TRX: Parser Error: syntax error at or near "WITH"
2025-06-17 07:03:04,346 - LogTransaccionesPipeline - ERROR - Error en SP_PRE_LOG_TRX: Parser Error: syntax error at or near "WITH"
2025-06-17 07:03:04,346 - LogTransaccionesPipeline - ERROR - Error en SP_PRE_LOG_TRX: Parser Error: syntax error at or near "WITH"
2025-06-17 07:03:04,347 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: ERROR: Error en SP_PRE_LOG_TRX: Parser Error: syntax error at or near "WITH"
2025-06-17 07:03:04,347 - LogTransaccionesPipeline - ERROR - ❌ Pipeline LOG_TRANSACCIONES falló después de 0.00 segundos: Parser Error: syntax error at or near "WITH"
2025-06-17 07:03:04,347 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: ERROR: Parser Error: syntax error at or near "WITH"
2025-06-17 07:03:54,471 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-17 07:03:54,482 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-17 07:03:54,532 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-17 07:03:54,532 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-15
2025-06-17 07:03:54,533 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 07:03:54,533 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 07:05:59,303 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX completado: 104122 registros -> TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-17 07:05:59,303 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: COMPLETADO
2025-06-17 07:05:59,303 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: INICIANDO
2025-06-17 07:05:59,303 - LogTransaccionesPipeline - INFO - Ejecutando SP_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 07:06:35,197 - LogTransaccionesPipeline - INFO - SP_LOG_TRX completado: 104122 registros -> TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-17 07:06:35,197 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: COMPLETADO
2025-06-17 07:06:35,197 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: INICIANDO
2025-06-17 07:06:35,197 - LogTransaccionesPipeline - INFO - Extrayendo CSV final para fecha: 2025-06-15
2025-06-17 07:06:35,446 - LogTransaccionesPipeline - INFO - CSV final generado: 104122 registros -> output/TR-********.csv
2025-06-17 07:06:35,446 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: COMPLETADO
2025-06-17 07:06:35,446 - LogTransaccionesPipeline - INFO - ✅ Pipeline LOG_TRANSACCIONES completado exitosamente en 160.91 segundos
2025-06-17 07:06:35,446 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: EXITOSO
2025-06-17 07:12:22,314 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-17 07:12:22,326 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-17 07:12:22,376 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-17 07:12:22,376 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-15
2025-06-17 07:12:22,376 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 07:12:22,376 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 07:12:22,380 - LogTransaccionesPipeline - INFO - Proceso PRE_LOG_TRX ya ejecutado para 2025-06-15: 104116 registros
2025-06-17 07:12:22,380 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX ya fue procesado para esta fecha
2025-06-17 07:12:22,380 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: COMPLETADO
2025-06-17 07:12:22,380 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: INICIANDO
2025-06-17 07:12:22,380 - LogTransaccionesPipeline - INFO - Ejecutando SP_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 07:12:57,418 - LogTransaccionesPipeline - INFO - SP_LOG_TRX completado: 104116 registros -> TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-17 07:12:57,419 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: COMPLETADO
2025-06-17 07:12:57,419 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: INICIANDO
2025-06-17 07:12:57,419 - LogTransaccionesPipeline - INFO - Extrayendo CSV final para fecha: 2025-06-15
2025-06-17 07:12:57,659 - LogTransaccionesPipeline - INFO - CSV final generado: 104116 registros -> output/TR-********.csv
2025-06-17 07:12:57,659 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: COMPLETADO
2025-06-17 07:12:57,659 - LogTransaccionesPipeline - INFO - ✅ Pipeline LOG_TRANSACCIONES completado exitosamente en 35.28 segundos
2025-06-17 07:12:57,659 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: EXITOSO
2025-06-17 07:13:18,137 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-17 07:13:18,149 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-17 07:13:18,201 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-17 07:13:18,201 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-15
2025-06-17 07:13:18,201 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 07:13:18,202 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 07:13:18,202 - LogTransaccionesPipeline - ERROR - Error completando SP_PRE_LOG_TRX: Parser Error: syntax error at or near "WITH"
2025-06-17 07:13:18,202 - LogTransaccionesPipeline - ERROR - Error en SP_PRE_LOG_TRX: Parser Error: syntax error at or near "WITH"
2025-06-17 07:13:18,202 - LogTransaccionesPipeline - ERROR - Error en SP_PRE_LOG_TRX: Parser Error: syntax error at or near "WITH"
2025-06-17 07:13:18,202 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: ERROR: Error en SP_PRE_LOG_TRX: Parser Error: syntax error at or near "WITH"
2025-06-17 07:13:18,202 - LogTransaccionesPipeline - ERROR - ❌ Pipeline LOG_TRANSACCIONES falló después de 0.00 segundos: Parser Error: syntax error at or near "WITH"
2025-06-17 07:13:18,203 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: ERROR: Parser Error: syntax error at or near "WITH"
2025-06-17 07:13:58,299 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-17 07:13:58,311 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-17 07:13:58,361 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-17 07:13:58,361 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-15
2025-06-17 07:13:58,362 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 07:13:58,362 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 07:16:01,065 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX completado con deduplicación automática:
2025-06-17 07:16:01,065 - LogTransaccionesPipeline - INFO -   - Registros finales: 104,116
2025-06-17 07:16:01,065 - LogTransaccionesPipeline - INFO -   - TransferIDs únicos: 104,116
2025-06-17 07:16:01,065 - LogTransaccionesPipeline - INFO -   - Duplicados eliminados: 0
2025-06-17 07:16:01,065 - LogTransaccionesPipeline - INFO -   - Archivo: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-17 07:16:01,065 - LogTransaccionesPipeline - INFO - 🎉 HOMOLOGACIÓN PERFECTA: Coincide exactamente con Oracle PRE_LOG_TRX
2025-06-17 07:16:01,066 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: COMPLETADO
2025-06-17 07:16:01,066 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: INICIANDO
2025-06-17 07:16:01,066 - LogTransaccionesPipeline - INFO - Ejecutando SP_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 07:16:09,604 - LogTransaccionesPipeline - INFO - SP_LOG_TRX completado: 104116 registros -> TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-17 07:16:09,604 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: COMPLETADO
2025-06-17 07:16:09,604 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: INICIANDO
2025-06-17 07:16:09,604 - LogTransaccionesPipeline - INFO - Extrayendo CSV final para fecha: 2025-06-15
2025-06-17 07:16:09,689 - LogTransaccionesPipeline - INFO - CSV final generado: 104116 registros -> output/TR-********.csv
2025-06-17 07:16:09,689 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: COMPLETADO
2025-06-17 07:16:09,689 - LogTransaccionesPipeline - INFO - ✅ Pipeline LOG_TRANSACCIONES completado exitosamente en 131.33 segundos
2025-06-17 07:16:09,689 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: EXITOSO
2025-06-17 07:21:13,475 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-17 07:21:13,488 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-17 07:21:13,538 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-17 07:21:13,538 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-15
2025-06-17 07:21:13,538 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 07:21:13,538 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 07:23:14,220 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX completado con deduplicación automática:
2025-06-17 07:23:14,220 - LogTransaccionesPipeline - INFO -   - Registros finales: 104,116
2025-06-17 07:23:14,220 - LogTransaccionesPipeline - INFO -   - TransferIDs únicos: 104,116
2025-06-17 07:23:14,220 - LogTransaccionesPipeline - INFO -   - Duplicados eliminados: 0
2025-06-17 07:23:14,220 - LogTransaccionesPipeline - INFO -   - Archivo: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-17 07:23:14,220 - LogTransaccionesPipeline - INFO - 🎉 HOMOLOGACIÓN PERFECTA: Coincide exactamente con Oracle PRE_LOG_TRX
2025-06-17 07:23:14,220 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: COMPLETADO
2025-06-17 07:23:14,220 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: INICIANDO
2025-06-17 07:23:14,220 - LogTransaccionesPipeline - INFO - Ejecutando SP_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 07:23:23,303 - LogTransaccionesPipeline - INFO - SP_LOG_TRX completado: 104116 registros -> TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-17 07:23:23,303 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: COMPLETADO
2025-06-17 07:23:23,303 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: INICIANDO
2025-06-17 07:23:23,303 - LogTransaccionesPipeline - INFO - Extrayendo CSV final para fecha: 2025-06-15
2025-06-17 07:23:23,377 - LogTransaccionesPipeline - INFO - CSV final generado: 104116 registros -> output/TR-********.csv
2025-06-17 07:23:23,378 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: COMPLETADO
2025-06-17 07:23:23,378 - LogTransaccionesPipeline - INFO - ✅ Pipeline LOG_TRANSACCIONES completado exitosamente en 129.84 segundos
2025-06-17 07:23:23,378 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: EXITOSO
2025-06-17 07:25:34,575 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-17 07:25:34,587 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-17 07:25:34,638 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-17 07:25:34,638 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-15
2025-06-17 07:25:34,638 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 07:25:34,638 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 07:27:31,127 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX completado con deduplicación automática:
2025-06-17 07:27:31,127 - LogTransaccionesPipeline - INFO -   - Registros finales: 104,116
2025-06-17 07:27:31,127 - LogTransaccionesPipeline - INFO -   - TransferIDs únicos: 104,116
2025-06-17 07:27:31,127 - LogTransaccionesPipeline - INFO -   - Duplicados eliminados: 0
2025-06-17 07:27:31,127 - LogTransaccionesPipeline - INFO -   - Archivo: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-17 07:27:31,127 - LogTransaccionesPipeline - INFO - 🎉 HOMOLOGACIÓN PERFECTA: Coincide exactamente con Oracle PRE_LOG_TRX
2025-06-17 07:27:31,127 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: COMPLETADO
2025-06-17 07:27:31,128 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: INICIANDO
2025-06-17 07:27:31,128 - LogTransaccionesPipeline - INFO - Ejecutando SP_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 07:27:40,103 - LogTransaccionesPipeline - INFO - SP_LOG_TRX completado: 104116 registros -> TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-17 07:27:40,104 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: COMPLETADO
2025-06-17 07:27:40,104 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: INICIANDO
2025-06-17 07:27:40,104 - LogTransaccionesPipeline - INFO - Extrayendo CSV final para fecha: 2025-06-15
2025-06-17 07:27:40,179 - LogTransaccionesPipeline - INFO - CSV final generado: 104116 registros -> output/TR-********.csv
2025-06-17 07:27:40,179 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: COMPLETADO
2025-06-17 07:27:40,179 - LogTransaccionesPipeline - INFO - ✅ Pipeline LOG_TRANSACCIONES completado exitosamente en 125.54 segundos
2025-06-17 07:27:40,180 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: EXITOSO
2025-06-17 07:31:16,219 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-17 07:31:16,232 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-17 07:31:16,281 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-17 07:31:16,281 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-15
2025-06-17 07:31:16,281 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 07:31:16,281 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 07:33:13,051 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX completado con deduplicación automática:
2025-06-17 07:33:13,051 - LogTransaccionesPipeline - INFO -   - Registros finales: 104,116
2025-06-17 07:33:13,051 - LogTransaccionesPipeline - INFO -   - TransferIDs únicos: 104,116
2025-06-17 07:33:13,051 - LogTransaccionesPipeline - INFO -   - Duplicados eliminados: 0
2025-06-17 07:33:13,052 - LogTransaccionesPipeline - INFO -   - Archivo: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-17 07:33:13,052 - LogTransaccionesPipeline - INFO - 🎉 HOMOLOGACIÓN PERFECTA: Coincide exactamente con Oracle PRE_LOG_TRX
2025-06-17 07:33:13,052 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: COMPLETADO
2025-06-17 07:33:13,052 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: INICIANDO
2025-06-17 07:33:13,052 - LogTransaccionesPipeline - INFO - Ejecutando SP_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 07:33:21,915 - LogTransaccionesPipeline - INFO - SP_LOG_TRX completado: 104116 registros -> TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-17 07:33:21,915 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: COMPLETADO
2025-06-17 07:33:21,915 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: INICIANDO
2025-06-17 07:33:21,915 - LogTransaccionesPipeline - INFO - Extrayendo CSV final para fecha: 2025-06-15
2025-06-17 07:33:21,990 - LogTransaccionesPipeline - INFO - CSV final generado: 104116 registros -> output/TR-********.csv
2025-06-17 07:33:21,990 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: COMPLETADO
2025-06-17 07:33:21,990 - LogTransaccionesPipeline - INFO - ✅ Pipeline LOG_TRANSACCIONES completado exitosamente en 125.71 segundos
2025-06-17 07:33:21,990 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: EXITOSO
2025-06-17 07:34:20,750 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-17 07:34:20,764 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-17 07:34:20,811 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-17 07:34:20,811 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-15
2025-06-17 07:34:20,811 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 07:34:20,811 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 07:36:15,286 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX completado con deduplicación automática:
2025-06-17 07:36:15,287 - LogTransaccionesPipeline - INFO -   - Registros finales: 104,116
2025-06-17 07:36:15,287 - LogTransaccionesPipeline - INFO -   - TransferIDs únicos: 104,116
2025-06-17 07:36:15,287 - LogTransaccionesPipeline - INFO -   - Duplicados eliminados: 0
2025-06-17 07:36:15,287 - LogTransaccionesPipeline - INFO -   - Archivo: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-17 07:36:15,287 - LogTransaccionesPipeline - INFO - 🎉 HOMOLOGACIÓN PERFECTA: Coincide exactamente con Oracle PRE_LOG_TRX
2025-06-17 07:36:15,287 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: COMPLETADO
2025-06-17 07:36:15,287 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: INICIANDO
2025-06-17 07:36:15,287 - LogTransaccionesPipeline - INFO - Ejecutando SP_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 07:36:23,949 - LogTransaccionesPipeline - INFO - SP_LOG_TRX completado: 104116 registros -> TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-17 07:36:23,949 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: COMPLETADO
2025-06-17 07:36:23,950 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: INICIANDO
2025-06-17 07:36:23,950 - LogTransaccionesPipeline - INFO - Extrayendo CSV final para fecha: 2025-06-15
2025-06-17 07:36:24,027 - LogTransaccionesPipeline - INFO - CSV final generado: 104116 registros -> output/TR-********.csv
2025-06-17 07:36:24,027 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: COMPLETADO
2025-06-17 07:36:24,027 - LogTransaccionesPipeline - INFO - ✅ Pipeline LOG_TRANSACCIONES completado exitosamente en 123.22 segundos
2025-06-17 07:36:24,027 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: EXITOSO
2025-06-17 16:53:47,651 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-17 16:53:47,667 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-17 16:53:47,717 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-17 16:53:47,717 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-15
2025-06-17 16:53:47,717 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 16:53:47,717 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 16:55:58,938 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX completado con deduplicación automática:
2025-06-17 16:55:58,939 - LogTransaccionesPipeline - INFO -   - Registros finales: 104,116
2025-06-17 16:55:58,939 - LogTransaccionesPipeline - INFO -   - TransferIDs únicos: 104,116
2025-06-17 16:55:58,939 - LogTransaccionesPipeline - INFO -   - Duplicados eliminados: 0
2025-06-17 16:55:58,939 - LogTransaccionesPipeline - INFO -   - Archivo: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-17 16:55:58,939 - LogTransaccionesPipeline - INFO - 🎉 HOMOLOGACIÓN PERFECTA: Coincide exactamente con Oracle PRE_LOG_TRX
2025-06-17 16:55:58,939 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: COMPLETADO
2025-06-17 16:55:58,939 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: INICIANDO
2025-06-17 16:55:58,939 - LogTransaccionesPipeline - INFO - Ejecutando SP_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 16:56:07,548 - LogTransaccionesPipeline - INFO - SP_LOG_TRX completado: 104116 registros -> TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-17 16:56:07,548 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: COMPLETADO
2025-06-17 16:56:07,548 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: INICIANDO
2025-06-17 16:56:07,548 - LogTransaccionesPipeline - INFO - Extrayendo CSV final para fecha: 2025-06-15
2025-06-17 16:56:07,622 - LogTransaccionesPipeline - INFO - CSV final generado: 104116 registros -> output/TR-********.csv
2025-06-17 16:56:07,622 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: COMPLETADO
2025-06-17 16:56:07,622 - LogTransaccionesPipeline - INFO - ✅ Pipeline LOG_TRANSACCIONES completado exitosamente en 139.90 segundos
2025-06-17 16:56:07,622 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: EXITOSO
2025-06-17 17:01:26,445 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-17 17:01:26,458 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-17 17:01:26,507 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-17 17:01:26,508 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-15
2025-06-17 17:01:26,508 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 17:01:26,508 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 17:05:26,475 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX completado con deduplicación automática:
2025-06-17 17:05:26,475 - LogTransaccionesPipeline - INFO -   - Registros finales: 104,116
2025-06-17 17:05:26,475 - LogTransaccionesPipeline - INFO -   - TransferIDs únicos: 104,116
2025-06-17 17:05:26,475 - LogTransaccionesPipeline - INFO -   - Duplicados eliminados: 0
2025-06-17 17:05:26,475 - LogTransaccionesPipeline - INFO -   - Archivo: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-17 17:05:26,475 - LogTransaccionesPipeline - INFO - 🎉 HOMOLOGACIÓN PERFECTA: Coincide exactamente con Oracle PRE_LOG_TRX
2025-06-17 17:05:26,475 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: COMPLETADO
2025-06-17 17:05:26,475 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: INICIANDO
2025-06-17 17:05:26,475 - LogTransaccionesPipeline - INFO - Ejecutando SP_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 17:05:36,751 - LogTransaccionesPipeline - INFO - SP_LOG_TRX completado: 104116 registros -> TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-17 17:05:36,751 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: COMPLETADO
2025-06-17 17:05:36,751 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: INICIANDO
2025-06-17 17:05:36,752 - LogTransaccionesPipeline - INFO - Extrayendo CSV final para fecha: 2025-06-15
2025-06-17 17:05:36,831 - LogTransaccionesPipeline - INFO - CSV final generado: 104116 registros -> output/TR-********.csv
2025-06-17 17:05:36,831 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: COMPLETADO
2025-06-17 17:05:36,831 - LogTransaccionesPipeline - INFO - ✅ Pipeline LOG_TRANSACCIONES completado exitosamente en 250.32 segundos
2025-06-17 17:05:36,831 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: EXITOSO
2025-06-17 17:07:11,926 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-17 17:07:11,938 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-17 17:07:11,988 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-17 17:07:11,988 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-15
2025-06-17 17:07:11,988 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 17:07:11,988 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 17:10:22,972 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX completado con deduplicación automática:
2025-06-17 17:10:22,973 - LogTransaccionesPipeline - INFO -   - Registros finales: 104,116
2025-06-17 17:10:22,973 - LogTransaccionesPipeline - INFO -   - TransferIDs únicos: 104,116
2025-06-17 17:10:22,973 - LogTransaccionesPipeline - INFO -   - Duplicados eliminados: 0
2025-06-17 17:10:22,973 - LogTransaccionesPipeline - INFO -   - Archivo: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-17 17:10:22,973 - LogTransaccionesPipeline - INFO - 🎉 HOMOLOGACIÓN PERFECTA: Coincide exactamente con Oracle PRE_LOG_TRX
2025-06-17 17:10:22,973 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: COMPLETADO
2025-06-17 17:10:22,973 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: INICIANDO
2025-06-17 17:10:22,973 - LogTransaccionesPipeline - INFO - Ejecutando SP_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 17:10:32,134 - LogTransaccionesPipeline - INFO - SP_LOG_TRX completado: 104116 registros -> TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-17 17:10:32,135 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: COMPLETADO
2025-06-17 17:10:32,135 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: INICIANDO
2025-06-17 17:10:32,135 - LogTransaccionesPipeline - INFO - Extrayendo CSV final para fecha: 2025-06-15
2025-06-17 17:10:32,211 - LogTransaccionesPipeline - INFO - CSV final generado: 104116 registros -> output/TR-********.csv
2025-06-17 17:10:32,212 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: COMPLETADO
2025-06-17 17:10:32,212 - LogTransaccionesPipeline - INFO - ✅ Pipeline LOG_TRANSACCIONES completado exitosamente en 200.22 segundos
2025-06-17 17:10:32,212 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: EXITOSO
2025-06-17 17:12:19,973 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-17 17:12:19,991 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-17 17:12:20,040 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-17 17:12:20,041 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-15
2025-06-17 17:12:20,041 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 17:12:20,041 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 17:14:28,610 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX completado con deduplicación automática:
2025-06-17 17:14:28,610 - LogTransaccionesPipeline - INFO -   - Registros finales: 104,116
2025-06-17 17:14:28,610 - LogTransaccionesPipeline - INFO -   - TransferIDs únicos: 104,116
2025-06-17 17:14:28,610 - LogTransaccionesPipeline - INFO -   - Duplicados eliminados: 0
2025-06-17 17:14:28,610 - LogTransaccionesPipeline - INFO -   - Archivo: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-17 17:14:28,610 - LogTransaccionesPipeline - INFO - 🎉 HOMOLOGACIÓN PERFECTA: Coincide exactamente con Oracle PRE_LOG_TRX
2025-06-17 17:14:28,610 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: COMPLETADO
2025-06-17 17:14:28,610 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: INICIANDO
2025-06-17 17:14:28,610 - LogTransaccionesPipeline - INFO - Ejecutando SP_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 17:14:37,582 - LogTransaccionesPipeline - INFO - SP_LOG_TRX completado: 104116 registros -> TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-17 17:14:37,582 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: COMPLETADO
2025-06-17 17:14:37,583 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: INICIANDO
2025-06-17 17:14:37,583 - LogTransaccionesPipeline - INFO - Extrayendo CSV final para fecha: 2025-06-15
2025-06-17 17:14:37,659 - LogTransaccionesPipeline - INFO - CSV final generado: 104116 registros -> output/TR-********.csv
2025-06-17 17:14:37,659 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: COMPLETADO
2025-06-17 17:14:37,659 - LogTransaccionesPipeline - INFO - ✅ Pipeline LOG_TRANSACCIONES completado exitosamente en 137.62 segundos
2025-06-17 17:14:37,659 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: EXITOSO
2025-06-17 17:17:35,592 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-17 17:17:35,604 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-17 17:17:35,651 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-17 17:17:35,651 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-15
2025-06-17 17:17:35,652 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 17:17:35,652 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 17:19:43,410 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX completado con deduplicación automática:
2025-06-17 17:19:43,410 - LogTransaccionesPipeline - INFO -   - Registros finales: 104,116
2025-06-17 17:19:43,410 - LogTransaccionesPipeline - INFO -   - TransferIDs únicos: 104,116
2025-06-17 17:19:43,410 - LogTransaccionesPipeline - INFO -   - Duplicados eliminados: 0
2025-06-17 17:19:43,410 - LogTransaccionesPipeline - INFO -   - Archivo: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-17 17:19:43,410 - LogTransaccionesPipeline - INFO - 🎉 HOMOLOGACIÓN PERFECTA: Coincide exactamente con Oracle PRE_LOG_TRX
2025-06-17 17:19:43,410 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: COMPLETADO
2025-06-17 17:19:43,410 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: INICIANDO
2025-06-17 17:19:43,410 - LogTransaccionesPipeline - INFO - Ejecutando SP_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 17:19:51,895 - LogTransaccionesPipeline - INFO - SP_LOG_TRX completado: 104116 registros -> TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-17 17:19:51,896 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: COMPLETADO
2025-06-17 17:19:51,896 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: INICIANDO
2025-06-17 17:19:51,896 - LogTransaccionesPipeline - INFO - Extrayendo CSV final para fecha: 2025-06-15
2025-06-17 17:19:51,968 - LogTransaccionesPipeline - INFO - CSV final generado: 104116 registros -> output/TR-********.csv
2025-06-17 17:19:51,969 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: COMPLETADO
2025-06-17 17:19:51,969 - LogTransaccionesPipeline - INFO - ✅ Pipeline LOG_TRANSACCIONES completado exitosamente en 136.32 segundos
2025-06-17 17:19:51,969 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: EXITOSO
2025-06-17 17:29:37,868 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-17 17:29:37,880 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-17 17:29:37,926 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-17 17:29:37,926 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-15
2025-06-17 17:29:37,926 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 17:29:37,926 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 17:31:50,751 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX completado con deduplicación automática:
2025-06-17 17:31:50,751 - LogTransaccionesPipeline - INFO -   - Registros finales: 104,116
2025-06-17 17:31:50,752 - LogTransaccionesPipeline - INFO -   - TransferIDs únicos: 104,116
2025-06-17 17:31:50,752 - LogTransaccionesPipeline - INFO -   - Duplicados eliminados: 0
2025-06-17 17:31:50,752 - LogTransaccionesPipeline - INFO -   - Archivo: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-17 17:31:50,752 - LogTransaccionesPipeline - INFO - 🎉 HOMOLOGACIÓN PERFECTA: Coincide exactamente con Oracle PRE_LOG_TRX
2025-06-17 17:31:50,752 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: COMPLETADO
2025-06-17 17:31:50,752 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: INICIANDO
2025-06-17 17:31:50,752 - LogTransaccionesPipeline - INFO - Ejecutando SP_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 17:31:59,846 - LogTransaccionesPipeline - INFO - SP_LOG_TRX completado: 104116 registros -> TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-17 17:31:59,847 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: COMPLETADO
2025-06-17 17:31:59,847 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: INICIANDO
2025-06-17 17:31:59,847 - LogTransaccionesPipeline - INFO - Extrayendo CSV final para fecha: 2025-06-15
2025-06-17 17:31:59,923 - LogTransaccionesPipeline - INFO - CSV final generado: 104116 registros -> output/TR-********.csv
2025-06-17 17:31:59,924 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: COMPLETADO
2025-06-17 17:31:59,924 - LogTransaccionesPipeline - INFO - ✅ Pipeline LOG_TRANSACCIONES completado exitosamente en 142.00 segundos
2025-06-17 17:31:59,924 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: EXITOSO
2025-06-17 17:38:07,068 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-17 17:38:07,087 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-17 17:38:07,134 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-17 17:38:07,134 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-15
2025-06-17 17:38:07,134 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 17:38:07,134 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 17:40:20,591 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX completado con deduplicación automática:
2025-06-17 17:40:20,591 - LogTransaccionesPipeline - INFO -   - Registros finales: 104,116
2025-06-17 17:40:20,591 - LogTransaccionesPipeline - INFO -   - TransferIDs únicos: 104,116
2025-06-17 17:40:20,591 - LogTransaccionesPipeline - INFO -   - Duplicados eliminados: 0
2025-06-17 17:40:20,591 - LogTransaccionesPipeline - INFO -   - Archivo: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-17 17:40:20,591 - LogTransaccionesPipeline - INFO - 🎉 HOMOLOGACIÓN PERFECTA: Coincide exactamente con Oracle PRE_LOG_TRX
2025-06-17 17:40:20,591 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: COMPLETADO
2025-06-17 17:40:20,591 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: INICIANDO
2025-06-17 17:40:20,591 - LogTransaccionesPipeline - INFO - Ejecutando SP_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 17:40:30,150 - LogTransaccionesPipeline - INFO - SP_LOG_TRX completado: 104116 registros -> TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-17 17:40:30,150 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: COMPLETADO
2025-06-17 17:40:30,150 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: INICIANDO
2025-06-17 17:40:30,150 - LogTransaccionesPipeline - INFO - Extrayendo CSV final para fecha: 2025-06-15
2025-06-17 17:40:30,227 - LogTransaccionesPipeline - INFO - CSV final generado: 104116 registros -> output/TR-********.csv
2025-06-17 17:40:30,227 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: COMPLETADO
2025-06-17 17:40:30,227 - LogTransaccionesPipeline - INFO - ✅ Pipeline LOG_TRANSACCIONES completado exitosamente en 143.09 segundos
2025-06-17 17:40:30,227 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: EXITOSO
2025-06-17 17:42:15,788 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-17 17:42:15,801 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-17 17:42:15,849 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-17 17:42:15,850 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-15
2025-06-17 17:42:15,850 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 17:42:15,850 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 17:44:14,266 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX completado con deduplicación automática:
2025-06-17 17:44:14,266 - LogTransaccionesPipeline - INFO -   - Registros finales: 104,116
2025-06-17 17:44:14,266 - LogTransaccionesPipeline - INFO -   - TransferIDs únicos: 104,116
2025-06-17 17:44:14,266 - LogTransaccionesPipeline - INFO -   - Duplicados eliminados: 0
2025-06-17 17:44:14,266 - LogTransaccionesPipeline - INFO -   - Archivo: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-17 17:44:14,266 - LogTransaccionesPipeline - INFO - 🎉 HOMOLOGACIÓN PERFECTA: Coincide exactamente con Oracle PRE_LOG_TRX
2025-06-17 17:44:14,266 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: COMPLETADO
2025-06-17 17:44:14,266 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: INICIANDO
2025-06-17 17:44:14,266 - LogTransaccionesPipeline - INFO - Ejecutando SP_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 17:44:22,854 - LogTransaccionesPipeline - INFO - SP_LOG_TRX completado: 104116 registros -> TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-17 17:44:22,854 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: COMPLETADO
2025-06-17 17:44:22,855 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: INICIANDO
2025-06-17 17:44:22,855 - LogTransaccionesPipeline - INFO - Extrayendo CSV final para fecha: 2025-06-15
2025-06-17 17:44:22,929 - LogTransaccionesPipeline - INFO - CSV final generado: 104116 registros -> output/TR-********.csv
2025-06-17 17:44:22,929 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: COMPLETADO
2025-06-17 17:44:22,929 - LogTransaccionesPipeline - INFO - ✅ Pipeline LOG_TRANSACCIONES completado exitosamente en 127.08 segundos
2025-06-17 17:44:22,929 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: EXITOSO
2025-06-17 17:53:46,601 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-17 17:53:46,614 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-17 17:53:46,666 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-17 17:53:46,667 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-15
2025-06-17 17:53:46,667 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 17:53:46,667 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 17:56:25,209 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX completado con deduplicación automática:
2025-06-17 17:56:25,209 - LogTransaccionesPipeline - INFO -   - Registros finales: 104,116
2025-06-17 17:56:25,209 - LogTransaccionesPipeline - INFO -   - TransferIDs únicos: 104,116
2025-06-17 17:56:25,209 - LogTransaccionesPipeline - INFO -   - Duplicados eliminados: 0
2025-06-17 17:56:25,209 - LogTransaccionesPipeline - INFO -   - Archivo: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-17 17:56:25,209 - LogTransaccionesPipeline - INFO - 🎉 HOMOLOGACIÓN PERFECTA: Coincide exactamente con Oracle PRE_LOG_TRX
2025-06-17 17:56:25,209 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: COMPLETADO
2025-06-17 17:56:25,209 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: INICIANDO
2025-06-17 17:56:25,209 - LogTransaccionesPipeline - INFO - Ejecutando SP_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 17:56:34,364 - LogTransaccionesPipeline - INFO - SP_LOG_TRX completado: 104116 registros -> TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-17 17:56:34,364 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: COMPLETADO
2025-06-17 17:56:34,364 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: INICIANDO
2025-06-17 17:56:34,364 - LogTransaccionesPipeline - INFO - Extrayendo CSV final para fecha: 2025-06-15
2025-06-17 17:56:34,450 - LogTransaccionesPipeline - INFO - CSV final generado: 104116 registros -> output/TR-********.csv
2025-06-17 17:56:34,450 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: COMPLETADO
2025-06-17 17:56:34,450 - LogTransaccionesPipeline - INFO - ✅ Pipeline LOG_TRANSACCIONES completado exitosamente en 167.78 segundos
2025-06-17 17:56:34,450 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: EXITOSO
2025-06-17 18:22:16,153 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-17 18:22:16,167 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-17 18:22:16,218 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-17 18:22:16,218 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-15
2025-06-17 18:22:16,218 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 18:22:16,218 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 18:22:16,218 - LogTransaccionesPipeline - INFO - Creando tabla temporal USER_ACCOUNTS_ORACLE para homologación 100% perfecta...
2025-06-17 18:22:27,681 - LogTransaccionesPipeline - INFO - USER_IDs en transacciones: 33,896
2025-06-17 18:22:28,897 - LogTransaccionesPipeline - INFO - Datos Oracle USER_ACCOUNTS obtenidos: 33,846 registros
2025-06-17 18:22:29,250 - LogTransaccionesPipeline - INFO - Tabla temporal USER_ACCOUNTS_ORACLE creada: 33,846 registros
2025-06-17 18:25:08,190 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX completado con deduplicación automática:
2025-06-17 18:25:08,190 - LogTransaccionesPipeline - INFO -   - Registros finales: 104,116
2025-06-17 18:25:08,190 - LogTransaccionesPipeline - INFO -   - TransferIDs únicos: 104,116
2025-06-17 18:25:08,190 - LogTransaccionesPipeline - INFO -   - Duplicados eliminados: 0
2025-06-17 18:25:08,191 - LogTransaccionesPipeline - INFO -   - Archivo: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-17 18:25:08,191 - LogTransaccionesPipeline - INFO - 🎉 HOMOLOGACIÓN PERFECTA: Coincide exactamente con Oracle PRE_LOG_TRX
2025-06-17 18:25:08,191 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: COMPLETADO
2025-06-17 18:25:08,191 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: INICIANDO
2025-06-17 18:25:08,191 - LogTransaccionesPipeline - INFO - Ejecutando SP_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 18:25:17,576 - LogTransaccionesPipeline - INFO - SP_LOG_TRX completado: 104116 registros -> TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-17 18:25:17,576 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: COMPLETADO
2025-06-17 18:25:17,576 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: INICIANDO
2025-06-17 18:25:17,576 - LogTransaccionesPipeline - INFO - Extrayendo CSV final para fecha: 2025-06-15
2025-06-17 18:25:17,650 - LogTransaccionesPipeline - INFO - CSV final generado: 104116 registros -> output/TR-********.csv
2025-06-17 18:25:17,650 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: COMPLETADO
2025-06-17 18:25:17,650 - LogTransaccionesPipeline - INFO - ✅ Pipeline LOG_TRANSACCIONES completado exitosamente en 181.43 segundos
2025-06-17 18:25:17,650 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: EXITOSO
2025-06-17 18:33:32,609 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-17 18:33:32,627 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-17 18:33:32,680 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-17 18:33:32,680 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-15
2025-06-17 18:33:32,681 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 18:33:32,681 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 18:33:32,681 - LogTransaccionesPipeline - INFO - Creando tabla temporal USER_ACCOUNTS_ORACLE para homologación 100% perfecta...
2025-06-17 18:33:44,285 - LogTransaccionesPipeline - INFO - USER_IDs en transacciones: 33,896
2025-06-17 18:33:45,670 - LogTransaccionesPipeline - INFO - Datos Oracle USER_ACCOUNTS obtenidos: 33,896 registros
2025-06-17 18:33:46,028 - LogTransaccionesPipeline - INFO - Tabla temporal USER_ACCOUNTS_ORACLE creada: 33,896 registros
2025-06-17 18:35:58,942 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX completado con deduplicación automática:
2025-06-17 18:35:58,942 - LogTransaccionesPipeline - INFO -   - Registros finales: 104,116
2025-06-17 18:35:58,942 - LogTransaccionesPipeline - INFO -   - TransferIDs únicos: 104,116
2025-06-17 18:35:58,942 - LogTransaccionesPipeline - INFO -   - Duplicados eliminados: 0
2025-06-17 18:35:58,942 - LogTransaccionesPipeline - INFO -   - Archivo: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-17 18:35:58,942 - LogTransaccionesPipeline - INFO - 🎉 HOMOLOGACIÓN PERFECTA: Coincide exactamente con Oracle PRE_LOG_TRX
2025-06-17 18:35:58,942 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: COMPLETADO
2025-06-17 18:35:58,942 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: INICIANDO
2025-06-17 18:35:58,942 - LogTransaccionesPipeline - INFO - Ejecutando SP_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 18:36:07,837 - LogTransaccionesPipeline - INFO - SP_LOG_TRX completado: 104116 registros -> TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-17 18:36:07,838 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: COMPLETADO
2025-06-17 18:36:07,838 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: INICIANDO
2025-06-17 18:36:07,838 - LogTransaccionesPipeline - INFO - Extrayendo CSV final para fecha: 2025-06-15
2025-06-17 18:36:07,911 - LogTransaccionesPipeline - INFO - CSV final generado: 104116 registros -> output/TR-********.csv
2025-06-17 18:36:07,911 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: COMPLETADO
2025-06-17 18:36:07,911 - LogTransaccionesPipeline - INFO - ✅ Pipeline LOG_TRANSACCIONES completado exitosamente en 155.23 segundos
2025-06-17 18:36:07,911 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: EXITOSO
2025-06-17 18:42:35,408 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-17 18:42:35,420 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-17 18:42:35,469 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-17 18:42:35,469 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-15
2025-06-17 18:42:35,469 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 18:42:35,469 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 18:42:35,469 - LogTransaccionesPipeline - INFO - Creando tabla temporal USER_ACCOUNTS_ORACLE para homologación 100% perfecta...
2025-06-17 18:42:46,926 - LogTransaccionesPipeline - INFO - USER_IDs en transacciones: 33,896
2025-06-17 18:42:48,112 - LogTransaccionesPipeline - INFO - Datos Oracle USER_ACCOUNTS obtenidos: 33,896 registros
2025-06-17 18:42:48,611 - LogTransaccionesPipeline - INFO - Tabla temporal USER_ACCOUNTS_ORACLE creada: 33,896 registros
2025-06-17 18:45:01,635 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX completado con deduplicación automática:
2025-06-17 18:45:01,635 - LogTransaccionesPipeline - INFO -   - Registros finales: 104,116
2025-06-17 18:45:01,635 - LogTransaccionesPipeline - INFO -   - TransferIDs únicos: 104,116
2025-06-17 18:45:01,635 - LogTransaccionesPipeline - INFO -   - Duplicados eliminados: 0
2025-06-17 18:45:01,635 - LogTransaccionesPipeline - INFO -   - Archivo: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-17 18:45:01,635 - LogTransaccionesPipeline - INFO - 🎉 HOMOLOGACIÓN PERFECTA: Coincide exactamente con Oracle PRE_LOG_TRX
2025-06-17 18:45:01,635 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: COMPLETADO
2025-06-17 18:45:01,635 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: INICIANDO
2025-06-17 18:45:01,635 - LogTransaccionesPipeline - INFO - Ejecutando SP_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 18:45:10,212 - LogTransaccionesPipeline - INFO - SP_LOG_TRX completado: 104116 registros -> TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-17 18:45:10,212 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: COMPLETADO
2025-06-17 18:45:10,212 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: INICIANDO
2025-06-17 18:45:10,212 - LogTransaccionesPipeline - INFO - Extrayendo CSV final para fecha: 2025-06-15
2025-06-17 18:45:10,293 - LogTransaccionesPipeline - INFO - CSV final generado: 104116 registros -> output/TR-********.csv
2025-06-17 18:45:10,293 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: COMPLETADO
2025-06-17 18:45:10,293 - LogTransaccionesPipeline - INFO - ✅ Pipeline LOG_TRANSACCIONES completado exitosamente en 154.82 segundos
2025-06-17 18:45:10,293 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: EXITOSO
2025-06-17 18:48:54,284 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-17 18:48:54,297 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-17 18:48:54,345 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-17 18:48:54,345 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-15
2025-06-17 18:48:54,345 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 18:48:54,345 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 18:48:54,345 - LogTransaccionesPipeline - INFO - Creando tabla temporal USER_ACCOUNTS_ORACLE para homologación 100% perfecta...
2025-06-17 18:49:05,594 - LogTransaccionesPipeline - INFO - USER_IDs en transacciones: 33,896
2025-06-17 18:49:06,721 - LogTransaccionesPipeline - INFO - Datos Oracle USER_ACCOUNTS obtenidos: 33,896 registros
2025-06-17 18:49:07,217 - LogTransaccionesPipeline - INFO - Tabla temporal USER_ACCOUNTS_ORACLE creada: 33,896 registros
2025-06-17 18:49:19,487 - LogTransaccionesPipeline - ERROR - Error completando SP_PRE_LOG_TRX: Binder Error: Cannot compare values of type TIMESTAMP and type VARCHAR - an explicit cast is required

LINE 225:                      AND MW_TEMP.MODIFIED_ON <= MTH.TRANSFER_DATE
                                                       ^
2025-06-17 18:49:19,488 - LogTransaccionesPipeline - ERROR - Error en SP_PRE_LOG_TRX: Binder Error: Cannot compare values of type TIMESTAMP and type VARCHAR - an explicit cast is required

LINE 225:                      AND MW_TEMP.MODIFIED_ON <= MTH.TRANSFER_DATE
                                                       ^
2025-06-17 18:49:19,488 - LogTransaccionesPipeline - ERROR - Error en SP_PRE_LOG_TRX: Binder Error: Cannot compare values of type TIMESTAMP and type VARCHAR - an explicit cast is required

LINE 225:                      AND MW_TEMP.MODIFIED_ON <= MTH.TRANSFER_DATE
                                                       ^
2025-06-17 18:49:19,488 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: ERROR: Error en SP_PRE_LOG_TRX: Binder Error: Cannot compare values of type TIMESTAMP and type VARCHAR - an explicit cast is required

LINE 225:                      AND MW_TEMP.MODIFIED_ON <= MTH.TRANSFER_DATE
                                                       ^
2025-06-17 18:49:19,488 - LogTransaccionesPipeline - ERROR - ❌ Pipeline LOG_TRANSACCIONES falló después de 25.14 segundos: Binder Error: Cannot compare values of type TIMESTAMP and type VARCHAR - an explicit cast is required

LINE 225:                      AND MW_TEMP.MODIFIED_ON <= MTH.TRANSFER_DATE
                                                       ^
2025-06-17 18:49:19,488 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: ERROR: Binder Error: Cannot compare values of type TIMESTAMP and type VARCHAR - an explicit cast is required

LINE 225:                      AND MW_TEMP.MODIFIED_ON <= MTH.TRANSFER_DATE
                                                       ^
2025-06-17 18:49:56,519 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-17 18:49:56,531 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-17 18:49:56,579 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-17 18:49:56,580 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-15
2025-06-17 18:49:56,580 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 18:49:56,580 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 18:49:56,580 - LogTransaccionesPipeline - INFO - Creando tabla temporal USER_ACCOUNTS_ORACLE para homologación 100% perfecta...
2025-06-17 18:50:08,086 - LogTransaccionesPipeline - INFO - USER_IDs en transacciones: 33,896
2025-06-17 18:50:09,173 - LogTransaccionesPipeline - INFO - Datos Oracle USER_ACCOUNTS obtenidos: 33,896 registros
2025-06-17 18:50:09,669 - LogTransaccionesPipeline - INFO - Tabla temporal USER_ACCOUNTS_ORACLE creada: 33,896 registros
2025-06-17 18:52:21,618 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX completado con deduplicación automática:
2025-06-17 18:52:21,618 - LogTransaccionesPipeline - INFO -   - Registros finales: 104,116
2025-06-17 18:52:21,618 - LogTransaccionesPipeline - INFO -   - TransferIDs únicos: 104,116
2025-06-17 18:52:21,618 - LogTransaccionesPipeline - INFO -   - Duplicados eliminados: 0
2025-06-17 18:52:21,618 - LogTransaccionesPipeline - INFO -   - Archivo: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-17 18:52:21,618 - LogTransaccionesPipeline - INFO - 🎉 HOMOLOGACIÓN PERFECTA: Coincide exactamente con Oracle PRE_LOG_TRX
2025-06-17 18:52:21,618 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: COMPLETADO
2025-06-17 18:52:21,618 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: INICIANDO
2025-06-17 18:52:21,618 - LogTransaccionesPipeline - INFO - Ejecutando SP_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 18:52:30,274 - LogTransaccionesPipeline - INFO - SP_LOG_TRX completado: 104116 registros -> TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-17 18:52:30,274 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: COMPLETADO
2025-06-17 18:52:30,274 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: INICIANDO
2025-06-17 18:52:30,274 - LogTransaccionesPipeline - INFO - Extrayendo CSV final para fecha: 2025-06-15
2025-06-17 18:52:30,346 - LogTransaccionesPipeline - INFO - CSV final generado: 104116 registros -> output/TR-********.csv
2025-06-17 18:52:30,346 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: COMPLETADO
2025-06-17 18:52:30,346 - LogTransaccionesPipeline - INFO - ✅ Pipeline LOG_TRANSACCIONES completado exitosamente en 153.77 segundos
2025-06-17 18:52:30,346 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: EXITOSO
2025-06-17 18:54:29,065 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-17 18:54:29,077 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-17 18:54:29,126 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-17 18:54:29,127 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-15
2025-06-17 18:54:29,127 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 18:54:29,127 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 18:54:29,127 - LogTransaccionesPipeline - INFO - Creando tabla temporal USER_ACCOUNTS_ORACLE para homologación 100% perfecta...
2025-06-17 18:54:40,933 - LogTransaccionesPipeline - INFO - USER_IDs en transacciones: 33,896
2025-06-17 18:54:42,086 - LogTransaccionesPipeline - INFO - Datos Oracle USER_ACCOUNTS obtenidos: 33,896 registros
2025-06-17 18:54:42,586 - LogTransaccionesPipeline - INFO - Tabla temporal USER_ACCOUNTS_ORACLE creada: 33,896 registros
2025-06-17 18:56:47,890 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX completado con deduplicación automática:
2025-06-17 18:56:47,891 - LogTransaccionesPipeline - INFO -   - Registros finales: 104,116
2025-06-17 18:56:47,891 - LogTransaccionesPipeline - INFO -   - TransferIDs únicos: 104,116
2025-06-17 18:56:47,891 - LogTransaccionesPipeline - INFO -   - Duplicados eliminados: 0
2025-06-17 18:56:47,891 - LogTransaccionesPipeline - INFO -   - Archivo: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-17 18:56:47,891 - LogTransaccionesPipeline - INFO - 🎉 HOMOLOGACIÓN PERFECTA: Coincide exactamente con Oracle PRE_LOG_TRX
2025-06-17 18:56:47,891 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: COMPLETADO
2025-06-17 18:56:47,891 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: INICIANDO
2025-06-17 18:56:47,891 - LogTransaccionesPipeline - INFO - Ejecutando SP_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 18:56:56,748 - LogTransaccionesPipeline - INFO - SP_LOG_TRX completado: 104116 registros -> TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-17 18:56:56,748 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: COMPLETADO
2025-06-17 18:56:56,748 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: INICIANDO
2025-06-17 18:56:56,748 - LogTransaccionesPipeline - INFO - Extrayendo CSV final para fecha: 2025-06-15
2025-06-17 18:56:56,825 - LogTransaccionesPipeline - INFO - CSV final generado: 104116 registros -> output/TR-********.csv
2025-06-17 18:56:56,826 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: COMPLETADO
2025-06-17 18:56:56,826 - LogTransaccionesPipeline - INFO - ✅ Pipeline LOG_TRANSACCIONES completado exitosamente en 147.70 segundos
2025-06-17 18:56:56,826 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: EXITOSO
2025-06-17 18:58:29,561 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-17 18:58:29,574 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-17 18:58:29,624 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-17 18:58:29,624 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-15
2025-06-17 18:58:29,624 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 18:58:29,624 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 18:58:29,624 - LogTransaccionesPipeline - INFO - Creando tabla temporal USER_ACCOUNTS_ORACLE para homologación 100% perfecta...
2025-06-17 19:01:22,956 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-17 19:01:22,969 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-17 19:01:23,019 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-17 19:01:23,019 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-15
2025-06-17 19:01:23,019 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 19:01:23,019 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 19:01:23,019 - LogTransaccionesPipeline - INFO - Creando tabla temporal USER_ACCOUNTS_ORACLE para homologación 100% perfecta...
2025-06-17 19:01:34,454 - LogTransaccionesPipeline - INFO - USER_IDs en transacciones: 33,896
2025-06-17 19:01:35,523 - LogTransaccionesPipeline - INFO - Datos Oracle USER_ACCOUNTS obtenidos: 33,896 registros
2025-06-17 19:01:36,023 - LogTransaccionesPipeline - INFO - Tabla temporal USER_ACCOUNTS_ORACLE creada: 33,896 registros
2025-06-17 19:01:48,664 - LogTransaccionesPipeline - ERROR - Error completando SP_PRE_LOG_TRX: Binder Error: Values list "MTH" does not have a column named "TransferID"

LINE 222:                     WHEN MTH."TransferID" = '***************' AND UPAYER.M_USER_ID...
                                   ^
2025-06-17 19:01:48,665 - LogTransaccionesPipeline - ERROR - Error en SP_PRE_LOG_TRX: Binder Error: Values list "MTH" does not have a column named "TransferID"

LINE 222:                     WHEN MTH."TransferID" = '***************' AND UPAYER.M_USER_ID...
                                   ^
2025-06-17 19:01:48,665 - LogTransaccionesPipeline - ERROR - Error en SP_PRE_LOG_TRX: Binder Error: Values list "MTH" does not have a column named "TransferID"

LINE 222:                     WHEN MTH."TransferID" = '***************' AND UPAYER.M_USER_ID...
                                   ^
2025-06-17 19:01:48,665 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: ERROR: Error en SP_PRE_LOG_TRX: Binder Error: Values list "MTH" does not have a column named "TransferID"

LINE 222:                     WHEN MTH."TransferID" = '***************' AND UPAYER.M_USER_ID...
                                   ^
2025-06-17 19:01:48,665 - LogTransaccionesPipeline - ERROR - ❌ Pipeline LOG_TRANSACCIONES falló después de 25.65 segundos: Binder Error: Values list "MTH" does not have a column named "TransferID"

LINE 222:                     WHEN MTH."TransferID" = '***************' AND UPAYER.M_USER_ID...
                                   ^
2025-06-17 19:01:48,665 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: ERROR: Binder Error: Values list "MTH" does not have a column named "TransferID"

LINE 222:                     WHEN MTH."TransferID" = '***************' AND UPAYER.M_USER_ID...
                                   ^
2025-06-17 19:02:24,934 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-17 19:02:24,947 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-17 19:02:24,996 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-17 19:02:24,996 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-15
2025-06-17 19:02:24,996 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 19:02:24,996 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 19:02:24,997 - LogTransaccionesPipeline - INFO - Creando tabla temporal USER_ACCOUNTS_ORACLE para homologación 100% perfecta...
2025-06-17 19:02:36,485 - LogTransaccionesPipeline - INFO - USER_IDs en transacciones: 33,896
2025-06-17 19:02:37,635 - LogTransaccionesPipeline - INFO - Datos Oracle USER_ACCOUNTS obtenidos: 33,896 registros
2025-06-17 19:02:38,143 - LogTransaccionesPipeline - INFO - Tabla temporal USER_ACCOUNTS_ORACLE creada: 33,896 registros
2025-06-17 19:04:49,305 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX completado con deduplicación automática:
2025-06-17 19:04:49,305 - LogTransaccionesPipeline - INFO -   - Registros finales: 104,116
2025-06-17 19:04:49,305 - LogTransaccionesPipeline - INFO -   - TransferIDs únicos: 104,116
2025-06-17 19:04:49,305 - LogTransaccionesPipeline - INFO -   - Duplicados eliminados: 0
2025-06-17 19:04:49,305 - LogTransaccionesPipeline - INFO -   - Archivo: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-17 19:04:49,305 - LogTransaccionesPipeline - INFO - 🎉 HOMOLOGACIÓN PERFECTA: Coincide exactamente con Oracle PRE_LOG_TRX
2025-06-17 19:04:49,305 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX_UPDATE para correcciones específicas...
2025-06-17 19:04:49,306 - LogTransaccionesPipeline - ERROR - Error en SP_PRE_LOG_TRX_UPDATE: Parser Error: syntax error at or near "("
2025-06-17 19:04:49,306 - LogTransaccionesPipeline - ERROR - Error completando SP_PRE_LOG_TRX: Parser Error: syntax error at or near "("
2025-06-17 19:04:49,306 - LogTransaccionesPipeline - ERROR - Error en SP_PRE_LOG_TRX: Parser Error: syntax error at or near "("
2025-06-17 19:04:49,306 - LogTransaccionesPipeline - ERROR - Error en SP_PRE_LOG_TRX: Parser Error: syntax error at or near "("
2025-06-17 19:04:49,306 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: ERROR: Error en SP_PRE_LOG_TRX: Parser Error: syntax error at or near "("
2025-06-17 19:04:49,306 - LogTransaccionesPipeline - ERROR - ❌ Pipeline LOG_TRANSACCIONES falló después de 144.31 segundos: Parser Error: syntax error at or near "("
2025-06-17 19:04:49,306 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: ERROR: Parser Error: syntax error at or near "("
2025-06-17 19:05:19,659 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-17 19:05:19,674 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-17 19:05:19,722 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-17 19:05:19,723 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-15
2025-06-17 19:05:19,723 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 19:05:19,723 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 19:05:19,723 - LogTransaccionesPipeline - INFO - Creando tabla temporal USER_ACCOUNTS_ORACLE para homologación 100% perfecta...
2025-06-17 19:05:31,454 - LogTransaccionesPipeline - INFO - USER_IDs en transacciones: 33,896
2025-06-17 19:05:32,590 - LogTransaccionesPipeline - INFO - Datos Oracle USER_ACCOUNTS obtenidos: 33,896 registros
2025-06-17 19:05:33,086 - LogTransaccionesPipeline - INFO - Tabla temporal USER_ACCOUNTS_ORACLE creada: 33,896 registros
2025-06-17 19:07:36,554 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX completado con deduplicación automática:
2025-06-17 19:07:36,554 - LogTransaccionesPipeline - INFO -   - Registros finales: 104,116
2025-06-17 19:07:36,554 - LogTransaccionesPipeline - INFO -   - TransferIDs únicos: 104,116
2025-06-17 19:07:36,555 - LogTransaccionesPipeline - INFO -   - Duplicados eliminados: 0
2025-06-17 19:07:36,555 - LogTransaccionesPipeline - INFO -   - Archivo: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-17 19:07:36,555 - LogTransaccionesPipeline - INFO - 🎉 HOMOLOGACIÓN PERFECTA: Coincide exactamente con Oracle PRE_LOG_TRX
2025-06-17 19:07:36,555 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: COMPLETADO
2025-06-17 19:07:36,555 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: INICIANDO
2025-06-17 19:07:36,555 - LogTransaccionesPipeline - INFO - Ejecutando SP_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 19:07:45,095 - LogTransaccionesPipeline - INFO - SP_LOG_TRX completado: 104116 registros -> TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-17 19:07:45,095 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: COMPLETADO
2025-06-17 19:07:45,095 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: INICIANDO
2025-06-17 19:07:45,095 - LogTransaccionesPipeline - INFO - Extrayendo CSV final para fecha: 2025-06-15
2025-06-17 19:07:45,180 - LogTransaccionesPipeline - INFO - CSV final generado: 104116 registros -> output/TR-********.csv
2025-06-17 19:07:45,181 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: COMPLETADO
2025-06-17 19:07:45,181 - LogTransaccionesPipeline - INFO - ✅ Pipeline LOG_TRANSACCIONES completado exitosamente en 145.46 segundos
2025-06-17 19:07:45,181 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: EXITOSO
2025-06-17 19:09:23,756 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-17 19:09:23,768 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-17 19:09:23,818 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-17 19:09:23,818 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-15
2025-06-17 19:09:23,819 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 19:09:23,819 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 19:09:23,819 - LogTransaccionesPipeline - INFO - Creando tabla temporal USER_ACCOUNTS_ORACLE para homologación 100% perfecta...
2025-06-17 19:09:35,110 - LogTransaccionesPipeline - INFO - USER_IDs en transacciones: 33,896
2025-06-17 19:09:36,211 - LogTransaccionesPipeline - INFO - Datos Oracle USER_ACCOUNTS obtenidos: 33,896 registros
2025-06-17 19:09:36,721 - LogTransaccionesPipeline - INFO - Tabla temporal USER_ACCOUNTS_ORACLE creada: 33,896 registros
2025-06-17 19:11:42,689 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX completado con deduplicación automática:
2025-06-17 19:11:42,689 - LogTransaccionesPipeline - INFO -   - Registros finales: 104,116
2025-06-17 19:11:42,689 - LogTransaccionesPipeline - INFO -   - TransferIDs únicos: 104,116
2025-06-17 19:11:42,689 - LogTransaccionesPipeline - INFO -   - Duplicados eliminados: 0
2025-06-17 19:11:42,689 - LogTransaccionesPipeline - INFO -   - Archivo: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-17 19:11:42,689 - LogTransaccionesPipeline - INFO - 🎉 HOMOLOGACIÓN PERFECTA: Coincide exactamente con Oracle PRE_LOG_TRX
2025-06-17 19:11:42,689 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: COMPLETADO
2025-06-17 19:11:42,689 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: INICIANDO
2025-06-17 19:11:42,689 - LogTransaccionesPipeline - INFO - Ejecutando SP_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 19:11:51,741 - LogTransaccionesPipeline - INFO - SP_LOG_TRX completado: 104116 registros -> TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-17 19:11:51,741 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: COMPLETADO
2025-06-17 19:11:51,741 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: INICIANDO
2025-06-17 19:11:51,741 - LogTransaccionesPipeline - INFO - Extrayendo CSV final para fecha: 2025-06-15
2025-06-17 19:11:51,818 - LogTransaccionesPipeline - INFO - CSV final generado: 104116 registros -> output/TR-********.csv
2025-06-17 19:11:51,818 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: COMPLETADO
2025-06-17 19:11:51,818 - LogTransaccionesPipeline - INFO - ✅ Pipeline LOG_TRANSACCIONES completado exitosamente en 148.00 segundos
2025-06-17 19:11:51,818 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: EXITOSO
2025-06-17 19:13:27,111 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-17 19:13:27,124 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-17 19:13:27,172 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-17 19:13:27,172 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-15
2025-06-17 19:13:27,172 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 19:13:27,172 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 19:13:27,172 - LogTransaccionesPipeline - INFO - Creando tabla temporal USER_ACCOUNTS_ORACLE para homologación 100% perfecta...
2025-06-17 19:13:38,603 - LogTransaccionesPipeline - INFO - USER_IDs en transacciones: 33,896
2025-06-17 19:13:39,813 - LogTransaccionesPipeline - INFO - Datos Oracle USER_ACCOUNTS obtenidos: 33,896 registros
2025-06-17 19:13:40,323 - LogTransaccionesPipeline - INFO - Tabla temporal USER_ACCOUNTS_ORACLE creada: 33,896 registros
2025-06-17 19:15:45,528 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX completado con deduplicación automática:
2025-06-17 19:15:45,528 - LogTransaccionesPipeline - INFO -   - Registros finales: 104,116
2025-06-17 19:15:45,528 - LogTransaccionesPipeline - INFO -   - TransferIDs únicos: 104,116
2025-06-17 19:15:45,528 - LogTransaccionesPipeline - INFO -   - Duplicados eliminados: 0
2025-06-17 19:15:45,528 - LogTransaccionesPipeline - INFO -   - Archivo: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-17 19:15:45,528 - LogTransaccionesPipeline - INFO - 🎉 HOMOLOGACIÓN PERFECTA: Coincide exactamente con Oracle PRE_LOG_TRX
2025-06-17 19:15:45,528 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: COMPLETADO
2025-06-17 19:15:45,528 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: INICIANDO
2025-06-17 19:15:45,528 - LogTransaccionesPipeline - INFO - Ejecutando SP_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 19:15:54,258 - LogTransaccionesPipeline - INFO - SP_LOG_TRX completado: 104116 registros -> TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-17 19:15:54,258 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: COMPLETADO
2025-06-17 19:15:54,258 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: INICIANDO
2025-06-17 19:15:54,258 - LogTransaccionesPipeline - INFO - Extrayendo CSV final para fecha: 2025-06-15
2025-06-17 19:15:54,329 - LogTransaccionesPipeline - INFO - CSV final generado: 104116 registros -> output/TR-********.csv
2025-06-17 19:15:54,329 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: COMPLETADO
2025-06-17 19:15:54,329 - LogTransaccionesPipeline - INFO - ✅ Pipeline LOG_TRANSACCIONES completado exitosamente en 147.16 segundos
2025-06-17 19:15:54,329 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: EXITOSO
2025-06-17 19:19:34,572 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-17 19:19:34,584 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-17 19:19:34,634 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-17 19:19:34,634 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-15
2025-06-17 19:19:34,634 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 19:19:34,634 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 19:19:34,634 - LogTransaccionesPipeline - INFO - Creando tabla temporal USER_ACCOUNTS_ORACLE para homologación 100% perfecta...
2025-06-17 19:19:45,998 - LogTransaccionesPipeline - INFO - USER_IDs en transacciones: 33,896
2025-06-17 19:19:47,090 - LogTransaccionesPipeline - INFO - Datos Oracle USER_ACCOUNTS obtenidos: 33,896 registros
2025-06-17 19:19:47,591 - LogTransaccionesPipeline - INFO - Tabla temporal USER_ACCOUNTS_ORACLE creada: 33,896 registros
2025-06-17 19:19:59,763 - LogTransaccionesPipeline - ERROR - Error completando SP_PRE_LOG_TRX: Binder Error: Referenced column "TransferValue" not found in FROM clause!
Candidate bindings: "TransferDate", "TransferStatus", "TransferID", "TransferID_Mob", "TransactionType"

LINE 20:                 "TransferValue",
                         ^
2025-06-17 19:19:59,763 - LogTransaccionesPipeline - ERROR - Error en SP_PRE_LOG_TRX: Binder Error: Referenced column "TransferValue" not found in FROM clause!
Candidate bindings: "TransferDate", "TransferStatus", "TransferID", "TransferID_Mob", "TransactionType"

LINE 20:                 "TransferValue",
                         ^
2025-06-17 19:19:59,763 - LogTransaccionesPipeline - ERROR - Error en SP_PRE_LOG_TRX: Binder Error: Referenced column "TransferValue" not found in FROM clause!
Candidate bindings: "TransferDate", "TransferStatus", "TransferID", "TransferID_Mob", "TransactionType"

LINE 20:                 "TransferValue",
                         ^
2025-06-17 19:19:59,763 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: ERROR: Error en SP_PRE_LOG_TRX: Binder Error: Referenced column "TransferValue" not found in FROM clause!
Candidate bindings: "TransferDate", "TransferStatus", "TransferID", "TransferID_Mob", "TransactionType"

LINE 20:                 "TransferValue",
                         ^
2025-06-17 19:19:59,763 - LogTransaccionesPipeline - ERROR - ❌ Pipeline LOG_TRANSACCIONES falló después de 25.13 segundos: Binder Error: Referenced column "TransferValue" not found in FROM clause!
Candidate bindings: "TransferDate", "TransferStatus", "TransferID", "TransferID_Mob", "TransactionType"

LINE 20:                 "TransferValue",
                         ^
2025-06-17 19:19:59,763 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: ERROR: Binder Error: Referenced column "TransferValue" not found in FROM clause!
Candidate bindings: "TransferDate", "TransferStatus", "TransferID", "TransferID_Mob", "TransactionType"

LINE 20:                 "TransferValue",
                         ^
2025-06-17 19:20:38,986 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-17 19:20:38,999 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-17 19:20:39,047 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-17 19:20:39,047 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-15
2025-06-17 19:20:39,047 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 19:20:39,047 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 19:20:39,047 - LogTransaccionesPipeline - INFO - Creando tabla temporal USER_ACCOUNTS_ORACLE para homologación 100% perfecta...
2025-06-17 19:20:50,501 - LogTransaccionesPipeline - INFO - USER_IDs en transacciones: 33,896
2025-06-17 19:20:51,647 - LogTransaccionesPipeline - INFO - Datos Oracle USER_ACCOUNTS obtenidos: 33,896 registros
2025-06-17 19:20:52,150 - LogTransaccionesPipeline - INFO - Tabla temporal USER_ACCOUNTS_ORACLE creada: 33,896 registros
2025-06-17 19:22:12,004 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX completado con deduplicación automática:
2025-06-17 19:22:12,004 - LogTransaccionesPipeline - INFO -   - Registros finales: 104,116
2025-06-17 19:22:12,004 - LogTransaccionesPipeline - INFO -   - TransferIDs únicos: 104,116
2025-06-17 19:22:12,004 - LogTransaccionesPipeline - INFO -   - Duplicados eliminados: 0
2025-06-17 19:22:12,004 - LogTransaccionesPipeline - INFO -   - Archivo: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-17 19:22:12,004 - LogTransaccionesPipeline - INFO - 🎉 HOMOLOGACIÓN PERFECTA: Coincide exactamente con Oracle PRE_LOG_TRX
2025-06-17 19:22:12,004 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: COMPLETADO
2025-06-17 19:22:12,005 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: INICIANDO
2025-06-17 19:22:12,005 - LogTransaccionesPipeline - INFO - Ejecutando SP_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 19:22:12,172 - LogTransaccionesPipeline - ERROR - Error en SP_LOG_TRX: Binder Error: Column "Context" referenced that exists in the SELECT clause - but this column cannot be referenced before it is defined
2025-06-17 19:22:12,173 - LogTransaccionesPipeline - ERROR - Error en SP_LOG_TRX: Binder Error: Column "Context" referenced that exists in the SELECT clause - but this column cannot be referenced before it is defined
2025-06-17 19:22:12,173 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: ERROR: Error en SP_LOG_TRX: Binder Error: Column "Context" referenced that exists in the SELECT clause - but this column cannot be referenced before it is defined
2025-06-17 19:22:12,173 - LogTransaccionesPipeline - ERROR - ❌ Pipeline LOG_TRANSACCIONES falló después de 93.13 segundos: Binder Error: Column "Context" referenced that exists in the SELECT clause - but this column cannot be referenced before it is defined
2025-06-17 19:22:12,173 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: ERROR: Binder Error: Column "Context" referenced that exists in the SELECT clause - but this column cannot be referenced before it is defined
2025-06-17 19:27:25,356 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-17 19:27:25,369 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-17 19:27:25,424 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-17 19:27:25,425 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-15
2025-06-17 19:27:25,425 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 19:27:25,425 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX usando 100% tablas S3 para fecha: 2025-06-15
2025-06-17 19:28:43,320 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX completado con deduplicación automática:
2025-06-17 19:28:43,320 - LogTransaccionesPipeline - INFO -   - Registros finales: 104,116
2025-06-17 19:28:43,320 - LogTransaccionesPipeline - INFO -   - TransferIDs únicos: 104,116
2025-06-17 19:28:43,320 - LogTransaccionesPipeline - INFO -   - Duplicados eliminados: 0
2025-06-17 19:28:43,320 - LogTransaccionesPipeline - INFO -   - Archivo: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-17 19:28:43,320 - LogTransaccionesPipeline - INFO - 🎉 HOMOLOGACIÓN PERFECTA: Coincide exactamente con Oracle PRE_LOG_TRX
2025-06-17 19:28:43,320 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: COMPLETADO
2025-06-17 19:28:43,320 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: INICIANDO
2025-06-17 19:28:43,320 - LogTransaccionesPipeline - INFO - Ejecutando SP_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 19:28:43,459 - LogTransaccionesPipeline - ERROR - Error en SP_LOG_TRX: Binder Error: Column "Context" referenced that exists in the SELECT clause - but this column cannot be referenced before it is defined
2025-06-17 19:28:43,459 - LogTransaccionesPipeline - ERROR - Error en SP_LOG_TRX: Binder Error: Column "Context" referenced that exists in the SELECT clause - but this column cannot be referenced before it is defined
2025-06-17 19:28:43,459 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: ERROR: Error en SP_LOG_TRX: Binder Error: Column "Context" referenced that exists in the SELECT clause - but this column cannot be referenced before it is defined
2025-06-17 19:28:43,459 - LogTransaccionesPipeline - ERROR - ❌ Pipeline LOG_TRANSACCIONES falló después de 78.03 segundos: Binder Error: Column "Context" referenced that exists in the SELECT clause - but this column cannot be referenced before it is defined
2025-06-17 19:28:43,459 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: ERROR: Binder Error: Column "Context" referenced that exists in the SELECT clause - but this column cannot be referenced before it is defined
2025-06-17 19:34:06,797 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-17 19:34:06,810 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-17 19:34:06,859 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-17 19:34:06,859 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-15
2025-06-17 19:34:06,859 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 19:34:06,859 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX usando 100% tablas S3 para fecha: 2025-06-15
2025-06-17 19:35:24,597 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX completado con deduplicación automática:
2025-06-17 19:35:24,597 - LogTransaccionesPipeline - INFO -   - Registros finales: 104,116
2025-06-17 19:35:24,597 - LogTransaccionesPipeline - INFO -   - TransferIDs únicos: 104,116
2025-06-17 19:35:24,597 - LogTransaccionesPipeline - INFO -   - Duplicados eliminados: 0
2025-06-17 19:35:24,597 - LogTransaccionesPipeline - INFO -   - Archivo: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-17 19:35:24,597 - LogTransaccionesPipeline - INFO - 🎉 HOMOLOGACIÓN PERFECTA: Coincide exactamente con Oracle PRE_LOG_TRX
2025-06-17 19:35:24,597 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: COMPLETADO
2025-06-17 19:35:24,597 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: INICIANDO
2025-06-17 19:35:24,598 - LogTransaccionesPipeline - INFO - Ejecutando SP_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 19:35:24,729 - LogTransaccionesPipeline - ERROR - Error en SP_LOG_TRX: Binder Error: Column "Context" referenced that exists in the SELECT clause - but this column cannot be referenced before it is defined
2025-06-17 19:35:24,729 - LogTransaccionesPipeline - ERROR - Error en SP_LOG_TRX: Binder Error: Column "Context" referenced that exists in the SELECT clause - but this column cannot be referenced before it is defined
2025-06-17 19:35:24,729 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: ERROR: Error en SP_LOG_TRX: Binder Error: Column "Context" referenced that exists in the SELECT clause - but this column cannot be referenced before it is defined
2025-06-17 19:35:24,729 - LogTransaccionesPipeline - ERROR - ❌ Pipeline LOG_TRANSACCIONES falló después de 77.87 segundos: Binder Error: Column "Context" referenced that exists in the SELECT clause - but this column cannot be referenced before it is defined
2025-06-17 19:35:24,729 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: ERROR: Binder Error: Column "Context" referenced that exists in the SELECT clause - but this column cannot be referenced before it is defined
2025-06-17 22:28:32,527 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-17 22:28:32,540 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-17 22:28:32,590 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-17 22:28:32,591 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-15
2025-06-17 22:28:32,591 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 22:28:32,591 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX usando 100% tablas S3 para fecha: 2025-06-15
2025-06-17 22:29:53,253 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX completado con deduplicación automática:
2025-06-17 22:29:53,253 - LogTransaccionesPipeline - INFO -   - Registros finales: 104,116
2025-06-17 22:29:53,254 - LogTransaccionesPipeline - INFO -   - TransferIDs únicos: 104,116
2025-06-17 22:29:53,254 - LogTransaccionesPipeline - INFO -   - Duplicados eliminados: 0
2025-06-17 22:29:53,254 - LogTransaccionesPipeline - INFO -   - Archivo: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-17 22:29:53,254 - LogTransaccionesPipeline - INFO - 🎉 HOMOLOGACIÓN PERFECTA: Coincide exactamente con Oracle PRE_LOG_TRX
2025-06-17 22:29:53,254 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: COMPLETADO
2025-06-17 22:29:53,254 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: INICIANDO
2025-06-17 22:29:53,254 - LogTransaccionesPipeline - INFO - Ejecutando SP_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 22:29:53,391 - LogTransaccionesPipeline - ERROR - Error en SP_LOG_TRX: Binder Error: Column "Context" referenced that exists in the SELECT clause - but this column cannot be referenced before it is defined
2025-06-17 22:29:53,391 - LogTransaccionesPipeline - ERROR - Error en SP_LOG_TRX: Binder Error: Column "Context" referenced that exists in the SELECT clause - but this column cannot be referenced before it is defined
2025-06-17 22:29:53,392 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: ERROR: Error en SP_LOG_TRX: Binder Error: Column "Context" referenced that exists in the SELECT clause - but this column cannot be referenced before it is defined
2025-06-17 22:29:53,392 - LogTransaccionesPipeline - ERROR - ❌ Pipeline LOG_TRANSACCIONES falló después de 80.80 segundos: Binder Error: Column "Context" referenced that exists in the SELECT clause - but this column cannot be referenced before it is defined
2025-06-17 22:29:53,392 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: ERROR: Binder Error: Column "Context" referenced that exists in the SELECT clause - but this column cannot be referenced before it is defined
2025-06-17 22:36:14,213 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-17 22:36:14,227 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-17 22:36:14,277 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-17 22:36:14,277 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-15
2025-06-17 22:36:14,277 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 22:36:14,277 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX usando 100% tablas S3 para fecha: 2025-06-15
2025-06-17 22:37:33,807 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX completado con deduplicación automática:
2025-06-17 22:37:33,808 - LogTransaccionesPipeline - INFO -   - Registros finales: 104,116
2025-06-17 22:37:33,808 - LogTransaccionesPipeline - INFO -   - TransferIDs únicos: 104,116
2025-06-17 22:37:33,808 - LogTransaccionesPipeline - INFO -   - Duplicados eliminados: 0
2025-06-17 22:37:33,808 - LogTransaccionesPipeline - INFO -   - Archivo: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-17 22:37:33,808 - LogTransaccionesPipeline - INFO - 🎉 HOMOLOGACIÓN PERFECTA: Coincide exactamente con Oracle PRE_LOG_TRX
2025-06-17 22:37:33,808 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: COMPLETADO
2025-06-17 22:37:33,808 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: INICIANDO
2025-06-17 22:37:33,808 - LogTransaccionesPipeline - INFO - Ejecutando SP_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 22:37:33,945 - LogTransaccionesPipeline - ERROR - Error en SP_LOG_TRX: Binder Error: Table "MTH" does not have a column named "ExternalTransactionID"

Candidate bindings: : "TransactionType"

LINE 7:                         THEN MTH."ExternalTransactionID"
                                     ^
2025-06-17 22:37:33,945 - LogTransaccionesPipeline - ERROR - Error en SP_LOG_TRX: Binder Error: Table "MTH" does not have a column named "ExternalTransactionID"

Candidate bindings: : "TransactionType"

LINE 7:                         THEN MTH."ExternalTransactionID"
                                     ^
2025-06-17 22:37:33,945 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: ERROR: Error en SP_LOG_TRX: Binder Error: Table "MTH" does not have a column named "ExternalTransactionID"

Candidate bindings: : "TransactionType"

LINE 7:                         THEN MTH."ExternalTransactionID"
                                     ^
2025-06-17 22:37:33,945 - LogTransaccionesPipeline - ERROR - ❌ Pipeline LOG_TRANSACCIONES falló después de 79.67 segundos: Binder Error: Table "MTH" does not have a column named "ExternalTransactionID"

Candidate bindings: : "TransactionType"

LINE 7:                         THEN MTH."ExternalTransactionID"
                                     ^
2025-06-17 22:37:33,945 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: ERROR: Binder Error: Table "MTH" does not have a column named "ExternalTransactionID"

Candidate bindings: : "TransactionType"

LINE 7:                         THEN MTH."ExternalTransactionID"
                                     ^
2025-06-17 22:38:33,856 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-17 22:38:33,870 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-17 22:38:33,921 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-17 22:38:33,921 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-15
2025-06-17 22:38:33,921 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 22:38:33,922 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX usando 100% tablas S3 para fecha: 2025-06-15
2025-06-17 22:38:45,569 - LogTransaccionesPipeline - ERROR - Error completando SP_PRE_LOG_TRX: Binder Error: Referenced column "TransferValue" not found in FROM clause!
Candidate bindings: "TransferDate", "TransferStatus", "TransferID", "TransferID_Mob", "TransactionType"

LINE 20:                 "TransferValue",
                         ^
2025-06-17 22:38:45,569 - LogTransaccionesPipeline - ERROR - Error en SP_PRE_LOG_TRX: Binder Error: Referenced column "TransferValue" not found in FROM clause!
Candidate bindings: "TransferDate", "TransferStatus", "TransferID", "TransferID_Mob", "TransactionType"

LINE 20:                 "TransferValue",
                         ^
2025-06-17 22:38:45,569 - LogTransaccionesPipeline - ERROR - Error en SP_PRE_LOG_TRX: Binder Error: Referenced column "TransferValue" not found in FROM clause!
Candidate bindings: "TransferDate", "TransferStatus", "TransferID", "TransferID_Mob", "TransactionType"

LINE 20:                 "TransferValue",
                         ^
2025-06-17 22:38:45,569 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: ERROR: Error en SP_PRE_LOG_TRX: Binder Error: Referenced column "TransferValue" not found in FROM clause!
Candidate bindings: "TransferDate", "TransferStatus", "TransferID", "TransferID_Mob", "TransactionType"

LINE 20:                 "TransferValue",
                         ^
2025-06-17 22:38:45,569 - LogTransaccionesPipeline - ERROR - ❌ Pipeline LOG_TRANSACCIONES falló después de 11.65 segundos: Binder Error: Referenced column "TransferValue" not found in FROM clause!
Candidate bindings: "TransferDate", "TransferStatus", "TransferID", "TransferID_Mob", "TransactionType"

LINE 20:                 "TransferValue",
                         ^
2025-06-17 22:38:45,569 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: ERROR: Binder Error: Referenced column "TransferValue" not found in FROM clause!
Candidate bindings: "TransferDate", "TransferStatus", "TransferID", "TransferID_Mob", "TransactionType"

LINE 20:                 "TransferValue",
                         ^
2025-06-17 22:39:57,032 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-17 22:39:57,045 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-17 22:39:57,097 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-17 22:39:57,097 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-15
2025-06-17 22:39:57,098 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 22:39:57,098 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX usando 100% tablas S3 para fecha: 2025-06-15
2025-06-17 22:41:44,969 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX completado con deduplicación automática:
2025-06-17 22:41:44,969 - LogTransaccionesPipeline - INFO -   - Registros finales: 104,116
2025-06-17 22:41:44,969 - LogTransaccionesPipeline - INFO -   - TransferIDs únicos: 104,116
2025-06-17 22:41:44,969 - LogTransaccionesPipeline - INFO -   - Duplicados eliminados: 0
2025-06-17 22:41:44,969 - LogTransaccionesPipeline - INFO -   - Archivo: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-17 22:41:44,969 - LogTransaccionesPipeline - INFO - 🎉 HOMOLOGACIÓN PERFECTA: Coincide exactamente con Oracle PRE_LOG_TRX
2025-06-17 22:41:44,969 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: COMPLETADO
2025-06-17 22:41:44,969 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: INICIANDO
2025-06-17 22:41:44,970 - LogTransaccionesPipeline - INFO - Ejecutando SP_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 22:41:45,260 - LogTransaccionesPipeline - INFO - SP_LOG_TRX completado: 104116 registros -> TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-17 22:41:45,260 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: COMPLETADO
2025-06-17 22:41:45,260 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: INICIANDO
2025-06-17 22:41:45,260 - LogTransaccionesPipeline - INFO - Extrayendo CSV final para fecha: 2025-06-15
2025-06-17 22:41:45,331 - LogTransaccionesPipeline - ERROR - Error extrayendo CSV final: IO Error: Could not write file "output/TR-********.csv": No space left on device
2025-06-17 22:41:45,331 - LogTransaccionesPipeline - ERROR - Error en extracción CSV: IO Error: Could not write file "output/TR-********.csv": No space left on device
2025-06-17 22:41:45,331 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: ERROR: Error en extracción CSV: IO Error: Could not write file "output/TR-********.csv": No space left on device
2025-06-17 22:41:45,331 - LogTransaccionesPipeline - ERROR - ❌ Pipeline LOG_TRANSACCIONES falló después de 108.23 segundos: IO Error: Could not write file "output/TR-********.csv": No space left on device
2025-06-17 22:41:45,331 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: ERROR: IO Error: Could not write file "output/TR-********.csv": No space left on device
2025-06-17 22:46:47,708 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-17 22:46:47,721 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-17 22:46:47,771 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-17 22:46:47,771 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-15
2025-06-17 22:46:47,771 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 22:46:47,771 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX usando 100% tablas S3 para fecha: 2025-06-15
2025-06-17 22:48:38,236 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX completado con deduplicación automática:
2025-06-17 22:48:38,236 - LogTransaccionesPipeline - INFO -   - Registros finales: 104,116
2025-06-17 22:48:38,236 - LogTransaccionesPipeline - INFO -   - TransferIDs únicos: 104,116
2025-06-17 22:48:38,237 - LogTransaccionesPipeline - INFO -   - Duplicados eliminados: 0
2025-06-17 22:48:38,237 - LogTransaccionesPipeline - INFO -   - Archivo: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-17 22:48:38,237 - LogTransaccionesPipeline - INFO - 🎉 HOMOLOGACIÓN PERFECTA: Coincide exactamente con Oracle PRE_LOG_TRX
2025-06-17 22:48:38,237 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: COMPLETADO
2025-06-17 22:48:38,237 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: INICIANDO
2025-06-17 22:48:38,237 - LogTransaccionesPipeline - INFO - Ejecutando SP_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 22:48:38,503 - LogTransaccionesPipeline - INFO - SP_LOG_TRX completado: 104116 registros -> TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-17 22:48:38,503 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: COMPLETADO
2025-06-17 22:48:38,503 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: INICIANDO
2025-06-17 22:48:38,503 - LogTransaccionesPipeline - INFO - Extrayendo CSV final para fecha: 2025-06-15
2025-06-17 22:48:38,571 - LogTransaccionesPipeline - ERROR - Error extrayendo CSV final: IO Error: Could not write file "output/TR-********.csv": No space left on device
2025-06-17 22:48:38,572 - LogTransaccionesPipeline - ERROR - Error en extracción CSV: IO Error: Could not write file "output/TR-********.csv": No space left on device
2025-06-17 22:48:38,572 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: ERROR: Error en extracción CSV: IO Error: Could not write file "output/TR-********.csv": No space left on device
2025-06-17 22:48:38,572 - LogTransaccionesPipeline - ERROR - ❌ Pipeline LOG_TRANSACCIONES falló después de 110.80 segundos: IO Error: Could not write file "output/TR-********.csv": No space left on device
2025-06-17 22:48:38,572 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: ERROR: IO Error: Could not write file "output/TR-********.csv": No space left on device
2025-06-17 22:11:52,887 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-17 22:11:52,905 - botocore.credentials - INFO - Found credentials from IAM Role: ec-landing-process-01-EC2-Role
2025-06-17 22:11:52,942 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-17 22:12:46,176 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-17 22:12:46,194 - botocore.credentials - INFO - Found credentials from IAM Role: ec-landing-process-01-EC2-Role
2025-06-17 22:12:46,232 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-17 22:12:46,233 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX usando 100% tablas S3 para fecha: 2025-06-15
2025-06-17 22:12:46,234 - LogTransaccionesPipeline - INFO - Proceso PRE_LOG_TRX ya ejecutado para 2025-06-15: 104116 registros
2025-06-17 22:12:46,234 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX ya fue procesado para esta fecha
2025-06-17 22:13:03,967 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-17 22:13:03,986 - botocore.credentials - INFO - Found credentials from IAM Role: ec-landing-process-01-EC2-Role
2025-06-17 22:13:04,025 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-17 22:13:04,025 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX usando 100% tablas S3 para fecha: 2025-06-15
2025-06-17 22:14:40,322 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX completado con deduplicación automática:
2025-06-17 22:14:40,322 - LogTransaccionesPipeline - INFO -   - Registros finales: 104,116
2025-06-17 22:14:40,322 - LogTransaccionesPipeline - INFO -   - TransferIDs únicos: 104,116
2025-06-17 22:14:40,322 - LogTransaccionesPipeline - INFO -   - Duplicados eliminados: 0
2025-06-17 22:14:40,322 - LogTransaccionesPipeline - INFO -   - Archivo: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-17 22:14:40,322 - LogTransaccionesPipeline - INFO - 🎉 HOMOLOGACIÓN PERFECTA: Coincide exactamente con Oracle PRE_LOG_TRX
2025-06-17 22:16:58,357 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-17 22:16:58,376 - botocore.credentials - INFO - Found credentials from IAM Role: ec-landing-process-01-EC2-Role
2025-06-17 22:16:58,413 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-17 22:16:58,414 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-15
2025-06-17 22:16:58,415 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 22:16:58,415 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX usando 100% tablas S3 para fecha: 2025-06-15
2025-06-17 22:16:58,416 - LogTransaccionesPipeline - INFO - Proceso PRE_LOG_TRX ya ejecutado para 2025-06-15: 104116 registros
2025-06-17 22:16:58,416 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX ya fue procesado para esta fecha
2025-06-17 22:16:58,417 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: COMPLETADO
2025-06-17 22:16:58,418 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: INICIANDO
2025-06-17 22:16:58,418 - LogTransaccionesPipeline - INFO - Ejecutando SP_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 22:16:58,419 - LogTransaccionesPipeline - WARNING - Error verificando si LOG_TRX_FINAL fue procesado: Binder Error: Referenced column "TransferDate" not found in FROM clause!
Candidate bindings: "TransactionType", "TransactionID", "ToUsername", "TransactionStatus", "FromUsername"

LINE 3:                     WHERE CAST("TransferDate" AS DATE) = CAST('2025-06-15' AS DATE)
                                       ^
2025-06-17 22:16:58,805 - LogTransaccionesPipeline - INFO - SP_LOG_TRX completado: 104116 registros -> TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-17 22:16:58,806 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: COMPLETADO
2025-06-17 22:16:58,807 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: INICIANDO
2025-06-17 22:16:58,807 - LogTransaccionesPipeline - INFO - Extrayendo CSV final para fecha: 2025-06-15
2025-06-17 22:16:58,971 - LogTransaccionesPipeline - INFO - CSV final generado: 104116 registros -> output/TR-********.csv
2025-06-17 22:16:58,973 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: COMPLETADO
2025-06-17 22:16:58,973 - LogTransaccionesPipeline - INFO - ✅ Pipeline LOG_TRANSACCIONES completado exitosamente en 0.56 segundos
2025-06-17 22:16:58,974 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: EXITOSO
2025-06-17 22:17:22,478 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-17 22:17:22,496 - botocore.credentials - INFO - Found credentials from IAM Role: ec-landing-process-01-EC2-Role
2025-06-17 22:17:22,532 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-17 22:17:22,533 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-15
2025-06-17 22:17:22,534 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 22:17:22,534 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX usando 100% tablas S3 para fecha: 2025-06-15
2025-06-17 22:18:56,242 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX completado con deduplicación automática:
2025-06-17 22:18:56,243 - LogTransaccionesPipeline - INFO -   - Registros finales: 104,116
2025-06-17 22:18:56,243 - LogTransaccionesPipeline - INFO -   - TransferIDs únicos: 104,116
2025-06-17 22:18:56,243 - LogTransaccionesPipeline - INFO -   - Duplicados eliminados: 0
2025-06-17 22:18:56,243 - LogTransaccionesPipeline - INFO -   - Archivo: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-17 22:18:56,243 - LogTransaccionesPipeline - INFO - 🎉 HOMOLOGACIÓN PERFECTA: Coincide exactamente con Oracle PRE_LOG_TRX
2025-06-17 22:18:56,244 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: COMPLETADO
2025-06-17 22:18:56,245 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: INICIANDO
2025-06-17 22:18:56,245 - LogTransaccionesPipeline - INFO - Ejecutando SP_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 22:18:56,634 - LogTransaccionesPipeline - INFO - SP_LOG_TRX completado: 104116 registros -> TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-17 22:18:56,635 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: COMPLETADO
2025-06-17 22:18:56,636 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: INICIANDO
2025-06-17 22:18:56,636 - LogTransaccionesPipeline - INFO - Extrayendo CSV final para fecha: 2025-06-15
2025-06-17 22:18:56,773 - LogTransaccionesPipeline - INFO - CSV final generado: 104116 registros -> output/TR-********.csv
2025-06-17 22:18:56,775 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: COMPLETADO
2025-06-17 22:18:56,775 - LogTransaccionesPipeline - INFO - ✅ Pipeline LOG_TRANSACCIONES completado exitosamente en 94.24 segundos
2025-06-17 22:18:56,776 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: EXITOSO
2025-06-17 22:19:08,883 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-17 22:19:08,902 - botocore.credentials - INFO - Found credentials from IAM Role: ec-landing-process-01-EC2-Role
2025-06-17 22:19:08,940 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-17 22:19:08,941 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-15
2025-06-17 22:19:08,942 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 22:19:08,942 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX usando 100% tablas S3 para fecha: 2025-06-15
2025-06-17 22:19:08,943 - LogTransaccionesPipeline - INFO - Proceso PRE_LOG_TRX ya ejecutado para 2025-06-15: 104116 registros
2025-06-17 22:19:08,943 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX ya fue procesado para esta fecha
2025-06-17 22:19:08,944 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: COMPLETADO
2025-06-17 22:19:08,946 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: INICIANDO
2025-06-17 22:19:08,946 - LogTransaccionesPipeline - INFO - Ejecutando SP_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 22:19:08,946 - LogTransaccionesPipeline - WARNING - Error verificando si LOG_TRX_FINAL fue procesado: Binder Error: Referenced column "TransferDate" not found in FROM clause!
Candidate bindings: "TransactionType", "TransactionID", "ToUsername", "TransactionStatus", "FromUsername"

LINE 3:                     WHERE CAST("TransferDate" AS DATE) = CAST('2025-06-15' AS DATE)
                                       ^
2025-06-17 22:19:09,329 - LogTransaccionesPipeline - INFO - SP_LOG_TRX completado: 104116 registros -> TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-17 22:19:09,330 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: COMPLETADO
2025-06-17 22:19:09,331 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: INICIANDO
2025-06-17 22:19:09,331 - LogTransaccionesPipeline - INFO - Extrayendo CSV final para fecha: 2025-06-15
2025-06-17 22:19:09,484 - LogTransaccionesPipeline - INFO - CSV final generado: 104116 registros -> output/TR-********.csv
2025-06-17 22:19:09,486 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: COMPLETADO
2025-06-17 22:19:09,486 - LogTransaccionesPipeline - INFO - ✅ Pipeline LOG_TRANSACCIONES completado exitosamente en 0.55 segundos
2025-06-17 22:19:09,487 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: EXITOSO
2025-06-17 22:24:05,520 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-17 22:24:05,539 - botocore.credentials - INFO - Found credentials from IAM Role: ec-landing-process-01-EC2-Role
2025-06-17 22:24:05,578 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-17 22:24:05,578 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX usando 100% tablas S3 para fecha: 2025-06-15
2025-06-17 22:25:44,453 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX completado con deduplicación automática:
2025-06-17 22:25:44,453 - LogTransaccionesPipeline - INFO -   - Registros finales: 104,116
2025-06-17 22:25:44,453 - LogTransaccionesPipeline - INFO -   - TransferIDs únicos: 104,116
2025-06-17 22:25:44,453 - LogTransaccionesPipeline - INFO -   - Duplicados eliminados: 0
2025-06-17 22:25:44,453 - LogTransaccionesPipeline - INFO -   - Archivo: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-17 22:25:44,453 - LogTransaccionesPipeline - INFO - 🎉 HOMOLOGACIÓN PERFECTA: Coincide exactamente con Oracle PRE_LOG_TRX
2025-06-17 22:28:58,468 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-17 22:28:58,486 - botocore.credentials - INFO - Found credentials from IAM Role: ec-landing-process-01-EC2-Role
2025-06-17 22:28:58,526 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-17 22:28:58,526 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-15
2025-06-17 22:28:58,527 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 22:28:58,527 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX usando 100% tablas S3 para fecha: 2025-06-15
2025-06-17 22:30:34,329 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX completado con deduplicación automática:
2025-06-17 22:30:34,329 - LogTransaccionesPipeline - INFO -   - Registros finales: 104,116
2025-06-17 22:30:34,329 - LogTransaccionesPipeline - INFO -   - TransferIDs únicos: 104,116
2025-06-17 22:30:34,329 - LogTransaccionesPipeline - INFO -   - Duplicados eliminados: 0
2025-06-17 22:30:34,329 - LogTransaccionesPipeline - INFO -   - Archivo: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-17 22:30:34,329 - LogTransaccionesPipeline - INFO - 🎉 HOMOLOGACIÓN PERFECTA: Coincide exactamente con Oracle PRE_LOG_TRX
2025-06-17 22:30:34,331 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: COMPLETADO
2025-06-17 22:30:34,332 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: INICIANDO
2025-06-17 22:30:34,332 - LogTransaccionesPipeline - INFO - Ejecutando SP_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 22:30:34,695 - LogTransaccionesPipeline - INFO - SP_LOG_TRX completado: 104116 registros -> TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-17 22:30:34,697 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: COMPLETADO
2025-06-17 22:30:34,698 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: INICIANDO
2025-06-17 22:30:34,698 - LogTransaccionesPipeline - INFO - Extrayendo CSV final para fecha: 2025-06-15
2025-06-17 22:30:34,831 - LogTransaccionesPipeline - INFO - CSV final generado: 104116 registros -> output/TR-********.csv
2025-06-17 22:30:34,833 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: COMPLETADO
2025-06-17 22:30:34,833 - LogTransaccionesPipeline - INFO - ✅ Pipeline LOG_TRANSACCIONES completado exitosamente en 96.31 segundos
2025-06-17 22:30:34,834 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: EXITOSO
2025-06-17 22:31:00,210 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-17 22:31:00,228 - botocore.credentials - INFO - Found credentials from IAM Role: ec-landing-process-01-EC2-Role
2025-06-17 22:31:00,266 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-17 22:31:00,266 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-15
2025-06-17 22:31:00,267 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 22:31:00,267 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX usando 100% tablas S3 para fecha: 2025-06-15
2025-06-17 22:31:00,268 - LogTransaccionesPipeline - INFO - Proceso PRE_LOG_TRX ya ejecutado para 2025-06-15: 104116 registros
2025-06-17 22:31:00,269 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX ya fue procesado para esta fecha
2025-06-17 22:31:00,270 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: COMPLETADO
2025-06-17 22:31:00,271 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: INICIANDO
2025-06-17 22:31:00,271 - LogTransaccionesPipeline - INFO - Ejecutando SP_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 22:31:00,272 - LogTransaccionesPipeline - WARNING - Error verificando si LOG_TRX_FINAL fue procesado: Binder Error: Referenced column "TransferDate" not found in FROM clause!
Candidate bindings: "TransactionType", "TransactionID", "ToUsername", "TransactionStatus", "FromUsername"

LINE 3:                     WHERE CAST("TransferDate" AS DATE) = CAST('2025-06-15' AS DATE)
                                       ^
2025-06-17 22:31:00,665 - LogTransaccionesPipeline - INFO - SP_LOG_TRX completado: 104116 registros -> TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-17 22:31:00,666 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: COMPLETADO
2025-06-17 22:31:00,667 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: INICIANDO
2025-06-17 22:31:00,667 - LogTransaccionesPipeline - INFO - Extrayendo CSV final para fecha: 2025-06-15
2025-06-17 22:31:00,839 - LogTransaccionesPipeline - INFO - CSV final generado: 104116 registros -> output/TR-********.csv
2025-06-17 22:31:00,840 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: COMPLETADO
2025-06-17 22:31:00,841 - LogTransaccionesPipeline - INFO - ✅ Pipeline LOG_TRANSACCIONES completado exitosamente en 0.57 segundos
2025-06-17 22:31:00,842 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: EXITOSO
2025-06-17 22:31:44,120 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-17 22:31:44,138 - botocore.credentials - INFO - Found credentials from IAM Role: ec-landing-process-01-EC2-Role
2025-06-17 22:31:44,177 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-17 22:31:44,177 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-15
2025-06-17 22:31:44,178 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 22:31:44,178 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX usando 100% tablas S3 para fecha: 2025-06-15
2025-06-17 22:31:44,179 - LogTransaccionesPipeline - INFO - Proceso PRE_LOG_TRX ya ejecutado para 2025-06-15: 104116 registros
2025-06-17 22:31:44,180 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX ya fue procesado para esta fecha
2025-06-17 22:31:44,181 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: COMPLETADO
2025-06-17 22:31:44,182 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: INICIANDO
2025-06-17 22:31:44,182 - LogTransaccionesPipeline - INFO - Ejecutando SP_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 22:31:44,183 - LogTransaccionesPipeline - WARNING - Error verificando si LOG_TRX_FINAL fue procesado: Binder Error: Referenced column "TransferDate" not found in FROM clause!
Candidate bindings: "TransactionType", "TransactionID", "ToUsername", "TransactionStatus", "FromUsername"

LINE 3:                     WHERE CAST("TransferDate" AS DATE) = CAST('2025-06-15' AS DATE)
                                       ^
2025-06-17 22:31:44,521 - LogTransaccionesPipeline - INFO - SP_LOG_TRX completado: 104116 registros -> TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-17 22:31:44,522 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: COMPLETADO
2025-06-17 22:31:44,524 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: INICIANDO
2025-06-17 22:31:44,524 - LogTransaccionesPipeline - INFO - Extrayendo CSV final para fecha: 2025-06-15
2025-06-17 22:31:44,664 - LogTransaccionesPipeline - INFO - CSV final generado: 104116 registros -> output/TR-********.csv
2025-06-17 22:31:44,665 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: COMPLETADO
2025-06-17 22:31:44,665 - LogTransaccionesPipeline - INFO - ✅ Pipeline LOG_TRANSACCIONES completado exitosamente en 0.49 segundos
2025-06-17 22:31:44,667 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: EXITOSO
2025-06-17 22:47:13,414 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-17 22:47:13,445 - botocore.credentials - INFO - Found credentials from IAM Role: ec-landing-process-01-EC2-Role
2025-06-17 22:47:13,484 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-17 22:47:13,484 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-15
2025-06-17 22:47:13,485 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 22:47:13,486 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX usando 100% tablas S3 para fecha: 2025-06-15
2025-06-17 22:47:13,486 - LogTransaccionesPipeline - INFO - Archivo previo eliminado: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-17 22:48:52,853 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX completado con deduplicación automática:
2025-06-17 22:48:52,853 - LogTransaccionesPipeline - INFO -   - Registros finales: 104,116
2025-06-17 22:48:52,853 - LogTransaccionesPipeline - INFO -   - TransferIDs únicos: 104,116
2025-06-17 22:48:52,853 - LogTransaccionesPipeline - INFO -   - Duplicados eliminados: 0
2025-06-17 22:48:52,853 - LogTransaccionesPipeline - INFO -   - Archivo: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-17 22:48:52,853 - LogTransaccionesPipeline - INFO - 🎉 HOMOLOGACIÓN PERFECTA: Coincide exactamente con Oracle PRE_LOG_TRX
2025-06-17 22:48:52,855 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: COMPLETADO
2025-06-17 22:48:52,856 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: INICIANDO
2025-06-17 22:48:52,856 - LogTransaccionesPipeline - INFO - Ejecutando SP_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 22:48:52,857 - LogTransaccionesPipeline - INFO - Archivo previo eliminado: TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-17 22:48:53,181 - LogTransaccionesPipeline - INFO - SP_LOG_TRX completado: 104116 registros -> TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-17 22:48:53,182 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: COMPLETADO
2025-06-17 22:48:53,184 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: INICIANDO
2025-06-17 22:48:53,184 - LogTransaccionesPipeline - INFO - Extrayendo CSV final para fecha: 2025-06-15
2025-06-17 22:48:53,319 - LogTransaccionesPipeline - INFO - CSV final generado: 104116 registros -> output/TR-********.csv
2025-06-17 22:48:53,321 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: COMPLETADO
2025-06-17 22:48:53,321 - LogTransaccionesPipeline - INFO - ✅ Pipeline LOG_TRANSACCIONES completado exitosamente en 99.84 segundos
2025-06-17 22:48:53,322 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: EXITOSO
2025-06-17 23:02:33,180 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-17 23:02:33,198 - botocore.credentials - INFO - Found credentials from IAM Role: ec-landing-process-01-EC2-Role
2025-06-17 23:02:33,235 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-17 23:02:33,236 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-15
2025-06-17 23:02:33,237 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 23:02:33,237 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX usando 100% tablas S3 para fecha: 2025-06-15
2025-06-17 23:02:33,238 - LogTransaccionesPipeline - INFO - Archivo previo eliminado: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-17 23:12:42,462 - LogTransaccionesPipeline - ERROR - Error completando SP_PRE_LOG_TRX: Query interrupted
2025-06-17 23:12:42,462 - LogTransaccionesPipeline - ERROR - Error en SP_PRE_LOG_TRX: Query interrupted
2025-06-17 23:12:42,462 - LogTransaccionesPipeline - ERROR - Error en SP_PRE_LOG_TRX: Query interrupted
2025-06-17 23:12:42,464 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: ERROR: Error en SP_PRE_LOG_TRX: Query interrupted
2025-06-17 23:12:42,464 - LogTransaccionesPipeline - ERROR - ❌ Pipeline LOG_TRANSACCIONES falló después de 609.23 segundos: Query interrupted
2025-06-17 23:12:42,465 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: ERROR: Query interrupted
2025-06-17 23:14:53,666 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-17 23:14:53,684 - botocore.credentials - INFO - Found credentials from IAM Role: ec-landing-process-01-EC2-Role
2025-06-17 23:14:53,722 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-17 23:14:53,722 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-15
2025-06-17 23:14:53,724 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 23:14:53,724 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX usando 100% tablas S3 para fecha: 2025-06-15
2025-06-17 23:14:53,724 - LogTransaccionesPipeline - INFO - Archivo previo eliminado: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-17 23:16:33,474 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX completado con deduplicación automática:
2025-06-17 23:16:33,475 - LogTransaccionesPipeline - INFO -   - Registros finales: 104,116
2025-06-17 23:16:33,475 - LogTransaccionesPipeline - INFO -   - TransferIDs únicos: 104,116
2025-06-17 23:16:33,475 - LogTransaccionesPipeline - INFO -   - Duplicados eliminados: 0
2025-06-17 23:16:33,475 - LogTransaccionesPipeline - INFO -   - Archivo: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-17 23:16:33,475 - LogTransaccionesPipeline - INFO - 🎉 HOMOLOGACIÓN PERFECTA: Coincide exactamente con Oracle PRE_LOG_TRX
2025-06-17 23:16:33,476 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: COMPLETADO
2025-06-17 23:16:33,478 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: INICIANDO
2025-06-17 23:16:33,478 - LogTransaccionesPipeline - INFO - Ejecutando SP_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 23:16:33,478 - LogTransaccionesPipeline - INFO - Archivo previo eliminado: TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-17 23:16:33,827 - LogTransaccionesPipeline - INFO - SP_LOG_TRX completado: 104116 registros -> TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-17 23:16:33,829 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: COMPLETADO
2025-06-17 23:16:33,830 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: INICIANDO
2025-06-17 23:16:33,830 - LogTransaccionesPipeline - INFO - Extrayendo CSV final para fecha: 2025-06-15
2025-06-17 23:16:33,974 - LogTransaccionesPipeline - INFO - CSV final generado: 104116 registros -> output/TR-********.csv
2025-06-17 23:16:33,976 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: COMPLETADO
2025-06-17 23:16:33,976 - LogTransaccionesPipeline - INFO - ✅ Pipeline LOG_TRANSACCIONES completado exitosamente en 100.25 segundos
2025-06-17 23:16:33,977 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: EXITOSO
2025-06-17 23:19:40,598 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-17 23:19:40,617 - botocore.credentials - INFO - Found credentials from IAM Role: ec-landing-process-01-EC2-Role
2025-06-17 23:19:40,658 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-17 23:19:40,658 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-15
2025-06-17 23:19:40,659 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 23:19:40,659 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX usando 100% tablas S3 para fecha: 2025-06-15
2025-06-17 23:19:40,660 - LogTransaccionesPipeline - INFO - Archivo previo eliminado: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-17 23:21:16,376 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX completado con deduplicación automática:
2025-06-17 23:21:16,376 - LogTransaccionesPipeline - INFO -   - Registros finales: 104,116
2025-06-17 23:21:16,376 - LogTransaccionesPipeline - INFO -   - TransferIDs únicos: 104,116
2025-06-17 23:21:16,376 - LogTransaccionesPipeline - INFO -   - Duplicados eliminados: 0
2025-06-17 23:21:16,376 - LogTransaccionesPipeline - INFO -   - Archivo: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-17 23:21:16,376 - LogTransaccionesPipeline - INFO - 🎉 HOMOLOGACIÓN PERFECTA: Coincide exactamente con Oracle PRE_LOG_TRX
2025-06-17 23:21:16,377 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: COMPLETADO
2025-06-17 23:21:16,379 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: INICIANDO
2025-06-17 23:21:16,379 - LogTransaccionesPipeline - INFO - Ejecutando SP_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 23:21:16,379 - LogTransaccionesPipeline - INFO - Archivo previo eliminado: TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-17 23:21:16,722 - LogTransaccionesPipeline - INFO - SP_LOG_TRX completado: 104116 registros -> TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-17 23:21:16,723 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: COMPLETADO
2025-06-17 23:21:16,724 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: INICIANDO
2025-06-17 23:21:16,724 - LogTransaccionesPipeline - INFO - Extrayendo CSV final para fecha: 2025-06-15
2025-06-17 23:21:16,856 - LogTransaccionesPipeline - INFO - CSV final generado: 104116 registros -> output/TR-********.csv
2025-06-17 23:21:16,858 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: COMPLETADO
2025-06-17 23:21:16,858 - LogTransaccionesPipeline - INFO - ✅ Pipeline LOG_TRANSACCIONES completado exitosamente en 96.20 segundos
2025-06-17 23:21:16,859 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: EXITOSO
2025-06-17 23:42:37,786 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-17 23:42:37,804 - botocore.credentials - INFO - Found credentials from IAM Role: ec-landing-process-01-EC2-Role
2025-06-17 23:42:37,846 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-17 23:42:37,846 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-15
2025-06-17 23:42:37,847 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 23:42:37,847 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX usando 100% tablas S3 para fecha: 2025-06-15
2025-06-17 23:42:37,848 - LogTransaccionesPipeline - INFO - Archivo previo eliminado: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-17 23:44:14,964 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX completado con deduplicación automática:
2025-06-17 23:44:14,965 - LogTransaccionesPipeline - INFO -   - Registros finales: 104,116
2025-06-17 23:44:14,965 - LogTransaccionesPipeline - INFO -   - TransferIDs únicos: 104,116
2025-06-17 23:44:14,965 - LogTransaccionesPipeline - INFO -   - Duplicados eliminados: 0
2025-06-17 23:44:14,965 - LogTransaccionesPipeline - INFO -   - Archivo: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-17 23:44:14,965 - LogTransaccionesPipeline - INFO - 🎉 HOMOLOGACIÓN PERFECTA: Coincide exactamente con Oracle PRE_LOG_TRX
2025-06-17 23:44:14,966 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: COMPLETADO
2025-06-17 23:44:14,967 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: INICIANDO
2025-06-17 23:44:14,968 - LogTransaccionesPipeline - INFO - Ejecutando SP_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 23:44:14,968 - LogTransaccionesPipeline - INFO - Archivo previo eliminado: TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-17 23:44:15,293 - LogTransaccionesPipeline - INFO - SP_LOG_TRX completado: 104116 registros -> TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-17 23:44:15,294 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: COMPLETADO
2025-06-17 23:44:15,296 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: INICIANDO
2025-06-17 23:44:15,296 - LogTransaccionesPipeline - INFO - Extrayendo CSV final para fecha: 2025-06-15
2025-06-17 23:44:15,417 - LogTransaccionesPipeline - INFO - CSV final generado: 104116 registros -> output/TR-********.csv
2025-06-17 23:44:15,419 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: COMPLETADO
2025-06-17 23:44:15,419 - LogTransaccionesPipeline - INFO - ✅ Pipeline LOG_TRANSACCIONES completado exitosamente en 97.57 segundos
2025-06-17 23:44:15,421 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: EXITOSO
2025-06-17 23:54:50,311 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-17 23:54:50,330 - botocore.credentials - INFO - Found credentials from IAM Role: ec-landing-process-01-EC2-Role
2025-06-17 23:54:50,368 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-17 23:54:50,369 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-15
2025-06-17 23:54:50,370 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 23:54:50,370 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX usando 100% tablas S3 para fecha: 2025-06-15
2025-06-17 23:54:50,371 - LogTransaccionesPipeline - INFO - Archivo previo eliminado: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-17 23:56:28,422 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX completado con deduplicación automática:
2025-06-17 23:56:28,422 - LogTransaccionesPipeline - INFO -   - Registros finales: 104,116
2025-06-17 23:56:28,422 - LogTransaccionesPipeline - INFO -   - TransferIDs únicos: 104,116
2025-06-17 23:56:28,422 - LogTransaccionesPipeline - INFO -   - Duplicados eliminados: 0
2025-06-17 23:56:28,422 - LogTransaccionesPipeline - INFO -   - Archivo: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-17 23:56:28,422 - LogTransaccionesPipeline - INFO - 🎉 HOMOLOGACIÓN PERFECTA: Coincide exactamente con Oracle PRE_LOG_TRX
2025-06-17 23:56:28,423 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: COMPLETADO
2025-06-17 23:56:28,425 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: INICIANDO
2025-06-17 23:56:28,425 - LogTransaccionesPipeline - INFO - Ejecutando SP_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 23:56:28,425 - LogTransaccionesPipeline - INFO - Archivo previo eliminado: TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-17 23:56:28,833 - LogTransaccionesPipeline - INFO - SP_LOG_TRX completado: 104116 registros -> TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-17 23:56:28,834 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: COMPLETADO
2025-06-17 23:56:28,836 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: INICIANDO
2025-06-17 23:56:28,836 - LogTransaccionesPipeline - INFO - Extrayendo CSV final para fecha: 2025-06-15
2025-06-17 23:56:28,974 - LogTransaccionesPipeline - INFO - CSV final generado: 104116 registros -> output/TR-********.csv
2025-06-17 23:56:28,976 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: COMPLETADO
2025-06-17 23:56:28,976 - LogTransaccionesPipeline - INFO - ✅ Pipeline LOG_TRANSACCIONES completado exitosamente en 98.61 segundos
2025-06-17 23:56:28,977 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: EXITOSO
2025-06-17 23:58:10,516 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-17 23:58:10,534 - botocore.credentials - INFO - Found credentials from IAM Role: ec-landing-process-01-EC2-Role
2025-06-17 23:58:10,577 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-17 23:58:10,577 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-15
2025-06-17 23:58:10,578 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 23:58:10,578 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX usando 100% tablas S3 para fecha: 2025-06-15
2025-06-17 23:58:10,579 - LogTransaccionesPipeline - INFO - Archivo previo eliminado: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-17 23:59:46,231 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX completado con deduplicación automática:
2025-06-17 23:59:46,231 - LogTransaccionesPipeline - INFO -   - Registros finales: 104,116
2025-06-17 23:59:46,231 - LogTransaccionesPipeline - INFO -   - TransferIDs únicos: 104,116
2025-06-17 23:59:46,231 - LogTransaccionesPipeline - INFO -   - Duplicados eliminados: 0
2025-06-17 23:59:46,231 - LogTransaccionesPipeline - INFO -   - Archivo: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-17 23:59:46,231 - LogTransaccionesPipeline - INFO - 🎉 HOMOLOGACIÓN PERFECTA: Coincide exactamente con Oracle PRE_LOG_TRX
2025-06-17 23:59:46,232 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: COMPLETADO
2025-06-17 23:59:46,234 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: INICIANDO
2025-06-17 23:59:46,234 - LogTransaccionesPipeline - INFO - Ejecutando SP_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-17 23:59:46,234 - LogTransaccionesPipeline - INFO - Archivo previo eliminado: TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-17 23:59:46,567 - LogTransaccionesPipeline - INFO - SP_LOG_TRX completado: 104116 registros -> TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-17 23:59:46,568 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: COMPLETADO
2025-06-17 23:59:46,570 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: INICIANDO
2025-06-17 23:59:46,570 - LogTransaccionesPipeline - INFO - Extrayendo CSV final para fecha: 2025-06-15
2025-06-17 23:59:46,694 - LogTransaccionesPipeline - INFO - CSV final generado: 104116 registros -> output/TR-********.csv
2025-06-17 23:59:46,696 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: COMPLETADO
2025-06-17 23:59:46,696 - LogTransaccionesPipeline - INFO - ✅ Pipeline LOG_TRANSACCIONES completado exitosamente en 96.12 segundos
2025-06-17 23:59:46,697 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: EXITOSO
2025-06-18 00:07:31,681 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-18 00:07:31,699 - botocore.credentials - INFO - Found credentials from IAM Role: ec-landing-process-01-EC2-Role
2025-06-18 00:07:31,738 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-18 00:07:31,738 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-15
2025-06-18 00:07:31,739 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-18 00:07:31,740 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX usando 100% tablas S3 para fecha: 2025-06-15
2025-06-18 00:07:31,740 - LogTransaccionesPipeline - INFO - Archivo previo eliminado: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-18 00:09:10,593 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX completado con deduplicación automática:
2025-06-18 00:09:10,594 - LogTransaccionesPipeline - INFO -   - Registros finales: 104,116
2025-06-18 00:09:10,594 - LogTransaccionesPipeline - INFO -   - TransferIDs únicos: 104,116
2025-06-18 00:09:10,594 - LogTransaccionesPipeline - INFO -   - Duplicados eliminados: 0
2025-06-18 00:09:10,594 - LogTransaccionesPipeline - INFO -   - Archivo: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-18 00:09:10,594 - LogTransaccionesPipeline - INFO - 🎉 HOMOLOGACIÓN PERFECTA: Coincide exactamente con Oracle PRE_LOG_TRX
2025-06-18 00:09:10,595 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: COMPLETADO
2025-06-18 00:09:10,597 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: INICIANDO
2025-06-18 00:09:10,597 - LogTransaccionesPipeline - INFO - Ejecutando SP_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-18 00:09:10,597 - LogTransaccionesPipeline - INFO - Archivo previo eliminado: TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-18 00:09:10,914 - LogTransaccionesPipeline - INFO - SP_LOG_TRX completado: 104116 registros -> TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-18 00:09:10,915 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: COMPLETADO
2025-06-18 00:09:10,917 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: INICIANDO
2025-06-18 00:09:10,917 - LogTransaccionesPipeline - INFO - Extrayendo CSV final para fecha: 2025-06-15
2025-06-18 00:09:11,041 - LogTransaccionesPipeline - INFO - CSV final generado: 104116 registros -> output/TR-********.csv
2025-06-18 00:09:11,042 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: COMPLETADO
2025-06-18 00:09:11,043 - LogTransaccionesPipeline - INFO - ✅ Pipeline LOG_TRANSACCIONES completado exitosamente en 99.30 segundos
2025-06-18 00:09:11,044 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: EXITOSO
2025-06-18 00:11:15,948 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-18 00:11:15,966 - botocore.credentials - INFO - Found credentials from IAM Role: ec-landing-process-01-EC2-Role
2025-06-18 00:11:16,003 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-18 00:11:16,003 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-15
2025-06-18 00:11:16,004 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-18 00:11:16,004 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX usando 100% tablas S3 para fecha: 2025-06-15
2025-06-18 00:11:16,005 - LogTransaccionesPipeline - INFO - Archivo previo eliminado: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-18 00:12:51,593 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX completado con deduplicación automática:
2025-06-18 00:12:51,593 - LogTransaccionesPipeline - INFO -   - Registros finales: 104,116
2025-06-18 00:12:51,593 - LogTransaccionesPipeline - INFO -   - TransferIDs únicos: 104,116
2025-06-18 00:12:51,593 - LogTransaccionesPipeline - INFO -   - Duplicados eliminados: 0
2025-06-18 00:12:51,593 - LogTransaccionesPipeline - INFO -   - Archivo: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-18 00:12:51,593 - LogTransaccionesPipeline - INFO - 🎉 HOMOLOGACIÓN PERFECTA: Coincide exactamente con Oracle PRE_LOG_TRX
2025-06-18 00:12:51,595 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: COMPLETADO
2025-06-18 00:12:51,596 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: INICIANDO
2025-06-18 00:12:51,596 - LogTransaccionesPipeline - INFO - Ejecutando SP_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-18 00:12:51,597 - LogTransaccionesPipeline - INFO - Archivo previo eliminado: TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-18 00:12:51,917 - LogTransaccionesPipeline - INFO - SP_LOG_TRX completado: 104116 registros -> TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-18 00:12:51,918 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: COMPLETADO
2025-06-18 00:12:51,919 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: INICIANDO
2025-06-18 00:12:51,919 - LogTransaccionesPipeline - INFO - Extrayendo CSV final para fecha: 2025-06-15
2025-06-18 00:12:52,039 - LogTransaccionesPipeline - INFO - CSV final generado: 104116 registros -> output/TR-********.csv
2025-06-18 00:12:52,041 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: COMPLETADO
2025-06-18 00:12:52,041 - LogTransaccionesPipeline - INFO - ✅ Pipeline LOG_TRANSACCIONES completado exitosamente en 96.04 segundos
2025-06-18 00:12:52,042 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: EXITOSO
2025-06-18 00:17:52,874 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-18 00:17:52,892 - botocore.credentials - INFO - Found credentials from IAM Role: ec-landing-process-01-EC2-Role
2025-06-18 00:17:52,939 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-18 00:17:52,939 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-15
2025-06-18 00:17:52,941 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-18 00:17:52,941 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX usando 100% tablas S3 para fecha: 2025-06-15
2025-06-18 00:17:52,942 - LogTransaccionesPipeline - INFO - Archivo previo eliminado: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-18 00:19:28,958 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX completado con deduplicación automática:
2025-06-18 00:19:28,958 - LogTransaccionesPipeline - INFO -   - Registros finales: 104,116
2025-06-18 00:19:28,958 - LogTransaccionesPipeline - INFO -   - TransferIDs únicos: 104,116
2025-06-18 00:19:28,958 - LogTransaccionesPipeline - INFO -   - Duplicados eliminados: 0
2025-06-18 00:19:28,958 - LogTransaccionesPipeline - INFO -   - Archivo: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-18 00:19:28,958 - LogTransaccionesPipeline - INFO - 🎉 HOMOLOGACIÓN PERFECTA: Coincide exactamente con Oracle PRE_LOG_TRX
2025-06-18 00:19:28,959 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: COMPLETADO
2025-06-18 00:19:28,961 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: INICIANDO
2025-06-18 00:19:28,961 - LogTransaccionesPipeline - INFO - Ejecutando SP_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-18 00:19:28,962 - LogTransaccionesPipeline - INFO - Archivo previo eliminado: TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-18 00:19:29,285 - LogTransaccionesPipeline - INFO - SP_LOG_TRX completado: 104116 registros -> TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-18 00:19:29,287 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: COMPLETADO
2025-06-18 00:19:29,288 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: INICIANDO
2025-06-18 00:19:29,288 - LogTransaccionesPipeline - INFO - Extrayendo CSV final para fecha: 2025-06-15
2025-06-18 00:19:29,414 - LogTransaccionesPipeline - INFO - CSV final generado: 104116 registros -> output/TR-********.csv
2025-06-18 00:19:29,416 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: COMPLETADO
2025-06-18 00:19:29,416 - LogTransaccionesPipeline - INFO - ✅ Pipeline LOG_TRANSACCIONES completado exitosamente en 96.48 segundos
2025-06-18 00:19:29,417 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: EXITOSO
2025-06-18 00:20:57,942 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-18 00:20:57,961 - botocore.credentials - INFO - Found credentials from IAM Role: ec-landing-process-01-EC2-Role
2025-06-18 00:20:57,999 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-18 00:20:57,999 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-15
2025-06-18 00:20:58,000 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-18 00:20:58,000 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX usando 100% tablas S3 para fecha: 2025-06-15
2025-06-18 00:20:58,001 - LogTransaccionesPipeline - INFO - Archivo previo eliminado: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-18 00:22:32,783 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX completado con deduplicación automática:
2025-06-18 00:22:32,784 - LogTransaccionesPipeline - INFO -   - Registros finales: 104,116
2025-06-18 00:22:32,784 - LogTransaccionesPipeline - INFO -   - TransferIDs únicos: 104,116
2025-06-18 00:22:32,784 - LogTransaccionesPipeline - INFO -   - Duplicados eliminados: 0
2025-06-18 00:22:32,784 - LogTransaccionesPipeline - INFO -   - Archivo: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-18 00:22:32,784 - LogTransaccionesPipeline - INFO - 🎉 HOMOLOGACIÓN PERFECTA: Coincide exactamente con Oracle PRE_LOG_TRX
2025-06-18 00:22:32,785 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: COMPLETADO
2025-06-18 00:22:32,787 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: INICIANDO
2025-06-18 00:22:32,787 - LogTransaccionesPipeline - INFO - Ejecutando SP_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-18 00:22:32,787 - LogTransaccionesPipeline - INFO - Archivo previo eliminado: TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-18 00:22:33,115 - LogTransaccionesPipeline - INFO - SP_LOG_TRX completado: 104116 registros -> TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-18 00:22:33,117 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: COMPLETADO
2025-06-18 00:22:33,118 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: INICIANDO
2025-06-18 00:22:33,118 - LogTransaccionesPipeline - INFO - Extrayendo CSV final para fecha: 2025-06-15
2025-06-18 00:22:33,254 - LogTransaccionesPipeline - INFO - CSV final generado: 104116 registros -> output/TR-********.csv
2025-06-18 00:22:33,256 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: COMPLETADO
2025-06-18 00:22:33,256 - LogTransaccionesPipeline - INFO - ✅ Pipeline LOG_TRANSACCIONES completado exitosamente en 95.26 segundos
2025-06-18 00:22:33,258 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: EXITOSO
2025-06-18 00:49:01,228 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-18 00:49:01,247 - botocore.credentials - INFO - Found credentials from IAM Role: ec-landing-process-01-EC2-Role
2025-06-18 00:49:01,284 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-18 00:49:01,285 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-15
2025-06-18 00:49:01,286 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-18 00:49:01,286 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX usando 100% tablas S3 para fecha: 2025-06-15
2025-06-18 00:49:01,287 - LogTransaccionesPipeline - INFO - Archivo previo eliminado: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-18 00:49:07,727 - LogTransaccionesPipeline - ERROR - Error completando SP_PRE_LOG_TRX: HTTP Error: HTTP GET error reading 's3://ec-landing-process-01/datalake/MMONEY/PDP_PROD10_MAINDBBUS/MTX_WALLET/ec-landing-process-01/?encoding-type=url&list-type=2&prefix=datalake%2FMMONEY%2FPDP_PROD10_MAINDBBUS%2FMTX_WALLET%2F' in region 'us-east-1' (HTTP 404 Not Found)

Authentication Failure - this is usually caused by invalid or missing credentials.
* Credentials are provided, but they did not work.
* See https://duckdb.org/docs/stable/extensions/httpfs/s3api.html
2025-06-18 00:49:07,727 - LogTransaccionesPipeline - ERROR - Error en SP_PRE_LOG_TRX: HTTP Error: HTTP GET error reading 's3://ec-landing-process-01/datalake/MMONEY/PDP_PROD10_MAINDBBUS/MTX_WALLET/ec-landing-process-01/?encoding-type=url&list-type=2&prefix=datalake%2FMMONEY%2FPDP_PROD10_MAINDBBUS%2FMTX_WALLET%2F' in region 'us-east-1' (HTTP 404 Not Found)

Authentication Failure - this is usually caused by invalid or missing credentials.
* Credentials are provided, but they did not work.
* See https://duckdb.org/docs/stable/extensions/httpfs/s3api.html
2025-06-18 00:49:07,727 - LogTransaccionesPipeline - ERROR - Error en SP_PRE_LOG_TRX: HTTP Error: HTTP GET error reading 's3://ec-landing-process-01/datalake/MMONEY/PDP_PROD10_MAINDBBUS/MTX_WALLET/ec-landing-process-01/?encoding-type=url&list-type=2&prefix=datalake%2FMMONEY%2FPDP_PROD10_MAINDBBUS%2FMTX_WALLET%2F' in region 'us-east-1' (HTTP 404 Not Found)

Authentication Failure - this is usually caused by invalid or missing credentials.
* Credentials are provided, but they did not work.
* See https://duckdb.org/docs/stable/extensions/httpfs/s3api.html
2025-06-18 00:49:07,729 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: ERROR: Error en SP_PRE_LOG_TRX: HTTP Error: HTTP GET error reading 's3://ec-landing-process-01/datalake/MMONEY/PDP_PROD10_MAINDBBUS/MTX_WALLET/ec-landing-process-01/?encoding-type=url&list-type=2&prefix=datalake%2FMMONEY%2FPDP_PROD10_MAINDBBUS%2FMTX_WALLET%2F' in region 'us-east-1' (HTTP 404 Not Found)

Authentication Failure - this is usually caused by invalid or missing credentials.
* Credentials are provided, but they did not work.
* See https://duckdb.org/docs/stable/extensions/httpfs/s3api.html
2025-06-18 00:49:07,729 - LogTransaccionesPipeline - ERROR - ❌ Pipeline LOG_TRANSACCIONES falló después de 6.44 segundos: HTTP Error: HTTP GET error reading 's3://ec-landing-process-01/datalake/MMONEY/PDP_PROD10_MAINDBBUS/MTX_WALLET/ec-landing-process-01/?encoding-type=url&list-type=2&prefix=datalake%2FMMONEY%2FPDP_PROD10_MAINDBBUS%2FMTX_WALLET%2F' in region 'us-east-1' (HTTP 404 Not Found)

Authentication Failure - this is usually caused by invalid or missing credentials.
* Credentials are provided, but they did not work.
* See https://duckdb.org/docs/stable/extensions/httpfs/s3api.html
2025-06-18 00:49:07,730 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: ERROR: HTTP Error: HTTP GET error reading 's3://ec-landing-process-01/datalake/MMONEY/PDP_PROD10_MAINDBBUS/MTX_WALLET/ec-landing-process-01/?encoding-type=url&list-type=2&prefix=datalake%2FMMONEY%2FPDP_PROD10_MAINDBBUS%2FMTX_WALLET%2F' in region 'us-east-1' (HTTP 404 Not Found)

Authentication Failure - this is usually caused by invalid or missing credentials.
* Credentials are provided, but they did not work.
* See https://duckdb.org/docs/stable/extensions/httpfs/s3api.html
2025-06-18 00:52:38,746 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-18 00:52:38,765 - botocore.credentials - INFO - Found credentials from IAM Role: ec-landing-process-01-EC2-Role
2025-06-18 00:52:38,803 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-18 00:52:38,803 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-15
2025-06-18 00:52:38,804 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-18 00:52:38,805 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX usando 100% tablas S3 para fecha: 2025-06-15
2025-06-18 00:55:06,981 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX completado con deduplicación automática:
2025-06-18 00:55:06,981 - LogTransaccionesPipeline - INFO -   - Registros finales: 104,116
2025-06-18 00:55:06,981 - LogTransaccionesPipeline - INFO -   - TransferIDs únicos: 104,116
2025-06-18 00:55:06,981 - LogTransaccionesPipeline - INFO -   - Duplicados eliminados: 0
2025-06-18 00:55:06,981 - LogTransaccionesPipeline - INFO -   - Archivo: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-18 00:55:06,981 - LogTransaccionesPipeline - INFO - 🎉 HOMOLOGACIÓN PERFECTA: Coincide exactamente con Oracle PRE_LOG_TRX
2025-06-18 00:55:06,982 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: COMPLETADO
2025-06-18 00:55:06,984 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: INICIANDO
2025-06-18 00:55:06,984 - LogTransaccionesPipeline - INFO - Ejecutando SP_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-18 00:55:06,984 - LogTransaccionesPipeline - INFO - Archivo previo eliminado: TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-18 00:55:07,317 - LogTransaccionesPipeline - INFO - SP_LOG_TRX completado: 104116 registros -> TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-18 00:55:07,318 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: COMPLETADO
2025-06-18 00:55:07,320 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: INICIANDO
2025-06-18 00:55:07,320 - LogTransaccionesPipeline - INFO - Extrayendo CSV final para fecha: 2025-06-15
2025-06-18 00:55:07,481 - LogTransaccionesPipeline - INFO - CSV final generado: 104116 registros -> output/TR-********.csv
2025-06-18 00:55:07,483 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: COMPLETADO
2025-06-18 00:55:07,483 - LogTransaccionesPipeline - INFO - ✅ Pipeline LOG_TRANSACCIONES completado exitosamente en 148.68 segundos
2025-06-18 00:55:07,484 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: EXITOSO
2025-06-18 00:56:59,800 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-18 00:56:59,818 - botocore.credentials - INFO - Found credentials from IAM Role: ec-landing-process-01-EC2-Role
2025-06-18 00:56:59,857 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-18 00:56:59,857 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-15
2025-06-18 00:56:59,858 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-18 00:56:59,858 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX usando 100% tablas S3 para fecha: 2025-06-15
2025-06-18 00:56:59,859 - LogTransaccionesPipeline - INFO - Archivo previo eliminado: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-18 00:58:44,283 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX completado con deduplicación automática:
2025-06-18 00:58:44,283 - LogTransaccionesPipeline - INFO -   - Registros finales: 104,116
2025-06-18 00:58:44,283 - LogTransaccionesPipeline - INFO -   - TransferIDs únicos: 104,116
2025-06-18 00:58:44,283 - LogTransaccionesPipeline - INFO -   - Duplicados eliminados: 0
2025-06-18 00:58:44,283 - LogTransaccionesPipeline - INFO -   - Archivo: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-18 00:58:44,283 - LogTransaccionesPipeline - INFO - 🎉 HOMOLOGACIÓN PERFECTA: Coincide exactamente con Oracle PRE_LOG_TRX
2025-06-18 00:58:44,285 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: COMPLETADO
2025-06-18 00:58:44,286 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: INICIANDO
2025-06-18 00:58:44,286 - LogTransaccionesPipeline - INFO - Ejecutando SP_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-18 00:58:44,287 - LogTransaccionesPipeline - INFO - Archivo previo eliminado: TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-18 00:58:44,625 - LogTransaccionesPipeline - INFO - SP_LOG_TRX completado: 104116 registros -> TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-18 00:58:44,627 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: COMPLETADO
2025-06-18 00:58:44,629 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: INICIANDO
2025-06-18 00:58:44,629 - LogTransaccionesPipeline - INFO - Extrayendo CSV final para fecha: 2025-06-15
2025-06-18 00:58:44,767 - LogTransaccionesPipeline - INFO - CSV final generado: 104116 registros -> output/TR-********.csv
2025-06-18 00:58:44,769 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: COMPLETADO
2025-06-18 00:58:44,769 - LogTransaccionesPipeline - INFO - ✅ Pipeline LOG_TRANSACCIONES completado exitosamente en 104.91 segundos
2025-06-18 00:58:44,771 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: EXITOSO
2025-06-18 00:59:39,290 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-18 00:59:39,308 - botocore.credentials - INFO - Found credentials from IAM Role: ec-landing-process-01-EC2-Role
2025-06-18 00:59:39,344 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-18 00:59:39,344 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-15
2025-06-18 00:59:39,346 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-18 00:59:39,346 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX usando 100% tablas S3 para fecha: 2025-06-15
2025-06-18 00:59:39,347 - LogTransaccionesPipeline - INFO - Archivo previo eliminado: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-18 01:01:26,072 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX completado con deduplicación automática:
2025-06-18 01:01:26,072 - LogTransaccionesPipeline - INFO -   - Registros finales: 104,116
2025-06-18 01:01:26,072 - LogTransaccionesPipeline - INFO -   - TransferIDs únicos: 104,116
2025-06-18 01:01:26,072 - LogTransaccionesPipeline - INFO -   - Duplicados eliminados: 0
2025-06-18 01:01:26,073 - LogTransaccionesPipeline - INFO -   - Archivo: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-18 01:01:26,073 - LogTransaccionesPipeline - INFO - 🎉 HOMOLOGACIÓN PERFECTA: Coincide exactamente con Oracle PRE_LOG_TRX
2025-06-18 01:01:26,074 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: COMPLETADO
2025-06-18 01:01:26,076 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: INICIANDO
2025-06-18 01:01:26,076 - LogTransaccionesPipeline - INFO - Ejecutando SP_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-18 01:01:26,076 - LogTransaccionesPipeline - INFO - Archivo previo eliminado: TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-18 01:01:26,417 - LogTransaccionesPipeline - INFO - SP_LOG_TRX completado: 104116 registros -> TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-18 01:01:26,418 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: COMPLETADO
2025-06-18 01:01:26,419 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: INICIANDO
2025-06-18 01:01:26,420 - LogTransaccionesPipeline - INFO - Extrayendo CSV final para fecha: 2025-06-15
2025-06-18 01:01:26,568 - LogTransaccionesPipeline - INFO - CSV final generado: 104116 registros -> output/TR-********.csv
2025-06-18 01:01:26,569 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: COMPLETADO
2025-06-18 01:01:26,569 - LogTransaccionesPipeline - INFO - ✅ Pipeline LOG_TRANSACCIONES completado exitosamente en 107.22 segundos
2025-06-18 01:01:26,571 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: EXITOSO
2025-06-18 01:23:03,106 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-18 01:23:03,127 - botocore.credentials - INFO - Found credentials from IAM Role: ec-landing-process-01-EC2-Role
2025-06-18 01:23:03,492 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-18 01:23:03,493 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-15
2025-06-18 01:23:03,495 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-18 01:23:03,495 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX usando 100% tablas S3 para fecha: 2025-06-15
2025-06-18 01:23:03,496 - LogTransaccionesPipeline - INFO - Archivo previo eliminado: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-18 01:23:03,496 - LogTransaccionesPipeline - ERROR - Error en SP_PRE_LOG_TRX: 'user_profile'
2025-06-18 01:23:03,496 - LogTransaccionesPipeline - ERROR - Error en SP_PRE_LOG_TRX: 'user_profile'
2025-06-18 01:23:03,497 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: ERROR: Error en SP_PRE_LOG_TRX: 'user_profile'
2025-06-18 01:23:03,497 - LogTransaccionesPipeline - ERROR - ❌ Pipeline LOG_TRANSACCIONES falló después de 0.00 segundos: 'user_profile'
2025-06-18 01:23:03,499 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: ERROR: 'user_profile'
2025-06-18 01:24:06,604 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-18 01:24:06,622 - botocore.credentials - INFO - Found credentials from IAM Role: ec-landing-process-01-EC2-Role
2025-06-18 01:24:06,658 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-18 01:24:06,658 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-15
2025-06-18 01:24:06,660 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-18 01:24:06,660 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX usando 100% tablas S3 para fecha: 2025-06-15
2025-06-18 01:24:11,967 - LogTransaccionesPipeline - ERROR - Error completando SP_PRE_LOG_TRX: Catalog Error: Table with name WALLET_ACTIVOS does not exist!
Did you mean "pg_am"?

LINE 392:             LEFT JOIN WALLET_ACTIVOS WA_PAYER ON UPAYER.M_USER_ID = WA_PAYER.USER...
                                ^
2025-06-18 01:24:11,967 - LogTransaccionesPipeline - ERROR - Error en SP_PRE_LOG_TRX: Catalog Error: Table with name WALLET_ACTIVOS does not exist!
Did you mean "pg_am"?

LINE 392:             LEFT JOIN WALLET_ACTIVOS WA_PAYER ON UPAYER.M_USER_ID = WA_PAYER.USER...
                                ^
2025-06-18 01:24:11,967 - LogTransaccionesPipeline - ERROR - Error en SP_PRE_LOG_TRX: Catalog Error: Table with name WALLET_ACTIVOS does not exist!
Did you mean "pg_am"?

LINE 392:             LEFT JOIN WALLET_ACTIVOS WA_PAYER ON UPAYER.M_USER_ID = WA_PAYER.USER...
                                ^
2025-06-18 01:24:11,969 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: ERROR: Error en SP_PRE_LOG_TRX: Catalog Error: Table with name WALLET_ACTIVOS does not exist!
Did you mean "pg_am"?

LINE 392:             LEFT JOIN WALLET_ACTIVOS WA_PAYER ON UPAYER.M_USER_ID = WA_PAYER.USER...
                                ^
2025-06-18 01:24:11,969 - LogTransaccionesPipeline - ERROR - ❌ Pipeline LOG_TRANSACCIONES falló después de 5.31 segundos: Catalog Error: Table with name WALLET_ACTIVOS does not exist!
Did you mean "pg_am"?

LINE 392:             LEFT JOIN WALLET_ACTIVOS WA_PAYER ON UPAYER.M_USER_ID = WA_PAYER.USER...
                                ^
2025-06-18 01:24:11,970 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: ERROR: Catalog Error: Table with name WALLET_ACTIVOS does not exist!
Did you mean "pg_am"?

LINE 392:             LEFT JOIN WALLET_ACTIVOS WA_PAYER ON UPAYER.M_USER_ID = WA_PAYER.USER...
                                ^
2025-06-18 01:25:23,460 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-18 01:25:23,479 - botocore.credentials - INFO - Found credentials from IAM Role: ec-landing-process-01-EC2-Role
2025-06-18 01:25:23,518 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-18 01:25:23,518 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-15
2025-06-18 01:25:23,520 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-18 01:25:23,520 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX usando 100% tablas S3 para fecha: 2025-06-15
2025-06-18 01:27:02,502 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX completado con deduplicación automática:
2025-06-18 01:27:02,502 - LogTransaccionesPipeline - INFO -   - Registros finales: 104,116
2025-06-18 01:27:02,502 - LogTransaccionesPipeline - INFO -   - TransferIDs únicos: 104,116
2025-06-18 01:27:02,502 - LogTransaccionesPipeline - INFO -   - Duplicados eliminados: 0
2025-06-18 01:27:02,502 - LogTransaccionesPipeline - INFO -   - Archivo: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-18 01:27:02,502 - LogTransaccionesPipeline - INFO - 🎉 HOMOLOGACIÓN PERFECTA: Coincide exactamente con Oracle PRE_LOG_TRX
2025-06-18 01:27:02,504 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: COMPLETADO
2025-06-18 01:27:02,505 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: INICIANDO
2025-06-18 01:27:02,505 - LogTransaccionesPipeline - INFO - Ejecutando SP_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-18 01:27:02,506 - LogTransaccionesPipeline - INFO - Archivo previo eliminado: TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-18 01:27:02,872 - LogTransaccionesPipeline - INFO - SP_LOG_TRX completado: 104116 registros -> TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-18 01:27:02,873 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: COMPLETADO
2025-06-18 01:27:02,875 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: INICIANDO
2025-06-18 01:27:02,875 - LogTransaccionesPipeline - INFO - Extrayendo CSV final para fecha: 2025-06-15
2025-06-18 01:27:02,995 - LogTransaccionesPipeline - INFO - CSV final generado: 104116 registros -> output/TR-********.csv
2025-06-18 01:27:02,997 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: COMPLETADO
2025-06-18 01:27:02,997 - LogTransaccionesPipeline - INFO - ✅ Pipeline LOG_TRANSACCIONES completado exitosamente en 99.48 segundos
2025-06-18 01:27:02,999 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: EXITOSO
2025-06-18 01:30:00,155 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-18 01:30:00,178 - botocore.credentials - INFO - Found credentials from IAM Role: ec-landing-process-01-EC2-Role
2025-06-18 01:30:00,217 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-18 01:30:00,218 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-15
2025-06-18 01:30:00,219 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-18 01:30:00,219 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX usando 100% tablas S3 para fecha: 2025-06-15
2025-06-18 01:30:00,220 - LogTransaccionesPipeline - INFO - Archivo previo eliminado: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-18 01:31:36,390 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX completado con deduplicación automática:
2025-06-18 01:31:36,390 - LogTransaccionesPipeline - INFO -   - Registros finales: 104,116
2025-06-18 01:31:36,390 - LogTransaccionesPipeline - INFO -   - TransferIDs únicos: 104,116
2025-06-18 01:31:36,390 - LogTransaccionesPipeline - INFO -   - Duplicados eliminados: 0
2025-06-18 01:31:36,390 - LogTransaccionesPipeline - INFO -   - Archivo: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-18 01:31:36,390 - LogTransaccionesPipeline - INFO - 🎉 HOMOLOGACIÓN PERFECTA: Coincide exactamente con Oracle PRE_LOG_TRX
2025-06-18 01:31:36,392 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: COMPLETADO
2025-06-18 01:31:36,393 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: INICIANDO
2025-06-18 01:31:36,393 - LogTransaccionesPipeline - INFO - Ejecutando SP_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-18 01:31:36,394 - LogTransaccionesPipeline - INFO - Archivo previo eliminado: TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-18 01:31:36,715 - LogTransaccionesPipeline - INFO - SP_LOG_TRX completado: 104116 registros -> TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-18 01:31:36,716 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: COMPLETADO
2025-06-18 01:31:36,718 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: INICIANDO
2025-06-18 01:31:36,718 - LogTransaccionesPipeline - INFO - Extrayendo CSV final para fecha: 2025-06-15
2025-06-18 01:31:36,850 - LogTransaccionesPipeline - INFO - CSV final generado: 104116 registros -> output/TR-********.csv
2025-06-18 01:31:36,851 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: COMPLETADO
2025-06-18 01:31:36,852 - LogTransaccionesPipeline - INFO - ✅ Pipeline LOG_TRANSACCIONES completado exitosamente en 96.63 segundos
2025-06-18 01:31:36,853 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: EXITOSO
2025-06-18 12:34:33,307 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-18 12:34:33,328 - botocore.credentials - INFO - Found credentials from IAM Role: ec-landing-process-01-EC2-Role
2025-06-18 12:34:33,688 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-18 12:34:33,689 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-15
2025-06-18 12:34:33,690 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-18 12:34:33,690 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX usando 100% tablas S3 para fecha: 2025-06-15
2025-06-18 12:34:33,691 - LogTransaccionesPipeline - INFO - Archivo previo eliminado: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-18 12:36:14,704 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX completado con deduplicación automática:
2025-06-18 12:36:14,704 - LogTransaccionesPipeline - INFO -   - Registros finales: 104,116
2025-06-18 12:36:14,704 - LogTransaccionesPipeline - INFO -   - TransferIDs únicos: 104,116
2025-06-18 12:36:14,704 - LogTransaccionesPipeline - INFO -   - Duplicados eliminados: 0
2025-06-18 12:36:14,704 - LogTransaccionesPipeline - INFO -   - Archivo: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-18 12:36:14,704 - LogTransaccionesPipeline - INFO - 🎉 HOMOLOGACIÓN PERFECTA: Coincide exactamente con Oracle PRE_LOG_TRX
2025-06-18 12:36:14,706 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: COMPLETADO
2025-06-18 12:36:14,707 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: INICIANDO
2025-06-18 12:36:14,707 - LogTransaccionesPipeline - INFO - Ejecutando SP_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-18 12:36:14,708 - LogTransaccionesPipeline - INFO - Archivo previo eliminado: TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-18 12:36:15,044 - LogTransaccionesPipeline - INFO - SP_LOG_TRX completado: 104116 registros -> TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-18 12:36:15,046 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: COMPLETADO
2025-06-18 12:36:15,047 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: INICIANDO
2025-06-18 12:36:15,047 - LogTransaccionesPipeline - INFO - Extrayendo CSV final para fecha: 2025-06-15
2025-06-18 12:36:15,178 - LogTransaccionesPipeline - INFO - CSV final generado: 104116 registros -> output/TR-********.csv
2025-06-18 12:36:15,180 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: COMPLETADO
2025-06-18 12:36:15,180 - LogTransaccionesPipeline - INFO - ✅ Pipeline LOG_TRANSACCIONES completado exitosamente en 101.49 segundos
2025-06-18 12:36:15,181 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: EXITOSO
2025-06-18 12:40:35,283 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-18 12:40:35,301 - botocore.credentials - INFO - Found credentials from IAM Role: ec-landing-process-01-EC2-Role
2025-06-18 12:40:35,337 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-18 12:40:35,337 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-15
2025-06-18 12:40:35,338 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-18 12:40:35,338 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX usando 100% tablas S3 para fecha: 2025-06-15
2025-06-18 12:40:35,339 - LogTransaccionesPipeline - INFO - Archivo previo eliminado: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-18 12:42:14,963 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX completado con deduplicación automática:
2025-06-18 12:42:14,964 - LogTransaccionesPipeline - INFO -   - Registros finales: 104,116
2025-06-18 12:42:14,964 - LogTransaccionesPipeline - INFO -   - TransferIDs únicos: 104,116
2025-06-18 12:42:14,964 - LogTransaccionesPipeline - INFO -   - Duplicados eliminados: 0
2025-06-18 12:42:14,964 - LogTransaccionesPipeline - INFO -   - Archivo: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-18 12:42:14,964 - LogTransaccionesPipeline - INFO - 🎉 HOMOLOGACIÓN PERFECTA: Coincide exactamente con Oracle PRE_LOG_TRX
2025-06-18 12:42:14,965 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: COMPLETADO
2025-06-18 12:42:14,967 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: INICIANDO
2025-06-18 12:42:14,967 - LogTransaccionesPipeline - INFO - Ejecutando SP_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-18 12:42:14,967 - LogTransaccionesPipeline - INFO - Archivo previo eliminado: TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-18 12:42:15,306 - LogTransaccionesPipeline - INFO - SP_LOG_TRX completado: 104116 registros -> TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-18 12:42:15,308 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: COMPLETADO
2025-06-18 12:42:15,309 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: INICIANDO
2025-06-18 12:42:15,309 - LogTransaccionesPipeline - INFO - Extrayendo CSV final para fecha: 2025-06-15
2025-06-18 12:42:15,435 - LogTransaccionesPipeline - INFO - CSV final generado: 104116 registros -> output/TR-********.csv
2025-06-18 12:42:15,437 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: COMPLETADO
2025-06-18 12:42:15,437 - LogTransaccionesPipeline - INFO - ✅ Pipeline LOG_TRANSACCIONES completado exitosamente en 100.10 segundos
2025-06-18 12:42:15,438 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: EXITOSO
2025-06-18 13:16:10,412 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-18 13:16:10,431 - botocore.credentials - INFO - Found credentials from IAM Role: ec-landing-process-01-EC2-Role
2025-06-18 13:16:10,470 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-18 13:16:10,470 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-15
2025-06-18 13:16:10,472 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-18 13:16:10,472 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX usando 100% tablas S3 para fecha: 2025-06-15
2025-06-18 13:16:10,473 - LogTransaccionesPipeline - INFO - Archivo previo eliminado: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-18 13:17:52,272 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX completado con deduplicación automática:
2025-06-18 13:17:52,272 - LogTransaccionesPipeline - INFO -   - Registros finales: 104,116
2025-06-18 13:17:52,272 - LogTransaccionesPipeline - INFO -   - TransferIDs únicos: 104,116
2025-06-18 13:17:52,272 - LogTransaccionesPipeline - INFO -   - Duplicados eliminados: 0
2025-06-18 13:17:52,272 - LogTransaccionesPipeline - INFO -   - Archivo: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-18 13:17:52,272 - LogTransaccionesPipeline - INFO - 🎉 HOMOLOGACIÓN PERFECTA: Coincide exactamente con Oracle PRE_LOG_TRX
2025-06-18 13:17:52,274 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: COMPLETADO
2025-06-18 13:17:52,276 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: INICIANDO
2025-06-18 13:17:52,276 - LogTransaccionesPipeline - INFO - Ejecutando SP_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-18 13:17:52,276 - LogTransaccionesPipeline - INFO - Archivo previo eliminado: TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-18 13:17:52,625 - LogTransaccionesPipeline - INFO - SP_LOG_TRX completado: 104116 registros -> TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-18 13:17:52,627 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: COMPLETADO
2025-06-18 13:17:52,628 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: INICIANDO
2025-06-18 13:17:52,628 - LogTransaccionesPipeline - INFO - Extrayendo CSV final para fecha: 2025-06-15
2025-06-18 13:17:52,755 - LogTransaccionesPipeline - INFO - CSV final generado: 104116 registros -> output/TR-********.csv
2025-06-18 13:17:52,757 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: COMPLETADO
2025-06-18 13:17:52,757 - LogTransaccionesPipeline - INFO - ✅ Pipeline LOG_TRANSACCIONES completado exitosamente en 102.29 segundos
2025-06-18 13:17:52,758 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: EXITOSO
2025-06-18 13:21:52,260 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-18 13:21:52,279 - botocore.credentials - INFO - Found credentials from IAM Role: ec-landing-process-01-EC2-Role
2025-06-18 13:21:52,317 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-18 13:21:52,317 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-15
2025-06-18 13:21:52,318 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-18 13:21:52,319 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX usando 100% tablas S3 para fecha: 2025-06-15
2025-06-18 13:21:52,320 - LogTransaccionesPipeline - INFO - Archivo previo eliminado: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-18 13:23:33,215 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX completado con deduplicación automática:
2025-06-18 13:23:33,215 - LogTransaccionesPipeline - INFO -   - Registros finales: 104,116
2025-06-18 13:23:33,215 - LogTransaccionesPipeline - INFO -   - TransferIDs únicos: 104,116
2025-06-18 13:23:33,215 - LogTransaccionesPipeline - INFO -   - Duplicados eliminados: 0
2025-06-18 13:23:33,215 - LogTransaccionesPipeline - INFO -   - Archivo: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-18 13:23:33,215 - LogTransaccionesPipeline - INFO - 🎉 HOMOLOGACIÓN PERFECTA: Coincide exactamente con Oracle PRE_LOG_TRX
2025-06-18 13:23:33,216 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: COMPLETADO
2025-06-18 13:23:33,218 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: INICIANDO
2025-06-18 13:23:33,218 - LogTransaccionesPipeline - INFO - Ejecutando SP_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-18 13:23:33,219 - LogTransaccionesPipeline - INFO - Archivo previo eliminado: TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-18 13:23:33,631 - LogTransaccionesPipeline - INFO - SP_LOG_TRX completado: 104116 registros -> TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-18 13:23:33,633 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: COMPLETADO
2025-06-18 13:23:33,634 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: INICIANDO
2025-06-18 13:23:33,634 - LogTransaccionesPipeline - INFO - Extrayendo CSV final para fecha: 2025-06-15
2025-06-18 13:23:33,767 - LogTransaccionesPipeline - INFO - CSV final generado: 104116 registros -> output/TR-********.csv
2025-06-18 13:23:33,769 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: COMPLETADO
2025-06-18 13:23:33,769 - LogTransaccionesPipeline - INFO - ✅ Pipeline LOG_TRANSACCIONES completado exitosamente en 101.45 segundos
2025-06-18 13:23:33,771 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: EXITOSO
2025-06-18 13:57:20,182 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-18 13:57:20,201 - botocore.credentials - INFO - Found credentials from IAM Role: ec-landing-process-01-EC2-Role
2025-06-18 13:57:20,241 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-18 13:57:20,241 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-15
2025-06-18 13:57:20,243 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-18 13:57:20,243 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX usando 100% tablas S3 para fecha: 2025-06-15
2025-06-18 13:57:20,244 - LogTransaccionesPipeline - INFO - Archivo previo eliminado: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-18 13:59:01,183 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX completado con deduplicación automática:
2025-06-18 13:59:01,183 - LogTransaccionesPipeline - INFO -   - Registros finales: 104,116
2025-06-18 13:59:01,183 - LogTransaccionesPipeline - INFO -   - TransferIDs únicos: 104,116
2025-06-18 13:59:01,183 - LogTransaccionesPipeline - INFO -   - Duplicados eliminados: 0
2025-06-18 13:59:01,183 - LogTransaccionesPipeline - INFO -   - Archivo: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-18 13:59:01,183 - LogTransaccionesPipeline - INFO - 🎉 HOMOLOGACIÓN PERFECTA: Coincide exactamente con Oracle PRE_LOG_TRX
2025-06-18 13:59:01,185 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: COMPLETADO
2025-06-18 13:59:01,187 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: INICIANDO
2025-06-18 13:59:01,187 - LogTransaccionesPipeline - INFO - Ejecutando SP_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-18 13:59:01,187 - LogTransaccionesPipeline - INFO - Archivo previo eliminado: TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-18 13:59:01,552 - LogTransaccionesPipeline - INFO - SP_LOG_TRX completado: 104116 registros -> TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-18 13:59:01,554 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: COMPLETADO
2025-06-18 13:59:01,555 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: INICIANDO
2025-06-18 13:59:01,555 - LogTransaccionesPipeline - INFO - Extrayendo CSV final para fecha: 2025-06-15
2025-06-18 13:59:01,695 - LogTransaccionesPipeline - INFO - CSV final generado: 104116 registros -> output/TR-********.csv
2025-06-18 13:59:01,697 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: COMPLETADO
2025-06-18 13:59:01,697 - LogTransaccionesPipeline - INFO - ✅ Pipeline LOG_TRANSACCIONES completado exitosamente en 101.46 segundos
2025-06-18 13:59:01,698 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: EXITOSO
2025-06-18 13:59:42,425 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-18 13:59:42,443 - botocore.credentials - INFO - Found credentials from IAM Role: ec-landing-process-01-EC2-Role
2025-06-18 13:59:42,485 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-18 13:59:42,485 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-15
2025-06-18 13:59:42,487 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-18 13:59:42,487 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX usando 100% tablas S3 para fecha: 2025-06-15
2025-06-18 13:59:42,488 - LogTransaccionesPipeline - INFO - Archivo previo eliminado: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-18 14:01:21,172 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX completado con deduplicación automática:
2025-06-18 14:01:21,172 - LogTransaccionesPipeline - INFO -   - Registros finales: 104,116
2025-06-18 14:01:21,172 - LogTransaccionesPipeline - INFO -   - TransferIDs únicos: 104,116
2025-06-18 14:01:21,172 - LogTransaccionesPipeline - INFO -   - Duplicados eliminados: 0
2025-06-18 14:01:21,172 - LogTransaccionesPipeline - INFO -   - Archivo: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-18 14:01:21,172 - LogTransaccionesPipeline - INFO - 🎉 HOMOLOGACIÓN PERFECTA: Coincide exactamente con Oracle PRE_LOG_TRX
2025-06-18 14:01:21,173 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: COMPLETADO
2025-06-18 14:01:21,175 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: INICIANDO
2025-06-18 14:01:21,175 - LogTransaccionesPipeline - INFO - Ejecutando SP_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-18 14:01:21,176 - LogTransaccionesPipeline - INFO - Archivo previo eliminado: TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-18 14:01:21,503 - LogTransaccionesPipeline - INFO - SP_LOG_TRX completado: 104116 registros -> TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-18 14:01:21,505 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: COMPLETADO
2025-06-18 14:01:21,506 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: INICIANDO
2025-06-18 14:01:21,506 - LogTransaccionesPipeline - INFO - Extrayendo CSV final para fecha: 2025-06-15
2025-06-18 14:01:21,622 - LogTransaccionesPipeline - INFO - CSV final generado: 104116 registros -> output/TR-********.csv
2025-06-18 14:01:21,624 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: COMPLETADO
2025-06-18 14:01:21,624 - LogTransaccionesPipeline - INFO - ✅ Pipeline LOG_TRANSACCIONES completado exitosamente en 99.14 segundos
2025-06-18 14:01:21,625 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: EXITOSO
2025-06-18 14:08:35,766 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-18 14:08:35,784 - botocore.credentials - INFO - Found credentials from IAM Role: ec-landing-process-01-EC2-Role
2025-06-18 14:08:35,822 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-18 14:08:35,822 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-15
2025-06-18 14:08:35,824 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-18 14:08:35,824 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX usando 100% tablas S3 para fecha: 2025-06-15
2025-06-18 14:08:35,825 - LogTransaccionesPipeline - INFO - Archivo previo eliminado: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-18 14:10:16,789 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX completado con deduplicación automática:
2025-06-18 14:10:16,789 - LogTransaccionesPipeline - INFO -   - Registros finales: 104,116
2025-06-18 14:10:16,789 - LogTransaccionesPipeline - INFO -   - TransferIDs únicos: 104,116
2025-06-18 14:10:16,789 - LogTransaccionesPipeline - INFO -   - Duplicados eliminados: 0
2025-06-18 14:10:16,789 - LogTransaccionesPipeline - INFO -   - Archivo: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-18 14:10:16,789 - LogTransaccionesPipeline - INFO - 🎉 HOMOLOGACIÓN PERFECTA: Coincide exactamente con Oracle PRE_LOG_TRX
2025-06-18 14:10:16,790 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: COMPLETADO
2025-06-18 14:10:16,792 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: INICIANDO
2025-06-18 14:10:16,792 - LogTransaccionesPipeline - INFO - Ejecutando SP_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-18 14:10:16,792 - LogTransaccionesPipeline - INFO - Archivo previo eliminado: TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-18 14:10:17,146 - LogTransaccionesPipeline - INFO - SP_LOG_TRX completado: 104116 registros -> TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-18 14:10:17,148 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: COMPLETADO
2025-06-18 14:10:17,149 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: INICIANDO
2025-06-18 14:10:17,149 - LogTransaccionesPipeline - INFO - Extrayendo CSV final para fecha: 2025-06-15
2025-06-18 14:10:17,296 - LogTransaccionesPipeline - INFO - CSV final generado: 104116 registros -> output/TR-********.csv
2025-06-18 14:10:17,298 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: COMPLETADO
2025-06-18 14:10:17,298 - LogTransaccionesPipeline - INFO - ✅ Pipeline LOG_TRANSACCIONES completado exitosamente en 101.48 segundos
2025-06-18 14:10:17,299 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: EXITOSO
2025-06-18 14:21:09,763 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-18 14:21:09,782 - botocore.credentials - INFO - Found credentials from IAM Role: ec-landing-process-01-EC2-Role
2025-06-18 14:21:09,825 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-18 14:21:09,826 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-15
2025-06-18 14:21:09,827 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-18 14:21:09,827 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX usando 100% tablas S3 para fecha: 2025-06-15
2025-06-18 14:21:09,828 - LogTransaccionesPipeline - INFO - Archivo previo eliminado: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-18 14:22:52,308 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX completado con deduplicación automática:
2025-06-18 14:22:52,308 - LogTransaccionesPipeline - INFO -   - Registros finales: 104,116
2025-06-18 14:22:52,308 - LogTransaccionesPipeline - INFO -   - TransferIDs únicos: 104,116
2025-06-18 14:22:52,308 - LogTransaccionesPipeline - INFO -   - Duplicados eliminados: 0
2025-06-18 14:22:52,308 - LogTransaccionesPipeline - INFO -   - Archivo: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-18 14:22:52,308 - LogTransaccionesPipeline - INFO - 🎉 HOMOLOGACIÓN PERFECTA: Coincide exactamente con Oracle PRE_LOG_TRX
2025-06-18 14:22:52,309 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: COMPLETADO
2025-06-18 14:22:52,311 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: INICIANDO
2025-06-18 14:22:52,311 - LogTransaccionesPipeline - INFO - Ejecutando SP_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-18 14:22:52,312 - LogTransaccionesPipeline - INFO - Archivo previo eliminado: TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-18 14:22:52,711 - LogTransaccionesPipeline - INFO - SP_LOG_TRX completado: 104116 registros -> TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-18 14:22:52,713 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: COMPLETADO
2025-06-18 14:22:52,714 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: INICIANDO
2025-06-18 14:22:52,714 - LogTransaccionesPipeline - INFO - Extrayendo CSV final para fecha: 2025-06-15
2025-06-18 14:22:52,880 - LogTransaccionesPipeline - INFO - CSV final generado: 104116 registros -> output/TR-********.csv
2025-06-18 14:22:52,882 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: COMPLETADO
2025-06-18 14:22:52,882 - LogTransaccionesPipeline - INFO - ✅ Pipeline LOG_TRANSACCIONES completado exitosamente en 103.06 segundos
2025-06-18 14:22:52,884 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: EXITOSO
2025-06-18 19:05:07,827 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-18 19:05:07,846 - botocore.credentials - INFO - Found credentials from IAM Role: ec-landing-process-01-EC2-Role
2025-06-18 19:05:07,885 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-18 19:05:07,885 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-15
2025-06-18 19:05:07,888 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-18 19:05:07,888 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX usando 100% tablas S3 para fecha: 2025-06-15
2025-06-18 19:05:07,888 - LogTransaccionesPipeline - INFO - Archivo previo eliminado: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-18 19:06:48,561 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX completado con deduplicación automática:
2025-06-18 19:06:48,561 - LogTransaccionesPipeline - INFO -   - Registros finales: 104,116
2025-06-18 19:06:48,561 - LogTransaccionesPipeline - INFO -   - TransferIDs únicos: 104,116
2025-06-18 19:06:48,561 - LogTransaccionesPipeline - INFO -   - Duplicados eliminados: 0
2025-06-18 19:06:48,561 - LogTransaccionesPipeline - INFO -   - Archivo: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-18 19:06:48,561 - LogTransaccionesPipeline - INFO - 🎉 HOMOLOGACIÓN PERFECTA: Coincide exactamente con Oracle PRE_LOG_TRX
2025-06-18 19:06:48,563 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: COMPLETADO
2025-06-18 19:06:48,564 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: INICIANDO
2025-06-18 19:06:48,564 - LogTransaccionesPipeline - INFO - Ejecutando SP_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-18 19:06:48,565 - LogTransaccionesPipeline - INFO - Archivo previo eliminado: TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-18 19:06:48,969 - LogTransaccionesPipeline - INFO - SP_LOG_TRX completado: 104116 registros -> TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-18 19:06:48,971 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: COMPLETADO
2025-06-18 19:06:48,972 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: INICIANDO
2025-06-18 19:06:48,972 - LogTransaccionesPipeline - INFO - Extrayendo CSV final para fecha: 2025-06-15
2025-06-18 19:06:49,112 - LogTransaccionesPipeline - INFO - CSV final generado: 104116 registros -> output/TR-********.csv
2025-06-18 19:06:49,114 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: COMPLETADO
2025-06-18 19:06:49,114 - LogTransaccionesPipeline - INFO - ✅ Pipeline LOG_TRANSACCIONES completado exitosamente en 101.23 segundos
2025-06-18 19:06:49,116 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: EXITOSO
2025-06-18 19:10:25,484 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-18 19:10:25,502 - botocore.credentials - INFO - Found credentials from IAM Role: ec-landing-process-01-EC2-Role
2025-06-18 19:10:25,538 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-18 19:10:25,538 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-15
2025-06-18 19:10:25,540 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-18 19:10:25,540 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX usando 100% tablas S3 para fecha: 2025-06-15
2025-06-18 19:10:25,541 - LogTransaccionesPipeline - INFO - Archivo previo eliminado: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-18 19:12:04,193 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX completado con deduplicación automática:
2025-06-18 19:12:04,193 - LogTransaccionesPipeline - INFO -   - Registros finales: 104,116
2025-06-18 19:12:04,193 - LogTransaccionesPipeline - INFO -   - TransferIDs únicos: 104,116
2025-06-18 19:12:04,193 - LogTransaccionesPipeline - INFO -   - Duplicados eliminados: 0
2025-06-18 19:12:04,193 - LogTransaccionesPipeline - INFO -   - Archivo: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-18 19:12:04,193 - LogTransaccionesPipeline - INFO - 🎉 HOMOLOGACIÓN PERFECTA: Coincide exactamente con Oracle PRE_LOG_TRX
2025-06-18 19:12:04,195 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: COMPLETADO
2025-06-18 19:12:04,197 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: INICIANDO
2025-06-18 19:12:04,197 - LogTransaccionesPipeline - INFO - Ejecutando SP_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-18 19:12:04,197 - LogTransaccionesPipeline - INFO - Archivo previo eliminado: TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-18 19:12:04,544 - LogTransaccionesPipeline - INFO - SP_LOG_TRX completado: 104116 registros -> TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-18 19:12:04,546 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: COMPLETADO
2025-06-18 19:12:04,547 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: INICIANDO
2025-06-18 19:12:04,547 - LogTransaccionesPipeline - INFO - Extrayendo CSV final para fecha: 2025-06-15
2025-06-18 19:12:04,693 - LogTransaccionesPipeline - INFO - CSV final generado: 104116 registros -> output/TR-********.csv
2025-06-18 19:12:04,695 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: COMPLETADO
2025-06-18 19:12:04,695 - LogTransaccionesPipeline - INFO - ✅ Pipeline LOG_TRANSACCIONES completado exitosamente en 99.16 segundos
2025-06-18 19:12:04,696 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: EXITOSO
2025-06-18 19:17:02,183 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-18 19:17:02,202 - botocore.credentials - INFO - Found credentials from IAM Role: ec-landing-process-01-EC2-Role
2025-06-18 19:17:02,241 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-18 19:17:02,241 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-15
2025-06-18 19:17:02,244 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-18 19:17:02,244 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX usando 100% tablas S3 para fecha: 2025-06-15
2025-06-18 19:17:02,245 - LogTransaccionesPipeline - INFO - Archivo previo eliminado: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-18 19:18:42,174 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX completado con deduplicación automática:
2025-06-18 19:18:42,174 - LogTransaccionesPipeline - INFO -   - Registros finales: 104,116
2025-06-18 19:18:42,175 - LogTransaccionesPipeline - INFO -   - TransferIDs únicos: 104,116
2025-06-18 19:18:42,175 - LogTransaccionesPipeline - INFO -   - Duplicados eliminados: 0
2025-06-18 19:18:42,175 - LogTransaccionesPipeline - INFO -   - Archivo: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-18 19:18:42,175 - LogTransaccionesPipeline - INFO - 🎉 HOMOLOGACIÓN PERFECTA: Coincide exactamente con Oracle PRE_LOG_TRX
2025-06-18 19:18:42,176 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: COMPLETADO
2025-06-18 19:18:42,178 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: INICIANDO
2025-06-18 19:18:42,178 - LogTransaccionesPipeline - INFO - Ejecutando SP_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-18 19:18:42,178 - LogTransaccionesPipeline - INFO - Archivo previo eliminado: TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-18 19:18:42,560 - LogTransaccionesPipeline - INFO - SP_LOG_TRX completado: 104116 registros -> TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-18 19:18:42,561 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: COMPLETADO
2025-06-18 19:18:42,563 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: INICIANDO
2025-06-18 19:18:42,563 - LogTransaccionesPipeline - INFO - Extrayendo CSV final para fecha: 2025-06-15
2025-06-18 19:18:42,689 - LogTransaccionesPipeline - INFO - CSV final generado: 104116 registros -> output/TR-********.csv
2025-06-18 19:18:42,691 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: COMPLETADO
2025-06-18 19:18:42,691 - LogTransaccionesPipeline - INFO - ✅ Pipeline LOG_TRANSACCIONES completado exitosamente en 100.45 segundos
2025-06-18 19:18:42,692 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: EXITOSO
2025-06-18 19:22:28,824 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-18 19:22:28,843 - botocore.credentials - INFO - Found credentials from IAM Role: ec-landing-process-01-EC2-Role
2025-06-18 19:22:28,881 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-18 19:22:28,881 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-15
2025-06-18 19:22:28,882 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-18 19:22:28,883 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX usando 100% tablas S3 para fecha: 2025-06-15
2025-06-18 19:22:28,883 - LogTransaccionesPipeline - INFO - Archivo previo eliminado: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-18 19:24:07,837 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX completado con deduplicación automática:
2025-06-18 19:24:07,837 - LogTransaccionesPipeline - INFO -   - Registros finales: 104,116
2025-06-18 19:24:07,837 - LogTransaccionesPipeline - INFO -   - TransferIDs únicos: 104,116
2025-06-18 19:24:07,837 - LogTransaccionesPipeline - INFO -   - Duplicados eliminados: 0
2025-06-18 19:24:07,837 - LogTransaccionesPipeline - INFO -   - Archivo: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-18 19:24:07,837 - LogTransaccionesPipeline - INFO - 🎉 HOMOLOGACIÓN PERFECTA: Coincide exactamente con Oracle PRE_LOG_TRX
2025-06-18 19:24:07,839 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: COMPLETADO
2025-06-18 19:24:07,840 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: INICIANDO
2025-06-18 19:24:07,840 - LogTransaccionesPipeline - INFO - Ejecutando SP_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-18 19:24:07,841 - LogTransaccionesPipeline - INFO - Archivo previo eliminado: TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-18 19:24:08,263 - LogTransaccionesPipeline - INFO - SP_LOG_TRX completado: 104116 registros -> TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-18 19:24:08,265 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: COMPLETADO
2025-06-18 19:24:08,266 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: INICIANDO
2025-06-18 19:24:08,266 - LogTransaccionesPipeline - INFO - Extrayendo CSV final para fecha: 2025-06-15
2025-06-18 19:24:08,389 - LogTransaccionesPipeline - INFO - CSV final generado: 104116 registros -> output/TR-********.csv
2025-06-18 19:24:08,391 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: COMPLETADO
2025-06-18 19:24:08,391 - LogTransaccionesPipeline - INFO - ✅ Pipeline LOG_TRANSACCIONES completado exitosamente en 99.51 segundos
2025-06-18 19:24:08,392 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: EXITOSO
2025-06-18 19:39:43,100 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-18 19:39:43,121 - botocore.credentials - INFO - Found credentials from IAM Role: ec-landing-process-01-EC2-Role
2025-06-18 19:39:43,163 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-18 19:39:43,164 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-15
2025-06-18 19:39:43,165 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-18 19:39:43,165 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX usando 100% tablas S3 para fecha: 2025-06-15
2025-06-18 19:39:43,166 - LogTransaccionesPipeline - INFO - Archivo previo eliminado: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-18 19:41:22,990 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX completado con deduplicación automática:
2025-06-18 19:41:22,991 - LogTransaccionesPipeline - INFO -   - Registros finales: 104,116
2025-06-18 19:41:22,991 - LogTransaccionesPipeline - INFO -   - TransferIDs únicos: 104,116
2025-06-18 19:41:22,991 - LogTransaccionesPipeline - INFO -   - Duplicados eliminados: 0
2025-06-18 19:41:22,991 - LogTransaccionesPipeline - INFO -   - Archivo: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-18 19:41:22,991 - LogTransaccionesPipeline - INFO - 🎉 HOMOLOGACIÓN PERFECTA: Coincide exactamente con Oracle PRE_LOG_TRX
2025-06-18 19:41:22,992 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: COMPLETADO
2025-06-18 19:41:22,994 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: INICIANDO
2025-06-18 19:41:22,994 - LogTransaccionesPipeline - INFO - Ejecutando SP_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-18 19:41:22,994 - LogTransaccionesPipeline - INFO - Archivo previo eliminado: TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-18 19:41:23,400 - LogTransaccionesPipeline - INFO - SP_LOG_TRX completado: 104116 registros -> TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-18 19:41:23,402 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: COMPLETADO
2025-06-18 19:41:23,403 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: INICIANDO
2025-06-18 19:41:23,403 - LogTransaccionesPipeline - INFO - Extrayendo CSV final para fecha: 2025-06-15
2025-06-18 19:41:23,542 - LogTransaccionesPipeline - INFO - CSV final generado: 104116 registros -> output/TR-********.csv
2025-06-18 19:41:23,544 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: COMPLETADO
2025-06-18 19:41:23,544 - LogTransaccionesPipeline - INFO - ✅ Pipeline LOG_TRANSACCIONES completado exitosamente en 100.38 segundos
2025-06-18 19:41:23,545 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: EXITOSO
2025-06-18 19:47:06,122 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-18 19:47:06,147 - botocore.credentials - INFO - Found credentials from IAM Role: ec-landing-process-01-EC2-Role
2025-06-18 19:47:06,186 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-18 19:47:06,186 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-15
2025-06-18 19:47:06,188 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-18 19:47:06,188 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX usando 100% tablas S3 para fecha: 2025-06-15
2025-06-18 19:47:06,188 - LogTransaccionesPipeline - INFO - Archivo previo eliminado: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-18 19:48:44,685 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX completado con deduplicación automática:
2025-06-18 19:48:44,685 - LogTransaccionesPipeline - INFO -   - Registros finales: 104,116
2025-06-18 19:48:44,685 - LogTransaccionesPipeline - INFO -   - TransferIDs únicos: 104,116
2025-06-18 19:48:44,685 - LogTransaccionesPipeline - INFO -   - Duplicados eliminados: 0
2025-06-18 19:48:44,685 - LogTransaccionesPipeline - INFO -   - Archivo: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-18 19:48:44,685 - LogTransaccionesPipeline - INFO - 🎉 HOMOLOGACIÓN PERFECTA: Coincide exactamente con Oracle PRE_LOG_TRX
2025-06-18 19:48:44,687 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: COMPLETADO
2025-06-18 19:48:44,688 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: INICIANDO
2025-06-18 19:48:44,688 - LogTransaccionesPipeline - INFO - Ejecutando SP_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-18 19:48:44,689 - LogTransaccionesPipeline - INFO - Archivo previo eliminado: TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-18 19:48:45,034 - LogTransaccionesPipeline - INFO - SP_LOG_TRX completado: 104116 registros -> TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-18 19:48:45,036 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: COMPLETADO
2025-06-18 19:48:45,037 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: INICIANDO
2025-06-18 19:48:45,037 - LogTransaccionesPipeline - INFO - Extrayendo CSV final para fecha: 2025-06-15
2025-06-18 19:48:45,159 - LogTransaccionesPipeline - INFO - CSV final generado: 104116 registros -> output/TR-********.csv
2025-06-18 19:48:45,161 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: COMPLETADO
2025-06-18 19:48:45,161 - LogTransaccionesPipeline - INFO - ✅ Pipeline LOG_TRANSACCIONES completado exitosamente en 98.97 segundos
2025-06-18 19:48:45,162 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: EXITOSO
2025-06-18 19:51:30,483 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-18 19:51:30,501 - botocore.credentials - INFO - Found credentials from IAM Role: ec-landing-process-01-EC2-Role
2025-06-18 19:51:30,539 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-18 19:51:30,539 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-15
2025-06-18 19:51:30,540 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-18 19:51:30,540 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX usando 100% tablas S3 para fecha: 2025-06-15
2025-06-18 19:51:30,541 - LogTransaccionesPipeline - INFO - Archivo previo eliminado: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-18 19:53:08,378 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX completado con deduplicación automática:
2025-06-18 19:53:08,378 - LogTransaccionesPipeline - INFO -   - Registros finales: 104,116
2025-06-18 19:53:08,378 - LogTransaccionesPipeline - INFO -   - TransferIDs únicos: 104,116
2025-06-18 19:53:08,378 - LogTransaccionesPipeline - INFO -   - Duplicados eliminados: 0
2025-06-18 19:53:08,378 - LogTransaccionesPipeline - INFO -   - Archivo: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-18 19:53:08,378 - LogTransaccionesPipeline - INFO - 🎉 HOMOLOGACIÓN PERFECTA: Coincide exactamente con Oracle PRE_LOG_TRX
2025-06-18 19:53:08,379 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: COMPLETADO
2025-06-18 19:53:08,381 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: INICIANDO
2025-06-18 19:53:08,381 - LogTransaccionesPipeline - INFO - Ejecutando SP_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-18 19:53:08,382 - LogTransaccionesPipeline - INFO - Archivo previo eliminado: TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-18 19:53:08,715 - LogTransaccionesPipeline - INFO - SP_LOG_TRX completado: 104116 registros -> TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-18 19:53:08,716 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: COMPLETADO
2025-06-18 19:53:08,718 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: INICIANDO
2025-06-18 19:53:08,718 - LogTransaccionesPipeline - INFO - Extrayendo CSV final para fecha: 2025-06-15
2025-06-18 19:53:08,884 - LogTransaccionesPipeline - INFO - CSV final generado: 104116 registros -> output/TR-********.csv
2025-06-18 19:53:08,886 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: COMPLETADO
2025-06-18 19:53:08,886 - LogTransaccionesPipeline - INFO - ✅ Pipeline LOG_TRANSACCIONES completado exitosamente en 98.35 segundos
2025-06-18 19:53:08,887 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: EXITOSO
2025-06-18 22:53:50,158 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-18 22:53:50,177 - botocore.credentials - INFO - Found credentials from IAM Role: ec-landing-process-01-EC2-Role
2025-06-18 22:53:50,216 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-18 22:53:50,217 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-15
2025-06-18 22:53:50,218 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-18 22:53:50,218 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX usando 100% tablas S3 para fecha: 2025-06-15
2025-06-18 22:53:50,219 - LogTransaccionesPipeline - INFO - Archivo previo eliminado: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-18 22:55:29,482 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX completado con deduplicación automática:
2025-06-18 22:55:29,483 - LogTransaccionesPipeline - INFO -   - Registros finales: 104,116
2025-06-18 22:55:29,483 - LogTransaccionesPipeline - INFO -   - TransferIDs únicos: 104,116
2025-06-18 22:55:29,483 - LogTransaccionesPipeline - INFO -   - Duplicados eliminados: 0
2025-06-18 22:55:29,483 - LogTransaccionesPipeline - INFO -   - Archivo: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-18 22:55:29,483 - LogTransaccionesPipeline - INFO - 🎉 HOMOLOGACIÓN PERFECTA: Coincide exactamente con Oracle PRE_LOG_TRX
2025-06-18 22:55:29,484 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: COMPLETADO
2025-06-18 22:55:29,486 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: INICIANDO
2025-06-18 22:55:29,486 - LogTransaccionesPipeline - INFO - Ejecutando SP_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-18 22:55:29,486 - LogTransaccionesPipeline - INFO - Archivo previo eliminado: TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-18 22:55:29,831 - LogTransaccionesPipeline - INFO - SP_LOG_TRX completado: 104116 registros -> TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-18 22:55:29,833 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: COMPLETADO
2025-06-18 22:55:29,834 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: INICIANDO
2025-06-18 22:55:29,834 - LogTransaccionesPipeline - INFO - Extrayendo CSV final para fecha: 2025-06-15
2025-06-18 22:55:29,970 - LogTransaccionesPipeline - INFO - CSV final generado: 104116 registros -> output/TR-********.csv
2025-06-18 22:55:29,972 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: COMPLETADO
2025-06-18 22:55:29,972 - LogTransaccionesPipeline - INFO - ✅ Pipeline LOG_TRANSACCIONES completado exitosamente en 99.76 segundos
2025-06-18 22:55:29,973 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: EXITOSO
2025-06-18 22:58:52,734 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-18 22:58:52,754 - botocore.credentials - INFO - Found credentials from IAM Role: ec-landing-process-01-EC2-Role
2025-06-18 22:58:52,792 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-18 22:59:25,414 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-18 22:59:25,433 - botocore.credentials - INFO - Found credentials from IAM Role: ec-landing-process-01-EC2-Role
2025-06-18 22:59:25,471 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-18 22:59:25,471 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-15
2025-06-18 22:59:25,472 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-18 22:59:25,472 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX usando 100% tablas S3 para fecha: 2025-06-15
2025-06-18 22:59:25,473 - LogTransaccionesPipeline - INFO - Archivo previo eliminado: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-18 23:01:38,564 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX completado con deduplicación automática:
2025-06-18 23:01:38,564 - LogTransaccionesPipeline - INFO -   - Registros finales: 104,116
2025-06-18 23:01:38,564 - LogTransaccionesPipeline - INFO -   - TransferIDs únicos: 104,116
2025-06-18 23:01:38,564 - LogTransaccionesPipeline - INFO -   - Duplicados eliminados: 0
2025-06-18 23:01:38,564 - LogTransaccionesPipeline - INFO -   - Archivo: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-18 23:01:38,564 - LogTransaccionesPipeline - INFO - 🎉 HOMOLOGACIÓN PERFECTA: Coincide exactamente con Oracle PRE_LOG_TRX
2025-06-18 23:01:38,566 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: COMPLETADO
2025-06-18 23:01:38,567 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: INICIANDO
2025-06-18 23:01:38,567 - LogTransaccionesPipeline - INFO - Ejecutando SP_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-18 23:01:38,568 - LogTransaccionesPipeline - INFO - Archivo previo eliminado: TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-18 23:01:38,951 - LogTransaccionesPipeline - INFO - SP_LOG_TRX completado: 104116 registros -> TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-18 23:01:38,952 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: COMPLETADO
2025-06-18 23:01:38,954 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: INICIANDO
2025-06-18 23:01:38,954 - LogTransaccionesPipeline - INFO - Extrayendo CSV final para fecha: 2025-06-15
2025-06-18 23:01:39,103 - LogTransaccionesPipeline - INFO - CSV final generado: 104116 registros -> output/TR-********.csv
2025-06-18 23:01:39,105 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: COMPLETADO
2025-06-18 23:01:39,105 - LogTransaccionesPipeline - INFO - ✅ Pipeline LOG_TRANSACCIONES completado exitosamente en 133.63 segundos
2025-06-18 23:01:39,106 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: EXITOSO
2025-06-18 23:17:45,914 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-18 23:17:45,932 - botocore.credentials - INFO - Found credentials from IAM Role: ec-landing-process-01-EC2-Role
2025-06-18 23:17:45,971 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-18 23:17:45,971 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-15
2025-06-18 23:17:45,972 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-18 23:17:45,972 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX usando 100% tablas S3 para fecha: 2025-06-15
2025-06-18 23:17:45,973 - LogTransaccionesPipeline - INFO - Archivo previo eliminado: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-18 23:19:26,545 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX completado con deduplicación automática:
2025-06-18 23:19:26,545 - LogTransaccionesPipeline - INFO -   - Registros finales: 104,116
2025-06-18 23:19:26,545 - LogTransaccionesPipeline - INFO -   - TransferIDs únicos: 104,116
2025-06-18 23:19:26,545 - LogTransaccionesPipeline - INFO -   - Duplicados eliminados: 0
2025-06-18 23:19:26,545 - LogTransaccionesPipeline - INFO -   - Archivo: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-18 23:19:26,545 - LogTransaccionesPipeline - INFO - 🎉 HOMOLOGACIÓN PERFECTA: Coincide exactamente con Oracle PRE_LOG_TRX
2025-06-18 23:19:26,546 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: COMPLETADO
2025-06-18 23:19:26,548 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: INICIANDO
2025-06-18 23:19:26,548 - LogTransaccionesPipeline - INFO - Ejecutando SP_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-18 23:19:26,548 - LogTransaccionesPipeline - INFO - Archivo previo eliminado: TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-18 23:19:26,878 - LogTransaccionesPipeline - INFO - SP_LOG_TRX completado: 104116 registros -> TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-18 23:19:26,879 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: COMPLETADO
2025-06-18 23:19:26,881 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: INICIANDO
2025-06-18 23:19:26,881 - LogTransaccionesPipeline - INFO - Extrayendo CSV final para fecha: 2025-06-15
2025-06-18 23:19:27,013 - LogTransaccionesPipeline - INFO - CSV final generado: 104116 registros -> output/TR-********.csv
2025-06-18 23:19:27,015 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: COMPLETADO
2025-06-18 23:19:27,015 - LogTransaccionesPipeline - INFO - ✅ Pipeline LOG_TRANSACCIONES completado exitosamente en 101.04 segundos
2025-06-18 23:19:27,016 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: EXITOSO
2025-06-18 23:58:09,186 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-18 23:58:09,206 - botocore.credentials - INFO - Found credentials from IAM Role: ec-landing-process-01-EC2-Role
2025-06-18 23:58:09,247 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-18 23:58:09,247 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-17
2025-06-18 23:58:09,248 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-18 23:58:09,248 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX usando 100% tablas S3 para fecha: 2025-06-17
2025-06-19 00:02:55,606 - LogTransaccionesPipeline - ERROR - Error completando SP_PRE_LOG_TRX: Query interrupted
2025-06-19 00:02:55,606 - LogTransaccionesPipeline - ERROR - Error en SP_PRE_LOG_TRX: Query interrupted
2025-06-19 00:02:55,606 - LogTransaccionesPipeline - ERROR - Error en SP_PRE_LOG_TRX: Query interrupted
2025-06-19 00:02:55,608 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: ERROR: Error en SP_PRE_LOG_TRX: Query interrupted
2025-06-19 00:02:55,608 - LogTransaccionesPipeline - ERROR - ❌ Pipeline LOG_TRANSACCIONES falló después de 286.36 segundos: Query interrupted
2025-06-19 00:02:55,609 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: ERROR: Query interrupted
2025-06-19 00:03:28,434 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-19 00:03:28,452 - botocore.credentials - INFO - Found credentials from IAM Role: ec-landing-process-01-EC2-Role
2025-06-19 00:03:28,491 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-19 00:03:28,491 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-15
2025-06-19 00:03:28,492 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-19 00:03:28,492 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX usando 100% tablas S3 para fecha: 2025-06-15
2025-06-19 00:03:28,493 - LogTransaccionesPipeline - INFO - Archivo previo eliminado: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-19 00:05:05,288 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX completado con deduplicación automática:
2025-06-19 00:05:05,288 - LogTransaccionesPipeline - INFO -   - Registros finales: 104,116
2025-06-19 00:05:05,288 - LogTransaccionesPipeline - INFO -   - TransferIDs únicos: 104,116
2025-06-19 00:05:05,288 - LogTransaccionesPipeline - INFO -   - Duplicados eliminados: 0
2025-06-19 00:05:05,288 - LogTransaccionesPipeline - INFO -   - Archivo: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-19 00:05:05,288 - LogTransaccionesPipeline - INFO - 🎉 HOMOLOGACIÓN PERFECTA: Coincide exactamente con Oracle PRE_LOG_TRX
2025-06-19 00:05:05,290 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: COMPLETADO
2025-06-19 00:05:05,291 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: INICIANDO
2025-06-19 00:05:05,291 - LogTransaccionesPipeline - INFO - Ejecutando SP_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-19 00:05:05,292 - LogTransaccionesPipeline - INFO - Archivo previo eliminado: TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-19 00:05:05,626 - LogTransaccionesPipeline - INFO - SP_LOG_TRX completado: 104116 registros -> TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-19 00:05:05,628 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: COMPLETADO
2025-06-19 00:05:05,629 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: INICIANDO
2025-06-19 00:05:05,629 - LogTransaccionesPipeline - INFO - Extrayendo CSV final para fecha: 2025-06-15
2025-06-19 00:05:05,786 - LogTransaccionesPipeline - INFO - CSV final generado: 104116 registros -> output/TR-********.csv
2025-06-19 00:05:05,788 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: COMPLETADO
2025-06-19 00:05:05,788 - LogTransaccionesPipeline - INFO - ✅ Pipeline LOG_TRANSACCIONES completado exitosamente en 97.30 segundos
2025-06-19 00:05:05,789 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: EXITOSO
2025-06-19 00:06:12,527 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-19 00:06:12,546 - botocore.credentials - INFO - Found credentials from IAM Role: ec-landing-process-01-EC2-Role
2025-06-19 00:06:12,583 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-19 00:06:12,583 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-17
2025-06-19 00:06:12,585 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-19 00:06:12,585 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX usando 100% tablas S3 para fecha: 2025-06-17
2025-06-19 00:06:12,585 - LogTransaccionesPipeline - INFO - Archivo previo eliminado: TEMP_LOGS_TRANSACCIONES/20250617/PRE_LOG_TRX.parquet
2025-06-19 00:11:24,921 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX completado con deduplicación automática:
2025-06-19 00:11:24,921 - LogTransaccionesPipeline - INFO -   - Registros finales: 217,544
2025-06-19 00:11:24,921 - LogTransaccionesPipeline - INFO -   - TransferIDs únicos: 217,544
2025-06-19 00:11:24,922 - LogTransaccionesPipeline - INFO -   - Duplicados eliminados: 0
2025-06-19 00:11:24,922 - LogTransaccionesPipeline - INFO -   - Archivo: TEMP_LOGS_TRANSACCIONES/20250617/PRE_LOG_TRX.parquet
2025-06-19 00:11:24,922 - LogTransaccionesPipeline - WARNING - ⚠️ Diferencia con Oracle: +113,428 registros
2025-06-19 00:11:24,923 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: COMPLETADO
2025-06-19 00:11:24,925 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: INICIANDO
2025-06-19 00:11:24,925 - LogTransaccionesPipeline - INFO - Ejecutando SP_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-17
2025-06-19 00:11:25,300 - LogTransaccionesPipeline - INFO - SP_LOG_TRX completado: 217544 registros -> TEMP_LOGS_TRANSACCIONES/20250617/LOG_TRX_FINAL.parquet
2025-06-19 00:11:25,301 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: COMPLETADO
2025-06-19 00:11:25,303 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: INICIANDO
2025-06-19 00:11:25,303 - LogTransaccionesPipeline - INFO - Extrayendo CSV final para fecha: 2025-06-17
2025-06-19 00:11:25,485 - LogTransaccionesPipeline - INFO - CSV final generado: 217544 registros -> output/TR-20250617.csv
2025-06-19 00:11:25,487 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: COMPLETADO
2025-06-19 00:11:25,487 - LogTransaccionesPipeline - INFO - ✅ Pipeline LOG_TRANSACCIONES completado exitosamente en 312.90 segundos
2025-06-19 00:11:25,489 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: EXITOSO
2025-06-19 00:31:18,157 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-19 00:31:18,175 - botocore.credentials - INFO - Found credentials from IAM Role: ec-landing-process-01-EC2-Role
2025-06-19 00:31:18,533 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-19 00:31:18,533 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-15
2025-06-19 00:31:18,536 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-19 00:31:18,536 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX usando 100% tablas S3 para fecha: 2025-06-15
2025-06-19 00:31:18,537 - LogTransaccionesPipeline - INFO - Archivo previo eliminado: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-19 00:31:18,537 - LogTransaccionesPipeline - INFO - 🚀 OPTIMIZACIÓN NINJA: Rutas dinámicas generadas para fecha 2025-06-15
2025-06-19 00:31:18,537 - LogTransaccionesPipeline - INFO -    📁 MTX_TRANSACTION_HEADER: s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_HEADER_ORA/year=2025/month=06/day=15/*.parquet
2025-06-19 00:31:18,537 - LogTransaccionesPipeline - INFO -    📁 MTX_TRANSACTION_ITEMS: s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_ITEMS_ORA/year=2025/month=06/day=15/*.parquet
2025-06-19 00:31:19,412 - LogTransaccionesPipeline - ERROR - Error completando SP_PRE_LOG_TRX: IO Error: No files found that match the pattern "s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_HEADER_ORA/year=2025/month=06/day=15/*.parquet"
2025-06-19 00:31:19,413 - LogTransaccionesPipeline - ERROR - Error en SP_PRE_LOG_TRX: IO Error: No files found that match the pattern "s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_HEADER_ORA/year=2025/month=06/day=15/*.parquet"
2025-06-19 00:31:19,413 - LogTransaccionesPipeline - ERROR - Error en SP_PRE_LOG_TRX: IO Error: No files found that match the pattern "s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_HEADER_ORA/year=2025/month=06/day=15/*.parquet"
2025-06-19 00:31:19,414 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: ERROR: Error en SP_PRE_LOG_TRX: IO Error: No files found that match the pattern "s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_HEADER_ORA/year=2025/month=06/day=15/*.parquet"
2025-06-19 00:31:19,414 - LogTransaccionesPipeline - ERROR - ❌ Pipeline LOG_TRANSACCIONES falló después de 0.88 segundos: IO Error: No files found that match the pattern "s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_HEADER_ORA/year=2025/month=06/day=15/*.parquet"
2025-06-19 00:31:19,416 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: ERROR: IO Error: No files found that match the pattern "s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_HEADER_ORA/year=2025/month=06/day=15/*.parquet"
2025-06-19 00:32:37,641 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-19 00:32:37,660 - botocore.credentials - INFO - Found credentials from IAM Role: ec-landing-process-01-EC2-Role
2025-06-19 00:32:37,699 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-19 00:32:37,699 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-15
2025-06-19 00:32:37,700 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-19 00:32:37,700 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX usando 100% tablas S3 para fecha: 2025-06-15
2025-06-19 00:32:37,700 - LogTransaccionesPipeline - INFO - 🚀 OPTIMIZACIÓN NINJA: Rutas dinámicas generadas para fecha 2025-06-15
2025-06-19 00:32:37,700 - LogTransaccionesPipeline - INFO -    📁 MTX_TRANSACTION_HEADER: s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_HEADER_ORA/2025/06/15/*.parquet
2025-06-19 00:32:37,700 - LogTransaccionesPipeline - INFO -    📁 MTX_TRANSACTION_ITEMS: s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_ITEMS_ORA/2025/06/15/*.parquet
2025-06-19 00:33:54,416 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX completado con deduplicación automática:
2025-06-19 00:33:54,417 - LogTransaccionesPipeline - INFO -   - Registros finales: 104,116
2025-06-19 00:33:54,417 - LogTransaccionesPipeline - INFO -   - TransferIDs únicos: 104,116
2025-06-19 00:33:54,417 - LogTransaccionesPipeline - INFO -   - Duplicados eliminados: 0
2025-06-19 00:33:54,417 - LogTransaccionesPipeline - INFO -   - Archivo: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-19 00:33:54,417 - LogTransaccionesPipeline - INFO - 🎉 HOMOLOGACIÓN PERFECTA: Coincide exactamente con Oracle PRE_LOG_TRX
2025-06-19 00:33:54,418 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: COMPLETADO
2025-06-19 00:33:54,420 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: INICIANDO
2025-06-19 00:33:54,420 - LogTransaccionesPipeline - INFO - Ejecutando SP_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-19 00:33:54,420 - LogTransaccionesPipeline - INFO - Archivo previo eliminado: TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-19 00:33:54,421 - LogTransaccionesPipeline - INFO - 🚀 OPTIMIZACIÓN NINJA: Rutas dinámicas generadas para fecha 2025-06-15
2025-06-19 00:33:54,421 - LogTransaccionesPipeline - INFO -    📁 MTX_TRANSACTION_HEADER: s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_HEADER_ORA/2025/06/15/*.parquet
2025-06-19 00:33:54,421 - LogTransaccionesPipeline - INFO -    📁 MTX_TRANSACTION_ITEMS: s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_ITEMS_ORA/2025/06/15/*.parquet
2025-06-19 00:33:54,763 - LogTransaccionesPipeline - INFO - SP_LOG_TRX completado: 104116 registros -> TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-19 00:33:54,765 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: COMPLETADO
2025-06-19 00:33:54,766 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: INICIANDO
2025-06-19 00:33:54,766 - LogTransaccionesPipeline - INFO - Extrayendo CSV final para fecha: 2025-06-15
2025-06-19 00:33:54,894 - LogTransaccionesPipeline - INFO - CSV final generado: 104116 registros -> output/TR-********.csv
2025-06-19 00:33:54,896 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: COMPLETADO
2025-06-19 00:33:54,896 - LogTransaccionesPipeline - INFO - ✅ Pipeline LOG_TRANSACCIONES completado exitosamente en 77.20 segundos
2025-06-19 00:33:54,897 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: EXITOSO
2025-06-19 00:34:06,761 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-19 00:34:06,780 - botocore.credentials - INFO - Found credentials from IAM Role: ec-landing-process-01-EC2-Role
2025-06-19 00:34:06,822 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-19 00:34:06,822 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-01-10
2025-06-19 00:34:06,824 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-19 00:34:06,824 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX usando 100% tablas S3 para fecha: 2025-01-10
2025-06-19 00:34:06,824 - LogTransaccionesPipeline - INFO - 🚀 OPTIMIZACIÓN NINJA: Rutas dinámicas generadas para fecha 2025-01-10
2025-06-19 00:34:06,824 - LogTransaccionesPipeline - INFO -    📁 MTX_TRANSACTION_HEADER: s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_HEADER_ORA/2025/01/10/*.parquet
2025-06-19 00:34:06,824 - LogTransaccionesPipeline - INFO -    📁 MTX_TRANSACTION_ITEMS: s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_ITEMS_ORA/2025/01/10/*.parquet
2025-06-19 00:34:08,053 - LogTransaccionesPipeline - ERROR - Error completando SP_PRE_LOG_TRX: IO Error: No files found that match the pattern "s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_ITEMS_ORA/2025/01/10/*.parquet"
2025-06-19 00:34:08,053 - LogTransaccionesPipeline - ERROR - Error en SP_PRE_LOG_TRX: IO Error: No files found that match the pattern "s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_ITEMS_ORA/2025/01/10/*.parquet"
2025-06-19 00:34:08,053 - LogTransaccionesPipeline - ERROR - Error en SP_PRE_LOG_TRX: IO Error: No files found that match the pattern "s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_ITEMS_ORA/2025/01/10/*.parquet"
2025-06-19 00:34:08,054 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: ERROR: Error en SP_PRE_LOG_TRX: IO Error: No files found that match the pattern "s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_ITEMS_ORA/2025/01/10/*.parquet"
2025-06-19 00:34:08,055 - LogTransaccionesPipeline - ERROR - ❌ Pipeline LOG_TRANSACCIONES falló después de 1.23 segundos: IO Error: No files found that match the pattern "s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_ITEMS_ORA/2025/01/10/*.parquet"
2025-06-19 00:34:08,056 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: ERROR: IO Error: No files found that match the pattern "s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_ITEMS_ORA/2025/01/10/*.parquet"
2025-06-19 00:34:20,431 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-19 00:34:20,451 - botocore.credentials - INFO - Found credentials from IAM Role: ec-landing-process-01-EC2-Role
2025-06-19 00:34:20,494 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-19 00:34:20,495 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-14
2025-06-19 00:34:20,496 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-19 00:34:20,496 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX usando 100% tablas S3 para fecha: 2025-06-14
2025-06-19 00:34:20,496 - LogTransaccionesPipeline - INFO - 🚀 OPTIMIZACIÓN NINJA: Rutas dinámicas generadas para fecha 2025-06-14
2025-06-19 00:34:20,496 - LogTransaccionesPipeline - INFO -    📁 MTX_TRANSACTION_HEADER: s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_HEADER_ORA/2025/06/14/*.parquet
2025-06-19 00:34:20,496 - LogTransaccionesPipeline - INFO -    📁 MTX_TRANSACTION_ITEMS: s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_ITEMS_ORA/2025/06/14/*.parquet
2025-06-19 00:36:47,820 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX completado con deduplicación automática:
2025-06-19 00:36:47,820 - LogTransaccionesPipeline - INFO -   - Registros finales: 148,148
2025-06-19 00:36:47,821 - LogTransaccionesPipeline - INFO -   - TransferIDs únicos: 148,148
2025-06-19 00:36:47,821 - LogTransaccionesPipeline - INFO -   - Duplicados eliminados: 0
2025-06-19 00:36:47,821 - LogTransaccionesPipeline - INFO -   - Archivo: TEMP_LOGS_TRANSACCIONES/20250614/PRE_LOG_TRX.parquet
2025-06-19 00:36:47,821 - LogTransaccionesPipeline - WARNING - ⚠️ Diferencia con Oracle: +44,032 registros
2025-06-19 00:36:47,822 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: COMPLETADO
2025-06-19 00:36:47,824 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: INICIANDO
2025-06-19 00:36:47,824 - LogTransaccionesPipeline - INFO - Ejecutando SP_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-14
2025-06-19 00:36:47,824 - LogTransaccionesPipeline - INFO - 🚀 OPTIMIZACIÓN NINJA: Rutas dinámicas generadas para fecha 2025-06-14
2025-06-19 00:36:47,824 - LogTransaccionesPipeline - INFO -    📁 MTX_TRANSACTION_HEADER: s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_HEADER_ORA/2025/06/14/*.parquet
2025-06-19 00:36:47,824 - LogTransaccionesPipeline - INFO -    📁 MTX_TRANSACTION_ITEMS: s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_ITEMS_ORA/2025/06/14/*.parquet
2025-06-19 00:36:48,165 - LogTransaccionesPipeline - INFO - SP_LOG_TRX completado: 148148 registros -> TEMP_LOGS_TRANSACCIONES/20250614/LOG_TRX_FINAL.parquet
2025-06-19 00:36:48,167 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: COMPLETADO
2025-06-19 00:36:48,168 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: INICIANDO
2025-06-19 00:36:48,168 - LogTransaccionesPipeline - INFO - Extrayendo CSV final para fecha: 2025-06-14
2025-06-19 00:36:48,318 - LogTransaccionesPipeline - INFO - CSV final generado: 148148 registros -> output/TR-20250614.csv
2025-06-19 00:36:48,319 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: COMPLETADO
2025-06-19 00:36:48,319 - LogTransaccionesPipeline - INFO - ✅ Pipeline LOG_TRANSACCIONES completado exitosamente en 147.82 segundos
2025-06-19 00:36:48,321 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: EXITOSO
2025-06-19 00:43:41,145 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-19 00:43:41,164 - botocore.credentials - INFO - Found credentials from IAM Role: ec-landing-process-01-EC2-Role
2025-06-19 00:43:41,205 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-19 00:43:41,205 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-15
2025-06-19 00:43:41,206 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-19 00:43:41,206 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX usando 100% tablas S3 para fecha: 2025-06-15
2025-06-19 00:43:41,207 - LogTransaccionesPipeline - INFO - Archivo previo eliminado: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-19 00:43:41,207 - LogTransaccionesPipeline - INFO - 🚀 OPTIMIZACIÓN NINJA: Rutas dinámicas generadas para fecha 2025-06-15
2025-06-19 00:43:41,207 - LogTransaccionesPipeline - INFO -    📁 MTX_TRANSACTION_HEADER: s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_HEADER_ORA/2025/06/15/*.parquet
2025-06-19 00:43:41,207 - LogTransaccionesPipeline - INFO -    📁 MTX_TRANSACTION_ITEMS: s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_ITEMS_ORA/2025/06/15/*.parquet
2025-06-19 00:44:57,538 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX completado con deduplicación automática:
2025-06-19 00:44:57,538 - LogTransaccionesPipeline - INFO -   - Registros finales: 104,116
2025-06-19 00:44:57,538 - LogTransaccionesPipeline - INFO -   - TransferIDs únicos: 104,116
2025-06-19 00:44:57,538 - LogTransaccionesPipeline - INFO -   - Duplicados eliminados: 0
2025-06-19 00:44:57,538 - LogTransaccionesPipeline - INFO -   - Archivo: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-19 00:44:57,538 - LogTransaccionesPipeline - INFO - ✅ SP_PRE_LOG_TRX procesado exitosamente: 104,116 registros
2025-06-19 00:44:57,540 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: COMPLETADO
2025-06-19 00:44:57,541 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: INICIANDO
2025-06-19 00:44:57,541 - LogTransaccionesPipeline - INFO - Ejecutando SP_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-19 00:44:57,542 - LogTransaccionesPipeline - INFO - Archivo previo eliminado: TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-19 00:44:57,542 - LogTransaccionesPipeline - INFO - 🚀 OPTIMIZACIÓN NINJA: Rutas dinámicas generadas para fecha 2025-06-15
2025-06-19 00:44:57,542 - LogTransaccionesPipeline - INFO -    📁 MTX_TRANSACTION_HEADER: s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_HEADER_ORA/2025/06/15/*.parquet
2025-06-19 00:44:57,542 - LogTransaccionesPipeline - INFO -    📁 MTX_TRANSACTION_ITEMS: s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_ITEMS_ORA/2025/06/15/*.parquet
2025-06-19 00:44:57,886 - LogTransaccionesPipeline - INFO - SP_LOG_TRX completado: 104116 registros -> TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-19 00:44:57,887 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: COMPLETADO
2025-06-19 00:44:57,889 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: INICIANDO
2025-06-19 00:44:57,889 - LogTransaccionesPipeline - INFO - Extrayendo CSV final para fecha: 2025-06-15
2025-06-19 00:44:58,026 - LogTransaccionesPipeline - INFO - CSV final generado: 104116 registros -> output/TR-********.csv
2025-06-19 00:44:58,027 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: COMPLETADO
2025-06-19 00:44:58,028 - LogTransaccionesPipeline - INFO - ✅ Pipeline LOG_TRANSACCIONES completado exitosamente en 76.82 segundos
2025-06-19 00:44:58,029 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: EXITOSO
2025-06-19 15:13:26,837 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-19 15:13:26,874 - botocore.credentials - INFO - Found credentials from IAM Role: ec-landing-process-01-EC2-Role
2025-06-19 15:13:27,234 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-19 15:13:27,234 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-16
2025-06-19 15:13:27,237 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-19 15:13:27,237 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX usando 100% tablas S3 para fecha: 2025-06-16
2025-06-19 15:13:27,237 - LogTransaccionesPipeline - INFO - 🚀 OPTIMIZACIÓN NINJA: Rutas dinámicas generadas para fecha 2025-06-16
2025-06-19 15:13:27,237 - LogTransaccionesPipeline - INFO -    📁 MTX_TRANSACTION_HEADER: s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_HEADER_ORA/2025/06/16/*.parquet
2025-06-19 15:13:27,237 - LogTransaccionesPipeline - INFO -    📁 MTX_TRANSACTION_ITEMS: s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_ITEMS_ORA/2025/06/16/*.parquet
2025-06-19 15:18:29,569 - LogTransaccionesPipeline - ERROR - Error completando SP_PRE_LOG_TRX: Query interrupted
2025-06-19 15:18:29,569 - LogTransaccionesPipeline - ERROR - Error en SP_PRE_LOG_TRX: Query interrupted
2025-06-19 15:18:29,569 - LogTransaccionesPipeline - ERROR - Error en SP_PRE_LOG_TRX: Query interrupted
2025-06-19 15:18:29,570 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: ERROR: Error en SP_PRE_LOG_TRX: Query interrupted
2025-06-19 15:18:29,571 - LogTransaccionesPipeline - ERROR - ❌ Pipeline LOG_TRANSACCIONES falló después de 302.34 segundos: Query interrupted
2025-06-19 15:18:29,572 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: ERROR: Query interrupted
2025-06-19 15:23:07,501 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-19 15:23:07,523 - botocore.credentials - INFO - Found credentials from IAM Role: ec-landing-process-01-EC2-Role
2025-06-19 15:23:07,562 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-19 15:23:07,563 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-15
2025-06-19 15:23:07,564 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-19 15:23:07,564 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX usando 100% tablas S3 para fecha: 2025-06-15
2025-06-19 15:23:07,565 - LogTransaccionesPipeline - INFO - Archivo previo eliminado: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-19 15:23:07,565 - LogTransaccionesPipeline - INFO - 🚀 OPTIMIZACIÓN NINJA: Rutas dinámicas generadas para fecha 2025-06-15
2025-06-19 15:23:07,565 - LogTransaccionesPipeline - INFO -    📁 MTX_TRANSACTION_HEADER: s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_HEADER_ORA/2025/06/15/*.parquet
2025-06-19 15:23:07,565 - LogTransaccionesPipeline - INFO -    📁 MTX_TRANSACTION_ITEMS: s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_ITEMS_ORA/2025/06/15/*.parquet
2025-06-19 15:23:10,884 - LogTransaccionesPipeline - ERROR - Error completando SP_PRE_LOG_TRX: Query interrupted
2025-06-19 15:23:10,884 - LogTransaccionesPipeline - ERROR - Error en SP_PRE_LOG_TRX: Query interrupted
2025-06-19 15:23:10,884 - LogTransaccionesPipeline - ERROR - Error en SP_PRE_LOG_TRX: Query interrupted
2025-06-19 15:23:10,886 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: ERROR: Error en SP_PRE_LOG_TRX: Query interrupted
2025-06-19 15:23:10,886 - LogTransaccionesPipeline - ERROR - ❌ Pipeline LOG_TRANSACCIONES falló después de 3.32 segundos: Query interrupted
2025-06-19 15:23:10,888 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: ERROR: Query interrupted
2025-06-19 15:26:06,533 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-19 15:26:06,553 - botocore.credentials - INFO - Found credentials from IAM Role: ec-landing-process-01-EC2-Role
2025-06-19 15:26:06,595 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-19 15:26:06,595 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-15
2025-06-19 15:26:06,596 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-19 15:26:06,596 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX usando 100% tablas S3 para fecha: 2025-06-15
2025-06-19 15:26:06,597 - LogTransaccionesPipeline - INFO - Archivo previo eliminado: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-19 15:26:06,597 - LogTransaccionesPipeline - INFO - 🚀 OPTIMIZACIÓN NINJA: Rutas dinámicas generadas para fecha 2025-06-15
2025-06-19 15:26:06,597 - LogTransaccionesPipeline - INFO -    📁 MTX_TRANSACTION_HEADER: s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_HEADER_ORA/2025/06/15/*.parquet
2025-06-19 15:26:06,597 - LogTransaccionesPipeline - INFO -    📁 MTX_TRANSACTION_ITEMS: s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_ITEMS_ORA/2025/06/15/*.parquet
2025-06-19 15:27:24,217 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX completado con deduplicación automática:
2025-06-19 15:27:24,217 - LogTransaccionesPipeline - INFO -   - Registros finales: 104,116
2025-06-19 15:27:24,217 - LogTransaccionesPipeline - INFO -   - TransferIDs únicos: 104,116
2025-06-19 15:27:24,217 - LogTransaccionesPipeline - INFO -   - Duplicados eliminados: 0
2025-06-19 15:27:24,217 - LogTransaccionesPipeline - INFO -   - Archivo: TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-19 15:27:24,217 - LogTransaccionesPipeline - INFO - ✅ SP_PRE_LOG_TRX procesado exitosamente: 104,116 registros
2025-06-19 15:27:24,219 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: COMPLETADO
2025-06-19 15:27:24,220 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: INICIANDO
2025-06-19 15:27:24,220 - LogTransaccionesPipeline - INFO - Ejecutando SP_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-15
2025-06-19 15:27:24,221 - LogTransaccionesPipeline - INFO - Archivo previo eliminado: TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-19 15:27:24,221 - LogTransaccionesPipeline - INFO - 🚀 OPTIMIZACIÓN NINJA: Rutas dinámicas generadas para fecha 2025-06-15
2025-06-19 15:27:24,221 - LogTransaccionesPipeline - INFO -    📁 MTX_TRANSACTION_HEADER: s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_HEADER_ORA/2025/06/15/*.parquet
2025-06-19 15:27:24,221 - LogTransaccionesPipeline - INFO -    📁 MTX_TRANSACTION_ITEMS: s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_ITEMS_ORA/2025/06/15/*.parquet
2025-06-19 15:27:24,673 - LogTransaccionesPipeline - INFO - SP_LOG_TRX completado: 104116 registros -> TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-19 15:27:24,674 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: COMPLETADO
2025-06-19 15:27:24,676 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: INICIANDO
2025-06-19 15:27:24,676 - LogTransaccionesPipeline - INFO - Extrayendo CSV final para fecha: 2025-06-15
2025-06-19 15:27:24,839 - LogTransaccionesPipeline - INFO - CSV final generado: 104116 registros -> output/TR-********.csv
2025-06-19 15:27:24,840 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: COMPLETADO
2025-06-19 15:27:24,841 - LogTransaccionesPipeline - INFO - ✅ Pipeline LOG_TRANSACCIONES completado exitosamente en 78.25 segundos
2025-06-19 15:27:24,842 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: EXITOSO
2025-06-19 15:28:25,910 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-19 15:28:25,929 - botocore.credentials - INFO - Found credentials from IAM Role: ec-landing-process-01-EC2-Role
2025-06-19 15:28:25,972 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-19 15:28:25,973 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-16
2025-06-19 15:28:25,974 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-19 15:28:25,974 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX usando 100% tablas S3 para fecha: 2025-06-16
2025-06-19 15:28:25,974 - LogTransaccionesPipeline - INFO - Archivo previo eliminado: TEMP_LOGS_TRANSACCIONES/20250616/PRE_LOG_TRX.parquet
2025-06-19 15:28:25,975 - LogTransaccionesPipeline - INFO - 🚀 OPTIMIZACIÓN NINJA: Rutas dinámicas generadas para fecha 2025-06-16
2025-06-19 15:28:25,975 - LogTransaccionesPipeline - INFO -    📁 MTX_TRANSACTION_HEADER: s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_HEADER_ORA/2025/06/16/*.parquet
2025-06-19 15:28:25,975 - LogTransaccionesPipeline - INFO -    📁 MTX_TRANSACTION_ITEMS: s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_ITEMS_ORA/2025/06/16/*.parquet
2025-06-19 15:28:52,492 - LogTransaccionesPipeline - ERROR - Error completando SP_PRE_LOG_TRX: Query interrupted
2025-06-19 15:28:52,492 - LogTransaccionesPipeline - ERROR - Error en SP_PRE_LOG_TRX: Query interrupted
2025-06-19 15:28:52,492 - LogTransaccionesPipeline - ERROR - Error en SP_PRE_LOG_TRX: Query interrupted
2025-06-19 15:28:52,494 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: ERROR: Error en SP_PRE_LOG_TRX: Query interrupted
2025-06-19 15:28:52,494 - LogTransaccionesPipeline - ERROR - ❌ Pipeline LOG_TRANSACCIONES falló después de 26.52 segundos: Query interrupted
2025-06-19 15:28:52,495 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: ERROR: Query interrupted
2025-06-19 20:16:03,700 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-19 20:16:03,723 - botocore.credentials - INFO - Found credentials from IAM Role: ec-landing-process-01-EC2-Role
2025-06-19 20:16:04,084 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-19 20:16:04,084 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-16
2025-06-19 20:16:04,087 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-19 20:16:04,087 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX usando 100% tablas S3 para fecha: 2025-06-16
2025-06-19 20:16:04,087 - LogTransaccionesPipeline - INFO - Archivo previo eliminado: TEMP_LOGS_TRANSACCIONES/20250616/PRE_LOG_TRX.parquet
2025-06-19 20:16:04,087 - LogTransaccionesPipeline - INFO - 🚀 OPTIMIZACIÓN NINJA: Rutas dinámicas generadas para fecha 2025-06-16
2025-06-19 20:16:04,087 - LogTransaccionesPipeline - INFO -    📁 MTX_TRANSACTION_HEADER: s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_HEADER_ORA/2025/06/16/*.parquet
2025-06-19 20:16:04,087 - LogTransaccionesPipeline - INFO -    📁 MTX_TRANSACTION_ITEMS: s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_ITEMS_ORA/2025/06/16/*.parquet
2025-06-19 20:20:56,531 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX completado con deduplicación automática:
2025-06-19 20:20:56,531 - LogTransaccionesPipeline - INFO -   - Registros finales: 212,503
2025-06-19 20:20:56,531 - LogTransaccionesPipeline - INFO -   - TransferIDs únicos: 212,503
2025-06-19 20:20:56,531 - LogTransaccionesPipeline - INFO -   - Duplicados eliminados: 0
2025-06-19 20:20:56,531 - LogTransaccionesPipeline - INFO -   - Archivo: TEMP_LOGS_TRANSACCIONES/20250616/PRE_LOG_TRX.parquet
2025-06-19 20:20:56,531 - LogTransaccionesPipeline - INFO - ✅ SP_PRE_LOG_TRX procesado exitosamente: 212,503 registros
2025-06-19 20:20:56,533 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: COMPLETADO
2025-06-19 20:20:56,534 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: INICIANDO
2025-06-19 20:20:56,534 - LogTransaccionesPipeline - INFO - Ejecutando SP_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-16
2025-06-19 20:20:56,534 - LogTransaccionesPipeline - INFO - 🚀 OPTIMIZACIÓN NINJA: Rutas dinámicas generadas para fecha 2025-06-16
2025-06-19 20:20:56,534 - LogTransaccionesPipeline - INFO -    📁 MTX_TRANSACTION_HEADER: s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_HEADER_ORA/2025/06/16/*.parquet
2025-06-19 20:20:56,534 - LogTransaccionesPipeline - INFO -    📁 MTX_TRANSACTION_ITEMS: s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_ITEMS_ORA/2025/06/16/*.parquet
2025-06-19 20:20:56,841 - LogTransaccionesPipeline - INFO - SP_LOG_TRX completado: 212503 registros -> TEMP_LOGS_TRANSACCIONES/20250616/LOG_TRX_FINAL.parquet
2025-06-19 20:20:56,842 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: COMPLETADO
2025-06-19 20:20:56,844 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: INICIANDO
2025-06-19 20:20:56,844 - LogTransaccionesPipeline - INFO - Extrayendo CSV final para fecha: 2025-06-16
2025-06-19 20:20:57,031 - LogTransaccionesPipeline - INFO - CSV final generado: 212499 registros -> output/TR-20250616.csv
2025-06-19 20:20:57,033 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: COMPLETADO
2025-06-19 20:20:57,033 - LogTransaccionesPipeline - INFO - ✅ Pipeline LOG_TRANSACCIONES completado exitosamente en 292.95 segundos
2025-06-19 20:20:57,035 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: EXITOSO
2025-06-19 20:34:32,972 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-19 20:34:32,991 - botocore.credentials - INFO - Found credentials from IAM Role: ec-landing-process-01-EC2-Role
2025-06-19 20:34:33,032 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-19 20:34:33,032 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-17
2025-06-19 20:34:33,034 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-19 20:34:33,034 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX usando 100% tablas S3 para fecha: 2025-06-17
2025-06-19 20:34:33,035 - LogTransaccionesPipeline - INFO - Archivo previo eliminado: TEMP_LOGS_TRANSACCIONES/20250617/PRE_LOG_TRX.parquet
2025-06-19 20:34:33,035 - LogTransaccionesPipeline - INFO - 🚀 OPTIMIZACIÓN NINJA: Rutas dinámicas generadas para fecha 2025-06-17
2025-06-19 20:34:33,035 - LogTransaccionesPipeline - INFO -    📁 MTX_TRANSACTION_HEADER: s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_HEADER_ORA/2025/06/17/*.parquet
2025-06-19 20:34:33,035 - LogTransaccionesPipeline - INFO -    📁 MTX_TRANSACTION_ITEMS: s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_ITEMS_ORA/2025/06/17/*.parquet
2025-06-19 20:34:37,606 - LogTransaccionesPipeline - ERROR - Error completando SP_PRE_LOG_TRX: Query interrupted
2025-06-19 20:34:37,607 - LogTransaccionesPipeline - ERROR - Error en SP_PRE_LOG_TRX: Query interrupted
2025-06-19 20:34:37,607 - LogTransaccionesPipeline - ERROR - Error en SP_PRE_LOG_TRX: Query interrupted
2025-06-19 20:34:37,608 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: ERROR: Error en SP_PRE_LOG_TRX: Query interrupted
2025-06-19 20:34:37,608 - LogTransaccionesPipeline - ERROR - ❌ Pipeline LOG_TRANSACCIONES falló después de 4.58 segundos: Query interrupted
2025-06-19 20:34:37,610 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: ERROR: Query interrupted
2025-06-19 20:34:44,347 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-19 20:34:44,367 - botocore.credentials - INFO - Found credentials from IAM Role: ec-landing-process-01-EC2-Role
2025-06-19 20:34:44,406 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-19 20:34:44,407 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-18
2025-06-19 20:34:44,408 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-19 20:34:44,408 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX usando 100% tablas S3 para fecha: 2025-06-18
2025-06-19 20:34:44,408 - LogTransaccionesPipeline - INFO - 🚀 OPTIMIZACIÓN NINJA: Rutas dinámicas generadas para fecha 2025-06-18
2025-06-19 20:34:44,408 - LogTransaccionesPipeline - INFO -    📁 MTX_TRANSACTION_HEADER: s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_HEADER_ORA/2025/06/18/*.parquet
2025-06-19 20:34:44,408 - LogTransaccionesPipeline - INFO -    📁 MTX_TRANSACTION_ITEMS: s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_ITEMS_ORA/2025/06/18/*.parquet
2025-06-19 20:42:41,281 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX completado con deduplicación automática:
2025-06-19 20:42:41,281 - LogTransaccionesPipeline - INFO -   - Registros finales: 221,354
2025-06-19 20:42:41,281 - LogTransaccionesPipeline - INFO -   - TransferIDs únicos: 221,354
2025-06-19 20:42:41,281 - LogTransaccionesPipeline - INFO -   - Duplicados eliminados: 0
2025-06-19 20:42:41,281 - LogTransaccionesPipeline - INFO -   - Archivo: TEMP_LOGS_TRANSACCIONES/20250618/PRE_LOG_TRX.parquet
2025-06-19 20:42:41,281 - LogTransaccionesPipeline - INFO - ✅ SP_PRE_LOG_TRX procesado exitosamente: 221,354 registros
2025-06-19 20:42:41,283 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: COMPLETADO
2025-06-19 20:42:41,284 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: INICIANDO
2025-06-19 20:42:41,284 - LogTransaccionesPipeline - INFO - Ejecutando SP_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-18
2025-06-19 20:42:41,284 - LogTransaccionesPipeline - INFO - 🚀 OPTIMIZACIÓN NINJA: Rutas dinámicas generadas para fecha 2025-06-18
2025-06-19 20:42:41,284 - LogTransaccionesPipeline - INFO -    📁 MTX_TRANSACTION_HEADER: s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_HEADER_ORA/2025/06/18/*.parquet
2025-06-19 20:42:41,284 - LogTransaccionesPipeline - INFO -    📁 MTX_TRANSACTION_ITEMS: s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_ITEMS_ORA/2025/06/18/*.parquet
2025-06-19 20:42:41,685 - LogTransaccionesPipeline - INFO - SP_LOG_TRX completado: 221354 registros -> TEMP_LOGS_TRANSACCIONES/20250618/LOG_TRX_FINAL.parquet
2025-06-19 20:42:41,687 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: COMPLETADO
2025-06-19 20:42:41,688 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: INICIANDO
2025-06-19 20:42:41,688 - LogTransaccionesPipeline - INFO - Extrayendo CSV final para fecha: 2025-06-18
2025-06-19 20:42:41,852 - LogTransaccionesPipeline - INFO - CSV final generado: 221299 registros -> output/TR-20250618.csv
2025-06-19 20:42:41,854 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: COMPLETADO
2025-06-19 20:42:41,854 - LogTransaccionesPipeline - INFO - ✅ Pipeline LOG_TRANSACCIONES completado exitosamente en 477.45 segundos
2025-06-19 20:42:41,856 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: EXITOSO
2025-06-19 21:18:25,700 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-19 21:18:25,747 - botocore.credentials - INFO - Found credentials from IAM Role: ec-landing-process-01-EC2-Role
2025-06-19 21:18:25,787 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-19 21:18:25,788 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-18
2025-06-19 21:18:25,789 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-19 21:18:25,789 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX usando 100% tablas S3 para fecha: 2025-06-18
2025-06-19 21:18:25,791 - LogTransaccionesPipeline - INFO - Archivo previo eliminado: TEMP_LOGS_TRANSACCIONES/20250618/PRE_LOG_TRX.parquet
2025-06-19 21:18:25,791 - LogTransaccionesPipeline - INFO - 🚀 OPTIMIZACIÓN NINJA: Rutas dinámicas generadas para fecha 2025-06-18
2025-06-19 21:18:25,791 - LogTransaccionesPipeline - INFO -    📁 MTX_TRANSACTION_HEADER: s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_HEADER_ORA/2025/06/18/*.parquet
2025-06-19 21:18:25,791 - LogTransaccionesPipeline - INFO -    📁 MTX_TRANSACTION_ITEMS: s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_ITEMS_ORA/2025/06/18/*.parquet
2025-06-19 21:23:40,826 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX completado con deduplicación automática:
2025-06-19 21:23:40,826 - LogTransaccionesPipeline - INFO -   - Registros finales: 221,354
2025-06-19 21:23:40,826 - LogTransaccionesPipeline - INFO -   - TransferIDs únicos: 221,354
2025-06-19 21:23:40,826 - LogTransaccionesPipeline - INFO -   - Duplicados eliminados: 0
2025-06-19 21:23:40,826 - LogTransaccionesPipeline - INFO -   - Archivo: TEMP_LOGS_TRANSACCIONES/20250618/PRE_LOG_TRX.parquet
2025-06-19 21:23:40,826 - LogTransaccionesPipeline - INFO - ✅ SP_PRE_LOG_TRX procesado exitosamente: 221,354 registros
2025-06-19 21:23:40,828 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: COMPLETADO
2025-06-19 21:23:40,830 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: INICIANDO
2025-06-19 21:23:40,830 - LogTransaccionesPipeline - INFO - Ejecutando SP_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-18
2025-06-19 21:23:40,831 - LogTransaccionesPipeline - INFO - Archivo previo eliminado: TEMP_LOGS_TRANSACCIONES/20250618/LOG_TRX_FINAL.parquet
2025-06-19 21:23:40,831 - LogTransaccionesPipeline - INFO - 🚀 OPTIMIZACIÓN NINJA: Rutas dinámicas generadas para fecha 2025-06-18
2025-06-19 21:23:40,831 - LogTransaccionesPipeline - INFO -    📁 MTX_TRANSACTION_HEADER: s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_HEADER_ORA/2025/06/18/*.parquet
2025-06-19 21:23:40,831 - LogTransaccionesPipeline - INFO -    📁 MTX_TRANSACTION_ITEMS: s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_ITEMS_ORA/2025/06/18/*.parquet
2025-06-19 21:23:41,187 - LogTransaccionesPipeline - INFO - SP_LOG_TRX completado: 221354 registros -> TEMP_LOGS_TRANSACCIONES/20250618/LOG_TRX_FINAL.parquet
2025-06-19 21:23:41,189 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: COMPLETADO
2025-06-19 21:23:41,190 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: INICIANDO
2025-06-19 21:23:41,190 - LogTransaccionesPipeline - INFO - Extrayendo CSV final para fecha: 2025-06-18
2025-06-19 21:23:41,365 - LogTransaccionesPipeline - INFO - CSV final generado: 221299 registros -> output/TR-20250618.csv
2025-06-19 21:23:41,367 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: COMPLETADO
2025-06-19 21:23:41,367 - LogTransaccionesPipeline - INFO - ✅ Pipeline LOG_TRANSACCIONES completado exitosamente en 315.58 segundos
2025-06-19 21:23:41,368 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: EXITOSO
2025-06-20 00:40:41,692 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-20 00:40:41,711 - botocore.credentials - INFO - Found credentials from IAM Role: ec-landing-process-01-EC2-Role
2025-06-20 00:40:41,754 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-20 00:40:41,754 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-18
2025-06-20 00:40:41,757 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-20 00:40:41,757 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX usando 100% tablas S3 para fecha: 2025-06-18
2025-06-20 00:40:41,758 - LogTransaccionesPipeline - INFO - Archivo previo eliminado: TEMP_LOGS_TRANSACCIONES/20250618/PRE_LOG_TRX.parquet
2025-06-20 00:40:41,758 - LogTransaccionesPipeline - INFO - 🚀 OPTIMIZACIÓN NINJA: Rutas dinámicas generadas para fecha 2025-06-18
2025-06-20 00:40:41,758 - LogTransaccionesPipeline - INFO -    📁 MTX_TRANSACTION_HEADER: s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_HEADER_ORA/2025/06/18/*.parquet
2025-06-20 00:40:41,758 - LogTransaccionesPipeline - INFO -    📁 MTX_TRANSACTION_ITEMS: s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_ITEMS_ORA/2025/06/18/*.parquet
2025-06-20 00:45:57,218 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX completado con deduplicación automática:
2025-06-20 00:45:57,218 - LogTransaccionesPipeline - INFO -   - Registros finales: 221,354
2025-06-20 00:45:57,218 - LogTransaccionesPipeline - INFO -   - TransferIDs únicos: 221,354
2025-06-20 00:45:57,218 - LogTransaccionesPipeline - INFO -   - Duplicados eliminados: 0
2025-06-20 00:45:57,218 - LogTransaccionesPipeline - INFO -   - Archivo: TEMP_LOGS_TRANSACCIONES/20250618/PRE_LOG_TRX.parquet
2025-06-20 00:45:57,218 - LogTransaccionesPipeline - INFO - ✅ SP_PRE_LOG_TRX procesado exitosamente: 221,354 registros
2025-06-20 00:45:57,220 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: COMPLETADO
2025-06-20 00:45:57,221 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: INICIANDO
2025-06-20 00:45:57,221 - LogTransaccionesPipeline - INFO - Ejecutando SP_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-18
2025-06-20 00:45:57,223 - LogTransaccionesPipeline - INFO - Archivo previo eliminado: TEMP_LOGS_TRANSACCIONES/20250618/LOG_TRX_FINAL.parquet
2025-06-20 00:45:57,223 - LogTransaccionesPipeline - INFO - 🚀 OPTIMIZACIÓN NINJA: Rutas dinámicas generadas para fecha 2025-06-18
2025-06-20 00:45:57,223 - LogTransaccionesPipeline - INFO -    📁 MTX_TRANSACTION_HEADER: s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_HEADER_ORA/2025/06/18/*.parquet
2025-06-20 00:45:57,223 - LogTransaccionesPipeline - INFO -    📁 MTX_TRANSACTION_ITEMS: s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_ITEMS_ORA/2025/06/18/*.parquet
2025-06-20 00:45:57,615 - LogTransaccionesPipeline - INFO - SP_LOG_TRX completado: 221354 registros -> TEMP_LOGS_TRANSACCIONES/20250618/LOG_TRX_FINAL.parquet
2025-06-20 00:45:57,617 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: COMPLETADO
2025-06-20 00:45:57,619 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: INICIANDO
2025-06-20 00:45:57,619 - LogTransaccionesPipeline - INFO - Extrayendo CSV final para fecha: 2025-06-18
2025-06-20 00:45:57,814 - LogTransaccionesPipeline - INFO - CSV final generado: 221299 registros -> output/TR-20250618.csv
2025-06-20 00:45:57,816 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: COMPLETADO
2025-06-20 00:45:57,816 - LogTransaccionesPipeline - INFO - ✅ Pipeline LOG_TRANSACCIONES completado exitosamente en 316.06 segundos
2025-06-20 00:45:57,817 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: EXITOSO
2025-06-20 00:50:33,040 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-20 00:50:33,059 - botocore.credentials - INFO - Found credentials from IAM Role: ec-landing-process-01-EC2-Role
2025-06-20 00:50:33,103 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-20 00:50:33,103 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-18
2025-06-20 00:50:33,105 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-20 00:50:33,105 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX usando 100% tablas S3 para fecha: 2025-06-18
2025-06-20 00:50:33,107 - LogTransaccionesPipeline - INFO - Archivo previo eliminado: TEMP_LOGS_TRANSACCIONES/20250618/PRE_LOG_TRX.parquet
2025-06-20 00:50:33,107 - LogTransaccionesPipeline - INFO - 🚀 OPTIMIZACIÓN NINJA: Rutas dinámicas generadas para fecha 2025-06-18
2025-06-20 00:50:33,107 - LogTransaccionesPipeline - INFO -    📁 MTX_TRANSACTION_HEADER: s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_HEADER_ORA/2025/06/18/*.parquet
2025-06-20 00:50:33,107 - LogTransaccionesPipeline - INFO -    📁 MTX_TRANSACTION_ITEMS: s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_ITEMS_ORA/2025/06/18/*.parquet
2025-06-20 00:55:50,354 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX completado con deduplicación automática:
2025-06-20 00:55:50,355 - LogTransaccionesPipeline - INFO -   - Registros finales: 221,180
2025-06-20 00:55:50,355 - LogTransaccionesPipeline - INFO -   - TransferIDs únicos: 221,180
2025-06-20 00:55:50,355 - LogTransaccionesPipeline - INFO -   - Duplicados eliminados: 0
2025-06-20 00:55:50,355 - LogTransaccionesPipeline - INFO -   - Archivo: TEMP_LOGS_TRANSACCIONES/20250618/PRE_LOG_TRX.parquet
2025-06-20 00:55:50,355 - LogTransaccionesPipeline - INFO - ✅ SP_PRE_LOG_TRX procesado exitosamente: 221,180 registros
2025-06-20 00:55:50,357 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: COMPLETADO
2025-06-20 00:55:50,358 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: INICIANDO
2025-06-20 00:55:50,358 - LogTransaccionesPipeline - INFO - Ejecutando SP_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-18
2025-06-20 00:55:50,359 - LogTransaccionesPipeline - INFO - Archivo previo eliminado: TEMP_LOGS_TRANSACCIONES/20250618/LOG_TRX_FINAL.parquet
2025-06-20 00:55:50,359 - LogTransaccionesPipeline - INFO - 🚀 OPTIMIZACIÓN NINJA: Rutas dinámicas generadas para fecha 2025-06-18
2025-06-20 00:55:50,360 - LogTransaccionesPipeline - INFO -    📁 MTX_TRANSACTION_HEADER: s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_HEADER_ORA/2025/06/18/*.parquet
2025-06-20 00:55:50,360 - LogTransaccionesPipeline - INFO -    📁 MTX_TRANSACTION_ITEMS: s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_ITEMS_ORA/2025/06/18/*.parquet
2025-06-20 00:55:50,726 - LogTransaccionesPipeline - INFO - SP_LOG_TRX completado: 221180 registros -> TEMP_LOGS_TRANSACCIONES/20250618/LOG_TRX_FINAL.parquet
2025-06-20 00:55:50,727 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: COMPLETADO
2025-06-20 00:55:50,729 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: INICIANDO
2025-06-20 00:55:50,729 - LogTransaccionesPipeline - INFO - Extrayendo CSV final para fecha: 2025-06-18
2025-06-20 00:55:50,922 - LogTransaccionesPipeline - INFO - CSV final generado: 221180 registros -> output/TR-20250618.csv
2025-06-20 00:55:50,924 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: COMPLETADO
2025-06-20 00:55:50,924 - LogTransaccionesPipeline - INFO - ✅ Pipeline LOG_TRANSACCIONES completado exitosamente en 317.82 segundos
2025-06-20 00:55:50,926 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: EXITOSO
2025-06-20 01:06:02,009 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-20 01:06:02,036 - botocore.credentials - INFO - Found credentials from IAM Role: ec-landing-process-01-EC2-Role
2025-06-20 01:06:02,077 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-20 01:06:02,077 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-18
2025-06-20 01:06:02,079 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-20 01:06:02,079 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX usando 100% tablas S3 para fecha: 2025-06-18
2025-06-20 01:06:02,079 - LogTransaccionesPipeline - INFO - 🚀 OPTIMIZACIÓN NINJA: Rutas dinámicas generadas para fecha 2025-06-18
2025-06-20 01:06:02,079 - LogTransaccionesPipeline - INFO -    📁 MTX_TRANSACTION_HEADER: s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_HEADER_ORA/2025/06/18/*.parquet
2025-06-20 01:06:02,079 - LogTransaccionesPipeline - INFO -    📁 MTX_TRANSACTION_ITEMS: s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_ITEMS_ORA/2025/06/18/*.parquet
2025-06-20 01:11:17,891 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX completado con deduplicación automática:
2025-06-20 01:11:17,891 - LogTransaccionesPipeline - INFO -   - Registros finales: 221,352
2025-06-20 01:11:17,891 - LogTransaccionesPipeline - INFO -   - TransferIDs únicos: 221,352
2025-06-20 01:11:17,891 - LogTransaccionesPipeline - INFO -   - Duplicados eliminados: 0
2025-06-20 01:11:17,891 - LogTransaccionesPipeline - INFO -   - Archivo: TEMP_LOGS_TRANSACCIONES/20250618/PRE_LOG_TRX.parquet
2025-06-20 01:11:17,891 - LogTransaccionesPipeline - INFO - ✅ SP_PRE_LOG_TRX procesado exitosamente: 221,352 registros
2025-06-20 01:11:17,893 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: COMPLETADO
2025-06-20 01:11:17,894 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: INICIANDO
2025-06-20 01:11:17,894 - LogTransaccionesPipeline - INFO - Ejecutando SP_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-18
2025-06-20 01:11:17,894 - LogTransaccionesPipeline - INFO - 🚀 OPTIMIZACIÓN NINJA: Rutas dinámicas generadas para fecha 2025-06-18
2025-06-20 01:11:17,894 - LogTransaccionesPipeline - INFO -    📁 MTX_TRANSACTION_HEADER: s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_HEADER_ORA/2025/06/18/*.parquet
2025-06-20 01:11:17,894 - LogTransaccionesPipeline - INFO -    📁 MTX_TRANSACTION_ITEMS: s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_ITEMS_ORA/2025/06/18/*.parquet
2025-06-20 01:11:18,258 - LogTransaccionesPipeline - INFO - SP_LOG_TRX completado: 221352 registros -> TEMP_LOGS_TRANSACCIONES/20250618/LOG_TRX_FINAL.parquet
2025-06-20 01:11:18,259 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: COMPLETADO
2025-06-20 01:11:18,261 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: INICIANDO
2025-06-20 01:11:18,261 - LogTransaccionesPipeline - INFO - Extrayendo CSV final para fecha: 2025-06-18
2025-06-20 01:11:18,432 - LogTransaccionesPipeline - INFO - CSV final generado: 221297 registros -> output/TR-20250618.csv
2025-06-20 01:11:18,434 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: COMPLETADO
2025-06-20 01:11:18,434 - LogTransaccionesPipeline - INFO - ✅ Pipeline LOG_TRANSACCIONES completado exitosamente en 316.36 segundos
2025-06-20 01:11:18,435 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: EXITOSO
2025-06-20 01:29:23,847 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-20 01:29:23,866 - botocore.credentials - INFO - Found credentials from IAM Role: ec-landing-process-01-EC2-Role
2025-06-20 01:29:23,904 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-20 01:29:23,904 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-18
2025-06-20 01:29:23,906 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-20 01:29:23,906 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX usando 100% tablas S3 para fecha: 2025-06-18
2025-06-20 01:29:23,908 - LogTransaccionesPipeline - INFO - Archivo previo eliminado: TEMP_LOGS_TRANSACCIONES/20250618/PRE_LOG_TRX.parquet
2025-06-20 01:29:23,908 - LogTransaccionesPipeline - INFO - 🚀 OPTIMIZACIÓN NINJA: Rutas dinámicas generadas para fecha 2025-06-18
2025-06-20 01:29:23,908 - LogTransaccionesPipeline - INFO -    📁 MTX_TRANSACTION_HEADER: s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_HEADER_ORA/2025/06/18/*.parquet
2025-06-20 01:29:23,908 - LogTransaccionesPipeline - INFO -    📁 MTX_TRANSACTION_ITEMS: s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_ITEMS_ORA/2025/06/18/*.parquet
2025-06-20 01:34:40,566 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX completado con deduplicación automática:
2025-06-20 01:34:40,566 - LogTransaccionesPipeline - INFO -   - Registros finales: 221,297
2025-06-20 01:34:40,566 - LogTransaccionesPipeline - INFO -   - TransferIDs únicos: 221,297
2025-06-20 01:34:40,566 - LogTransaccionesPipeline - INFO -   - Duplicados eliminados: 0
2025-06-20 01:34:40,566 - LogTransaccionesPipeline - INFO -   - Archivo: TEMP_LOGS_TRANSACCIONES/20250618/PRE_LOG_TRX.parquet
2025-06-20 01:34:40,566 - LogTransaccionesPipeline - INFO - ✅ SP_PRE_LOG_TRX procesado exitosamente: 221,297 registros
2025-06-20 01:34:40,568 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: COMPLETADO
2025-06-20 01:34:40,569 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: INICIANDO
2025-06-20 01:34:40,569 - LogTransaccionesPipeline - INFO - Ejecutando SP_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-18
2025-06-20 01:34:40,570 - LogTransaccionesPipeline - INFO - Archivo previo eliminado: TEMP_LOGS_TRANSACCIONES/20250618/LOG_TRX_FINAL.parquet
2025-06-20 01:34:40,570 - LogTransaccionesPipeline - INFO - 🚀 OPTIMIZACIÓN NINJA: Rutas dinámicas generadas para fecha 2025-06-18
2025-06-20 01:34:40,570 - LogTransaccionesPipeline - INFO -    📁 MTX_TRANSACTION_HEADER: s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_HEADER_ORA/2025/06/18/*.parquet
2025-06-20 01:34:40,570 - LogTransaccionesPipeline - INFO -    📁 MTX_TRANSACTION_ITEMS: s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_ITEMS_ORA/2025/06/18/*.parquet
2025-06-20 01:34:40,894 - LogTransaccionesPipeline - INFO - SP_LOG_TRX completado: 221297 registros -> TEMP_LOGS_TRANSACCIONES/20250618/LOG_TRX_FINAL.parquet
2025-06-20 01:34:40,896 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: COMPLETADO
2025-06-20 01:34:40,897 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: INICIANDO
2025-06-20 01:34:40,898 - LogTransaccionesPipeline - INFO - Extrayendo CSV final para fecha: 2025-06-18
2025-06-20 01:34:41,075 - LogTransaccionesPipeline - INFO - CSV final generado: 221297 registros -> output/TR-20250618.csv
2025-06-20 01:34:41,077 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: COMPLETADO
2025-06-20 01:34:41,077 - LogTransaccionesPipeline - INFO - ✅ Pipeline LOG_TRANSACCIONES completado exitosamente en 317.17 segundos
2025-06-20 01:34:41,079 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: EXITOSO
2025-06-20 01:56:30,663 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-20 01:56:30,687 - botocore.credentials - INFO - Found credentials from IAM Role: ec-landing-process-01-EC2-Role
2025-06-20 01:56:30,726 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-20 01:56:30,727 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-19
2025-06-20 01:56:30,728 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-20 01:56:30,728 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX usando 100% tablas S3 para fecha: 2025-06-19
2025-06-20 01:56:30,728 - LogTransaccionesPipeline - INFO - 🚀 OPTIMIZACIÓN NINJA: Rutas dinámicas generadas para fecha 2025-06-19
2025-06-20 01:56:30,728 - LogTransaccionesPipeline - INFO -    📁 MTX_TRANSACTION_HEADER: s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_HEADER_ORA/2025/06/19/*.parquet
2025-06-20 01:56:30,728 - LogTransaccionesPipeline - INFO -    📁 MTX_TRANSACTION_ITEMS: s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_ITEMS_ORA/2025/06/19/*.parquet
2025-06-20 02:02:14,593 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX completado con deduplicación automática:
2025-06-20 02:02:14,594 - LogTransaccionesPipeline - INFO -   - Registros finales: 218,442
2025-06-20 02:02:14,594 - LogTransaccionesPipeline - INFO -   - TransferIDs únicos: 218,442
2025-06-20 02:02:14,594 - LogTransaccionesPipeline - INFO -   - Duplicados eliminados: 0
2025-06-20 02:02:14,594 - LogTransaccionesPipeline - INFO -   - Archivo: TEMP_LOGS_TRANSACCIONES/20250619/PRE_LOG_TRX.parquet
2025-06-20 02:02:14,594 - LogTransaccionesPipeline - INFO - ✅ SP_PRE_LOG_TRX procesado exitosamente: 218,442 registros
2025-06-20 02:02:14,595 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: COMPLETADO
2025-06-20 02:02:14,597 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: INICIANDO
2025-06-20 02:02:14,597 - LogTransaccionesPipeline - INFO - Ejecutando SP_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-19
2025-06-20 02:02:14,597 - LogTransaccionesPipeline - INFO - 🚀 OPTIMIZACIÓN NINJA: Rutas dinámicas generadas para fecha 2025-06-19
2025-06-20 02:02:14,597 - LogTransaccionesPipeline - INFO -    📁 MTX_TRANSACTION_HEADER: s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_HEADER_ORA/2025/06/19/*.parquet
2025-06-20 02:02:14,597 - LogTransaccionesPipeline - INFO -    📁 MTX_TRANSACTION_ITEMS: s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_ITEMS_ORA/2025/06/19/*.parquet
2025-06-20 02:02:14,933 - LogTransaccionesPipeline - INFO - SP_LOG_TRX completado: 218442 registros -> TEMP_LOGS_TRANSACCIONES/20250619/LOG_TRX_FINAL.parquet
2025-06-20 02:02:14,935 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: COMPLETADO
2025-06-20 02:02:14,936 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: INICIANDO
2025-06-20 02:02:14,936 - LogTransaccionesPipeline - INFO - Extrayendo CSV final para fecha: 2025-06-19
2025-06-20 02:02:15,119 - LogTransaccionesPipeline - INFO - CSV final generado: 218442 registros -> output/TR-20250619.csv
2025-06-20 02:02:15,121 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: COMPLETADO
2025-06-20 02:02:15,121 - LogTransaccionesPipeline - INFO - ✅ Pipeline LOG_TRANSACCIONES completado exitosamente en 344.39 segundos
2025-06-20 02:02:15,122 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: EXITOSO
2025-06-20 02:36:38,888 - LogTransaccionesPipeline - INFO - Configurando credenciales S3...
2025-06-20 02:36:38,909 - botocore.credentials - INFO - Found credentials from IAM Role: ec-landing-process-01-EC2-Role
2025-06-20 02:36:38,949 - LogTransaccionesPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-20 02:36:38,949 - LogTransaccionesPipeline - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-18
2025-06-20 02:36:38,951 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: INICIANDO
2025-06-20 02:36:38,951 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX usando 100% tablas S3 para fecha: 2025-06-18
2025-06-20 02:36:38,952 - LogTransaccionesPipeline - INFO - Archivo previo eliminado: TEMP_LOGS_TRANSACCIONES/20250618/PRE_LOG_TRX.parquet
2025-06-20 02:36:38,953 - LogTransaccionesPipeline - INFO - 🚀 OPTIMIZACIÓN NINJA: Rutas dinámicas generadas para fecha 2025-06-18
2025-06-20 02:36:38,953 - LogTransaccionesPipeline - INFO -    📁 MTX_TRANSACTION_HEADER: s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_HEADER_ORA/2025/06/18/*.parquet
2025-06-20 02:36:38,953 - LogTransaccionesPipeline - INFO -    📁 MTX_TRANSACTION_ITEMS: s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_ITEMS_ORA/2025/06/18/*.parquet
2025-06-20 02:41:54,420 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX completado con deduplicación automática:
2025-06-20 02:41:54,420 - LogTransaccionesPipeline - INFO -   - Registros finales: 221,297
2025-06-20 02:41:54,420 - LogTransaccionesPipeline - INFO -   - TransferIDs únicos: 221,297
2025-06-20 02:41:54,420 - LogTransaccionesPipeline - INFO -   - Duplicados eliminados: 0
2025-06-20 02:41:54,420 - LogTransaccionesPipeline - INFO -   - Archivo: TEMP_LOGS_TRANSACCIONES/20250618/PRE_LOG_TRX.parquet
2025-06-20 02:41:54,420 - LogTransaccionesPipeline - INFO - ✅ SP_PRE_LOG_TRX procesado exitosamente: 221,297 registros
2025-06-20 02:41:54,420 - LogTransaccionesPipeline - INFO - Ejecutando SP_PRE_LOG_TRX_UPDATE (DuckDB) para fecha: 2025-06-18
2025-06-20 02:41:54,421 - LogTransaccionesPipeline - INFO -   No hay casos Niubiz para actualizar - omitiendo SP_PRE_LOG_TRX_UPDATE
2025-06-20 02:41:54,423 - LogTransaccionesPipeline - INFO - SP_PRE_LOG_TRX: COMPLETADO
2025-06-20 02:41:54,425 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: INICIANDO
2025-06-20 02:41:54,425 - LogTransaccionesPipeline - INFO - Ejecutando SP_LOG_TRX EXACTO (igual a Oracle) para fecha: 2025-06-18
2025-06-20 02:41:54,426 - LogTransaccionesPipeline - INFO - Archivo previo eliminado: TEMP_LOGS_TRANSACCIONES/20250618/LOG_TRX_FINAL.parquet
2025-06-20 02:41:54,426 - LogTransaccionesPipeline - INFO - 🚀 OPTIMIZACIÓN NINJA: Rutas dinámicas generadas para fecha 2025-06-18
2025-06-20 02:41:54,426 - LogTransaccionesPipeline - INFO -    📁 MTX_TRANSACTION_HEADER: s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_HEADER_ORA/2025/06/18/*.parquet
2025-06-20 02:41:54,426 - LogTransaccionesPipeline - INFO -    📁 MTX_TRANSACTION_ITEMS: s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_ITEMS_ORA/2025/06/18/*.parquet
2025-06-20 02:41:54,775 - LogTransaccionesPipeline - INFO - SP_LOG_TRX completado: 221297 registros -> TEMP_LOGS_TRANSACCIONES/20250618/LOG_TRX_FINAL.parquet
2025-06-20 02:41:54,776 - LogTransaccionesPipeline - INFO - SP_LOG_TRX: COMPLETADO
2025-06-20 02:41:54,778 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: INICIANDO
2025-06-20 02:41:54,778 - LogTransaccionesPipeline - INFO - Extrayendo CSV final para fecha: 2025-06-18
2025-06-20 02:41:54,972 - LogTransaccionesPipeline - INFO - CSV final generado: 221297 registros -> output/TR-20250618.csv
2025-06-20 02:41:54,974 - LogTransaccionesPipeline - INFO - EXTRACCION_CSV: COMPLETADO
2025-06-20 02:41:54,974 - LogTransaccionesPipeline - INFO - ✅ Pipeline LOG_TRANSACCIONES completado exitosamente en 316.02 segundos
2025-06-20 02:41:54,975 - LogTransaccionesPipeline - INFO - PIPELINE_COMPLETO: EXITOSO
