#!/usr/bin/env python3
"""
Script para homologar específicamente la columna To_Profile
y verificar tipos de datos entre Oracle y S3
"""

import oracledb
import duckdb
import boto3
import sys
import pandas as pd
from pathlib import Path

def homologar_to_profile_y_tipos():
    """Homologa To_Profile y verifica tipos de datos"""
    print("🔍 HOMOLOGACIÓN ESPECÍFICA: To_Profile + Tipos de Datos")
    print("=" * 80)
    
    parquet_path = '/home/<USER>/REPORTE_2025/reports/generate/S3_LOG_TRANSACCIONES/TEMP_LOGS_TRANSACCIONES/20250615/PRE_LOG_TRX.parquet'
    
    print(f"Archivo S3: {parquet_path}")
    print()
    
    # 1. CONSULTAR ORACLE
    print("1️⃣ CONSULTANDO ORACLE PRE_LOG_TRX:")
    print("-" * 60)
    
    try:
        connection = oracledb.connect(
            user='usr_datalake',
            password='U2024b1mD4t4l4k5',
            dsn='*************:1521/MMONEY'
        )
        
        cursor = connection.cursor()
        
        # Obtener muestra de To_Profile de Oracle
        cursor.execute("""
            SELECT 
                "TransferID",
                "ToID_Mobiquity",
                "To_Profile",
                "To_BankDomain",
                "TransactionType"
            FROM USR_DATALAKE.PRE_LOG_TRX 
            WHERE ROWNUM <= 20
            ORDER BY "TransferID"
        """)
        
        oracle_data = cursor.fetchall()
        oracle_columns = [desc[0] for desc in cursor.description]
        
        print(f"✅ Oracle PRE_LOG_TRX - Muestra de To_Profile ({len(oracle_data)} registros):")
        print()
        print(f"{'TransferID':<20} {'ToID_Mobiquity':<25} {'To_Profile':<40} {'To_BankDomain':<15}")
        print("-" * 105)
        
        oracle_to_profiles = {}
        for row in oracle_data:
            transfer_id = row[0]
            to_id = row[1]
            to_profile = row[2]
            to_bank_domain = row[3]
            transaction_type = row[4]
            
            oracle_to_profiles[transfer_id] = {
                'to_id': to_id,
                'to_profile': to_profile,
                'to_bank_domain': to_bank_domain,
                'transaction_type': transaction_type
            }
            
            print(f"{transfer_id:<20} {to_id:<25} {to_profile:<40} {to_bank_domain:<15}")
        
        # Obtener tipos de datos de Oracle
        cursor.execute("""
            SELECT COLUMN_NAME, DATA_TYPE, DATA_LENGTH, DATA_PRECISION, DATA_SCALE
            FROM USER_TAB_COLUMNS 
            WHERE TABLE_NAME = 'PRE_LOG_TRX'
            AND COLUMN_NAME IN ('To_Profile', 'Amount', 'Fee', 'TransferDate', 'CurrencyCode')
            ORDER BY COLUMN_ID
        """)
        
        oracle_types = cursor.fetchall()
        
        print(f"\n📊 TIPOS DE DATOS ORACLE:")
        print(f"{'COLUMNA':<25} {'TIPO':<15} {'LONGITUD':<10} {'PRECISIÓN':<10} {'ESCALA':<10}")
        print("-" * 75)
        
        oracle_type_map = {}
        for row in oracle_types:
            col_name = row[0]
            data_type = row[1]
            data_length = row[2]
            data_precision = row[3]
            data_scale = row[4]
            
            oracle_type_map[col_name] = {
                'type': data_type,
                'length': data_length,
                'precision': data_precision,
                'scale': data_scale
            }
            
            print(f"{col_name:<25} {data_type:<15} {str(data_length):<10} {str(data_precision):<10} {str(data_scale):<10}")
        
        cursor.close()
        connection.close()
        
    except Exception as e:
        print(f"❌ Error consultando Oracle: {e}")
        return
    
    # 2. CONSULTAR S3
    print(f"\n2️⃣ CONSULTANDO S3 PRE_LOG_TRX:")
    print("-" * 60)
    
    try:
        # Configurar DuckDB con S3
        conn = duckdb.connect()
        
        session = boto3.Session()
        credentials = session.get_credentials().get_frozen_credentials()
        
        conn.sql("INSTALL httpfs;")
        conn.sql("LOAD httpfs;")
        conn.sql("SET s3_region='us-east-1';")
        conn.sql("SET s3_use_ssl=true;")
        conn.sql("SET s3_url_style='path';")
        conn.sql(f"SET s3_access_key_id='{credentials.access_key}';")
        conn.sql(f"SET s3_secret_access_key='{credentials.secret_key}';")
        if credentials.token:
            conn.sql(f"SET s3_session_token='{credentials.token}';")
        
        # Obtener muestra de To_Profile de S3 (mismos TransferIDs)
        transfer_ids = "', '".join(oracle_to_profiles.keys())
        
        s3_data = conn.execute(f"""
            SELECT 
                "TransferID",
                "ToID_Mobiquity",
                "To_Profile",
                "To_BankDomain",
                "TransactionType"
            FROM read_parquet('{parquet_path}')
            WHERE "TransferID" IN ('{transfer_ids}')
            ORDER BY "TransferID"
        """).fetchall()
        
        print(f"✅ S3 PRE_LOG_TRX - Muestra de To_Profile ({len(s3_data)} registros):")
        print()
        print(f"{'TransferID':<20} {'ToID_Mobiquity':<25} {'To_Profile':<40} {'To_BankDomain':<15}")
        print("-" * 105)
        
        s3_to_profiles = {}
        for row in s3_data:
            transfer_id = row[0]
            to_id = row[1]
            to_profile = row[2]
            to_bank_domain = row[3]
            transaction_type = row[4]
            
            s3_to_profiles[transfer_id] = {
                'to_id': to_id,
                'to_profile': to_profile,
                'to_bank_domain': to_bank_domain,
                'transaction_type': transaction_type
            }
            
            print(f"{transfer_id:<20} {to_id:<25} {to_profile:<40} {to_bank_domain:<15}")
        
        # Obtener tipos de datos de S3
        types_result = conn.execute(f"""
            SELECT column_name, column_type, null as data_length, null as data_precision, null as data_scale
            FROM (DESCRIBE SELECT * FROM read_parquet('{parquet_path}'))
            WHERE column_name IN ('To_Profile', 'Amount', 'Fee', 'TransferDate', 'CurrencyCode')
        """).fetchall()
        
        print(f"\n📊 TIPOS DE DATOS S3:")
        print(f"{'COLUMNA':<25} {'TIPO':<15} {'LONGITUD':<10} {'PRECISIÓN':<10} {'ESCALA':<10}")
        print("-" * 75)
        
        s3_type_map = {}
        for row in types_result:
            col_name = row[0]
            data_type = row[1]
            
            s3_type_map[col_name] = {
                'type': data_type,
                'length': 'N/A',
                'precision': 'N/A',
                'scale': 'N/A'
            }
            
            print(f"{col_name:<25} {data_type:<15} {'N/A':<10} {'N/A':<10} {'N/A':<10}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error consultando S3: {e}")
        return
    
    # 3. COMPARACIÓN DE To_Profile
    print(f"\n3️⃣ COMPARACIÓN DETALLADA DE To_Profile:")
    print("-" * 60)
    
    coincidencias_profile = 0
    diferencias_profile = 0
    
    print(f"{'TransferID':<20} {'ORACLE':<40} {'S3':<40} {'ESTADO'}")
    print("-" * 110)
    
    for transfer_id in oracle_to_profiles.keys():
        oracle_profile = oracle_to_profiles[transfer_id]['to_profile']
        s3_profile = s3_to_profiles.get(transfer_id, {}).get('to_profile', 'NO_ENCONTRADO')
        
        # Normalizar valores para comparación
        oracle_str = str(oracle_profile) if oracle_profile is not None else 'NULL'
        s3_str = str(s3_profile) if s3_profile is not None else 'NULL'
        
        if oracle_str == s3_str:
            coincidencias_profile += 1
            status = "✅ COINCIDE"
        else:
            diferencias_profile += 1
            status = "❌ DIFIERE"
        
        print(f"{transfer_id:<20} {oracle_str:<40} {s3_str:<40} {status}")
    
    # 4. COMPARACIÓN DE TIPOS DE DATOS
    print(f"\n4️⃣ COMPARACIÓN DE TIPOS DE DATOS:")
    print("-" * 60)
    
    print(f"{'COLUMNA':<25} {'ORACLE':<20} {'S3':<20} {'COMPATIBLE'}")
    print("-" * 85)
    
    type_compatibility = {
        'VARCHAR2': ['VARCHAR', 'TEXT'],
        'NUMBER': ['BIGINT', 'INTEGER', 'DOUBLE', 'DECIMAL'],
        'DATE': ['TIMESTAMP', 'DATE'],
        'TIMESTAMP': ['TIMESTAMP']
    }
    
    tipos_compatibles = 0
    tipos_incompatibles = 0
    
    for col_name in oracle_type_map.keys():
        oracle_type = oracle_type_map[col_name]['type']
        s3_type = s3_type_map.get(col_name, {}).get('type', 'NO_ENCONTRADO')
        
        # Verificar compatibilidad
        compatible = False
        if oracle_type in type_compatibility:
            compatible = s3_type in type_compatibility[oracle_type]
        
        if compatible:
            tipos_compatibles += 1
            status = "✅ COMPATIBLE"
        else:
            tipos_incompatibles += 1
            status = "❌ INCOMPATIBLE"
        
        print(f"{col_name:<25} {oracle_type:<20} {s3_type:<20} {status}")
    
    # 5. ANÁLISIS ESPECÍFICO DE DIFERENCIAS EN To_Profile
    if diferencias_profile > 0:
        print(f"\n5️⃣ ANÁLISIS DE DIFERENCIAS EN To_Profile:")
        print("-" * 60)
        
        for transfer_id in oracle_to_profiles.keys():
            oracle_data_item = oracle_to_profiles[transfer_id]
            s3_data_item = s3_to_profiles.get(transfer_id, {})
            
            oracle_profile = oracle_data_item['to_profile']
            s3_profile = s3_data_item.get('to_profile', 'NO_ENCONTRADO')
            
            if str(oracle_profile) != str(s3_profile):
                print(f"\n  🔍 DIFERENCIA EN {transfer_id}:")
                print(f"    ToID_Mobiquity: {oracle_data_item['to_id']}")
                print(f"    To_BankDomain: {oracle_data_item['to_bank_domain']}")
                print(f"    TransactionType: {oracle_data_item['transaction_type']}")
                print(f"    Oracle To_Profile: {oracle_profile}")
                print(f"    S3 To_Profile: {s3_profile}")
                print(f"    🔧 NECESITA CORRECCIÓN")
    
    # 6. RESUMEN FINAL
    print(f"\n6️⃣ RESUMEN FINAL:")
    print("-" * 60)
    
    porcentaje_profile = (coincidencias_profile / len(oracle_to_profiles)) * 100 if oracle_to_profiles else 0
    porcentaje_tipos = (tipos_compatibles / len(oracle_type_map)) * 100 if oracle_type_map else 0
    
    print(f"📊 ESTADÍSTICAS To_Profile:")
    print(f"  Registros coincidentes: {coincidencias_profile}")
    print(f"  Registros diferentes: {diferencias_profile}")
    print(f"  Porcentaje de coincidencia: {porcentaje_profile:.1f}%")
    
    print(f"\n📊 ESTADÍSTICAS Tipos de Datos:")
    print(f"  Tipos compatibles: {tipos_compatibles}")
    print(f"  Tipos incompatibles: {tipos_incompatibles}")
    print(f"  Porcentaje de compatibilidad: {porcentaje_tipos:.1f}%")
    
    if diferencias_profile == 0 and tipos_incompatibles == 0:
        print("\n🎉 ¡HOMOLOGACIÓN PERFECTA!")
        print("✅ To_Profile coincide 100%")
        print("✅ Tipos de datos son 100% compatibles")
        print("🚀 No se requieren correcciones")
    else:
        print(f"\n⚠️  HOMOLOGACIÓN NECESITA CORRECCIÓN")
        if diferencias_profile > 0:
            print(f"❌ {diferencias_profile} diferencias en To_Profile")
        if tipos_incompatibles > 0:
            print(f"❌ {tipos_incompatibles} tipos incompatibles")
        print("🔧 Revisar lógica de To_Profile en el pipeline")

def main():
    print("🚀 HOMOLOGACIÓN To_Profile + TIPOS DE DATOS")
    print("=" * 80)
    print("Verificando To_Profile y compatibilidad de tipos")
    print()
    
    homologar_to_profile_y_tipos()
    
    print("\n🏁 HOMOLOGACIÓN COMPLETADA")

if __name__ == "__main__":
    main()
