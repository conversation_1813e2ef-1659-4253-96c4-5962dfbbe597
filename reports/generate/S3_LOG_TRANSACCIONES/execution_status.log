2025-06-17 03:57:06 - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 03:57:06 - SP_PRE_LOG_TRX: ERROR: Error en SP_PRE_LOG_TRX: IO Error: No files found that match the pattern "s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_HEADER_ORA/year=2025/month=06/day=15/*.parquet"
2025-06-17 03:57:06 - PIPELINE_COMPLETO: ERROR: IO Error: No files found that match the pattern "s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_HEADER_ORA/year=2025/month=06/day=15/*.parquet"
2025-06-17 03:57:41 - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 03:57:42 - SP_PRE_LOG_TRX: ERROR: Error en SP_PRE_LOG_TRX: IO Error: No files found that match the pattern "s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_HEADER_ORA/year=2025/month=06/day=15/*.parquet"
2025-06-17 03:57:42 - PIPELINE_COMPLETO: ERROR: IO Error: No files found that match the pattern "s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_HEADER_ORA/year=2025/month=06/day=15/*.parquet"
2025-06-17 03:59:04 - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 03:59:05 - SP_PRE_LOG_TRX: ERROR: Error en SP_PRE_LOG_TRX: Binder Error: No function matches the given name and argument types 'replace(INTEGER, STRING_LITERAL, STRING_LITERAL)'. You might need to add explicit type casts.
	Candidate functions:
	replace(VARCHAR, VARCHAR, VARCHAR) -> VARCHAR


LINE 6:                     REPLACE(MTH.PAYEE_IDENTIFIER_VALUE,'1','') AS NEW_PAYEE_IDE...
                            ^
2025-06-17 03:59:05 - PIPELINE_COMPLETO: ERROR: Binder Error: No function matches the given name and argument types 'replace(INTEGER, STRING_LITERAL, STRING_LITERAL)'. You might need to add explicit type casts.
	Candidate functions:
	replace(VARCHAR, VARCHAR, VARCHAR) -> VARCHAR


LINE 6:                     REPLACE(MTH.PAYEE_IDENTIFIER_VALUE,'1','') AS NEW_PAYEE_IDE...
                            ^
2025-06-17 03:59:48 - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 03:59:50 - SP_PRE_LOG_TRX: ERROR: Error en SP_PRE_LOG_TRX: Binder Error: Cannot mix values of type VARCHAR and INTEGER in CASE expression - an explicit cast is required

LINE 86:                     CASE WHEN MTH.PAYER_USER_ID = 'IND012' THEN MTH.PAYER_IDENT...
                             ^
2025-06-17 03:59:50 - PIPELINE_COMPLETO: ERROR: Binder Error: Cannot mix values of type VARCHAR and INTEGER in CASE expression - an explicit cast is required

LINE 86:                     CASE WHEN MTH.PAYER_USER_ID = 'IND012' THEN MTH.PAYER_IDENT...
                             ^
2025-06-17 04:00:29 - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 04:00:50 - SP_PRE_LOG_TRX: ERROR: Error en SP_PRE_LOG_TRX: Conversion Error: In Parquet reader of file "s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_HEADER_ORA/2025/06/15/PDP_PROD10_MAINDBBUS-MTX_TRANSACTION_HEADER_ORA_20250616-145051_chunk_0.parquet": failed to cast column "RECONCILIATION_BY" from type VARCHAR to INTEGER: Could not convert string '5902506151015052360380' to INT32

In file "s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_HEADER_ORA/2025/06/15/PDP_PROD10_MAINDBBUS-MTX_TRANSACTION_HEADER_ORA_20250616-145051_chunk_0.parquet" the column "RECONCILIATION_BY" has type VARCHAR, but we are trying to read it as type INTEGER.
This can happen when reading multiple Parquet files. The schema information is taken from the first Parquet file by default. Possible solutions:
* Enable the union_by_name=True option to combine the schema of all Parquet files (duckdb.org/docs/data/multiple_files/combining_schemas)
* Use a COPY statement to automatically derive types from an existing table.
2025-06-17 04:00:50 - PIPELINE_COMPLETO: ERROR: Conversion Error: In Parquet reader of file "s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_HEADER_ORA/2025/06/15/PDP_PROD10_MAINDBBUS-MTX_TRANSACTION_HEADER_ORA_20250616-145051_chunk_0.parquet": failed to cast column "RECONCILIATION_BY" from type VARCHAR to INTEGER: Could not convert string '5902506151015052360380' to INT32

In file "s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_HEADER_ORA/2025/06/15/PDP_PROD10_MAINDBBUS-MTX_TRANSACTION_HEADER_ORA_20250616-145051_chunk_0.parquet" the column "RECONCILIATION_BY" has type VARCHAR, but we are trying to read it as type INTEGER.
This can happen when reading multiple Parquet files. The schema information is taken from the first Parquet file by default. Possible solutions:
* Enable the union_by_name=True option to combine the schema of all Parquet files (duckdb.org/docs/data/multiple_files/combining_schemas)
* Use a COPY statement to automatically derive types from an existing table.
2025-06-17 04:03:51 - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 04:03:55 - SP_PRE_LOG_TRX: ERROR: Error en SP_PRE_LOG_TRX: Binder Error: Cannot compare values of type VARCHAR and type TIMESTAMP - an explicit cast is required

LINE 11:                 WHERE MTH.TRANSFER_DATE >= CAST('2025-06-15' AS TIMESTAMP)
                                                 ^
2025-06-17 04:03:55 - PIPELINE_COMPLETO: ERROR: Binder Error: Cannot compare values of type VARCHAR and type TIMESTAMP - an explicit cast is required

LINE 11:                 WHERE MTH.TRANSFER_DATE >= CAST('2025-06-15' AS TIMESTAMP)
                                                 ^
2025-06-17 04:04:17 - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 04:06:12 - SP_PRE_LOG_TRX: COMPLETADO
2025-06-17 04:06:12 - SP_LOG_TRX: INICIANDO
2025-06-17 04:06:12 - SP_LOG_TRX: ERROR: Error en SP_LOG_TRX: Binder Error: Table "MTH" does not have a column named "FromID_Mobiquity"

Candidate bindings: : "ID_TYPE"

LINE 112:                 ON MTH."FromID_Mobiquity" = H_PAYER.USER_ID
                             ^
2025-06-17 04:06:12 - PIPELINE_COMPLETO: ERROR: Binder Error: Table "MTH" does not have a column named "FromID_Mobiquity"

Candidate bindings: : "ID_TYPE"

LINE 112:                 ON MTH."FromID_Mobiquity" = H_PAYER.USER_ID
                             ^
2025-06-17 04:11:01 - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 04:12:55 - SP_PRE_LOG_TRX: COMPLETADO
2025-06-17 04:12:55 - SP_LOG_TRX: INICIANDO
2025-06-17 04:13:31 - SP_LOG_TRX: COMPLETADO
2025-06-17 04:13:31 - EXTRACCION_CSV: INICIANDO
2025-06-17 04:13:31 - EXTRACCION_CSV: COMPLETADO
2025-06-17 04:13:31 - PIPELINE_COMPLETO: EXITOSO
2025-06-17 06:09:57 - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 06:11:56 - SP_PRE_LOG_TRX: COMPLETADO
2025-06-17 06:11:56 - SP_LOG_TRX: INICIANDO
2025-06-17 06:11:56 - SP_LOG_TRX: ERROR: Error en SP_LOG_TRX: Binder Error: Table "MTH" does not have a column named "NEW_PAYER_USER_ID"

Candidate bindings: : "TransferID"

LINE 112:                 ON MTH.NEW_PAYER_USER_ID = H_PAYER.USER_ID
                             ^
2025-06-17 06:11:56 - PIPELINE_COMPLETO: ERROR: Binder Error: Table "MTH" does not have a column named "NEW_PAYER_USER_ID"

Candidate bindings: : "TransferID"

LINE 112:                 ON MTH.NEW_PAYER_USER_ID = H_PAYER.USER_ID
                             ^
2025-06-17 06:12:49 - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 06:14:46 - SP_PRE_LOG_TRX: COMPLETADO
2025-06-17 06:14:46 - SP_LOG_TRX: INICIANDO
2025-06-17 06:14:46 - SP_LOG_TRX: ERROR: Error en SP_LOG_TRX: Binder Error: Table "MTH" does not have a column named "NEW_PAYER_USER_ID"

Candidate bindings: : "TransferID"

LINE 112:                 ON MTH.NEW_PAYER_USER_ID = H_PAYER.USER_ID
                             ^
2025-06-17 06:14:46 - PIPELINE_COMPLETO: ERROR: Binder Error: Table "MTH" does not have a column named "NEW_PAYER_USER_ID"

Candidate bindings: : "TransferID"

LINE 112:                 ON MTH.NEW_PAYER_USER_ID = H_PAYER.USER_ID
                             ^
2025-06-17 06:19:35 - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 06:19:35 - SP_PRE_LOG_TRX: COMPLETADO
2025-06-17 06:19:35 - SP_LOG_TRX: INICIANDO
2025-06-17 06:19:35 - SP_LOG_TRX: ERROR: Error en SP_LOG_TRX: Binder Error: Referenced column "TRANSFER_ID" not found in FROM clause!
Candidate bindings: "H_PAYEE.USER_ID", "H_PAYER.USER_ID", "H_PAYER.GRADE_OLD", "H_PAYEE.GRADE_OLD", "H_PAYEE.ACCOUNT_ID"

LINE 4:                 SELECT TRANSFER_ID, CONTEXT
                               ^
2025-06-17 06:19:35 - PIPELINE_COMPLETO: ERROR: Binder Error: Referenced column "TRANSFER_ID" not found in FROM clause!
Candidate bindings: "H_PAYEE.USER_ID", "H_PAYER.USER_ID", "H_PAYER.GRADE_OLD", "H_PAYEE.GRADE_OLD", "H_PAYEE.ACCOUNT_ID"

LINE 4:                 SELECT TRANSFER_ID, CONTEXT
                               ^
2025-06-17 06:20:05 - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 06:20:05 - SP_PRE_LOG_TRX: COMPLETADO
2025-06-17 06:20:05 - SP_LOG_TRX: INICIANDO
2025-06-17 06:20:13 - SP_LOG_TRX: COMPLETADO
2025-06-17 06:20:13 - EXTRACCION_CSV: INICIANDO
2025-06-17 06:20:13 - EXTRACCION_CSV: COMPLETADO
2025-06-17 06:20:13 - PIPELINE_COMPLETO: EXITOSO
2025-06-17 07:02:22 - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 07:02:22 - SP_PRE_LOG_TRX: ERROR: Error en SP_PRE_LOG_TRX: Parser Error: syntax error at or near "WITH"
2025-06-17 07:02:22 - PIPELINE_COMPLETO: ERROR: Parser Error: syntax error at or near "WITH"
2025-06-17 07:03:04 - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 07:03:04 - SP_PRE_LOG_TRX: ERROR: Error en SP_PRE_LOG_TRX: Parser Error: syntax error at or near "WITH"
2025-06-17 07:03:04 - PIPELINE_COMPLETO: ERROR: Parser Error: syntax error at or near "WITH"
2025-06-17 07:03:54 - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 07:05:59 - SP_PRE_LOG_TRX: COMPLETADO
2025-06-17 07:05:59 - SP_LOG_TRX: INICIANDO
2025-06-17 07:06:35 - SP_LOG_TRX: COMPLETADO
2025-06-17 07:06:35 - EXTRACCION_CSV: INICIANDO
2025-06-17 07:06:35 - EXTRACCION_CSV: COMPLETADO
2025-06-17 07:06:35 - PIPELINE_COMPLETO: EXITOSO
2025-06-17 07:12:22 - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 07:12:22 - SP_PRE_LOG_TRX: COMPLETADO
2025-06-17 07:12:22 - SP_LOG_TRX: INICIANDO
2025-06-17 07:12:57 - SP_LOG_TRX: COMPLETADO
2025-06-17 07:12:57 - EXTRACCION_CSV: INICIANDO
2025-06-17 07:12:57 - EXTRACCION_CSV: COMPLETADO
2025-06-17 07:12:57 - PIPELINE_COMPLETO: EXITOSO
2025-06-17 07:13:18 - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 07:13:18 - SP_PRE_LOG_TRX: ERROR: Error en SP_PRE_LOG_TRX: Parser Error: syntax error at or near "WITH"
2025-06-17 07:13:18 - PIPELINE_COMPLETO: ERROR: Parser Error: syntax error at or near "WITH"
2025-06-17 07:13:58 - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 07:16:01 - SP_PRE_LOG_TRX: COMPLETADO
2025-06-17 07:16:01 - SP_LOG_TRX: INICIANDO
2025-06-17 07:16:09 - SP_LOG_TRX: COMPLETADO
2025-06-17 07:16:09 - EXTRACCION_CSV: INICIANDO
2025-06-17 07:16:09 - EXTRACCION_CSV: COMPLETADO
2025-06-17 07:16:09 - PIPELINE_COMPLETO: EXITOSO
2025-06-17 07:21:13 - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 07:23:14 - SP_PRE_LOG_TRX: COMPLETADO
2025-06-17 07:23:14 - SP_LOG_TRX: INICIANDO
2025-06-17 07:23:23 - SP_LOG_TRX: COMPLETADO
2025-06-17 07:23:23 - EXTRACCION_CSV: INICIANDO
2025-06-17 07:23:23 - EXTRACCION_CSV: COMPLETADO
2025-06-17 07:23:23 - PIPELINE_COMPLETO: EXITOSO
2025-06-17 07:25:34 - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 07:27:31 - SP_PRE_LOG_TRX: COMPLETADO
2025-06-17 07:27:31 - SP_LOG_TRX: INICIANDO
2025-06-17 07:27:40 - SP_LOG_TRX: COMPLETADO
2025-06-17 07:27:40 - EXTRACCION_CSV: INICIANDO
2025-06-17 07:27:40 - EXTRACCION_CSV: COMPLETADO
2025-06-17 07:27:40 - PIPELINE_COMPLETO: EXITOSO
2025-06-17 07:31:16 - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 07:33:13 - SP_PRE_LOG_TRX: COMPLETADO
2025-06-17 07:33:13 - SP_LOG_TRX: INICIANDO
2025-06-17 07:33:21 - SP_LOG_TRX: COMPLETADO
2025-06-17 07:33:21 - EXTRACCION_CSV: INICIANDO
2025-06-17 07:33:21 - EXTRACCION_CSV: COMPLETADO
2025-06-17 07:33:21 - PIPELINE_COMPLETO: EXITOSO
2025-06-17 07:34:20 - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 07:36:15 - SP_PRE_LOG_TRX: COMPLETADO
2025-06-17 07:36:15 - SP_LOG_TRX: INICIANDO
2025-06-17 07:36:23 - SP_LOG_TRX: COMPLETADO
2025-06-17 07:36:23 - EXTRACCION_CSV: INICIANDO
2025-06-17 07:36:24 - EXTRACCION_CSV: COMPLETADO
2025-06-17 07:36:24 - PIPELINE_COMPLETO: EXITOSO
2025-06-17 16:53:47 - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 16:55:58 - SP_PRE_LOG_TRX: COMPLETADO
2025-06-17 16:55:58 - SP_LOG_TRX: INICIANDO
2025-06-17 16:56:07 - SP_LOG_TRX: COMPLETADO
2025-06-17 16:56:07 - EXTRACCION_CSV: INICIANDO
2025-06-17 16:56:07 - EXTRACCION_CSV: COMPLETADO
2025-06-17 16:56:07 - PIPELINE_COMPLETO: EXITOSO
2025-06-17 17:01:26 - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 17:05:26 - SP_PRE_LOG_TRX: COMPLETADO
2025-06-17 17:05:26 - SP_LOG_TRX: INICIANDO
2025-06-17 17:05:36 - SP_LOG_TRX: COMPLETADO
2025-06-17 17:05:36 - EXTRACCION_CSV: INICIANDO
2025-06-17 17:05:36 - EXTRACCION_CSV: COMPLETADO
2025-06-17 17:05:36 - PIPELINE_COMPLETO: EXITOSO
2025-06-17 17:07:11 - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 17:10:22 - SP_PRE_LOG_TRX: COMPLETADO
2025-06-17 17:10:22 - SP_LOG_TRX: INICIANDO
2025-06-17 17:10:32 - SP_LOG_TRX: COMPLETADO
2025-06-17 17:10:32 - EXTRACCION_CSV: INICIANDO
2025-06-17 17:10:32 - EXTRACCION_CSV: COMPLETADO
2025-06-17 17:10:32 - PIPELINE_COMPLETO: EXITOSO
2025-06-17 17:12:20 - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 17:14:28 - SP_PRE_LOG_TRX: COMPLETADO
2025-06-17 17:14:28 - SP_LOG_TRX: INICIANDO
2025-06-17 17:14:37 - SP_LOG_TRX: COMPLETADO
2025-06-17 17:14:37 - EXTRACCION_CSV: INICIANDO
2025-06-17 17:14:37 - EXTRACCION_CSV: COMPLETADO
2025-06-17 17:14:37 - PIPELINE_COMPLETO: EXITOSO
2025-06-17 17:17:35 - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 17:19:43 - SP_PRE_LOG_TRX: COMPLETADO
2025-06-17 17:19:43 - SP_LOG_TRX: INICIANDO
2025-06-17 17:19:51 - SP_LOG_TRX: COMPLETADO
2025-06-17 17:19:51 - EXTRACCION_CSV: INICIANDO
2025-06-17 17:19:51 - EXTRACCION_CSV: COMPLETADO
2025-06-17 17:19:51 - PIPELINE_COMPLETO: EXITOSO
2025-06-17 17:29:37 - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 17:31:50 - SP_PRE_LOG_TRX: COMPLETADO
2025-06-17 17:31:50 - SP_LOG_TRX: INICIANDO
2025-06-17 17:31:59 - SP_LOG_TRX: COMPLETADO
2025-06-17 17:31:59 - EXTRACCION_CSV: INICIANDO
2025-06-17 17:31:59 - EXTRACCION_CSV: COMPLETADO
2025-06-17 17:31:59 - PIPELINE_COMPLETO: EXITOSO
2025-06-17 17:38:07 - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 17:40:20 - SP_PRE_LOG_TRX: COMPLETADO
2025-06-17 17:40:20 - SP_LOG_TRX: INICIANDO
2025-06-17 17:40:30 - SP_LOG_TRX: COMPLETADO
2025-06-17 17:40:30 - EXTRACCION_CSV: INICIANDO
2025-06-17 17:40:30 - EXTRACCION_CSV: COMPLETADO
2025-06-17 17:40:30 - PIPELINE_COMPLETO: EXITOSO
2025-06-17 17:42:15 - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 17:44:14 - SP_PRE_LOG_TRX: COMPLETADO
2025-06-17 17:44:14 - SP_LOG_TRX: INICIANDO
2025-06-17 17:44:22 - SP_LOG_TRX: COMPLETADO
2025-06-17 17:44:22 - EXTRACCION_CSV: INICIANDO
2025-06-17 17:44:22 - EXTRACCION_CSV: COMPLETADO
2025-06-17 17:44:22 - PIPELINE_COMPLETO: EXITOSO
2025-06-17 17:53:46 - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 17:56:25 - SP_PRE_LOG_TRX: COMPLETADO
2025-06-17 17:56:25 - SP_LOG_TRX: INICIANDO
2025-06-17 17:56:34 - SP_LOG_TRX: COMPLETADO
2025-06-17 17:56:34 - EXTRACCION_CSV: INICIANDO
2025-06-17 17:56:34 - EXTRACCION_CSV: COMPLETADO
2025-06-17 17:56:34 - PIPELINE_COMPLETO: EXITOSO
2025-06-17 18:22:16 - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 18:25:08 - SP_PRE_LOG_TRX: COMPLETADO
2025-06-17 18:25:08 - SP_LOG_TRX: INICIANDO
2025-06-17 18:25:17 - SP_LOG_TRX: COMPLETADO
2025-06-17 18:25:17 - EXTRACCION_CSV: INICIANDO
2025-06-17 18:25:17 - EXTRACCION_CSV: COMPLETADO
2025-06-17 18:25:17 - PIPELINE_COMPLETO: EXITOSO
2025-06-17 18:33:32 - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 18:35:58 - SP_PRE_LOG_TRX: COMPLETADO
2025-06-17 18:35:58 - SP_LOG_TRX: INICIANDO
2025-06-17 18:36:07 - SP_LOG_TRX: COMPLETADO
2025-06-17 18:36:07 - EXTRACCION_CSV: INICIANDO
2025-06-17 18:36:07 - EXTRACCION_CSV: COMPLETADO
2025-06-17 18:36:07 - PIPELINE_COMPLETO: EXITOSO
2025-06-17 18:42:35 - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 18:45:01 - SP_PRE_LOG_TRX: COMPLETADO
2025-06-17 18:45:01 - SP_LOG_TRX: INICIANDO
2025-06-17 18:45:10 - SP_LOG_TRX: COMPLETADO
2025-06-17 18:45:10 - EXTRACCION_CSV: INICIANDO
2025-06-17 18:45:10 - EXTRACCION_CSV: COMPLETADO
2025-06-17 18:45:10 - PIPELINE_COMPLETO: EXITOSO
2025-06-17 18:48:54 - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 18:49:19 - SP_PRE_LOG_TRX: ERROR: Error en SP_PRE_LOG_TRX: Binder Error: Cannot compare values of type TIMESTAMP and type VARCHAR - an explicit cast is required

LINE 225:                      AND MW_TEMP.MODIFIED_ON <= MTH.TRANSFER_DATE
                                                       ^
2025-06-17 18:49:19 - PIPELINE_COMPLETO: ERROR: Binder Error: Cannot compare values of type TIMESTAMP and type VARCHAR - an explicit cast is required

LINE 225:                      AND MW_TEMP.MODIFIED_ON <= MTH.TRANSFER_DATE
                                                       ^
2025-06-17 18:49:56 - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 18:52:21 - SP_PRE_LOG_TRX: COMPLETADO
2025-06-17 18:52:21 - SP_LOG_TRX: INICIANDO
2025-06-17 18:52:30 - SP_LOG_TRX: COMPLETADO
2025-06-17 18:52:30 - EXTRACCION_CSV: INICIANDO
2025-06-17 18:52:30 - EXTRACCION_CSV: COMPLETADO
2025-06-17 18:52:30 - PIPELINE_COMPLETO: EXITOSO
2025-06-17 18:54:29 - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 18:56:47 - SP_PRE_LOG_TRX: COMPLETADO
2025-06-17 18:56:47 - SP_LOG_TRX: INICIANDO
2025-06-17 18:56:56 - SP_LOG_TRX: COMPLETADO
2025-06-17 18:56:56 - EXTRACCION_CSV: INICIANDO
2025-06-17 18:56:56 - EXTRACCION_CSV: COMPLETADO
2025-06-17 18:56:56 - PIPELINE_COMPLETO: EXITOSO
2025-06-17 18:58:29 - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 19:01:23 - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 19:01:48 - SP_PRE_LOG_TRX: ERROR: Error en SP_PRE_LOG_TRX: Binder Error: Values list "MTH" does not have a column named "TransferID"

LINE 222:                     WHEN MTH."TransferID" = '175003230421458' AND UPAYER.M_USER_ID...
                                   ^
2025-06-17 19:01:48 - PIPELINE_COMPLETO: ERROR: Binder Error: Values list "MTH" does not have a column named "TransferID"

LINE 222:                     WHEN MTH."TransferID" = '175003230421458' AND UPAYER.M_USER_ID...
                                   ^
2025-06-17 19:02:24 - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 19:04:49 - SP_PRE_LOG_TRX: ERROR: Error en SP_PRE_LOG_TRX: Parser Error: syntax error at or near "("
2025-06-17 19:04:49 - PIPELINE_COMPLETO: ERROR: Parser Error: syntax error at or near "("
2025-06-17 19:05:19 - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 19:07:36 - SP_PRE_LOG_TRX: COMPLETADO
2025-06-17 19:07:36 - SP_LOG_TRX: INICIANDO
2025-06-17 19:07:45 - SP_LOG_TRX: COMPLETADO
2025-06-17 19:07:45 - EXTRACCION_CSV: INICIANDO
2025-06-17 19:07:45 - EXTRACCION_CSV: COMPLETADO
2025-06-17 19:07:45 - PIPELINE_COMPLETO: EXITOSO
2025-06-17 19:09:23 - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 19:11:42 - SP_PRE_LOG_TRX: COMPLETADO
2025-06-17 19:11:42 - SP_LOG_TRX: INICIANDO
2025-06-17 19:11:51 - SP_LOG_TRX: COMPLETADO
2025-06-17 19:11:51 - EXTRACCION_CSV: INICIANDO
2025-06-17 19:11:51 - EXTRACCION_CSV: COMPLETADO
2025-06-17 19:11:51 - PIPELINE_COMPLETO: EXITOSO
2025-06-17 19:13:27 - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 19:15:45 - SP_PRE_LOG_TRX: COMPLETADO
2025-06-17 19:15:45 - SP_LOG_TRX: INICIANDO
2025-06-17 19:15:54 - SP_LOG_TRX: COMPLETADO
2025-06-17 19:15:54 - EXTRACCION_CSV: INICIANDO
2025-06-17 19:15:54 - EXTRACCION_CSV: COMPLETADO
2025-06-17 19:15:54 - PIPELINE_COMPLETO: EXITOSO
2025-06-17 19:19:34 - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 19:19:59 - SP_PRE_LOG_TRX: ERROR: Error en SP_PRE_LOG_TRX: Binder Error: Referenced column "TransferValue" not found in FROM clause!
Candidate bindings: "TransferDate", "TransferStatus", "TransferID", "TransferID_Mob", "TransactionType"

LINE 20:                 "TransferValue",
                         ^
2025-06-17 19:19:59 - PIPELINE_COMPLETO: ERROR: Binder Error: Referenced column "TransferValue" not found in FROM clause!
Candidate bindings: "TransferDate", "TransferStatus", "TransferID", "TransferID_Mob", "TransactionType"

LINE 20:                 "TransferValue",
                         ^
2025-06-17 19:20:39 - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 19:22:12 - SP_PRE_LOG_TRX: COMPLETADO
2025-06-17 19:22:12 - SP_LOG_TRX: INICIANDO
2025-06-17 19:22:12 - SP_LOG_TRX: ERROR: Error en SP_LOG_TRX: Binder Error: Column "Context" referenced that exists in the SELECT clause - but this column cannot be referenced before it is defined
2025-06-17 19:22:12 - PIPELINE_COMPLETO: ERROR: Binder Error: Column "Context" referenced that exists in the SELECT clause - but this column cannot be referenced before it is defined
2025-06-17 19:27:25 - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 19:28:43 - SP_PRE_LOG_TRX: COMPLETADO
2025-06-17 19:28:43 - SP_LOG_TRX: INICIANDO
2025-06-17 19:28:43 - SP_LOG_TRX: ERROR: Error en SP_LOG_TRX: Binder Error: Column "Context" referenced that exists in the SELECT clause - but this column cannot be referenced before it is defined
2025-06-17 19:28:43 - PIPELINE_COMPLETO: ERROR: Binder Error: Column "Context" referenced that exists in the SELECT clause - but this column cannot be referenced before it is defined
2025-06-17 19:34:06 - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 19:35:24 - SP_PRE_LOG_TRX: COMPLETADO
2025-06-17 19:35:24 - SP_LOG_TRX: INICIANDO
2025-06-17 19:35:24 - SP_LOG_TRX: ERROR: Error en SP_LOG_TRX: Binder Error: Column "Context" referenced that exists in the SELECT clause - but this column cannot be referenced before it is defined
2025-06-17 19:35:24 - PIPELINE_COMPLETO: ERROR: Binder Error: Column "Context" referenced that exists in the SELECT clause - but this column cannot be referenced before it is defined
2025-06-17 22:28:32 - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 22:29:53 - SP_PRE_LOG_TRX: COMPLETADO
2025-06-17 22:29:53 - SP_LOG_TRX: INICIANDO
2025-06-17 22:29:53 - SP_LOG_TRX: ERROR: Error en SP_LOG_TRX: Binder Error: Column "Context" referenced that exists in the SELECT clause - but this column cannot be referenced before it is defined
2025-06-17 22:29:53 - PIPELINE_COMPLETO: ERROR: Binder Error: Column "Context" referenced that exists in the SELECT clause - but this column cannot be referenced before it is defined
2025-06-17 22:36:14 - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 22:37:33 - SP_PRE_LOG_TRX: COMPLETADO
2025-06-17 22:37:33 - SP_LOG_TRX: INICIANDO
2025-06-17 22:37:33 - SP_LOG_TRX: ERROR: Error en SP_LOG_TRX: Binder Error: Table "MTH" does not have a column named "ExternalTransactionID"

Candidate bindings: : "TransactionType"

LINE 7:                         THEN MTH."ExternalTransactionID"
                                     ^
2025-06-17 22:37:33 - PIPELINE_COMPLETO: ERROR: Binder Error: Table "MTH" does not have a column named "ExternalTransactionID"

Candidate bindings: : "TransactionType"

LINE 7:                         THEN MTH."ExternalTransactionID"
                                     ^
2025-06-17 22:38:33 - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 22:38:45 - SP_PRE_LOG_TRX: ERROR: Error en SP_PRE_LOG_TRX: Binder Error: Referenced column "TransferValue" not found in FROM clause!
Candidate bindings: "TransferDate", "TransferStatus", "TransferID", "TransferID_Mob", "TransactionType"

LINE 20:                 "TransferValue",
                         ^
2025-06-17 22:38:45 - PIPELINE_COMPLETO: ERROR: Binder Error: Referenced column "TransferValue" not found in FROM clause!
Candidate bindings: "TransferDate", "TransferStatus", "TransferID", "TransferID_Mob", "TransactionType"

LINE 20:                 "TransferValue",
                         ^
2025-06-17 22:39:57 - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 22:41:44 - SP_PRE_LOG_TRX: COMPLETADO
2025-06-17 22:41:44 - SP_LOG_TRX: INICIANDO
2025-06-17 22:41:45 - SP_LOG_TRX: COMPLETADO
2025-06-17 22:41:45 - EXTRACCION_CSV: INICIANDO
2025-06-17 22:41:45 - EXTRACCION_CSV: ERROR: Error en extracción CSV: IO Error: Could not write file "output/TR-20250615.csv": No space left on device
2025-06-17 22:41:45 - PIPELINE_COMPLETO: ERROR: IO Error: Could not write file "output/TR-20250615.csv": No space left on device
2025-06-17 22:46:47 - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 22:48:38 - SP_PRE_LOG_TRX: COMPLETADO
2025-06-17 22:48:38 - SP_LOG_TRX: INICIANDO
2025-06-17 22:48:38 - SP_LOG_TRX: COMPLETADO
2025-06-17 22:48:38 - EXTRACCION_CSV: INICIANDO
2025-06-17 22:48:38 - EXTRACCION_CSV: ERROR: Error en extracción CSV: IO Error: Could not write file "output/TR-20250615.csv": No space left on device
2025-06-17 22:48:38 - PIPELINE_COMPLETO: ERROR: IO Error: Could not write file "output/TR-20250615.csv": No space left on device
2025-06-17 22:16:58 - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 22:16:58 - SP_PRE_LOG_TRX: COMPLETADO
2025-06-17 22:16:58 - SP_LOG_TRX: INICIANDO
2025-06-17 22:16:58 - SP_LOG_TRX: COMPLETADO
2025-06-17 22:16:58 - EXTRACCION_CSV: INICIANDO
2025-06-17 22:16:58 - EXTRACCION_CSV: COMPLETADO
2025-06-17 22:16:58 - PIPELINE_COMPLETO: EXITOSO
2025-06-17 22:17:22 - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 22:18:56 - SP_PRE_LOG_TRX: COMPLETADO
2025-06-17 22:18:56 - SP_LOG_TRX: INICIANDO
2025-06-17 22:18:56 - SP_LOG_TRX: COMPLETADO
2025-06-17 22:18:56 - EXTRACCION_CSV: INICIANDO
2025-06-17 22:18:56 - EXTRACCION_CSV: COMPLETADO
2025-06-17 22:18:56 - PIPELINE_COMPLETO: EXITOSO
2025-06-17 22:19:08 - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 22:19:08 - SP_PRE_LOG_TRX: COMPLETADO
2025-06-17 22:19:08 - SP_LOG_TRX: INICIANDO
2025-06-17 22:19:09 - SP_LOG_TRX: COMPLETADO
2025-06-17 22:19:09 - EXTRACCION_CSV: INICIANDO
2025-06-17 22:19:09 - EXTRACCION_CSV: COMPLETADO
2025-06-17 22:19:09 - PIPELINE_COMPLETO: EXITOSO
2025-06-17 22:28:58 - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 22:30:34 - SP_PRE_LOG_TRX: COMPLETADO
2025-06-17 22:30:34 - SP_LOG_TRX: INICIANDO
2025-06-17 22:30:34 - SP_LOG_TRX: COMPLETADO
2025-06-17 22:30:34 - EXTRACCION_CSV: INICIANDO
2025-06-17 22:30:34 - EXTRACCION_CSV: COMPLETADO
2025-06-17 22:30:34 - PIPELINE_COMPLETO: EXITOSO
2025-06-17 22:31:00 - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 22:31:00 - SP_PRE_LOG_TRX: COMPLETADO
2025-06-17 22:31:00 - SP_LOG_TRX: INICIANDO
2025-06-17 22:31:00 - SP_LOG_TRX: COMPLETADO
2025-06-17 22:31:00 - EXTRACCION_CSV: INICIANDO
2025-06-17 22:31:00 - EXTRACCION_CSV: COMPLETADO
2025-06-17 22:31:00 - PIPELINE_COMPLETO: EXITOSO
2025-06-17 22:31:44 - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 22:31:44 - SP_PRE_LOG_TRX: COMPLETADO
2025-06-17 22:31:44 - SP_LOG_TRX: INICIANDO
2025-06-17 22:31:44 - SP_LOG_TRX: COMPLETADO
2025-06-17 22:31:44 - EXTRACCION_CSV: INICIANDO
2025-06-17 22:31:44 - EXTRACCION_CSV: COMPLETADO
2025-06-17 22:31:44 - PIPELINE_COMPLETO: EXITOSO
2025-06-17 22:47:13 - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 22:48:52 - SP_PRE_LOG_TRX: COMPLETADO
2025-06-17 22:48:52 - SP_LOG_TRX: INICIANDO
2025-06-17 22:48:53 - SP_LOG_TRX: COMPLETADO
2025-06-17 22:48:53 - EXTRACCION_CSV: INICIANDO
2025-06-17 22:48:53 - EXTRACCION_CSV: COMPLETADO
2025-06-17 22:48:53 - PIPELINE_COMPLETO: EXITOSO
2025-06-17 23:02:33 - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 23:12:42 - SP_PRE_LOG_TRX: ERROR: Error en SP_PRE_LOG_TRX: Query interrupted
2025-06-17 23:12:42 - PIPELINE_COMPLETO: ERROR: Query interrupted
2025-06-17 23:14:53 - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 23:16:33 - SP_PRE_LOG_TRX: COMPLETADO
2025-06-17 23:16:33 - SP_LOG_TRX: INICIANDO
2025-06-17 23:16:33 - SP_LOG_TRX: COMPLETADO
2025-06-17 23:16:33 - EXTRACCION_CSV: INICIANDO
2025-06-17 23:16:33 - EXTRACCION_CSV: COMPLETADO
2025-06-17 23:16:33 - PIPELINE_COMPLETO: EXITOSO
2025-06-17 23:19:40 - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 23:21:16 - SP_PRE_LOG_TRX: COMPLETADO
2025-06-17 23:21:16 - SP_LOG_TRX: INICIANDO
2025-06-17 23:21:16 - SP_LOG_TRX: COMPLETADO
2025-06-17 23:21:16 - EXTRACCION_CSV: INICIANDO
2025-06-17 23:21:16 - EXTRACCION_CSV: COMPLETADO
2025-06-17 23:21:16 - PIPELINE_COMPLETO: EXITOSO
2025-06-17 23:42:37 - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 23:44:14 - SP_PRE_LOG_TRX: COMPLETADO
2025-06-17 23:44:14 - SP_LOG_TRX: INICIANDO
2025-06-17 23:44:15 - SP_LOG_TRX: COMPLETADO
2025-06-17 23:44:15 - EXTRACCION_CSV: INICIANDO
2025-06-17 23:44:15 - EXTRACCION_CSV: COMPLETADO
2025-06-17 23:44:15 - PIPELINE_COMPLETO: EXITOSO
2025-06-17 23:54:50 - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 23:56:28 - SP_PRE_LOG_TRX: COMPLETADO
2025-06-17 23:56:28 - SP_LOG_TRX: INICIANDO
2025-06-17 23:56:28 - SP_LOG_TRX: COMPLETADO
2025-06-17 23:56:28 - EXTRACCION_CSV: INICIANDO
2025-06-17 23:56:28 - EXTRACCION_CSV: COMPLETADO
2025-06-17 23:56:28 - PIPELINE_COMPLETO: EXITOSO
2025-06-17 23:58:10 - SP_PRE_LOG_TRX: INICIANDO
2025-06-17 23:59:46 - SP_PRE_LOG_TRX: COMPLETADO
2025-06-17 23:59:46 - SP_LOG_TRX: INICIANDO
2025-06-17 23:59:46 - SP_LOG_TRX: COMPLETADO
2025-06-17 23:59:46 - EXTRACCION_CSV: INICIANDO
2025-06-17 23:59:46 - EXTRACCION_CSV: COMPLETADO
2025-06-17 23:59:46 - PIPELINE_COMPLETO: EXITOSO
2025-06-18 00:07:31 - SP_PRE_LOG_TRX: INICIANDO
2025-06-18 00:09:10 - SP_PRE_LOG_TRX: COMPLETADO
2025-06-18 00:09:10 - SP_LOG_TRX: INICIANDO
2025-06-18 00:09:10 - SP_LOG_TRX: COMPLETADO
2025-06-18 00:09:10 - EXTRACCION_CSV: INICIANDO
2025-06-18 00:09:11 - EXTRACCION_CSV: COMPLETADO
2025-06-18 00:09:11 - PIPELINE_COMPLETO: EXITOSO
2025-06-18 00:11:16 - SP_PRE_LOG_TRX: INICIANDO
2025-06-18 00:12:51 - SP_PRE_LOG_TRX: COMPLETADO
2025-06-18 00:12:51 - SP_LOG_TRX: INICIANDO
2025-06-18 00:12:51 - SP_LOG_TRX: COMPLETADO
2025-06-18 00:12:51 - EXTRACCION_CSV: INICIANDO
2025-06-18 00:12:52 - EXTRACCION_CSV: COMPLETADO
2025-06-18 00:12:52 - PIPELINE_COMPLETO: EXITOSO
2025-06-18 00:17:52 - SP_PRE_LOG_TRX: INICIANDO
2025-06-18 00:19:28 - SP_PRE_LOG_TRX: COMPLETADO
2025-06-18 00:19:28 - SP_LOG_TRX: INICIANDO
2025-06-18 00:19:29 - SP_LOG_TRX: COMPLETADO
2025-06-18 00:19:29 - EXTRACCION_CSV: INICIANDO
2025-06-18 00:19:29 - EXTRACCION_CSV: COMPLETADO
2025-06-18 00:19:29 - PIPELINE_COMPLETO: EXITOSO
2025-06-18 00:20:57 - SP_PRE_LOG_TRX: INICIANDO
2025-06-18 00:22:32 - SP_PRE_LOG_TRX: COMPLETADO
2025-06-18 00:22:32 - SP_LOG_TRX: INICIANDO
2025-06-18 00:22:33 - SP_LOG_TRX: COMPLETADO
2025-06-18 00:22:33 - EXTRACCION_CSV: INICIANDO
2025-06-18 00:22:33 - EXTRACCION_CSV: COMPLETADO
2025-06-18 00:22:33 - PIPELINE_COMPLETO: EXITOSO
2025-06-18 00:49:01 - SP_PRE_LOG_TRX: INICIANDO
2025-06-18 00:49:07 - SP_PRE_LOG_TRX: ERROR: Error en SP_PRE_LOG_TRX: HTTP Error: HTTP GET error reading 's3://ec-landing-process-01/datalake/MMONEY/PDP_PROD10_MAINDBBUS/MTX_WALLET/ec-landing-process-01/?encoding-type=url&list-type=2&prefix=datalake%2FMMONEY%2FPDP_PROD10_MAINDBBUS%2FMTX_WALLET%2F' in region 'us-east-1' (HTTP 404 Not Found)

Authentication Failure - this is usually caused by invalid or missing credentials.
* Credentials are provided, but they did not work.
* See https://duckdb.org/docs/stable/extensions/httpfs/s3api.html
2025-06-18 00:49:07 - PIPELINE_COMPLETO: ERROR: HTTP Error: HTTP GET error reading 's3://ec-landing-process-01/datalake/MMONEY/PDP_PROD10_MAINDBBUS/MTX_WALLET/ec-landing-process-01/?encoding-type=url&list-type=2&prefix=datalake%2FMMONEY%2FPDP_PROD10_MAINDBBUS%2FMTX_WALLET%2F' in region 'us-east-1' (HTTP 404 Not Found)

Authentication Failure - this is usually caused by invalid or missing credentials.
* Credentials are provided, but they did not work.
* See https://duckdb.org/docs/stable/extensions/httpfs/s3api.html
2025-06-18 00:52:38 - SP_PRE_LOG_TRX: INICIANDO
2025-06-18 00:55:06 - SP_PRE_LOG_TRX: COMPLETADO
2025-06-18 00:55:06 - SP_LOG_TRX: INICIANDO
2025-06-18 00:55:07 - SP_LOG_TRX: COMPLETADO
2025-06-18 00:55:07 - EXTRACCION_CSV: INICIANDO
2025-06-18 00:55:07 - EXTRACCION_CSV: COMPLETADO
2025-06-18 00:55:07 - PIPELINE_COMPLETO: EXITOSO
2025-06-18 00:56:59 - SP_PRE_LOG_TRX: INICIANDO
2025-06-18 00:58:44 - SP_PRE_LOG_TRX: COMPLETADO
2025-06-18 00:58:44 - SP_LOG_TRX: INICIANDO
2025-06-18 00:58:44 - SP_LOG_TRX: COMPLETADO
2025-06-18 00:58:44 - EXTRACCION_CSV: INICIANDO
2025-06-18 00:58:44 - EXTRACCION_CSV: COMPLETADO
2025-06-18 00:58:44 - PIPELINE_COMPLETO: EXITOSO
2025-06-18 00:59:39 - SP_PRE_LOG_TRX: INICIANDO
2025-06-18 01:01:26 - SP_PRE_LOG_TRX: COMPLETADO
2025-06-18 01:01:26 - SP_LOG_TRX: INICIANDO
2025-06-18 01:01:26 - SP_LOG_TRX: COMPLETADO
2025-06-18 01:01:26 - EXTRACCION_CSV: INICIANDO
2025-06-18 01:01:26 - EXTRACCION_CSV: COMPLETADO
2025-06-18 01:01:26 - PIPELINE_COMPLETO: EXITOSO
2025-06-18 01:23:03 - SP_PRE_LOG_TRX: INICIANDO
2025-06-18 01:23:03 - SP_PRE_LOG_TRX: ERROR: Error en SP_PRE_LOG_TRX: 'user_profile'
2025-06-18 01:23:03 - PIPELINE_COMPLETO: ERROR: 'user_profile'
2025-06-18 01:24:06 - SP_PRE_LOG_TRX: INICIANDO
2025-06-18 01:24:11 - SP_PRE_LOG_TRX: ERROR: Error en SP_PRE_LOG_TRX: Catalog Error: Table with name WALLET_ACTIVOS does not exist!
Did you mean "pg_am"?

LINE 392:             LEFT JOIN WALLET_ACTIVOS WA_PAYER ON UPAYER.M_USER_ID = WA_PAYER.USER...
                                ^
2025-06-18 01:24:11 - PIPELINE_COMPLETO: ERROR: Catalog Error: Table with name WALLET_ACTIVOS does not exist!
Did you mean "pg_am"?

LINE 392:             LEFT JOIN WALLET_ACTIVOS WA_PAYER ON UPAYER.M_USER_ID = WA_PAYER.USER...
                                ^
2025-06-18 01:25:23 - SP_PRE_LOG_TRX: INICIANDO
2025-06-18 01:27:02 - SP_PRE_LOG_TRX: COMPLETADO
2025-06-18 01:27:02 - SP_LOG_TRX: INICIANDO
2025-06-18 01:27:02 - SP_LOG_TRX: COMPLETADO
2025-06-18 01:27:02 - EXTRACCION_CSV: INICIANDO
2025-06-18 01:27:02 - EXTRACCION_CSV: COMPLETADO
2025-06-18 01:27:02 - PIPELINE_COMPLETO: EXITOSO
2025-06-18 01:30:00 - SP_PRE_LOG_TRX: INICIANDO
2025-06-18 01:31:36 - SP_PRE_LOG_TRX: COMPLETADO
2025-06-18 01:31:36 - SP_LOG_TRX: INICIANDO
2025-06-18 01:31:36 - SP_LOG_TRX: COMPLETADO
2025-06-18 01:31:36 - EXTRACCION_CSV: INICIANDO
2025-06-18 01:31:36 - EXTRACCION_CSV: COMPLETADO
2025-06-18 01:31:36 - PIPELINE_COMPLETO: EXITOSO
2025-06-18 12:34:33 - SP_PRE_LOG_TRX: INICIANDO
2025-06-18 12:36:14 - SP_PRE_LOG_TRX: COMPLETADO
2025-06-18 12:36:14 - SP_LOG_TRX: INICIANDO
2025-06-18 12:36:15 - SP_LOG_TRX: COMPLETADO
2025-06-18 12:36:15 - EXTRACCION_CSV: INICIANDO
2025-06-18 12:36:15 - EXTRACCION_CSV: COMPLETADO
2025-06-18 12:36:15 - PIPELINE_COMPLETO: EXITOSO
2025-06-18 12:40:35 - SP_PRE_LOG_TRX: INICIANDO
2025-06-18 12:42:14 - SP_PRE_LOG_TRX: COMPLETADO
2025-06-18 12:42:14 - SP_LOG_TRX: INICIANDO
2025-06-18 12:42:15 - SP_LOG_TRX: COMPLETADO
2025-06-18 12:42:15 - EXTRACCION_CSV: INICIANDO
2025-06-18 12:42:15 - EXTRACCION_CSV: COMPLETADO
2025-06-18 12:42:15 - PIPELINE_COMPLETO: EXITOSO
2025-06-18 13:16:10 - SP_PRE_LOG_TRX: INICIANDO
2025-06-18 13:17:52 - SP_PRE_LOG_TRX: COMPLETADO
2025-06-18 13:17:52 - SP_LOG_TRX: INICIANDO
2025-06-18 13:17:52 - SP_LOG_TRX: COMPLETADO
2025-06-18 13:17:52 - EXTRACCION_CSV: INICIANDO
2025-06-18 13:17:52 - EXTRACCION_CSV: COMPLETADO
2025-06-18 13:17:52 - PIPELINE_COMPLETO: EXITOSO
2025-06-18 13:21:52 - SP_PRE_LOG_TRX: INICIANDO
2025-06-18 13:23:33 - SP_PRE_LOG_TRX: COMPLETADO
2025-06-18 13:23:33 - SP_LOG_TRX: INICIANDO
2025-06-18 13:23:33 - SP_LOG_TRX: COMPLETADO
2025-06-18 13:23:33 - EXTRACCION_CSV: INICIANDO
2025-06-18 13:23:33 - EXTRACCION_CSV: COMPLETADO
2025-06-18 13:23:33 - PIPELINE_COMPLETO: EXITOSO
2025-06-18 13:57:20 - SP_PRE_LOG_TRX: INICIANDO
2025-06-18 13:59:01 - SP_PRE_LOG_TRX: COMPLETADO
2025-06-18 13:59:01 - SP_LOG_TRX: INICIANDO
2025-06-18 13:59:01 - SP_LOG_TRX: COMPLETADO
2025-06-18 13:59:01 - EXTRACCION_CSV: INICIANDO
2025-06-18 13:59:01 - EXTRACCION_CSV: COMPLETADO
2025-06-18 13:59:01 - PIPELINE_COMPLETO: EXITOSO
2025-06-18 13:59:42 - SP_PRE_LOG_TRX: INICIANDO
2025-06-18 14:01:21 - SP_PRE_LOG_TRX: COMPLETADO
2025-06-18 14:01:21 - SP_LOG_TRX: INICIANDO
2025-06-18 14:01:21 - SP_LOG_TRX: COMPLETADO
2025-06-18 14:01:21 - EXTRACCION_CSV: INICIANDO
2025-06-18 14:01:21 - EXTRACCION_CSV: COMPLETADO
2025-06-18 14:01:21 - PIPELINE_COMPLETO: EXITOSO
2025-06-18 14:08:35 - SP_PRE_LOG_TRX: INICIANDO
2025-06-18 14:10:16 - SP_PRE_LOG_TRX: COMPLETADO
2025-06-18 14:10:16 - SP_LOG_TRX: INICIANDO
2025-06-18 14:10:17 - SP_LOG_TRX: COMPLETADO
2025-06-18 14:10:17 - EXTRACCION_CSV: INICIANDO
2025-06-18 14:10:17 - EXTRACCION_CSV: COMPLETADO
2025-06-18 14:10:17 - PIPELINE_COMPLETO: EXITOSO
2025-06-18 14:21:09 - SP_PRE_LOG_TRX: INICIANDO
2025-06-18 14:22:52 - SP_PRE_LOG_TRX: COMPLETADO
2025-06-18 14:22:52 - SP_LOG_TRX: INICIANDO
2025-06-18 14:22:52 - SP_LOG_TRX: COMPLETADO
2025-06-18 14:22:52 - EXTRACCION_CSV: INICIANDO
2025-06-18 14:22:52 - EXTRACCION_CSV: COMPLETADO
2025-06-18 14:22:52 - PIPELINE_COMPLETO: EXITOSO
2025-06-18 19:05:07 - SP_PRE_LOG_TRX: INICIANDO
2025-06-18 19:06:48 - SP_PRE_LOG_TRX: COMPLETADO
2025-06-18 19:06:48 - SP_LOG_TRX: INICIANDO
2025-06-18 19:06:48 - SP_LOG_TRX: COMPLETADO
2025-06-18 19:06:48 - EXTRACCION_CSV: INICIANDO
2025-06-18 19:06:49 - EXTRACCION_CSV: COMPLETADO
2025-06-18 19:06:49 - PIPELINE_COMPLETO: EXITOSO
2025-06-18 19:10:25 - SP_PRE_LOG_TRX: INICIANDO
2025-06-18 19:12:04 - SP_PRE_LOG_TRX: COMPLETADO
2025-06-18 19:12:04 - SP_LOG_TRX: INICIANDO
2025-06-18 19:12:04 - SP_LOG_TRX: COMPLETADO
2025-06-18 19:12:04 - EXTRACCION_CSV: INICIANDO
2025-06-18 19:12:04 - EXTRACCION_CSV: COMPLETADO
2025-06-18 19:12:04 - PIPELINE_COMPLETO: EXITOSO
2025-06-18 19:17:02 - SP_PRE_LOG_TRX: INICIANDO
2025-06-18 19:18:42 - SP_PRE_LOG_TRX: COMPLETADO
2025-06-18 19:18:42 - SP_LOG_TRX: INICIANDO
2025-06-18 19:18:42 - SP_LOG_TRX: COMPLETADO
2025-06-18 19:18:42 - EXTRACCION_CSV: INICIANDO
2025-06-18 19:18:42 - EXTRACCION_CSV: COMPLETADO
2025-06-18 19:18:42 - PIPELINE_COMPLETO: EXITOSO
2025-06-18 19:22:28 - SP_PRE_LOG_TRX: INICIANDO
2025-06-18 19:24:07 - SP_PRE_LOG_TRX: COMPLETADO
2025-06-18 19:24:07 - SP_LOG_TRX: INICIANDO
2025-06-18 19:24:08 - SP_LOG_TRX: COMPLETADO
2025-06-18 19:24:08 - EXTRACCION_CSV: INICIANDO
2025-06-18 19:24:08 - EXTRACCION_CSV: COMPLETADO
2025-06-18 19:24:08 - PIPELINE_COMPLETO: EXITOSO
2025-06-18 19:39:43 - SP_PRE_LOG_TRX: INICIANDO
2025-06-18 19:41:22 - SP_PRE_LOG_TRX: COMPLETADO
2025-06-18 19:41:22 - SP_LOG_TRX: INICIANDO
2025-06-18 19:41:23 - SP_LOG_TRX: COMPLETADO
2025-06-18 19:41:23 - EXTRACCION_CSV: INICIANDO
2025-06-18 19:41:23 - EXTRACCION_CSV: COMPLETADO
2025-06-18 19:41:23 - PIPELINE_COMPLETO: EXITOSO
2025-06-18 19:47:06 - SP_PRE_LOG_TRX: INICIANDO
2025-06-18 19:48:44 - SP_PRE_LOG_TRX: COMPLETADO
2025-06-18 19:48:44 - SP_LOG_TRX: INICIANDO
2025-06-18 19:48:45 - SP_LOG_TRX: COMPLETADO
2025-06-18 19:48:45 - EXTRACCION_CSV: INICIANDO
2025-06-18 19:48:45 - EXTRACCION_CSV: COMPLETADO
2025-06-18 19:48:45 - PIPELINE_COMPLETO: EXITOSO
2025-06-18 19:51:30 - SP_PRE_LOG_TRX: INICIANDO
2025-06-18 19:53:08 - SP_PRE_LOG_TRX: COMPLETADO
2025-06-18 19:53:08 - SP_LOG_TRX: INICIANDO
2025-06-18 19:53:08 - SP_LOG_TRX: COMPLETADO
2025-06-18 19:53:08 - EXTRACCION_CSV: INICIANDO
2025-06-18 19:53:08 - EXTRACCION_CSV: COMPLETADO
2025-06-18 19:53:08 - PIPELINE_COMPLETO: EXITOSO
2025-06-18 22:53:50 - SP_PRE_LOG_TRX: INICIANDO
2025-06-18 22:55:29 - SP_PRE_LOG_TRX: COMPLETADO
2025-06-18 22:55:29 - SP_LOG_TRX: INICIANDO
2025-06-18 22:55:29 - SP_LOG_TRX: COMPLETADO
2025-06-18 22:55:29 - EXTRACCION_CSV: INICIANDO
2025-06-18 22:55:29 - EXTRACCION_CSV: COMPLETADO
2025-06-18 22:55:29 - PIPELINE_COMPLETO: EXITOSO
2025-06-18 22:59:25 - SP_PRE_LOG_TRX: INICIANDO
2025-06-18 23:01:38 - SP_PRE_LOG_TRX: COMPLETADO
2025-06-18 23:01:38 - SP_LOG_TRX: INICIANDO
2025-06-18 23:01:38 - SP_LOG_TRX: COMPLETADO
2025-06-18 23:01:38 - EXTRACCION_CSV: INICIANDO
2025-06-18 23:01:39 - EXTRACCION_CSV: COMPLETADO
2025-06-18 23:01:39 - PIPELINE_COMPLETO: EXITOSO
2025-06-18 23:17:45 - SP_PRE_LOG_TRX: INICIANDO
2025-06-18 23:19:26 - SP_PRE_LOG_TRX: COMPLETADO
2025-06-18 23:19:26 - SP_LOG_TRX: INICIANDO
2025-06-18 23:19:26 - SP_LOG_TRX: COMPLETADO
2025-06-18 23:19:26 - EXTRACCION_CSV: INICIANDO
2025-06-18 23:19:27 - EXTRACCION_CSV: COMPLETADO
2025-06-18 23:19:27 - PIPELINE_COMPLETO: EXITOSO
2025-06-18 23:58:09 - SP_PRE_LOG_TRX: INICIANDO
2025-06-19 00:02:55 - SP_PRE_LOG_TRX: ERROR: Error en SP_PRE_LOG_TRX: Query interrupted
2025-06-19 00:02:55 - PIPELINE_COMPLETO: ERROR: Query interrupted
2025-06-19 00:03:28 - SP_PRE_LOG_TRX: INICIANDO
2025-06-19 00:05:05 - SP_PRE_LOG_TRX: COMPLETADO
2025-06-19 00:05:05 - SP_LOG_TRX: INICIANDO
2025-06-19 00:05:05 - SP_LOG_TRX: COMPLETADO
2025-06-19 00:05:05 - EXTRACCION_CSV: INICIANDO
2025-06-19 00:05:05 - EXTRACCION_CSV: COMPLETADO
2025-06-19 00:05:05 - PIPELINE_COMPLETO: EXITOSO
2025-06-19 00:06:12 - SP_PRE_LOG_TRX: INICIANDO
2025-06-19 00:11:24 - SP_PRE_LOG_TRX: COMPLETADO
2025-06-19 00:11:24 - SP_LOG_TRX: INICIANDO
2025-06-19 00:11:25 - SP_LOG_TRX: COMPLETADO
2025-06-19 00:11:25 - EXTRACCION_CSV: INICIANDO
2025-06-19 00:11:25 - EXTRACCION_CSV: COMPLETADO
2025-06-19 00:11:25 - PIPELINE_COMPLETO: EXITOSO
2025-06-19 00:31:18 - SP_PRE_LOG_TRX: INICIANDO
2025-06-19 00:31:19 - SP_PRE_LOG_TRX: ERROR: Error en SP_PRE_LOG_TRX: IO Error: No files found that match the pattern "s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_HEADER_ORA/year=2025/month=06/day=15/*.parquet"
2025-06-19 00:31:19 - PIPELINE_COMPLETO: ERROR: IO Error: No files found that match the pattern "s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_HEADER_ORA/year=2025/month=06/day=15/*.parquet"
2025-06-19 00:32:37 - SP_PRE_LOG_TRX: INICIANDO
2025-06-19 00:33:54 - SP_PRE_LOG_TRX: COMPLETADO
2025-06-19 00:33:54 - SP_LOG_TRX: INICIANDO
2025-06-19 00:33:54 - SP_LOG_TRX: COMPLETADO
2025-06-19 00:33:54 - EXTRACCION_CSV: INICIANDO
2025-06-19 00:33:54 - EXTRACCION_CSV: COMPLETADO
2025-06-19 00:33:54 - PIPELINE_COMPLETO: EXITOSO
2025-06-19 00:34:06 - SP_PRE_LOG_TRX: INICIANDO
2025-06-19 00:34:08 - SP_PRE_LOG_TRX: ERROR: Error en SP_PRE_LOG_TRX: IO Error: No files found that match the pattern "s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_ITEMS_ORA/2025/01/10/*.parquet"
2025-06-19 00:34:08 - PIPELINE_COMPLETO: ERROR: IO Error: No files found that match the pattern "s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_ITEMS_ORA/2025/01/10/*.parquet"
2025-06-19 00:34:20 - SP_PRE_LOG_TRX: INICIANDO
2025-06-19 00:36:47 - SP_PRE_LOG_TRX: COMPLETADO
2025-06-19 00:36:47 - SP_LOG_TRX: INICIANDO
2025-06-19 00:36:48 - SP_LOG_TRX: COMPLETADO
2025-06-19 00:36:48 - EXTRACCION_CSV: INICIANDO
2025-06-19 00:36:48 - EXTRACCION_CSV: COMPLETADO
2025-06-19 00:36:48 - PIPELINE_COMPLETO: EXITOSO
2025-06-19 00:43:41 - SP_PRE_LOG_TRX: INICIANDO
2025-06-19 00:44:57 - SP_PRE_LOG_TRX: COMPLETADO
2025-06-19 00:44:57 - SP_LOG_TRX: INICIANDO
2025-06-19 00:44:57 - SP_LOG_TRX: COMPLETADO
2025-06-19 00:44:57 - EXTRACCION_CSV: INICIANDO
2025-06-19 00:44:58 - EXTRACCION_CSV: COMPLETADO
2025-06-19 00:44:58 - PIPELINE_COMPLETO: EXITOSO
2025-06-19 15:13:27 - SP_PRE_LOG_TRX: INICIANDO
2025-06-19 15:18:29 - SP_PRE_LOG_TRX: ERROR: Error en SP_PRE_LOG_TRX: Query interrupted
2025-06-19 15:18:29 - PIPELINE_COMPLETO: ERROR: Query interrupted
2025-06-19 15:23:07 - SP_PRE_LOG_TRX: INICIANDO
2025-06-19 15:23:10 - SP_PRE_LOG_TRX: ERROR: Error en SP_PRE_LOG_TRX: Query interrupted
2025-06-19 15:23:10 - PIPELINE_COMPLETO: ERROR: Query interrupted
2025-06-19 15:26:06 - SP_PRE_LOG_TRX: INICIANDO
2025-06-19 15:27:24 - SP_PRE_LOG_TRX: COMPLETADO
2025-06-19 15:27:24 - SP_LOG_TRX: INICIANDO
2025-06-19 15:27:24 - SP_LOG_TRX: COMPLETADO
2025-06-19 15:27:24 - EXTRACCION_CSV: INICIANDO
2025-06-19 15:27:24 - EXTRACCION_CSV: COMPLETADO
2025-06-19 15:27:24 - PIPELINE_COMPLETO: EXITOSO
2025-06-19 15:28:25 - SP_PRE_LOG_TRX: INICIANDO
2025-06-19 15:28:52 - SP_PRE_LOG_TRX: ERROR: Error en SP_PRE_LOG_TRX: Query interrupted
2025-06-19 15:28:52 - PIPELINE_COMPLETO: ERROR: Query interrupted
2025-06-19 20:16:04 - SP_PRE_LOG_TRX: INICIANDO
2025-06-19 20:20:56 - SP_PRE_LOG_TRX: COMPLETADO
2025-06-19 20:20:56 - SP_LOG_TRX: INICIANDO
2025-06-19 20:20:56 - SP_LOG_TRX: COMPLETADO
2025-06-19 20:20:56 - EXTRACCION_CSV: INICIANDO
2025-06-19 20:20:57 - EXTRACCION_CSV: COMPLETADO
2025-06-19 20:20:57 - PIPELINE_COMPLETO: EXITOSO
2025-06-19 20:34:33 - SP_PRE_LOG_TRX: INICIANDO
2025-06-19 20:34:37 - SP_PRE_LOG_TRX: ERROR: Error en SP_PRE_LOG_TRX: Query interrupted
2025-06-19 20:34:37 - PIPELINE_COMPLETO: ERROR: Query interrupted
2025-06-19 20:34:44 - SP_PRE_LOG_TRX: INICIANDO
2025-06-19 20:42:41 - SP_PRE_LOG_TRX: COMPLETADO
2025-06-19 20:42:41 - SP_LOG_TRX: INICIANDO
2025-06-19 20:42:41 - SP_LOG_TRX: COMPLETADO
2025-06-19 20:42:41 - EXTRACCION_CSV: INICIANDO
2025-06-19 20:42:41 - EXTRACCION_CSV: COMPLETADO
2025-06-19 20:42:41 - PIPELINE_COMPLETO: EXITOSO
2025-06-19 21:18:25 - SP_PRE_LOG_TRX: INICIANDO
2025-06-19 21:23:40 - SP_PRE_LOG_TRX: COMPLETADO
2025-06-19 21:23:40 - SP_LOG_TRX: INICIANDO
2025-06-19 21:23:41 - SP_LOG_TRX: COMPLETADO
2025-06-19 21:23:41 - EXTRACCION_CSV: INICIANDO
2025-06-19 21:23:41 - EXTRACCION_CSV: COMPLETADO
2025-06-19 21:23:41 - PIPELINE_COMPLETO: EXITOSO
