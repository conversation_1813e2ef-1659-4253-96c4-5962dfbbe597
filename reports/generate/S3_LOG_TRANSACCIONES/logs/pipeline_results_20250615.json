{"fecha": "2025-06-15", "inicio": "2025-06-18T19:47:06.186789", "etapas": {"SP_PRE_LOG_TRX": {"estado": "COMPLETADO", "duracion_segundos": 98.497818, "archivo_generado": "TEMP_LOGS_TRANSACCIONES/20250615/PRE_LOG_TRX.parquet"}, "SP_LOG_TRX": {"estado": "COMPLETADO", "duracion_segundos": 0.346287, "archivo_generado": "TEMP_LOGS_TRANSACCIONES/20250615/LOG_TRX_FINAL.parquet"}, "EXTRACCION_CSV": {"estado": "COMPLETADO", "duracion_segundos": 0.121953, "archivo_generado": "output/TR-20250615.csv"}}, "archivos_generados": ["TEMP_LOGS_TRANSACCIONES/20250615/PRE_LOG_TRX.parquet", "TEMP_LOGS_TRANSACCIONES/20250615/LOG_TRX_FINAL.parquet", "output/TR-20250615.csv"], "errores": [], "fin": "2025-06-18T19:48:45.161468", "duracion_total_segundos": 98.974679, "estado_final": "EXITOSO"}