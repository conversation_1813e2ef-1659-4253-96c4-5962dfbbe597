{"fecha": "2025-06-18", "inicio": "2025-06-19T21:18:25.788150", "etapas": {"SP_PRE_LOG_TRX": {"estado": "COMPLETADO", "duracion_segundos": 315.037203, "archivo_generado": "TEMP_LOGS_TRANSACCIONES/20250618/PRE_LOG_TRX.parquet"}, "SP_LOG_TRX": {"estado": "COMPLETADO", "duracion_segundos": 0.35743, "archivo_generado": "TEMP_LOGS_TRANSACCIONES/20250618/LOG_TRX_FINAL.parquet"}, "EXTRACCION_CSV": {"estado": "COMPLETADO", "duracion_segundos": 0.175017, "archivo_generado": "output/TR-20250618.csv"}}, "archivos_generados": ["TEMP_LOGS_TRANSACCIONES/20250618/PRE_LOG_TRX.parquet", "TEMP_LOGS_TRANSACCIONES/20250618/LOG_TRX_FINAL.parquet", "output/TR-20250618.csv"], "errores": [], "fin": "2025-06-19T21:23:41.367452", "duracion_total_segundos": 315.579302, "estado_final": "EXITOSO"}