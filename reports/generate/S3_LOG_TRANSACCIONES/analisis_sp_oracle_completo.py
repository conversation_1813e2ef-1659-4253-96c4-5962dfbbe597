#!/usr/bin/env python3
"""
Análisis Exhaustivo del SP_PRE_LOG_TRX Original de Oracle
Comparación línea por línea con nuestro pipeline para identificar diferencias exactas
"""

import oracledb
import duckdb
import pandas as pd
import sys
from datetime import datetime

class AnalisisSPOracleCompleto:
    def __init__(self):
        self.fecha = '2025-06-15'
        self.setup_connections()
        
    def setup_connections(self):
        """Configura conexiones"""
        try:
            # Oracle
            self.oracle_conn = oracledb.connect(
                user='usr_datalake',
                password='U2024b1mD4t4l4k5',
                dsn='*************:1521/MMONEY'
            )
            
            # DuckDB
            self.duck_conn = duckdb.connect()
            self.duck_conn.sql("INSTALL httpfs;")
            self.duck_conn.sql("LOAD httpfs;")
            
            print("✅ Conexiones establecidas")
            
        except Exception as e:
            print(f"❌ Error en conexiones: {e}")
            raise

    def analyze_oracle_filters_step_by_step(self):
        """Analiza cada filtro del SP_PRE_LOG_TRX paso a paso"""
        print("🔍 ANÁLISIS PASO A PASO DE FILTROS ORACLE")
        print("=" * 80)
        
        cursor = self.oracle_conn.cursor()
        
        # 1. TRX_HEADER - Filtros iniciales (líneas 36-40)
        print("📊 PASO 1: TRX_HEADER - Filtros iniciales")
        print("-" * 50)
        
        # Conteo total sin filtros
        cursor.execute(f"""
            SELECT COUNT(*)
            FROM PDP_PROD10_MAINDBBUS.MTX_TRANSACTION_HEADER MTH
            WHERE MTH.TRANSFER_DATE >= TO_DATE('{self.fecha}', 'YYYY-MM-DD HH24:MI:SS')
            AND MTH.TRANSFER_DATE < TO_DATE('{self.fecha}', 'YYYY-MM-DD HH24:MI:SS')+1
        """)
        total_fecha = cursor.fetchone()[0]
        print(f"   Total con filtro fecha: {total_fecha:,}")
        
        # Con filtro TRANSFER_STATUS
        cursor.execute(f"""
            SELECT COUNT(*)
            FROM PDP_PROD10_MAINDBBUS.MTX_TRANSACTION_HEADER MTH
            WHERE MTH.TRANSFER_DATE >= TO_DATE('{self.fecha}', 'YYYY-MM-DD HH24:MI:SS')
            AND MTH.TRANSFER_DATE < TO_DATE('{self.fecha}', 'YYYY-MM-DD HH24:MI:SS')+1
            AND MTH.TRANSFER_STATUS IN ('TA','TS')
        """)
        con_status = cursor.fetchone()[0]
        print(f"   + TRANSFER_STATUS IN ('TA','TS'): {con_status:,} (-{total_fecha - con_status})")
        
        # Con filtro TRANSFER_VALUE
        cursor.execute(f"""
            SELECT COUNT(*)
            FROM PDP_PROD10_MAINDBBUS.MTX_TRANSACTION_HEADER MTH
            WHERE MTH.TRANSFER_DATE >= TO_DATE('{self.fecha}', 'YYYY-MM-DD HH24:MI:SS')
            AND MTH.TRANSFER_DATE < TO_DATE('{self.fecha}', 'YYYY-MM-DD HH24:MI:SS')+1
            AND MTH.TRANSFER_STATUS IN ('TA','TS')
            AND MTH.TRANSFER_VALUE <> 0
        """)
        con_value = cursor.fetchone()[0]
        print(f"   + TRANSFER_VALUE <> 0: {con_value:,} (-{con_status - con_value})")
        
        # Con filtro IS_FINANCIAL (línea 40) - CRÍTICO
        cursor.execute(f"""
            SELECT COUNT(*)
            FROM PDP_PROD10_MAINDBBUS.MTX_TRANSACTION_HEADER MTH
            LEFT JOIN PDP_PROD10_MAINDB.SYS_SERVICE_TYPES SST ON MTH.SERVICE_TYPE = SST.SERVICE_TYPE 
            WHERE MTH.TRANSFER_DATE >= TO_DATE('{self.fecha}', 'YYYY-MM-DD HH24:MI:SS')
            AND MTH.TRANSFER_DATE < TO_DATE('{self.fecha}', 'YYYY-MM-DD HH24:MI:SS')+1
            AND MTH.TRANSFER_STATUS IN ('TA','TS')
            AND MTH.TRANSFER_VALUE <> 0
            AND SST.IS_FINANCIAL = 'Y'
        """)
        con_financial = cursor.fetchone()[0]
        print(f"   + SST.IS_FINANCIAL = 'Y': {con_financial:,} (-{con_value - con_financial})")
        print(f"   🎯 TRX_HEADER final: {con_financial:,}")
        
        return con_financial

    def analyze_final_filter_trx_service(self, trx_header_count):
        """Analiza el filtro final TRX_SERVICE (línea 253)"""
        print(f"\n📊 PASO 2: Filtro final TRX_SERVICE")
        print("-" * 50)
        
        cursor = self.oracle_conn.cursor()
        
        # Conteo antes del filtro final
        cursor.execute(f"""
            WITH
            TRX_HEADER AS (
                SELECT MTH.*,
                    REPLACE(MTH.PAYEE_IDENTIFIER_VALUE,'1','') AS NEW_PAYEE_IDENTIFIER_VALUE,
                    REPLACE(MTH.PAYER_IDENTIFIER_VALUE,'1','') AS NEW_PAYER_IDENTIFIER_VALUE
                FROM PDP_PROD10_MAINDBBUS.MTX_TRANSACTION_HEADER MTH
                LEFT JOIN PDP_PROD10_MAINDB.SYS_SERVICE_TYPES SST ON MTH.SERVICE_TYPE = SST.SERVICE_TYPE 
                WHERE MTH.TRANSFER_DATE >= TO_DATE('{self.fecha}', 'YYYY-MM-DD HH24:MI:SS')
                AND MTH.TRANSFER_DATE < TO_DATE('{self.fecha}', 'YYYY-MM-DD HH24:MI:SS')+1
                AND MTH.TRANSFER_STATUS IN ('TA','TS')
                AND MTH.TRANSFER_VALUE <> 0
                AND SST.IS_FINANCIAL = 'Y'
            ),
            TRX_ITEMS AS (
                SELECT MTI.TRANSFER_ID, MTI.TRANSFER_VALUE,MTI.WALLET_NUMBER, MTI.SECOND_PARTY_WALLET_NUMBER, MTI.SERVICE_TYPE,
                    MTI.ISSUER_ID, MTI.SECOND_PARTY_ISSUER_ID,MTI.SECOND_PARTY_MARKETING_PROFILE_CODE, MTI.TRANSACTION_TYPE
                FROM PDP_PROD10_MAINDBBUS.MTX_TRANSACTION_ITEMS MTI
                INNER JOIN TRX_HEADER MTH ON MTI.TRANSFER_ID = MTH.TRANSFER_ID
            ),
            USER_DATA AS (
                SELECT USER_ID, O_USER_ID AS M_USER_ID, PROFILE_TRX AS PROFILE, WALLET_NUMBER AS ACCOUNT_ID,
                    MSISDN, USER_CODE, LOGIN_ID, WORKSPACE_ID, ISSUER_CODE, ID_TYPE
                FROM USR_DATALAKE.USER_DATA_TRX
            ),
            MTI_SCP AS (
                SELECT MTI.TRANSFER_ID, MTI.TRANSFER_VALUE
                FROM TRX_ITEMS MTI
                WHERE MTI.TRANSACTION_TYPE = 'SCP'
            ),
            WALLETS_GRADE AS (
                SELECT MW.ISSUER_ID,MW.WALLET_NUMBER, CG.GRADE_NAME
                FROM PDP_PROD10_MAINDBBUS.MTX_WALLET MW 
                INNER JOIN PDP_PROD10_MAINDB.CHANNEL_GRADES CG ON MW.USER_GRADE = CG.GRADE_CODE
            ),
            MTI_MP AS (
                SELECT MTI.TRANSFER_ID, MTI.WALLET_NUMBER, MTI.SECOND_PARTY_WALLET_NUMBER, MTI.ISSUER_ID, MTI.SECOND_PARTY_ISSUER_ID, 
                    WG.GRADE_NAME AS GRADE, WG2.GRADE_NAME AS SECOND_GRADE
                FROM TRX_ITEMS MTI
                LEFT JOIN WALLETS_GRADE WG ON MTI.WALLET_NUMBER = WG.WALLET_NUMBER
                LEFT JOIN WALLETS_GRADE WG2 ON MTI.SECOND_PARTY_WALLET_NUMBER = WG2.WALLET_NUMBER
                WHERE MTI.TRANSACTION_TYPE = 'MP'
            ),
            MTI_MR AS (
                SELECT MTI.TRANSFER_ID, MTI.WALLET_NUMBER, MTI.ISSUER_ID, 
                    CASE 
                        WHEN MC.CATEGORY_NAME = 'Final User' THEN 'USUARIO FINAL'
                        WHEN MC.CATEGORY_NAME = 'BIMER User' THEN 'BIMER'
                    END AS PAYER_CATEGORY_CODE
                FROM TRX_ITEMS MTI
                INNER JOIN PDP_PROD10_MAINDB.MARKETING_PROFILE MP ON MTI.SECOND_PARTY_MARKETING_PROFILE_CODE = MP.MARKETING_PROFILE_CODE
                INNER JOIN PDP_PROD10_MAINDB.MTX_CATEGORIES MC ON MP.CATEGORY_CODE = MC.CATEGORY_CODE
                WHERE MTI.TRANSACTION_TYPE = 'MR'
                AND MTI.SERVICE_TYPE IN ('ISSUERMOV','CHANGECAT')
            ),
            REVERSAL AS (
                SELECT MTH.TRANSFER_ID, MTH.FIELD7 
                FROM TRX_HEADER mth
            ),
            TRX_DATA_DAY AS (
                SELECT MTH.FIELD7, MTH.TRANSFER_ID, MTH.FTXN_ID, MTH.SOURCE,
                    CASE WHEN MTH.PAYER_USER_ID = 'IND012' THEN MTH.PAYER_IDENTIFIER_VALUE ELSE MTH.PAYER_USER_ID END AS PAYER_USER_ID,
                    MTH.PAYEE_USER_ID, MTH.NEW_PAYEE_IDENTIFIER_VALUE AS PAYEE_IDENTIFIER_VALUE,
                    MTH.NEW_PAYER_IDENTIFIER_VALUE AS PAYER_IDENTIFIER_VALUE, MTH.CREATED_BY, MTH.MODIFIED_BY,
                    MTH.TRANSFER_DATE, MTH.TRANSFER_STATUS, MTH.TRANSFER_VALUE, MSC.TRANSFER_VALUE AS FEE,
                    MR.PAYER_CATEGORY_CODE, MTH.REQUEST_GATEWAY_TYPE AS CANAL,
                    CASE 
                        WHEN MTH.NEW_PAYEE_IDENTIFIER_VALUE IN ('claro','sentinel') THEN JSON_VALUE(MTH.PARTNER_DATA , '$.codigoPago')
                        WHEN MTH.NEW_PAYEE_IDENTIFIER_VALUE = 'bitel' THEN SUBSTR(FIELD8,1,INSTR(FIELD8,'@')-1)
                        WHEN MTH.NEW_PAYEE_IDENTIFIER_VALUE = 'unique' THEN SUBSTR(MTH.REMARKS,1,INSTR(MTH.REMARKS,'_')-1)
                        ELSE MTH.REMARKS
                    END AS REMARKS,
                    MTH.REQUEST_GATEWAY_TYPE, MP.WALLET_NUMBER AS PAYER_WALLET_NUMBER,
                    CASE 
                        WHEN MTH.SERVICE_TYPE IN ('ISSUERMOV','CHANGECAT') THEN MR.WALLET_NUMBER
                        ELSE MP.SECOND_PARTY_WALLET_NUMBER 
                    END AS PAYEE_WALLET_NUMBER,
                    UPPER(MP.GRADE) AS PAYER_GRADE, UPPER(MP.SECOND_GRADE) AS PAYEE_GRADE,
                    ID1.ISSUER_CODE AS PAYER_ISSUER_CODE, ID2.ISSUER_CODE AS PAYEE_ISSUER_CODE,
                    MTH.PAYER_PROVIDER_ID, MTH.RECONCILIATION_BY, MTH.FIELD2,
                    CASE
                        WHEN mth.service_type='CASHIN' AND mth.MODIFIED_BY <> 'IND012' THEN 'CASH_IN' 
                        WHEN mth.service_type='CASHOUT' THEN 'CASH_OUT' 
                        WHEN mth.service_type='P2P' THEN 'TRANSFER' 
                        WHEN (mth.service_type='BILLPAY' AND (mth.PAYEE_IDENTIFIER_VALUE = 'crandes' OR (mth.payer_user_id in ('466787','580943','1597312','1885838','US.****************','US.****************','US.****************') or mth.payee_user_id in ('466787','580943','1597312','1885838','US.****************','US.****************','US.****************')))) THEN 'PAYMENT' 
                        WHEN (mth.service_type='BILLPAY' AND (mth.PAYEE_IDENTIFIER_VALUE <> 'crandes' OR (mth.payer_user_id not in ('466787','580943','1597312','1885838','US.****************','US.****************','US.****************') and mth.payee_user_id not in ('466787','580943','1597312','1885838','US.****************','US.****************','US.****************')))) THEN 'EXTERNAL_PAYMENT' 
                        WHEN mth.service_type='OFFUSOUT' THEN 'PAYMENT' 
                        WHEN mth.service_type in ('STOCKCRT','STOCKINT','STOCK') THEN 'DEPOSIT' 
                        WHEN mth.service_type in ('C2C','INVC2C') THEN 'FLOAT_TRANSFER' 
                        WHEN mth.service_type in ('MULTIDRCR2') OR (MTH.SERVICE_TYPE = 'CASHIN' AND MTH.MODIFIED_BY = 'IND012') THEN 'BATCH_TRANSFER' 
                        WHEN mth.service_type in ('OPTW') THEN 'TRANSFER_TO_ANY_BANK_ACCOUNT' 
                        WHEN mth.service_type in ('STOCKTFR') THEN 'CUSTODY_ACCOUNTS_TRANSFER' 
                        WHEN mth.service_type in ('ATMCASHOUT') THEN 'CASH_OUT_ATM' 
                        WHEN mth.service_type in ('ISSUERMOV','CHANGECAT') THEN 'ADJUSTMENT'
                        WHEN mth.service_type in ('TXNCORRECT') and mth.ATTR_3_NAME = 'BULK_PAYMENT_BATCHID' THEN 'REVERSAL_BATCH_TRANSFER'
                        WHEN mth.service_type in ('TXNCORRECT') and (mth.ATTR_3_VALUE IN ('CASHOUT','CASHIN','ATMCASHOUT') OR MTH.ATTR_3_VALUE IS NULL) THEN 'REVERSAL'
                        WHEN mth.service_type in ('TXNCORRECT') and mth.ATTR_3_VALUE='BILLPAY' THEN CASE 
                            WHEN (mth.PAYEE_IDENTIFIER_VALUE = 'crandes' OR (mth.payer_user_id in ('466787','580943','1597312','1885838','US.****************','US.****************','US.****************') or mth.payee_user_id in ('466787','580943','1597312','1885838','US.****************','US.****************','US.****************'))) THEN 'TRANSFER' 
                            ELSE 'REFUND' 
                            END
                        else mth.service_type
                    END AS TRX_SERVICE
                FROM TRX_HEADER MTH
                LEFT JOIN MTI_MP MP ON MTH.TRANSFER_ID = MP.TRANSFER_ID
                LEFT JOIN MTI_MR MR ON MTH.TRANSFER_ID = MR.TRANSFER_ID
                LEFT JOIN MTI_SCP MSC ON MTH.TRANSFER_ID = MSC.TRANSFER_ID
                LEFT JOIN PDP_PROD10_MAINDB.ISSUER_DETAILS ID1 ON MP.ISSUER_ID = ID1.ISSUER_ID
                LEFT JOIN PDP_PROD10_MAINDB.ISSUER_DETAILS ID2 ON MP.SECOND_PARTY_ISSUER_ID = ID2.ISSUER_ID
            )
            SELECT COUNT(*)
            FROM TRX_DATA_DAY MTH
            LEFT JOIN USER_DATA UPAYER ON MTH.PAYER_USER_ID = UPAYER.M_USER_ID AND UPAYER.M_USER_ID IS NOT NULL
            LEFT JOIN USER_DATA UPAYEE ON MTH.PAYEE_USER_ID = UPAYEE.M_USER_ID AND UPAYEE.M_USER_ID IS NOT NULL
            LEFT JOIN PDP_PROD10_MAINDB.SYS_SERVICE_PROVIDER SSP ON MTH.PAYER_PROVIDER_ID = SSP.PROVIDER_ID
            LEFT JOIN REVERSAL TP ON MTH.RECONCILIATION_BY = TP.TRANSFER_ID AND MTH.TRX_SERVICE IN ('REVERSAL','REFUND','REVERSAL_BATCH_TRANSFER')
            WHERE 1=1
        """)
        
        antes_filtro_final = cursor.fetchone()[0]
        print(f"   Antes filtro TRX_SERVICE: {antes_filtro_final:,}")
        
        # Con filtro final TRX_SERVICE
        cursor.execute(f"""
            WITH
            TRX_HEADER AS (
                SELECT MTH.*,
                    REPLACE(MTH.PAYEE_IDENTIFIER_VALUE,'1','') AS NEW_PAYEE_IDENTIFIER_VALUE,
                    REPLACE(MTH.PAYER_IDENTIFIER_VALUE,'1','') AS NEW_PAYER_IDENTIFIER_VALUE
                FROM PDP_PROD10_MAINDBBUS.MTX_TRANSACTION_HEADER MTH
                LEFT JOIN PDP_PROD10_MAINDB.SYS_SERVICE_TYPES SST ON MTH.SERVICE_TYPE = SST.SERVICE_TYPE 
                WHERE MTH.TRANSFER_DATE >= TO_DATE('{self.fecha}', 'YYYY-MM-DD HH24:MI:SS')
                AND MTH.TRANSFER_DATE < TO_DATE('{self.fecha}', 'YYYY-MM-DD HH24:MI:SS')+1
                AND MTH.TRANSFER_STATUS IN ('TA','TS')
                AND MTH.TRANSFER_VALUE <> 0
                AND SST.IS_FINANCIAL = 'Y'
            )
            SELECT COUNT(*)
            FROM TRX_HEADER MTH
            WHERE MTH.SERVICE_TYPE NOT IN ('MULTIDRCR','FTBOA','MERCHPAY','TXNCORRECT','MULTIDRCR3')
        """)
        
        despues_filtro_final = cursor.fetchone()[0]
        print(f"   + NOT IN ('MULTIDRCR','FTBOA','MERCHPAY','TXNCORRECT','MULTIDRCR3'): {despues_filtro_final:,} (-{antes_filtro_final - despues_filtro_final})")
        
        # Verificar qué SERVICE_TYPE se están excluyendo
        cursor.execute(f"""
            SELECT MTH.SERVICE_TYPE, COUNT(*) as count
            FROM PDP_PROD10_MAINDBBUS.MTX_TRANSACTION_HEADER MTH
            LEFT JOIN PDP_PROD10_MAINDB.SYS_SERVICE_TYPES SST ON MTH.SERVICE_TYPE = SST.SERVICE_TYPE 
            WHERE MTH.TRANSFER_DATE >= TO_DATE('{self.fecha}', 'YYYY-MM-DD HH24:MI:SS')
            AND MTH.TRANSFER_DATE < TO_DATE('{self.fecha}', 'YYYY-MM-DD HH24:MI:SS')+1
            AND MTH.TRANSFER_STATUS IN ('TA','TS')
            AND MTH.TRANSFER_VALUE <> 0
            AND SST.IS_FINANCIAL = 'Y'
            AND MTH.SERVICE_TYPE IN ('MULTIDRCR','FTBOA','MERCHPAY','TXNCORRECT','MULTIDRCR3')
            GROUP BY MTH.SERVICE_TYPE
            ORDER BY count DESC
        """)
        
        excluded_services = cursor.fetchall()
        if excluded_services:
            print(f"   📋 SERVICE_TYPE excluidos:")
            for service, count in excluded_services:
                print(f"      {service}: {count:,} registros")
        
        cursor.close()
        return despues_filtro_final

    def compare_with_our_pipeline(self):
        """Compara con nuestro pipeline"""
        print(f"\n📊 PASO 3: Comparación con nuestro pipeline")
        print("-" * 50)
        
        parquet_path = f"TEMP_LOGS_TRANSACCIONES/20250615/PRE_LOG_TRX.parquet"
        
        # Conteo nuestro pipeline
        our_count = self.duck_conn.execute(f"""
            SELECT COUNT(*)
            FROM read_parquet('{parquet_path}')
            WHERE CAST("TransferDate" AS DATE) = CAST('{self.fecha}' AS DATE)
        """).fetchone()[0]
        
        print(f"   Nuestro pipeline: {our_count:,}")
        print(f"   Oracle final: 104,116")
        print(f"   Diferencia: {our_count - 104116}")
        
        return our_count

    def identify_missing_filters(self):
        """Identifica filtros faltantes específicos"""
        print(f"\n🔍 IDENTIFICANDO FILTROS FALTANTES")
        print("=" * 80)
        
        # Verificar si aplicamos el filtro IS_FINANCIAL
        print("📊 Verificando filtro IS_FINANCIAL en nuestro pipeline...")
        
        # Revisar nuestro código para ver si aplicamos IS_FINANCIAL
        with open('pipeline_log_transacciones_duckdb.py', 'r') as f:
            pipeline_code = f.read()
            
        if 'IS_FINANCIAL' in pipeline_code:
            print("✅ IS_FINANCIAL está en nuestro código")
        else:
            print("❌ IS_FINANCIAL NO está en nuestro código - FILTRO FALTANTE CRÍTICO")
        
        # Verificar filtro final TRX_SERVICE
        if "NOT IN ('MULTIDRCR','FTBOA','MERCHPAY','TXNCORRECT','MULTIDRCR3')" in pipeline_code:
            print("✅ Filtro final TRX_SERVICE está en nuestro código")
        else:
            print("❌ Filtro final TRX_SERVICE NO está en nuestro código - FILTRO FALTANTE")

    def run_complete_analysis(self):
        """Ejecuta el análisis completo"""
        print("🕵️ ANÁLISIS EXHAUSTIVO SP_PRE_LOG_TRX ORACLE")
        print("=" * 80)
        print(f"📅 Fecha: {self.fecha}")
        print(f"🎯 Objetivo: Identificar exactamente qué filtros faltan")
        
        try:
            # 1. Analizar filtros paso a paso
            trx_header_final = self.analyze_oracle_filters_step_by_step()
            
            # 2. Analizar filtro final
            oracle_final = self.analyze_final_filter_trx_service(trx_header_final)
            
            # 3. Comparar con nuestro pipeline
            our_count = self.compare_with_our_pipeline()
            
            # 4. Identificar filtros faltantes
            self.identify_missing_filters()
            
            # 5. Conclusión
            print(f"\n{'='*20} CONCLUSIÓN ANÁLISIS {'='*20}")
            print(f"🎯 Oracle final: {oracle_final:,}")
            print(f"🎯 Nuestro pipeline: {our_count:,}")
            print(f"🎯 Diferencia: {our_count - oracle_final}")
            
            if our_count == oracle_final:
                print("✅ ¡Homologación perfecta lograda!")
                return True
            else:
                print("❌ Se requieren correcciones en filtros")
                return False
            
        except Exception as e:
            print(f"❌ Error en análisis: {e}")
            raise
        
        finally:
            if hasattr(self, 'oracle_conn'):
                self.oracle_conn.close()
            if hasattr(self, 'duck_conn'):
                self.duck_conn.close()

def main():
    """Función principal"""
    try:
        analizador = AnalisisSPOracleCompleto()
        success = analizador.run_complete_analysis()
        
        sys.exit(0 if success else 1)
        
    except Exception as e:
        print(f"❌ Error crítico: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
