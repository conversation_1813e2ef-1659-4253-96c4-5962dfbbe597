#!/usr/bin/env python3
"""
Homologación exhaustiva de To_AccountID_Mobiquity
Verificar si la lógica implementada coincide 100% con Oracle
"""

import oracledb
import duckdb
import boto3
import sys
from collections import defaultdict

def homologar_to_accountid_mobiquity():
    """Homologa To_AccountID_Mobiquity exhaustivamente"""
    print("🔍 HOMOLOGACIÓN EXHAUSTIVA: To_AccountID_Mobiquity")
    print("=" * 80)
    
    parquet_path = '/home/<USER>/REPORTE_2025/reports/generate/S3_LOG_TRANSACCIONES/TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet'
    fecha = '2025-06-15'
    
    try:
        # 1. ANÁLISIS COMPLETO DE ORACLE
        print("1️⃣ ANÁLISIS COMPLETO DE ORACLE To_AccountID_Mobiquity:")
        print("-" * 60)
        
        connection = oracledb.connect(
            user='usr_datalake',
            password='U2024b1mD4t4l4k5',
            dsn='10.240.131.10:1521/MMONEY'
        )
        
        cursor = connection.cursor()
        
        # Obtener estadísticas de Oracle
        cursor.execute("""
            SELECT 
                "To_AccountID_Mobiquity",
                "ToID_Mobiquity",
                "To_Profile",
                "TransactionType",
                COUNT(*) as CASOS
            FROM USR_DATALAKE.PRE_LOG_TRX 
            WHERE TRUNC("TransferDate") = TO_DATE(:fecha, 'YYYY-MM-DD')
            GROUP BY "To_AccountID_Mobiquity", "ToID_Mobiquity", "To_Profile", "TransactionType"
            ORDER BY COUNT(*) DESC
        """, {'fecha': fecha})
        
        oracle_data = cursor.fetchall()
        
        print(f"✅ Oracle - Combinaciones To_AccountID encontradas: {len(oracle_data)}")
        print(f"\n📊 TOP 15 COMBINACIONES Oracle:")
        print(f"{'To_AccountID_Mobiquity':<25} {'ToID_Mobiquity':<20} {'To_Profile':<35} {'TransactionType':<20} {'CASOS'}")
        print("-" * 120)
        
        oracle_account_map = {}
        oracle_account_stats = defaultdict(int)
        oracle_total = 0
        
        for i, row in enumerate(oracle_data):
            to_account_id = row[0]
            to_id = row[1]
            to_profile = row[2]
            transaction_type = row[3]
            casos = row[4]
            
            key = (to_account_id, to_id, to_profile, transaction_type)
            oracle_account_map[key] = casos
            oracle_account_stats[to_account_id] += casos
            oracle_total += casos
            
            if i < 15:  # Mostrar top 15
                print(f"{str(to_account_id):<25} {str(to_id):<20} {to_profile:<35} {transaction_type:<20} {casos}")
        
        print(f"\n📈 RESUMEN Oracle:")
        print(f"  Total registros: {oracle_total:,}")
        print(f"  To_AccountID únicos: {len(oracle_account_stats)}")
        print(f"  Combinaciones únicas: {len(oracle_account_map)}")
        
        # Análisis de patrones específicos en Oracle
        print(f"\n🔍 ANÁLISIS DE PATRONES ESPECÍFICOS Oracle:")
        
        # Casos como 501101120105574549
        long_accounts_oracle = [acc for acc in oracle_account_stats.keys() if acc and len(str(acc)) > 15]
        print(f"  Cuentas largas (>15 dígitos): {len(long_accounts_oracle)}")
        if long_accounts_oracle:
            for acc in sorted(long_accounts_oracle)[:5]:
                print(f"    {acc} ({oracle_account_stats[acc]} casos)")
        
        cursor.close()
        connection.close()
        
        # 2. ANÁLISIS COMPLETO DE S3
        print(f"\n2️⃣ ANÁLISIS COMPLETO DE S3 To_AccountID_Mobiquity:")
        print("-" * 60)
        
        # Configurar DuckDB con S3
        conn = duckdb.connect()
        
        session = boto3.Session()
        credentials = session.get_credentials().get_frozen_credentials()
        
        conn.sql("INSTALL httpfs;")
        conn.sql("LOAD httpfs;")
        conn.sql("SET s3_region='us-east-1';")
        conn.sql("SET s3_use_ssl=true;")
        conn.sql("SET s3_url_style='path';")
        conn.sql(f"SET s3_access_key_id='{credentials.access_key}';")
        conn.sql(f"SET s3_secret_access_key='{credentials.secret_key}';")
        if credentials.token:
            conn.sql(f"SET s3_session_token='{credentials.token}';")
        
        # Obtener estadísticas de S3
        s3_data = conn.execute(f"""
            SELECT 
                "To_AccountID_Mobiquity",
                "ToID_Mobiquity",
                "To_Profile",
                "TransactionType",
                COUNT(*) as CASOS
            FROM read_parquet('{parquet_path}')
            GROUP BY "To_AccountID_Mobiquity", "ToID_Mobiquity", "To_Profile", "TransactionType"
            ORDER BY COUNT(*) DESC
        """).fetchall()
        
        print(f"✅ S3 - Combinaciones To_AccountID encontradas: {len(s3_data)}")
        print(f"\n📊 TOP 15 COMBINACIONES S3:")
        print(f"{'To_AccountID_Mobiquity':<25} {'ToID_Mobiquity':<20} {'To_Profile':<35} {'TransactionType':<20} {'CASOS'}")
        print("-" * 120)
        
        s3_account_map = {}
        s3_account_stats = defaultdict(int)
        s3_total = 0
        
        for i, row in enumerate(s3_data):
            to_account_id = row[0]
            to_id = row[1]
            to_profile = row[2]
            transaction_type = row[3]
            casos = row[4]
            
            key = (to_account_id, to_id, to_profile, transaction_type)
            s3_account_map[key] = casos
            s3_account_stats[to_account_id] += casos
            s3_total += casos
            
            if i < 15:  # Mostrar top 15
                print(f"{str(to_account_id):<25} {str(to_id):<20} {to_profile:<35} {transaction_type:<20} {casos}")
        
        print(f"\n📈 RESUMEN S3:")
        print(f"  Total registros: {s3_total:,}")
        print(f"  To_AccountID únicos: {len(s3_account_stats)}")
        print(f"  Combinaciones únicas: {len(s3_account_map)}")
        
        # Análisis de patrones específicos en S3
        print(f"\n🔍 ANÁLISIS DE PATRONES ESPECÍFICOS S3:")
        
        # Casos como 501101120105574549
        long_accounts_s3 = [acc for acc in s3_account_stats.keys() if acc and len(str(acc)) > 15]
        print(f"  Cuentas largas (>15 dígitos): {len(long_accounts_s3)}")
        if long_accounts_s3:
            for acc in sorted(long_accounts_s3)[:5]:
                print(f"    {acc} ({s3_account_stats[acc]} casos)")
        
        conn.close()
        
        # 3. COMPARACIÓN DETALLADA
        print(f"\n3️⃣ COMPARACIÓN DETALLADA To_AccountID_Mobiquity:")
        print("-" * 60)
        
        # Comparar cuentas únicas
        all_accounts = set(oracle_account_stats.keys()) | set(s3_account_stats.keys())
        
        coincidencias_cuentas = 0
        diferencias_cuentas = 0
        
        print(f"{'To_AccountID_Mobiquity':<30} {'Oracle':<15} {'S3':<15} {'ESTADO'}")
        print("-" * 75)
        
        # Mostrar solo las primeras 20 para no saturar
        for i, account in enumerate(sorted(all_accounts, key=lambda x: oracle_account_stats.get(x, 0) + s3_account_stats.get(x, 0), reverse=True)):
            if i >= 20:
                break
                
            oracle_count = oracle_account_stats.get(account, 0)
            s3_count = s3_account_stats.get(account, 0)
            
            if oracle_count == s3_count:
                coincidencias_cuentas += 1
                status = "✅ COINCIDE"
            else:
                diferencias_cuentas += 1
                status = "❌ DIFIERE"
            
            account_str = str(account) if account is not None else 'NULL'
            print(f"{account_str:<30} {oracle_count:<15} {s3_count:<15} {status}")
        
        if len(all_accounts) > 20:
            print(f"... y {len(all_accounts) - 20} cuentas más")
        
        # 4. ANÁLISIS DE COMBINACIONES ESPECÍFICAS
        print(f"\n4️⃣ ANÁLISIS DE COMBINACIONES ESPECÍFICAS:")
        print("-" * 60)
        
        combinaciones_coincidentes = 0
        combinaciones_diferentes = 0
        
        all_combinations = set(oracle_account_map.keys()) | set(s3_account_map.keys())
        
        print(f"Total combinaciones únicas: {len(all_combinations)}")
        
        # Mostrar solo diferencias
        diferencias_encontradas = []
        for combination in all_combinations:
            oracle_count = oracle_account_map.get(combination, 0)
            s3_count = s3_account_map.get(combination, 0)
            
            if oracle_count == s3_count:
                combinaciones_coincidentes += 1
            else:
                combinaciones_diferentes += 1
                diferencias_encontradas.append((combination, oracle_count, s3_count))
        
        if diferencias_encontradas:
            print(f"\nCOMBINACIONES CON DIFERENCIAS (Top 10):")
            print(f"{'To_AccountID':<20} {'ToID':<15} {'To_Profile':<25} {'TransactionType':<15} {'Oracle':<10} {'S3':<10}")
            print("-" * 110)
            
            for i, (combination, oracle_count, s3_count) in enumerate(sorted(diferencias_encontradas, key=lambda x: abs(x[1] - x[2]), reverse=True)[:10]):
                to_account_id, to_id, to_profile, transaction_type = combination
                account_str = str(to_account_id) if to_account_id is not None else 'NULL'
                print(f"{account_str:<20} {str(to_id):<15} {to_profile:<25} {transaction_type:<15} {oracle_count:<10} {s3_count:<10}")
        else:
            print("\n✅ NO HAY DIFERENCIAS EN COMBINACIONES")
        
        # 5. VERIFICACIÓN DE CASOS ESPECÍFICOS
        print(f"\n5️⃣ VERIFICACIÓN DE CASOS ESPECÍFICOS:")
        print("-" * 60)
        
        # Verificar los casos mencionados: 501101120105574549, 501101120105560450
        casos_especificos = ['501101120105574549', '501101120105560450']
        
        for caso in casos_especificos:
            oracle_count = oracle_account_stats.get(caso, 0)
            s3_count = s3_account_stats.get(caso, 0)
            
            print(f"  Caso {caso}:")
            print(f"    Oracle: {oracle_count} registros")
            print(f"    S3: {s3_count} registros")
            print(f"    Estado: {'✅ COINCIDE' if oracle_count == s3_count else '❌ DIFIERE'}")
        
        # 6. RESUMEN FINAL
        print(f"\n6️⃣ RESUMEN FINAL:")
        print("-" * 60)
        
        total_cuentas = len(all_accounts)
        porcentaje_cuentas = (coincidencias_cuentas / total_cuentas) * 100 if total_cuentas > 0 else 0
        porcentaje_combinaciones = (combinaciones_coincidentes / len(all_combinations)) * 100 if all_combinations else 0
        
        print(f"📊 ESTADÍSTICAS To_AccountID_Mobiquity:")
        print(f"  Total registros Oracle: {oracle_total:,}")
        print(f"  Total registros S3: {s3_total:,}")
        print(f"  Diferencia total: {abs(oracle_total - s3_total):,}")
        
        print(f"\n📊 ESTADÍSTICAS CUENTAS ÚNICAS:")
        print(f"  Cuentas únicas totales: {total_cuentas}")
        print(f"  Cuentas coincidentes: {coincidencias_cuentas}")
        print(f"  Cuentas diferentes: {diferencias_cuentas}")
        print(f"  Porcentaje coincidencia: {porcentaje_cuentas:.1f}%")
        
        print(f"\n📊 ESTADÍSTICAS COMBINACIONES:")
        print(f"  Combinaciones totales: {len(all_combinations)}")
        print(f"  Combinaciones coincidentes: {combinaciones_coincidentes}")
        print(f"  Combinaciones diferentes: {combinaciones_diferentes}")
        print(f"  Porcentaje coincidencia: {porcentaje_combinaciones:.1f}%")
        
        # 7. VEREDICTO FINAL
        print(f"\n7️⃣ VEREDICTO FINAL:")
        print("-" * 60)
        
        if (diferencias_cuentas == 0 and combinaciones_diferentes == 0 and oracle_total == s3_total):
            print("🎉 ¡HOMOLOGACIÓN PERFECTA!")
            print("✅ To_AccountID_Mobiquity: 100% coincidente")
            print("✅ Combinaciones: 100% coincidentes")
            print("✅ Total registros: 100% coincidentes")
            print("🚀 No se requieren correcciones")
        else:
            print(f"⚠️  HOMOLOGACIÓN NECESITA REVISIÓN")
            if diferencias_cuentas > 0:
                print(f"❌ {diferencias_cuentas} diferencias en cuentas únicas")
            if combinaciones_diferentes > 0:
                print(f"❌ {combinaciones_diferentes} combinaciones diferentes")
            if oracle_total != s3_total:
                print(f"❌ Total registros diferentes: Oracle={oracle_total:,}, S3={s3_total:,}")
            print("🔧 Revisar lógica de To_AccountID_Mobiquity en el pipeline")
        
    except Exception as e:
        print(f"❌ Error en homologación: {e}")
        import traceback
        traceback.print_exc()

def main():
    print("🚀 HOMOLOGACIÓN EXHAUSTIVA To_AccountID_Mobiquity")
    print("=" * 80)
    print("Verificando casos como 501101120105574549, 501101120105560450, etc.")
    print()
    
    homologar_to_accountid_mobiquity()
    
    print("\n🏁 HOMOLOGACIÓN COMPLETADA")

if __name__ == "__main__":
    main()
