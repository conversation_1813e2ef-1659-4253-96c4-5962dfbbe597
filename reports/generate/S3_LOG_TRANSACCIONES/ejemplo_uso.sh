#!/bin/bash

# =============================================================================
# EJEMPLO DE USO - Pipeline LOG_TRANSACCIONES Modernizado
# =============================================================================
# Este script muestra cómo usar el pipeline modernizado de log_transacciones
# que replica exactamente el flujo Oracle original usando DuckDB + Parquet
# =============================================================================

echo "🚀 PIPELINE LOG_TRANSACCIONES MODERNIZADO - EJEMPLO DE USO"
echo "============================================================"

# Directorio de trabajo
PIPELINE_DIR="/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_TRANSACCIONES"
cd "$PIPELINE_DIR"

echo "📁 Directorio de trabajo: $PIPELINE_DIR"
echo ""

# =============================================================================
# PASO 1: EJECUTAR PRUEBAS (RECOMENDADO)
# =============================================================================
echo "🧪 PASO 1: Ejecutando pruebas del pipeline..."
echo "=============================================="

python3 test_pipeline.py

if [ $? -eq 0 ]; then
    echo "✅ Todas las pruebas pasaron. El pipeline está listo."
else
    echo "❌ Algunas pruebas fallaron. Revise los errores antes de continuar."
    echo "💡 Tip: Verifique credenciales AWS y conectividad S3"
    exit 1
fi

echo ""

# =============================================================================
# PASO 2: EJECUTAR PIPELINE PARA UNA FECHA ESPECÍFICA
# =============================================================================
echo "🔄 PASO 2: Ejecutando pipeline para fecha específica..."
echo "====================================================="

# Fecha de ejemplo (ajustar según disponibilidad de datos)
FECHA_EJEMPLO="2025-05-02"

echo "📅 Procesando fecha: $FECHA_EJEMPLO"
echo "⏱️  Iniciando pipeline..."

python3 pipeline_log_transacciones_duckdb.py "$FECHA_EJEMPLO"

if [ $? -eq 0 ]; then
    echo "✅ Pipeline ejecutado exitosamente"
    
    # Verificar archivo generado
    ARCHIVO_CSV="output/TR-${FECHA_EJEMPLO//\-/}.csv"
    if [ -f "$ARCHIVO_CSV" ]; then
        echo "📄 Archivo generado: $ARCHIVO_CSV"
        echo "📊 Número de registros: $(wc -l < "$ARCHIVO_CSV")"
        echo "📋 Primeras 5 líneas:"
        head -5 "$ARCHIVO_CSV"
    else
        echo "⚠️  Archivo CSV no encontrado en la ubicación esperada"
    fi
else
    echo "❌ Pipeline falló. Revise los logs para más detalles."
    exit 1
fi

echo ""

# =============================================================================
# PASO 3: MOSTRAR ARCHIVOS GENERADOS
# =============================================================================
echo "📁 PASO 3: Archivos generados..."
echo "================================"

echo "🗂️  Archivos temporales:"
if [ -d "TEMP_LOGS_TRANSACCIONES" ]; then
    find TEMP_LOGS_TRANSACCIONES -name "*.parquet" -type f | head -10
else
    echo "   (No se encontraron archivos temporales)"
fi

echo ""
echo "📄 Archivos CSV finales:"
if [ -d "output" ]; then
    ls -la output/*.csv 2>/dev/null || echo "   (No se encontraron archivos CSV)"
else
    echo "   (Directorio output no existe)"
fi

echo ""
echo "📋 Logs de ejecución:"
if [ -d "logs" ]; then
    ls -la logs/*.json 2>/dev/null || echo "   (No se encontraron logs JSON)"
else
    echo "   (Directorio logs no existe)"
fi

echo ""

# =============================================================================
# PASO 4: COMPARACIÓN CON FLUJO ORIGINAL (OPCIONAL)
# =============================================================================
echo "🔍 PASO 4: Comparación con flujo original (opcional)..."
echo "======================================================"

ARCHIVO_ORIGINAL="/home/<USER>/output/csv/TR-${FECHA_EJEMPLO//\-/}.csv"
ARCHIVO_MODERNIZADO="output/TR-${FECHA_EJEMPLO//\-/}.csv"

if [ -f "$ARCHIVO_ORIGINAL" ] && [ -f "$ARCHIVO_MODERNIZADO" ]; then
    echo "📊 Comparando archivos:"
    echo "   Original:    $ARCHIVO_ORIGINAL"
    echo "   Modernizado: $ARCHIVO_MODERNIZADO"
    
    # Comparar número de líneas
    LINEAS_ORIGINAL=$(wc -l < "$ARCHIVO_ORIGINAL")
    LINEAS_MODERNIZADO=$(wc -l < "$ARCHIVO_MODERNIZADO")
    
    echo "📈 Número de registros:"
    echo "   Original:    $LINEAS_ORIGINAL"
    echo "   Modernizado: $LINEAS_MODERNIZADO"
    
    if [ "$LINEAS_ORIGINAL" -eq "$LINEAS_MODERNIZADO" ]; then
        echo "✅ Mismo número de registros"
    else
        echo "⚠️  Diferencia en número de registros"
    fi
    
    # Comparar primeras líneas (headers)
    echo "📋 Comparando headers:"
    if diff <(head -1 "$ARCHIVO_ORIGINAL") <(head -1 "$ARCHIVO_MODERNIZADO") > /dev/null; then
        echo "✅ Headers idénticos"
    else
        echo "⚠️  Headers diferentes"
    fi
    
else
    echo "ℹ️  No se puede comparar (archivo original no disponible)"
    echo "   Ubicación esperada: $ARCHIVO_ORIGINAL"
fi

echo ""

# =============================================================================
# RESUMEN FINAL
# =============================================================================
echo "📊 RESUMEN FINAL"
echo "================"
echo "✅ Pipeline LOG_TRANSACCIONES modernizado ejecutado exitosamente"
echo "🏗️  Arquitectura: DuckDB + Parquet (S3)"
echo "📅 Fecha procesada: $FECHA_EJEMPLO"
echo "📁 Directorio: $PIPELINE_DIR"
echo ""
echo "💡 PRÓXIMOS PASOS:"
echo "   1. Validar resultados comparando con flujo original"
echo "   2. Ejecutar para otras fechas según necesidad"
echo "   3. Integrar en procesos automatizados"
echo ""
echo "📖 Para más información, consulte: README.md"
echo "🔧 Para solución de problemas, revise los logs en: logs/"
echo ""
echo "🎉 ¡Pipeline modernizado listo para producción!"
