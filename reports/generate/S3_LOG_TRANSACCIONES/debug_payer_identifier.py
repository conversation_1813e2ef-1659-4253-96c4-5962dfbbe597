#!/usr/bin/env python3
"""
Debug para verificar qué valores tiene PAYER_IDENTIFIER_VALUE en S3
"""

import duckdb
import boto3
import sys

def debug_payer_identifier():
    """Debug PAYER_IDENTIFIER_VALUE en S3"""
    print("🔍 DEBUG: PAYER_IDENTIFIER_VALUE en S3")
    print("=" * 80)
    
    parquet_path = '/home/<USER>/REPORTE_2025/reports/generate/S3_LOG_TRANSACCIONES/TEMP_LOGS_TRANSACCIONES/20250615/PRE_LOG_TRX.parquet'
    
    try:
        # Configurar DuckDB con S3
        conn = duckdb.connect()
        
        session = boto3.Session()
        credentials = session.get_credentials().get_frozen_credentials()
        
        conn.sql("INSTALL httpfs;")
        conn.sql("LOAD httpfs;")
        conn.sql("SET s3_region='us-east-1';")
        conn.sql("SET s3_use_ssl=true;")
        conn.sql("SET s3_url_style='path';")
        conn.sql(f"SET s3_access_key_id='{credentials.access_key}';")
        conn.sql(f"SET s3_secret_access_key='{credentials.secret_key}';")
        if credentials.token:
            conn.sql(f"SET s3_session_token='{credentials.token}';")
        
        print("1️⃣ CASOS REFUND CON BACKUS:")
        print("-" * 60)
        
        # Verificar casos REFUND con BACKUS
        backus_data = conn.execute(f"""
            SELECT 
                "TransferID",
                "FromID_Mobiquity",
                "From_Profile",
                "From_Identifier",
                "TransactionType"
            FROM read_parquet('{parquet_path}')
            WHERE "TransactionType" = 'REFUND'
            AND "FromID_Mobiquity" = '3507575'
            LIMIT 5
        """).fetchall()
        
        print(f"  Casos REFUND BACKUS (FromID=3507575):")
        print(f"{'TransferID':<20} {'From_Profile':<30} {'From_Identifier':<20}")
        print("-" * 75)
        
        for row in backus_data:
            transfer_id = row[0]
            from_profile = row[2]
            from_identifier = row[3]
            
            print(f"{transfer_id:<20} {from_profile:<30} {from_identifier:<20}")
        
        print("\n2️⃣ CASOS REFUND CON LINDLEY:")
        print("-" * 60)
        
        # Verificar casos REFUND con LINDLEY
        lindley_data = conn.execute(f"""
            SELECT 
                "TransferID",
                "FromID_Mobiquity",
                "From_Profile",
                "From_Identifier",
                "TransactionType"
            FROM read_parquet('{parquet_path}')
            WHERE "TransactionType" = 'REFUND'
            AND "FromID_Mobiquity" = '3440411'
            LIMIT 5
        """).fetchall()
        
        print(f"  Casos REFUND LINDLEY (FromID=3440411):")
        print(f"{'TransferID':<20} {'From_Profile':<30} {'From_Identifier':<20}")
        print("-" * 75)
        
        for row in lindley_data:
            transfer_id = row[0]
            from_profile = row[2]
            from_identifier = row[3]
            
            print(f"{transfer_id:<20} {from_profile:<30} {from_identifier:<20}")
        
        print("\n3️⃣ ANÁLISIS GENERAL DE From_Identifier:")
        print("-" * 60)
        
        # Verificar todos los valores únicos de From_Identifier para REFUND
        identifier_stats = conn.execute(f"""
            SELECT 
                "From_Identifier",
                "From_Profile",
                COUNT(*) as CASOS
            FROM read_parquet('{parquet_path}')
            WHERE "TransactionType" = 'REFUND'
            GROUP BY "From_Identifier", "From_Profile"
            ORDER BY COUNT(*) DESC
        """).fetchall()
        
        print(f"  From_Identifier para REFUND:")
        print(f"{'From_Identifier':<20} {'From_Profile':<35} {'CASOS'}")
        print("-" * 70)
        
        for row in identifier_stats:
            from_identifier = row[0]
            from_profile = row[1]
            casos = row[2]
            
            print(f"{from_identifier:<20} {from_profile:<35} {casos}")
        
        print("\n4️⃣ VERIFICACIÓN DE LÓGICA ACTUAL:")
        print("-" * 60)
        
        # Simular la lógica actual del pipeline
        print("  Lógica actual en pipeline:")
        print("  CASE WHEN UPAYER.PROFILE <> 'COMERCIO'")
        print("       THEN UPPER(MTH.PAYER_IDENTIFIER_VALUE) || ' ' || UPAYER.PROFILE")
        print("       ELSE 'FCOMPARTAMOS COMERCIO' END")
        
        # Verificar qué produce esta lógica
        for row in identifier_stats:
            from_identifier = row[0]
            from_profile = row[1]
            
            # Simular la lógica
            if from_identifier and from_profile:
                # Extraer PROFILE de From_Profile actual
                if 'Biller' in from_profile:
                    profile = 'Biller'
                elif 'PROVEEDOR DE SERVICIOS' in from_profile:
                    profile = 'PROVEEDOR DE SERVICIOS'
                else:
                    profile = from_profile.split(' ')[-1] if ' ' in from_profile else from_profile
                
                if profile != 'COMERCIO':
                    expected = f"{from_identifier.upper()} {profile}"
                    print(f"    {from_identifier} + {profile} = {expected}")
                    print(f"    Actual: {from_profile}")
                    print(f"    ¿Coincide? {'✅' if expected == from_profile else '❌'}")
        
        conn.close()
        
        print("\n5️⃣ CONCLUSIONES:")
        print("-" * 60)
        print("1. Verificar valores reales de From_Identifier")
        print("2. Verificar si la lógica de concatenación es correcta")
        print("3. Identificar por qué no se está aplicando la lógica REFUND")
        
    except Exception as e:
        print(f"❌ Error en debug: {e}")
        import traceback
        traceback.print_exc()

def main():
    print("🚀 DEBUG PAYER_IDENTIFIER_VALUE")
    print("=" * 80)
    print("Verificando valores reales en S3")
    print()
    
    debug_payer_identifier()
    
    print("\n🏁 DEBUG COMPLETADO")

if __name__ == "__main__":
    main()
