#!/usr/bin/env python3
"""
Investigación Detective - Diferencia de 6 registros
Modo detective master para encontrar exactamente qué registros están de más
"""

import oracledb
import duckdb
import pandas as pd
import sys
from datetime import datetime

class InvestigacionDiferenciaRegistros:
    def __init__(self):
        self.fecha = '2025-06-15'
        self.setup_connections()
        
    def setup_connections(self):
        """Configura conexiones"""
        try:
            # Oracle
            self.oracle_conn = oracledb.connect(
                user='usr_datalake',
                password='U2024b1mD4t4l4k5',
                dsn='10.240.131.10:1521/MMONEY'
            )
            
            # DuckDB
            self.duck_conn = duckdb.connect()
            self.duck_conn.sql("INSTALL httpfs;")
            self.duck_conn.sql("LOAD httpfs;")
            
            print("✅ Conexiones establecidas")
            
        except Exception as e:
            print(f"❌ Error en conexiones: {e}")
            raise

    def get_oracle_transfer_ids(self):
        """Obtiene todos los TransferIDs de Oracle"""
        print("🔍 Obteniendo TransferIDs de Oracle...")
        
        cursor = self.oracle_conn.cursor()
        cursor.execute(f"""
            SELECT "TransferID"
            FROM USR_DATALAKE.PRE_LOG_TRX
            WHERE TRUNC("TransferDate") = TO_DATE('{self.fecha}', 'YYYY-MM-DD')
            ORDER BY "TransferID"
        """)
        
        oracle_ids = [row[0] for row in cursor.fetchall()]
        cursor.close()
        
        print(f"✅ Oracle: {len(oracle_ids):,} TransferIDs")
        return set(oracle_ids)

    def get_parquet_transfer_ids(self):
        """Obtiene todos los TransferIDs de nuestro Parquet"""
        print("🔍 Obteniendo TransferIDs de Parquet...")
        
        parquet_path = f"TEMP_LOGS_TRANSACCIONES/20250615/PRE_LOG_TRX.parquet"
        
        result = self.duck_conn.execute(f"""
            SELECT "TransferID"
            FROM read_parquet('{parquet_path}')
            WHERE CAST("TransferDate" AS DATE) = CAST('{self.fecha}' AS DATE)
            ORDER BY "TransferID"
        """).fetchall()
        
        parquet_ids = [row[0] for row in result]
        
        print(f"✅ Parquet: {len(parquet_ids):,} TransferIDs")
        return set(parquet_ids)

    def find_extra_records(self, oracle_ids, parquet_ids):
        """Encuentra los registros extra en Parquet"""
        print(f"\n🔍 IDENTIFICANDO REGISTROS EXTRA")
        print("=" * 60)
        
        extra_in_parquet = parquet_ids - oracle_ids
        missing_in_parquet = oracle_ids - parquet_ids
        
        print(f"📊 ANÁLISIS:")
        print(f"   Oracle:           {len(oracle_ids):,} registros")
        print(f"   Parquet:          {len(parquet_ids):,} registros")
        print(f"   Extra en Parquet: {len(extra_in_parquet)} registros")
        print(f"   Falta en Parquet: {len(missing_in_parquet)} registros")
        
        if extra_in_parquet:
            print(f"\n❌ REGISTROS EXTRA EN PARQUET:")
            for i, transfer_id in enumerate(sorted(extra_in_parquet), 1):
                print(f"   {i}. TransferID: {transfer_id}")
        
        if missing_in_parquet:
            print(f"\n❌ REGISTROS FALTANTES EN PARQUET:")
            for i, transfer_id in enumerate(sorted(missing_in_parquet), 1):
                print(f"   {i}. TransferID: {transfer_id}")
        
        return extra_in_parquet, missing_in_parquet

    def analyze_extra_records(self, extra_ids):
        """Analiza los registros extra para entender por qué están incluidos"""
        if not extra_ids:
            print("✅ No hay registros extra para analizar")
            return
        
        print(f"\n🔍 ANALIZANDO REGISTROS EXTRA")
        print("=" * 60)
        
        parquet_path = f"TEMP_LOGS_TRANSACCIONES/20250615/PRE_LOG_TRX.parquet"
        
        for i, transfer_id in enumerate(sorted(extra_ids), 1):
            print(f"\n📊 REGISTRO EXTRA {i}: {transfer_id}")
            
            # Obtener detalles del registro extra
            result = self.duck_conn.execute(f"""
                SELECT 
                    "TransferID", "TransferDate", "TransferStatus", 
                    "Amount", "TransactionType", "From_Msisdn", "To_Msisdn"
                FROM read_parquet('{parquet_path}')
                WHERE "TransferID" = '{transfer_id}'
            """).fetchone()
            
            if result:
                print(f"   TransferID: {result[0]}")
                print(f"   TransferDate: {result[1]}")
                print(f"   TransferStatus: {result[2]}")
                print(f"   Amount: {result[3]}")
                print(f"   TransactionType: {result[4]}")
                print(f"   From_Msisdn: {result[5]}")
                print(f"   To_Msisdn: {result[6]}")
                
                # Verificar si este TransferID existe en MTX_TRANSACTION_HEADER
                cursor = self.oracle_conn.cursor()
                cursor.execute(f"""
                    SELECT COUNT(*)
                    FROM PDP_PROD10_MAINDBBUS.MTX_TRANSACTION_HEADER
                    WHERE FIELD7 = '{transfer_id}'
                    AND TRUNC(TRANSFER_DATE) = TO_DATE('{self.fecha}', 'YYYY-MM-DD')
                """)
                
                mtx_count = cursor.fetchone()[0]
                print(f"   En MTX_HEADER: {'✅ SÍ' if mtx_count > 0 else '❌ NO'} ({mtx_count} registros)")
                
                # Si existe en MTX_HEADER, verificar por qué no está en Oracle PRE_LOG_TRX
                if mtx_count > 0:
                    cursor.execute(f"""
                        SELECT TRANSFER_STATUS, TRANSFER_VALUE, SERVICE_TYPE
                        FROM PDP_PROD10_MAINDBBUS.MTX_TRANSACTION_HEADER
                        WHERE FIELD7 = '{transfer_id}'
                        AND TRUNC(TRANSFER_DATE) = TO_DATE('{self.fecha}', 'YYYY-MM-DD')
                    """)
                    
                    mtx_details = cursor.fetchone()
                    if mtx_details:
                        print(f"   MTX Status: {mtx_details[0]}")
                        print(f"   MTX Value: {mtx_details[1]}")
                        print(f"   MTX Service: {mtx_details[2]}")
                        
                        # Verificar si cumple los filtros del SP_PRE_LOG_TRX
                        status_ok = mtx_details[0] in ('TA', 'TS')
                        value_ok = mtx_details[1] != 0
                        
                        print(f"   Filtro Status: {'✅' if status_ok else '❌'} ({mtx_details[0]} in TA,TS)")
                        print(f"   Filtro Value: {'✅' if value_ok else '❌'} ({mtx_details[1]} <> 0)")
                        
                        if status_ok and value_ok:
                            print("   🤔 DEBERÍA estar en Oracle PRE_LOG_TRX - investigar más")
                        else:
                            print("   💡 NO debería estar - nuestro filtro es incorrecto")
                
                cursor.close()

    def investigate_source_filters(self):
        """Investiga los filtros aplicados en la fuente"""
        print(f"\n🔍 INVESTIGANDO FILTROS DE FUENTE")
        print("=" * 60)
        
        cursor = self.oracle_conn.cursor()
        
        # Verificar conteo total en MTX_TRANSACTION_HEADER para la fecha
        cursor.execute(f"""
            SELECT COUNT(*)
            FROM PDP_PROD10_MAINDBBUS.MTX_TRANSACTION_HEADER
            WHERE TRUNC(TRANSFER_DATE) = TO_DATE('{self.fecha}', 'YYYY-MM-DD')
        """)
        total_mtx = cursor.fetchone()[0]
        
        # Verificar conteo con filtros del SP_PRE_LOG_TRX
        cursor.execute(f"""
            SELECT COUNT(*)
            FROM PDP_PROD10_MAINDBBUS.MTX_TRANSACTION_HEADER
            WHERE TRUNC(TRANSFER_DATE) = TO_DATE('{self.fecha}', 'YYYY-MM-DD')
            AND TRANSFER_STATUS IN ('TA','TS')
            AND TRANSFER_VALUE <> 0
        """)
        filtered_mtx = cursor.fetchone()[0]
        
        # Verificar conteo en Oracle PRE_LOG_TRX
        cursor.execute(f"""
            SELECT COUNT(*)
            FROM USR_DATALAKE.PRE_LOG_TRX
            WHERE TRUNC("TransferDate") = TO_DATE('{self.fecha}', 'YYYY-MM-DD')
        """)
        oracle_pre_log = cursor.fetchone()[0]
        
        print(f"📊 CONTEOS DE FILTROS:")
        print(f"   MTX_HEADER total:     {total_mtx:,}")
        print(f"   MTX_HEADER filtrado:  {filtered_mtx:,}")
        print(f"   Oracle PRE_LOG_TRX:   {oracle_pre_log:,}")
        print(f"   Nuestro Parquet:      104,122")
        
        print(f"\n🎯 ANÁLISIS:")
        if filtered_mtx == oracle_pre_log:
            print("✅ Filtros Oracle correctos")
        else:
            print(f"❌ Diferencia en filtros Oracle: {filtered_mtx - oracle_pre_log}")
        
        if filtered_mtx == 104122:
            print("✅ Nuestros filtros son correctos")
        else:
            print(f"❌ Nuestros filtros incorrectos: diferencia de {104122 - filtered_mtx}")
        
        cursor.close()

    def run_investigation(self):
        """Ejecuta la investigación completa"""
        print("🕵️ INICIANDO INVESTIGACIÓN DETECTIVE - DIFERENCIA DE REGISTROS")
        print("=" * 80)
        print(f"📅 Fecha: {self.fecha}")
        print(f"🎯 Objetivo: Encontrar exactamente qué 6 registros están de más")
        
        try:
            # 1. Obtener TransferIDs
            oracle_ids = self.get_oracle_transfer_ids()
            parquet_ids = self.get_parquet_transfer_ids()
            
            # 2. Encontrar registros extra
            extra_ids, missing_ids = self.find_extra_records(oracle_ids, parquet_ids)
            
            # 3. Analizar registros extra
            if extra_ids:
                self.analyze_extra_records(extra_ids)
            
            # 4. Investigar filtros de fuente
            self.investigate_source_filters()
            
            # 5. Conclusión
            print(f"\n{'='*20} CONCLUSIÓN INVESTIGACIÓN {'='*20}")
            
            if len(extra_ids) == 0 and len(missing_ids) == 0:
                print("✅ No hay diferencias - homologación perfecta")
                return True
            else:
                print(f"❌ Diferencias encontradas:")
                print(f"   Extra en Parquet: {len(extra_ids)}")
                print(f"   Falta en Parquet: {len(missing_ids)}")
                print("🔧 Se requiere corrección en filtros")
                return False
            
        except Exception as e:
            print(f"❌ Error en investigación: {e}")
            raise
        
        finally:
            if hasattr(self, 'oracle_conn'):
                self.oracle_conn.close()
            if hasattr(self, 'duck_conn'):
                self.duck_conn.close()

def main():
    """Función principal"""
    try:
        investigador = InvestigacionDiferenciaRegistros()
        success = investigador.run_investigation()
        
        sys.exit(0 if success else 1)
        
    except Exception as e:
        print(f"❌ Error crítico: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
