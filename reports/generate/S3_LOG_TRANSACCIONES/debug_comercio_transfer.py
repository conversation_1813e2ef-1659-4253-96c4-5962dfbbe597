#!/usr/bin/env python3
"""
Debug para el último problema: FCOMPARTAMOS COMERCIO vs FCOMPARTAMOS Biller en TRANSFER
"""

import duckdb
import boto3
import sys

def debug_comercio_transfer():
    """Debug COMERC<PERSON> vs <PERSON><PERSON> en TRANSFER"""
    print("🔍 DEBUG: COMERCIO vs Bill<PERSON> en TRANSFER")
    print("=" * 80)
    
    parquet_path = '/home/<USER>/REPORTE_2025/reports/generate/S3_LOG_TRANSACCIONES/TEMP_LOGS_TRANSACCIONES/20250615/PRE_LOG_TRX.parquet'
    
    try:
        # Configurar DuckDB con S3
        conn = duckdb.connect()
        
        session = boto3.Session()
        credentials = session.get_credentials().get_frozen_credentials()
        
        conn.sql("INSTALL httpfs;")
        conn.sql("LOAD httpfs;")
        conn.sql("SET s3_region='us-east-1';")
        conn.sql("SET s3_use_ssl=true;")
        conn.sql("SET s3_url_style='path';")
        conn.sql(f"SET s3_access_key_id='{credentials.access_key}';")
        conn.sql(f"SET s3_secret_access_key='{credentials.secret_key}';")
        if credentials.token:
            conn.sql(f"SET s3_session_token='{credentials.token}';")
        
        print("1️⃣ CASOS TRANSFER CON FCOMPARTAMOS Biller:")
        print("-" * 60)
        
        # Verificar casos TRANSFER con FCOMPARTAMOS Biller
        biller_data = conn.execute(f"""
            SELECT 
                "TransferID",
                "FromID_Mobiquity",
                "From_Profile",
                "From_Identifier",
                "TransactionType"
            FROM read_parquet('{parquet_path}')
            WHERE "TransactionType" = 'TRANSFER'
            AND "From_Profile" = 'FCOMPARTAMOS Biller'
            LIMIT 10
        """).fetchall()
        
        print(f"  Casos TRANSFER con FCOMPARTAMOS Biller:")
        print(f"{'TransferID':<20} {'FromID':<15} {'From_Identifier':<20}")
        print("-" * 60)
        
        user_ids_to_check = set()
        for row in biller_data:
            transfer_id = row[0]
            from_id = row[1]
            from_identifier = row[3]
            
            user_ids_to_check.add(from_id)
            print(f"{transfer_id:<20} {from_id:<15} {from_identifier:<20}")
        
        print(f"\n2️⃣ ANÁLISIS DE From_Identifier PARA TRANSFER:")
        print("-" * 60)
        
        # Verificar todos los From_Identifier para TRANSFER
        transfer_stats = conn.execute(f"""
            SELECT 
                "From_Identifier",
                "From_Profile",
                COUNT(*) as CASOS
            FROM read_parquet('{parquet_path}')
            WHERE "TransactionType" = 'TRANSFER'
            AND "From_Profile" IN ('FCOMPARTAMOS Biller', 'FCOMPARTAMOS COMERCIO')
            GROUP BY "From_Identifier", "From_Profile"
            ORDER BY COUNT(*) DESC
        """).fetchall()
        
        print(f"  From_Identifier para TRANSFER:")
        print(f"{'From_Identifier':<20} {'From_Profile':<25} {'CASOS'}")
        print("-" * 60)
        
        for row in transfer_stats:
            from_identifier = row[0]
            from_profile = row[1]
            casos = row[2]
            
            print(f"{from_identifier:<20} {from_profile:<25} {casos}")
        
        print(f"\n3️⃣ VERIFICACIÓN DE LÓGICA ACTUAL:")
        print("-" * 60)
        
        print("  Lógica actual para From_Profile:")
        print("  ELSE MTH.PAYER_ISSUER_CODE || ' ' || UPAYER.PROFILE")
        print("  Para TRANSFER: FCOMPARTAMOS + Biller = FCOMPARTAMOS Biller")
        print("  Oracle espera: FCOMPARTAMOS COMERCIO")
        
        print(f"\n  🔍 VERIFICANDO CASOS ESPECÍFICOS:")
        
        # Verificar casos específicos
        for user_id in sorted(user_ids_to_check):
            if user_id:
                print(f"    FromID {user_id}: Debería ser COMERCIO en lugar de Biller")
        
        print(f"\n4️⃣ SOLUCIÓN PROPUESTA:")
        print("-" * 60)
        print("  Para TRANSFER con PROFILE = 'Biller':")
        print("  Cambiar a: MTH.PAYER_ISSUER_CODE || ' COMERCIO'")
        print("  En lugar de: MTH.PAYER_ISSUER_CODE || ' ' || UPAYER.PROFILE")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error en debug: {e}")
        import traceback
        traceback.print_exc()

def main():
    print("🚀 DEBUG COMERCIO vs Biller")
    print("=" * 80)
    print("Último problema de From_Profile")
    print()
    
    debug_comercio_transfer()
    
    print("\n🏁 DEBUG COMPLETADO")

if __name__ == "__main__":
    main()
