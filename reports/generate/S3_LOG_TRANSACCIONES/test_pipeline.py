#!/usr/bin/env python3
"""
Script de prueba para el Pipeline LOG_TRANSACCIONES Modernizado
Valida la funcionalidad básica y conectividad antes de ejecutar el pipeline completo
"""

import sys
import os
from datetime import datetime, timedelta
from pathlib import Path

# Agregar el directorio actual al path para importar el pipeline
sys.path.append(str(Path(__file__).parent))

try:
    from pipeline_log_transacciones_duckdb import LogTransaccionesPipeline
except ImportError as e:
    print(f"❌ Error importando el pipeline: {e}")
    sys.exit(1)

def test_basic_connectivity():
    """Prueba la conectividad básica a S3 y DuckDB"""
    print("🔍 Probando conectividad básica...")
    
    try:
        pipeline = LogTransaccionesPipeline()
        print("✅ Pipeline inicializado correctamente")
        
        # Probar una query simple
        result = pipeline.conn.execute("SELECT 1 as test").fetchone()
        if result and result[0] == 1:
            print("✅ DuckDB funcionando correctamente")
        else:
            print("❌ Error en DuckDB")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ Error en conectividad básica: {e}")
        return False

def test_s3_access():
    """Prueba el acceso a las fuentes S3"""
    print("\n🔍 Probando acceso a fuentes S3...")
    
    try:
        pipeline = LogTransaccionesPipeline()
        
        # Probar acceso a una tabla consolidada (más rápido)
        test_sources = [
            ('sys_service_types', pipeline.s3_sources['sys_service_types']),
            ('issuer_details', pipeline.s3_sources['issuer_details']),
            ('user_data_trx', pipeline.s3_sources['user_data_trx'])
        ]
        
        for name, path in test_sources:
            try:
                result = pipeline.conn.execute(f"SELECT COUNT(*) FROM read_parquet('{path}') LIMIT 1").fetchone()
                if result:
                    print(f"✅ {name}: Accesible ({result[0]:,} registros)")
                else:
                    print(f"❌ {name}: Sin datos")
                    return False
            except Exception as e:
                print(f"❌ {name}: Error de acceso - {e}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error general en acceso S3: {e}")
        return False

def test_partitioned_access(fecha_test='2025-05-02'):
    """Prueba el acceso a tablas particionadas por fecha"""
    print(f"\n🔍 Probando acceso a tablas particionadas para fecha: {fecha_test}...")
    
    try:
        pipeline = LogTransaccionesPipeline()
        
        # Probar acceso a MTX_TRANSACTION_HEADER particionada
        mtx_header_path = pipeline.get_partitioned_path(
            pipeline.s3_sources['mtx_transaction_header'], 
            fecha_test
        )
        
        print(f"📁 Ruta construida: {mtx_header_path}")
        
        try:
            result = pipeline.conn.execute(f"""
                SELECT COUNT(*) 
                FROM read_parquet('{mtx_header_path}')
                WHERE CAST(TRANSFER_DATE AS DATE) = CAST('{fecha_test}' AS DATE)
                LIMIT 1
            """).fetchone()
            
            if result:
                print(f"✅ MTX_TRANSACTION_HEADER: {result[0]:,} registros para {fecha_test}")
                return True
            else:
                print(f"❌ MTX_TRANSACTION_HEADER: Sin datos para {fecha_test}")
                return False
                
        except Exception as e:
            print(f"❌ MTX_TRANSACTION_HEADER: Error de acceso - {e}")
            return False
        
    except Exception as e:
        print(f"❌ Error en prueba de particiones: {e}")
        return False

def test_directories():
    """Prueba la creación de directorios necesarios"""
    print("\n🔍 Probando estructura de directorios...")
    
    try:
        pipeline = LogTransaccionesPipeline()
        
        # Verificar directorios creados
        base_path = Path('/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_TRANSACCIONES')
        required_dirs = ['TEMP_LOGS_TRANSACCIONES', 'output', 'logs']
        
        for dir_name in required_dirs:
            dir_path = base_path / dir_name
            if dir_path.exists() and dir_path.is_dir():
                print(f"✅ Directorio {dir_name}: Existe")
            else:
                print(f"❌ Directorio {dir_name}: No existe")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error en prueba de directorios: {e}")
        return False

def run_mini_pipeline_test(fecha_test='2025-05-02'):
    """Ejecuta una prueba mínima del pipeline (solo verificaciones)"""
    print(f"\n🔍 Ejecutando prueba mínima del pipeline para fecha: {fecha_test}...")
    
    try:
        pipeline = LogTransaccionesPipeline()
        
        # Verificar si ya fue procesado (sin ejecutar)
        date_folder = fecha_test.replace('-', '')
        
        print(f"📁 Carpeta de fecha: {date_folder}")
        
        # Verificar lógica de verificación de procesamiento
        pre_log_processed = pipeline.check_if_processed(fecha_test, 'PRE_LOG_TRX')
        log_trx_processed = pipeline.check_if_processed(fecha_test, 'LOG_TRX_FINAL')
        
        print(f"📊 PRE_LOG_TRX ya procesado: {pre_log_processed}")
        print(f"📊 LOG_TRX_FINAL ya procesado: {log_trx_processed}")
        
        # Verificar construcción de rutas particionadas
        mtx_header_path = pipeline.get_partitioned_path(
            pipeline.s3_sources['mtx_transaction_header'], 
            fecha_test
        )
        mtx_items_path = pipeline.get_partitioned_path(
            pipeline.s3_sources['mtx_transaction_items'], 
            fecha_test
        )
        
        print(f"📁 Ruta MTX_HEADER: {mtx_header_path}")
        print(f"📁 Ruta MTX_ITEMS: {mtx_items_path}")
        
        print("✅ Prueba mínima del pipeline completada")
        return True
        
    except Exception as e:
        print(f"❌ Error en prueba mínima del pipeline: {e}")
        return False

def main():
    """Función principal de pruebas"""
    print("🧪 PRUEBAS DEL PIPELINE LOG_TRANSACCIONES MODERNIZADO")
    print("=" * 60)
    
    tests = [
        ("Conectividad Básica", test_basic_connectivity),
        ("Acceso S3", test_s3_access),
        ("Acceso Particionado", test_partitioned_access),
        ("Estructura Directorios", test_directories),
        ("Prueba Mínima Pipeline", run_mini_pipeline_test)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            success = test_func()
            results.append((test_name, success))
        except Exception as e:
            print(f"❌ Error inesperado en {test_name}: {e}")
            results.append((test_name, False))
    
    # Resumen final
    print("\n" + "="*60)
    print("📊 RESUMEN DE PRUEBAS")
    print("="*60)
    
    passed = 0
    total = len(results)
    
    for test_name, success in results:
        status = "✅ PASÓ" if success else "❌ FALLÓ"
        print(f"{status:10} {test_name}")
        if success:
            passed += 1
    
    print(f"\n📈 Resultado: {passed}/{total} pruebas pasaron")
    
    if passed == total:
        print("🎉 ¡Todas las pruebas pasaron! El pipeline está listo para usar.")
        print("\n💡 Para ejecutar el pipeline completo:")
        print("   python3 pipeline_log_transacciones_duckdb.py 2025-05-02")
        return True
    else:
        print("⚠️  Algunas pruebas fallaron. Revise los errores antes de ejecutar el pipeline.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
