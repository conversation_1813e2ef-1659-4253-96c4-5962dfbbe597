#!/bin/bash

# Variables de entorno
export PRIVATE_KEY_PATH="/home/<USER>/generate/FileSigner/private_key.key"
export PRIVATE_CRT_PATH="/home/<USER>/generate/FileSigner/private_key.crt"
export OUTPUT_ROUTE="/home/<USER>/output/excel/"
export OUTPUT_ROUTE_CSV="/home/<USER>/output/csv/"

# Definir arrays
values32=("32A" "32B-I" "32B-II" "32B-III" "32B-IV" "32B-V")
values=("COMISIONES-BIMER" "P2P" "BITEL-POST" "BITEL-PRE" "SERVICE-PROVIDER" "CASHIN" "CASHOUT" "AGENTES-BIM" "SERVICIOS-DIRECTOS" "COMERCIOS" "RECARGAS" "TRAZA-FEE" "RETIROS" "DEPOSITOS")
valuescsv=("BCRP-NETO-EMISORES" "CRANDES-PAGOS" "BCRP-OPERACIONES-EMISOR" "BCRP-TIPO-CUENTAS" "AZULITO" "INTEROPE-COBRAR-PDF" "INTEROPE-PAGAR-PDF" "LOG-USUARIOS" "UNIQUE" "MOVISTAR" "ENTEL" "RETIRO-SENTINEL" "RETIRO-WU-HUB" "USER-BALANCES" "QR-NIUBIZ" "QR-IZIPAY" "INTEROPE-COBRAR" "INTEROPE-PAGAR" "MTX-TRANSACTION" "LOG-TRANSACCIONES" "32A" "32B-I" "32B-II" "32B-III" "32B-IV" "32B-V" "COMISIONES")
export REPORTS_NO_S3="LOG-USUARIOS,LOG-TRANSACCIONES,MTX-TRANSACTION,USER-BALANCES,32A,32B-I,32B-II,32B-III,32B-IV,32B-V,INTEROPE-COBRAR-PDF,INTEROPE-PAGAR-PDF"

ROUTE_CSV="/home/<USER>/output/csv"
TARGET_PATH="/home/<USER>/output/load_rds"

if [ -z "$1" ]; then
    fecha=$(date -d "yesterday" +"%Y/%m/%d")
else
    fecha=$1
fi

fecha_path=$(date -d "$fecha + 1 day" +"%Y%m%d")

cd /home/<USER>/generate/

# Crear carpetas de logs
mkdir -p logs/{excel,csv,log_transacciones,reports32a_b,account_balances,log_usuarios,prepare,reporte_conciliacion,csv_to_pdf,prepare_rds,mysql_reports}

echo "== Instalando dependencias si faltan =="
pip install -r requirements.txt

#echo "== Preparando datos preliminares =="
#python3 prepare/main.py "$fecha" > "logs/prepare/PRE-REPORTES.log" 2>&1 &
#wait

echo "== Generando REPORTES EXCEL =="
for value in "${values[@]}"; do
    echo "Iniciando: $value"
    python3 exports_excel/main.py "$value" "$fecha" > "logs/excel/${value}.log" 2>&1 &
done

echo "== Generando REPORTES CSV =="
for value in "${valuescsv[@]}"; do
    echo "Iniciando: $value"
    python3 exports_csv/main.py "$value" "$fecha" > "logs/csv/${value}.log" 2>&1 &
done
wait

echo "== Procesando LOG TRANSACCIONES =="
cd /home/<USER>/generate/log_transacciones/
mkdir -p output/"$fecha_path"
python3 procesar.py "$fecha" > "/home/<USER>/generate/logs/log_transacciones/LOG-TRANSACCIONES.log" 2>&1 &
wait

echo "== Procesando LOG USUARIOS =="
cd /home/<USER>/generate/log_usuarios/
python3 procesar.py "$fecha" > "/home/<USER>/generate/logs/log_usuarios/LOG-USUARIOS.log" 2>&1 &
wait

echo "== Procesando ACCOUNT BALANCES =="
cd /home/<USER>/generate/account_balance/
python3 main.py "$fecha" > "/home/<USER>/generate/logs/account_balances/ACC-BALANCES.log" 2>&1 &
wait

echo "== Generando REPORTES 32x =="
cd /home/<USER>/generate/reports32a-b/
for value in "${values32[@]}"; do
    echo "Iniciando: $value"
    python3 main.py "$value" "$fecha" > "/home/<USER>/generate/logs/reports32a_b/${value}.log" 2>&1 &
done
wait

echo "== Ejecutando GOPAY =="
cd /home/<USER>/generate/mysql_reports/GOPAY
python3 main.py "$fecha" > "/home/<USER>/generate/logs/mysql_reports/GOPAY.log" 2>&1 &
wait

echo "== Ejecutando FULLCARGAS =="
cd /home/<USER>/generate/mysql_reports/Fullcargas
python3 main.py "$fecha" > "/home/<USER>/generate/logs/mysql_reports/FULLCARGAS.log" 2>&1 &
wait

echo "== Ejecutando SERVICIOS-WU =="
cd /home/<USER>/generate/mysql_reports/Servicios-WU
python3 main.py "$fecha" > "/home/<USER>/generate/logs/mysql_reports/SERVICIOS-WU.log" 2>&1 &
wait

echo "== Ejecutando CONCILIACION BIM =="
cd /home/<USER>/generate/reporte_conciliacion/
python3 main.py "$fecha" > "/home/<USER>/generate/logs/reporte_conciliacion/CONCILIACION-BIM.log" 2>&1 &
wait


echo "== Ejecutando CREACION DE PDFS =="
cd /home/<USER>/generate/csv_to_pdf/
mv -f /home/<USER>/output/csv/*PDF* /home/<USER>/output/pdf
python3 main.py "$fecha" > "/home/<USER>/generate/logs/csv_to_pdf/CSV_TO_PDF.log" 2>&1 &
wait

# Archivos especiales
log_trx_file="TR-${fecha_path}.csv"
mtx_trx_header_file="MTX_TRANSACTION_HEADER_${fecha_path}.csv"
log_trx_new_name="LOG_TRX_FINAL.csv"
mtx_trx_new_name="MTX_TRANSACTION_HEADER.csv"
mkdir -p "$TARGET_PATH"

# Mover archivos si existen
if [ -f "$ROUTE_CSV/$log_trx_file" ]; then
    mv "$ROUTE_CSV/$log_trx_file" "$TARGET_PATH/$log_trx_new_name"
    echo "✅ Archivo $log_trx_file movido a $log_trx_new_name"
else
    echo "❌ No se encontro el archivo $log_trx_file"
fi

if [ -f "$ROUTE_CSV/$mtx_trx_header_file" ]; then
    mv "$ROUTE_CSV/$mtx_trx_header_file" "$TARGET_PATH/$mtx_trx_new_name"
    echo "✅ Archivo $mtx_trx_header_file movido a $mtx_trx_new_name"
else
    echo "❌ No se encontro el archivo $mtx_trx_header_file"
fi

cd /home/<USER>/generate/prepare_rds/
python3 read_csv_sql.py "$TARGET_PATH" > "/home/<USER>/generate/logs/prepare_rds/CARGAR-RDS.log" 2>&1 &
wait

# 🔍 Resumen final de logs
cd /home/<USER>/generate/
echo ""
echo "== RESUMEN DE ESTADO =="
for value in "${values[@]}"; do
    if grep -qi "error" "logs/excel/${value}.log"; then
        echo "❌ $value (ver logs/excel/${value}.log)"
    else
        echo "✅ $value"
    fi
done

for value in "${valuescsv[@]}"; do
    if grep -qi "error" "logs/csv/${value}.log"; then
        echo "❌ $value (ver logs/csv/${value}.log)"
    else
        echo "✅ $value"
    fi
done

for value in "${values32[@]}"; do
    if grep -qi "error" "logs/reports32a_b/${value}.log"; then
        echo "❌ $value (ver logs/reports32a_b/${value}.log)"
    else
        echo "✅ $value"
    fi
done

echo "== FIN DE EJECUCIÓN =="

