import os
#private_key_path = os.environ.get("PRIVATE_KEY_PATH")
#public_key_path = os.environ.get("PUBLIC_KEY_PATH")
#public_key_path = os.environ.get("PRIVATE_CRT_PATH")
private_key_path = "/home/<USER>/generate/FileSigner/pdp_sign.key"
public_key_path = "/home/<USER>/generate/FileSigner/SignFileNC.crt"

bucket_s3 = "prd-datalake-reports-637423440311"
emisores = ["FCOMPARTAMOS", "BNACION", "CRANDES", "CCUSCO", "FCONFIANZ<PERSON>", "FQAPAQ"]
apps_signed_obligatory = ["INTEROPE-PAGAR", "INTEROPE-COBRAR", "COMISIONES"]
apps_with_date = ["BCRP-NETO-EMISORES" "BCRP-TIPO-CUENTAS" "32B-II", "32B-I", "32B-III", "32B-IV", "32B-V", "A<PERSON><PERSON><PERSON><PERSON><PERSON>", "UNIQUE", "MOVIST<PERSON>", "<PERSON>NT<PERSON>", "RETIRO-SENTINEL", "RETIRO-WU-HUB", "INTEROPE-COBRAR", "INTEROPE-PAGAR", "INTEROPE-COBRAR-PDF", "INTEROPE-PAGAR-PDF", "LOG-TRANSACCIONES", "LOG-USUARIOS", "BCRP_BALANCES", "32A", "CRANDES-PAGOS" "EQUIVALENCIA-LOG-TRX" "EQUIVALENCIA-PAGOS" "EQUIVALENCIA-AHORROS"]
#report_no_s3 = os.environ.get("REPORTS_NO_S3").split(',')
report_no_s3 = ["LOG-TRANSACCIONES", "MTX-TRANSACTION", "USER-BALANCES", "32A", "32B-I", "32B-II", "32B-III", "32B-IV", "32B-V", "LOG-USUARIOS", "INTEROPE-COBRAR-PDF", "INTEROPE-PAGAR-PDF"]
#output_route_csv = os.environ.get("OUTPUT_ROUTE_CSV")
output_route_csv = "/home/<USER>/output/csv/"
