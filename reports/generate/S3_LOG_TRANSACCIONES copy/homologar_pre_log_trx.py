#!/usr/bin/env python3
"""
Script para homologar completamente PRE_LOG_TRX entre S3 y Oracle
Compara TODOS los campos del TransferID específico para verificar coincidencia 100%
"""

import oracledb
import duckdb
import boto3
import sys
import pandas as pd
from pathlib import Path

def homologar_pre_log_trx():
    """Homologa completamente PRE_LOG_TRX entre S3 y Oracle"""
    print("🔍 HOMOLOGACIÓN COMPLETA PRE_LOG_TRX: S3 vs ORACLE")
    print("=" * 80)
    
    transfer_id = '175003230421458'
    fecha = '2025-06-15'
    parquet_path = '/home/<USER>/REPORTE_2025/reports/generate/S3_LOG_TRANSACCIONES/TEMP_LOGS_TRANSACCIONES/20250615/PRE_LOG_TRX.parquet'
    
    print(f"TransferID analizado: {transfer_id}")
    print(f"Fecha: {fecha}")
    print(f"Archivo S3: {parquet_path}")
    print()
    
    # 1. CONSULTAR ORACLE
    print("1️⃣ CONSULTANDO ORACLE PRE_LOG_TRX:")
    print("-" * 50)
    
    try:
        connection = oracledb.connect(
            user='usr_datalake',
            password='U2024b1mD4t4l4k5',
            dsn='*************:1521/MMONEY'
        )
        
        cursor = connection.cursor()
        
        # Obtener TODOS los campos de Oracle
        cursor.execute("""
            SELECT *
            FROM USR_DATALAKE.PRE_LOG_TRX 
            WHERE "TransferID" = :transfer_id
        """, {'transfer_id': transfer_id})
        
        oracle_data = cursor.fetchall()
        oracle_columns = [desc[0] for desc in cursor.description]
        
        if oracle_data:
            oracle_row = oracle_data[0]
            oracle_dict = dict(zip(oracle_columns, oracle_row))
            
            print(f"✅ Registro encontrado en Oracle ({len(oracle_columns)} campos)")
            
            # Mostrar campos clave
            campos_clave = [
                'TransferID', 'FromID_Mobiquity', 'From_AccountID_Mobiquity', 
                'ToID_Mobiquity', 'To_AccountID_Mobiquity', 'TransferDate',
                'TransactionType', 'From_BankDomain', 'To_BankDomain',
                'TransferValue', 'Fee', 'Context'
            ]
            
            print("\n📊 CAMPOS CLAVE ORACLE:")
            for campo in campos_clave:
                if campo in oracle_dict:
                    print(f"  {campo}: {oracle_dict[campo]}")
        else:
            print("❌ No se encontró el registro en Oracle")
            return
        
        cursor.close()
        connection.close()
        
    except Exception as e:
        print(f"❌ Error consultando Oracle: {e}")
        return
    
    # 2. CONSULTAR S3
    print("\n2️⃣ CONSULTANDO S3 PRE_LOG_TRX:")
    print("-" * 50)
    
    try:
        # Configurar DuckDB con S3
        conn = duckdb.connect()
        
        session = boto3.Session()
        credentials = session.get_credentials().get_frozen_credentials()
        
        conn.sql("INSTALL httpfs;")
        conn.sql("LOAD httpfs;")
        conn.sql("SET s3_region='us-east-1';")
        conn.sql("SET s3_use_ssl=true;")
        conn.sql("SET s3_url_style='path';")
        conn.sql(f"SET s3_access_key_id='{credentials.access_key}';")
        conn.sql(f"SET s3_secret_access_key='{credentials.secret_key}';")
        if credentials.token:
            conn.sql(f"SET s3_session_token='{credentials.token}';")
        
        # Obtener TODOS los campos de S3
        s3_data = conn.execute(f"""
            SELECT *
            FROM read_parquet('{parquet_path}')
            WHERE "TransferID" = '{transfer_id}'
        """).fetchall()
        
        s3_columns = [desc[0] for desc in conn.description]
        
        if s3_data:
            s3_row = s3_data[0]
            s3_dict = dict(zip(s3_columns, s3_row))
            
            print(f"✅ Registro encontrado en S3 ({len(s3_columns)} campos)")
            
            print("\n📊 CAMPOS CLAVE S3:")
            for campo in campos_clave:
                if campo in s3_dict:
                    print(f"  {campo}: {s3_dict[campo]}")
        else:
            print("❌ No se encontró el registro en S3")
            return
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error consultando S3: {e}")
        return
    
    # 3. COMPARACIÓN DETALLADA
    print("\n3️⃣ COMPARACIÓN DETALLADA CAMPO POR CAMPO:")
    print("-" * 50)
    
    # Campos comunes entre Oracle y S3
    campos_comunes = set(oracle_columns) & set(s3_columns)
    campos_solo_oracle = set(oracle_columns) - set(s3_columns)
    campos_solo_s3 = set(s3_columns) - set(oracle_columns)
    
    print(f"Campos comunes: {len(campos_comunes)}")
    print(f"Solo en Oracle: {len(campos_solo_oracle)}")
    print(f"Solo en S3: {len(campos_solo_s3)}")
    
    # Comparar campos comunes
    coincidencias = 0
    diferencias = 0
    
    print(f"\n📋 COMPARACIÓN DE CAMPOS COMUNES ({len(campos_comunes)} campos):")
    print("-" * 60)
    
    for campo in sorted(campos_comunes):
        oracle_val = oracle_dict.get(campo)
        s3_val = s3_dict.get(campo)
        
        # Normalizar valores para comparación
        oracle_str = str(oracle_val) if oracle_val is not None else 'NULL'
        s3_str = str(s3_val) if s3_val is not None else 'NULL'
        
        if oracle_str == s3_str:
            coincidencias += 1
            status = "✅"
        else:
            diferencias += 1
            status = "❌"
        
        # Mostrar solo campos importantes o con diferencias
        if campo in campos_clave or oracle_str != s3_str:
            print(f"  {status} {campo}:")
            print(f"      Oracle: {oracle_str}")
            print(f"      S3:     {s3_str}")
            if oracle_str != s3_str:
                print(f"      🔍 DIFERENCIA DETECTADA")
            print()
    
    # 4. RESUMEN FINAL
    print("4️⃣ RESUMEN DE HOMOLOGACIÓN:")
    print("-" * 50)
    
    porcentaje_coincidencia = (coincidencias / len(campos_comunes)) * 100 if campos_comunes else 0
    
    print(f"📊 ESTADÍSTICAS:")
    print(f"  Campos coincidentes: {coincidencias}")
    print(f"  Campos diferentes: {diferencias}")
    print(f"  Porcentaje de coincidencia: {porcentaje_coincidencia:.1f}%")
    
    if diferencias == 0:
        print("\n🎉 ¡HOMOLOGACIÓN PERFECTA!")
        print("✅ Todos los campos comunes coinciden exactamente")
        print("🚀 Tu migración S3 es 100% fiel a Oracle")
    else:
        print(f"\n⚠️  Se encontraron {diferencias} diferencias")
        print("🔧 Revisar los campos marcados con ❌")
    
    if campos_solo_oracle:
        print(f"\n📝 CAMPOS SOLO EN ORACLE ({len(campos_solo_oracle)}):")
        for campo in sorted(campos_solo_oracle):
            print(f"  - {campo}: {oracle_dict.get(campo)}")
    
    if campos_solo_s3:
        print(f"\n📝 CAMPOS SOLO EN S3 ({len(campos_solo_s3)}):")
        for campo in sorted(campos_solo_s3):
            print(f"  - {campo}: {s3_dict.get(campo)}")

def main():
    print("🚀 HOMOLOGACIÓN COMPLETA PRE_LOG_TRX")
    print("=" * 80)
    print("Verificando coincidencia 100% entre S3 y Oracle")
    print()
    
    homologar_pre_log_trx()
    
    print("\n🏁 HOMOLOGACIÓN COMPLETADA")

if __name__ == "__main__":
    main()
