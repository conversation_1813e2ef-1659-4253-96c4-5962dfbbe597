#!/usr/bin/env python3
"""
Debug específico para los casos problemáticos de To_AccountID_Mobiquity
"""

import oracledb
import duckdb
import boto3
import sys

def debug_to_accountid_casos():
    """Debug casos específicos de To_AccountID_Mobiquity"""
    print("🔍 DEBUG: Casos Específicos To_AccountID_Mobiquity")
    print("=" * 80)
    
    parquet_path = '/home/<USER>/REPORTE_2025/reports/generate/S3_LOG_TRANSACCIONES/TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet'
    
    # Casos problemáticos identificados
    casos_problematicos = [
        ('501101120105533600', '3262191'),  # Oracle vs S3 ToID
        ('501101120105554646', '3662073'),
        ('501101120105554650', '2078452'),
        ('501101120105554643', '1932101'),
        ('501101120105560450', None)  # Caso que no aparece en S3
    ]
    
    try:
        # 1. ANÁLISIS EN ORACLE
        print("1️⃣ ANÁLISIS EN ORACLE:")
        print("-" * 60)
        
        connection = oracledb.connect(
            user='usr_datalake',
            password='U2024b1mD4t4l4k5',
            dsn='10.240.131.10:1521/MMONEY'
        )
        
        cursor = connection.cursor()
        
        for oracle_account, to_id in casos_problematicos:
            if to_id:
                print(f"\n📋 CASO: To_AccountID={oracle_account}, ToID={to_id}")
                
                # Buscar en Oracle
                cursor.execute("""
                    SELECT 
                        "TransferID",
                        "ToID_Mobiquity",
                        "To_AccountID_Mobiquity",
                        "To_Profile",
                        "TransactionType"
                    FROM USR_DATALAKE.PRE_LOG_TRX 
                    WHERE "To_AccountID_Mobiquity" = :account_id
                    AND "ToID_Mobiquity" = :to_id
                    AND ROWNUM <= 3
                """, {'account_id': oracle_account, 'to_id': to_id})
                
                oracle_data = cursor.fetchall()
                
                print(f"  Oracle encontró {len(oracle_data)} registros:")
                for row in oracle_data:
                    print(f"    TransferID: {row[0]}")
                    print(f"    ToID: {row[1]}")
                    print(f"    To_AccountID: {row[2]}")
                    print(f"    To_Profile: {row[3]}")
                    print(f"    TransactionType: {row[4]}")
        
        cursor.close()
        connection.close()
        
        # 2. ANÁLISIS EN S3
        print(f"\n2️⃣ ANÁLISIS EN S3:")
        print("-" * 60)
        
        # Configurar DuckDB con S3
        conn = duckdb.connect()
        
        session = boto3.Session()
        credentials = session.get_credentials().get_frozen_credentials()
        
        conn.sql("INSTALL httpfs;")
        conn.sql("LOAD httpfs;")
        conn.sql("SET s3_region='us-east-1';")
        conn.sql("SET s3_use_ssl=true;")
        conn.sql("SET s3_url_style='path';")
        conn.sql(f"SET s3_access_key_id='{credentials.access_key}';")
        conn.sql(f"SET s3_secret_access_key='{credentials.secret_key}';")
        if credentials.token:
            conn.sql(f"SET s3_session_token='{credentials.token}';")
        
        for oracle_account, to_id in casos_problematicos:
            if to_id:
                print(f"\n📋 CASO: ToID={to_id}")
                
                # Buscar en S3 por ToID
                s3_data = conn.execute(f"""
                    SELECT 
                        "TransferID",
                        "ToID_Mobiquity",
                        "To_AccountID_Mobiquity",
                        "To_Profile",
                        "TransactionType"
                    FROM read_parquet('{parquet_path}')
                    WHERE "ToID_Mobiquity" = '{to_id}'
                    LIMIT 3
                """).fetchall()
                
                print(f"  S3 encontró {len(s3_data)} registros para ToID {to_id}:")
                for row in s3_data:
                    print(f"    TransferID: {row[0]}")
                    print(f"    ToID: {row[1]}")
                    print(f"    To_AccountID: {row[2]} ⭐ VALOR S3")
                    print(f"    To_Profile: {row[3]}")
                    print(f"    TransactionType: {row[4]}")
                
                # Comparar
                if s3_data:
                    s3_account = s3_data[0][2]
                    print(f"  🔍 COMPARACIÓN:")
                    print(f"    Oracle To_AccountID: {oracle_account}")
                    print(f"    S3 To_AccountID: {s3_account}")
                    print(f"    ¿Coincide? {'✅' if str(oracle_account) == str(s3_account) else '❌'}")
        
        # 3. INVESTIGAR LÓGICA DE USER_DATA_TRX
        print(f"\n3️⃣ INVESTIGAR USER_DATA_TRX:")
        print("-" * 60)
        
        # Verificar qué valores tienen estos usuarios en USER_DATA_TRX
        user_ids = ['3262191', '3662073', '2078452', '1932101']
        
        for user_id in user_ids:
            print(f"\n📋 USER_ID: {user_id}")
            
            # Buscar en S3 USER_DATA_TRX
            try:
                user_data = conn.execute(f"""
                    SELECT 
                        USER_ID,
                        O_USER_ID,
                        WALLET_NUMBER,
                        ACCOUNT_ID,
                        PROFILE_TRX
                    FROM read_parquet('s3://ec-landing-process-01/datalake/MMONEY/USR_DATALAKE/USER_DATA_TRX/**/*.parquet', union_by_name=true)
                    WHERE O_USER_ID = '{user_id}'
                    LIMIT 1
                """).fetchall()
                
                if user_data:
                    row = user_data[0]
                    print(f"  USER_DATA_TRX:")
                    print(f"    WALLET_NUMBER: {row[2]} ⭐ DEBERÍA SER To_AccountID")
                    print(f"    ACCOUNT_ID: {row[3]}")
                    print(f"    PROFILE_TRX: {row[4]}")
                else:
                    print(f"  ❌ No encontrado en USER_DATA_TRX")
            except Exception as e:
                print(f"  ❌ Error accediendo USER_DATA_TRX: {e}")
        
        # 4. VERIFICAR LÓGICA ESPECÍFICA
        print(f"\n4️⃣ VERIFICAR LÓGICA ESPECÍFICA:")
        print("-" * 60)
        
        print("  Lógica actual en pipeline:")
        print("  CASE")
        print("      WHEN UPAYEE.M_USER_ID = '945661' THEN")
        print("          -- Caso especial 945661")
        print("      ELSE")
        print("          UPAYEE.WALLET_NUMBER")
        print("  END AS To_AccountID_Mobiquity")
        
        print(f"\n  🔍 VERIFICACIÓN:")
        print("  - Los casos problemáticos NO son USER_ID 945661")
        print("  - Deberían usar UPAYEE.WALLET_NUMBER directamente")
        print("  - Problema puede estar en los datos de WALLET_NUMBER")
        
        conn.close()
        
        print(f"\n5️⃣ CONCLUSIONES:")
        print("-" * 60)
        print("1. Los casos problemáticos no son del USER_ID especial 945661")
        print("2. La lógica debería usar WALLET_NUMBER directamente")
        print("3. Verificar si WALLET_NUMBER en USER_DATA_TRX tiene valores correctos")
        print("4. Posible problema en la sincronización de datos USER_DATA_TRX")
        
    except Exception as e:
        print(f"❌ Error en debug: {e}")
        import traceback
        traceback.print_exc()

def main():
    print("🚀 DEBUG CASOS To_AccountID_Mobiquity")
    print("=" * 80)
    print("Investigando casos específicos problemáticos")
    print()
    
    debug_to_accountid_casos()
    
    print("\n🏁 DEBUG COMPLETADO")

if __name__ == "__main__":
    main()
