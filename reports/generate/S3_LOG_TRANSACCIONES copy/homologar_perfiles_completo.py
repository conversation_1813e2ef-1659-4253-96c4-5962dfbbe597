#!/usr/bin/env python3
"""
Homologación exhaustiva de From_Profile y To_Profile
Análisis por cada tipo de perfil para verificar 100% de coincidencia
"""

import oracledb
import duckdb
import boto3
import sys
from collections import defaultdict

def homologar_perfiles_completo():
    """Homologa From_Profile y To_Profile por tipos de perfil"""
    print("🔍 HOMOLOGACIÓN EXHAUSTIVA: From_Profile + To_Profile")
    print("=" * 80)
    
    parquet_path = '/home/<USER>/REPORTE_2025/reports/generate/S3_LOG_TRANSACCIONES/TEMP_LOGS_TRANSACCIONES/20250615/PRE_LOG_TRX.parquet'
    fecha = '2025-06-15'
    
    try:
        # 1. ANÁLISIS COMPLETO DE ORACLE
        print("1️⃣ ANÁLISIS COMPLETO DE ORACLE:")
        print("-" * 60)
        
        connection = oracledb.connect(
            user='usr_datalake',
            password='U2024b1mD4t4l4k5',
            dsn='*************:1521/MMONEY'
        )
        
        cursor = connection.cursor()
        
        # Obtener estadísticas completas de Oracle
        cursor.execute("""
            SELECT 
                "From_Profile",
                "To_Profile",
                "TransactionType",
                COUNT(*) as CASOS
            FROM USR_DATALAKE.PRE_LOG_TRX 
            WHERE TRUNC("TransferDate") = TO_DATE(:fecha, 'YYYY-MM-DD')
            GROUP BY "From_Profile", "To_Profile", "TransactionType"
            ORDER BY COUNT(*) DESC
        """, {'fecha': fecha})
        
        oracle_stats = cursor.fetchall()
        
        print(f"✅ Oracle - Tipos de perfiles encontrados: {len(oracle_stats)}")
        print(f"\n📊 TOP 20 COMBINACIONES Oracle:")
        print(f"{'From_Profile':<40} {'To_Profile':<40} {'TransactionType':<20} {'CASOS'}")
        print("-" * 120)
        
        oracle_profile_map = {}
        oracle_from_profiles = defaultdict(int)
        oracle_to_profiles = defaultdict(int)
        oracle_total = 0
        
        for i, row in enumerate(oracle_stats):
            from_profile = row[0]
            to_profile = row[1]
            transaction_type = row[2]
            casos = row[3]
            
            key = (from_profile, to_profile, transaction_type)
            oracle_profile_map[key] = casos
            oracle_from_profiles[from_profile] += casos
            oracle_to_profiles[to_profile] += casos
            oracle_total += casos
            
            if i < 20:  # Mostrar top 20
                print(f"{from_profile:<40} {to_profile:<40} {transaction_type:<20} {casos}")
        
        print(f"\n📈 RESUMEN Oracle:")
        print(f"  Total registros: {oracle_total:,}")
        print(f"  From_Profile únicos: {len(oracle_from_profiles)}")
        print(f"  To_Profile únicos: {len(oracle_to_profiles)}")
        print(f"  Combinaciones únicas: {len(oracle_profile_map)}")
        
        cursor.close()
        connection.close()
        
        # 2. ANÁLISIS COMPLETO DE S3
        print(f"\n2️⃣ ANÁLISIS COMPLETO DE S3:")
        print("-" * 60)
        
        # Configurar DuckDB con S3
        conn = duckdb.connect()
        
        session = boto3.Session()
        credentials = session.get_credentials().get_frozen_credentials()
        
        conn.sql("INSTALL httpfs;")
        conn.sql("LOAD httpfs;")
        conn.sql("SET s3_region='us-east-1';")
        conn.sql("SET s3_use_ssl=true;")
        conn.sql("SET s3_url_style='path';")
        conn.sql(f"SET s3_access_key_id='{credentials.access_key}';")
        conn.sql(f"SET s3_secret_access_key='{credentials.secret_key}';")
        if credentials.token:
            conn.sql(f"SET s3_session_token='{credentials.token}';")
        
        # Obtener estadísticas completas de S3
        s3_stats = conn.execute(f"""
            SELECT 
                "From_Profile",
                "To_Profile",
                "TransactionType",
                COUNT(*) as CASOS
            FROM read_parquet('{parquet_path}')
            GROUP BY "From_Profile", "To_Profile", "TransactionType"
            ORDER BY COUNT(*) DESC
        """).fetchall()
        
        print(f"✅ S3 - Tipos de perfiles encontrados: {len(s3_stats)}")
        print(f"\n📊 TOP 20 COMBINACIONES S3:")
        print(f"{'From_Profile':<40} {'To_Profile':<40} {'TransactionType':<20} {'CASOS'}")
        print("-" * 120)
        
        s3_profile_map = {}
        s3_from_profiles = defaultdict(int)
        s3_to_profiles = defaultdict(int)
        s3_total = 0
        
        for i, row in enumerate(s3_stats):
            from_profile = row[0]
            to_profile = row[1]
            transaction_type = row[2]
            casos = row[3]
            
            key = (from_profile, to_profile, transaction_type)
            s3_profile_map[key] = casos
            s3_from_profiles[from_profile] += casos
            s3_to_profiles[to_profile] += casos
            s3_total += casos
            
            if i < 20:  # Mostrar top 20
                print(f"{from_profile:<40} {to_profile:<40} {transaction_type:<20} {casos}")
        
        print(f"\n📈 RESUMEN S3:")
        print(f"  Total registros: {s3_total:,}")
        print(f"  From_Profile únicos: {len(s3_from_profiles)}")
        print(f"  To_Profile únicos: {len(s3_to_profiles)}")
        print(f"  Combinaciones únicas: {len(s3_profile_map)}")
        
        conn.close()
        
        # 3. COMPARACIÓN DETALLADA POR From_Profile
        print(f"\n3️⃣ COMPARACIÓN DETALLADA From_Profile:")
        print("-" * 60)
        
        print(f"{'From_Profile':<50} {'Oracle':<15} {'S3':<15} {'ESTADO'}")
        print("-" * 95)
        
        from_coincidencias = 0
        from_diferencias = 0
        from_registros_oracle = 0
        from_registros_s3 = 0
        
        all_from_profiles = set(oracle_from_profiles.keys()) | set(s3_from_profiles.keys())
        
        for from_profile in sorted(all_from_profiles):
            oracle_count = oracle_from_profiles.get(from_profile, 0)
            s3_count = s3_from_profiles.get(from_profile, 0)
            
            from_registros_oracle += oracle_count
            from_registros_s3 += s3_count
            
            if oracle_count == s3_count:
                from_coincidencias += 1
                status = "✅ COINCIDE"
            else:
                from_diferencias += 1
                status = "❌ DIFIERE"
            
            print(f"{from_profile:<50} {oracle_count:<15} {s3_count:<15} {status}")
        
        # 4. COMPARACIÓN DETALLADA POR To_Profile
        print(f"\n4️⃣ COMPARACIÓN DETALLADA To_Profile:")
        print("-" * 60)
        
        print(f"{'To_Profile':<50} {'Oracle':<15} {'S3':<15} {'ESTADO'}")
        print("-" * 95)
        
        to_coincidencias = 0
        to_diferencias = 0
        to_registros_oracle = 0
        to_registros_s3 = 0
        
        all_to_profiles = set(oracle_to_profiles.keys()) | set(s3_to_profiles.keys())
        
        for to_profile in sorted(all_to_profiles):
            oracle_count = oracle_to_profiles.get(to_profile, 0)
            s3_count = s3_to_profiles.get(to_profile, 0)
            
            to_registros_oracle += oracle_count
            to_registros_s3 += s3_count
            
            if oracle_count == s3_count:
                to_coincidencias += 1
                status = "✅ COINCIDE"
            else:
                to_diferencias += 1
                status = "❌ DIFIERE"
            
            print(f"{to_profile:<50} {oracle_count:<15} {s3_count:<15} {status}")
        
        # 5. ANÁLISIS DE COMBINACIONES ESPECÍFICAS
        print(f"\n5️⃣ ANÁLISIS DE COMBINACIONES ESPECÍFICAS:")
        print("-" * 60)
        
        combinaciones_coincidentes = 0
        combinaciones_diferentes = 0
        
        all_combinations = set(oracle_profile_map.keys()) | set(s3_profile_map.keys())
        
        print(f"Total combinaciones únicas: {len(all_combinations)}")
        print(f"\nCOMBINACIONES CON DIFERENCIAS:")
        print(f"{'From_Profile':<30} {'To_Profile':<30} {'TransactionType':<20} {'Oracle':<10} {'S3':<10}")
        print("-" * 110)
        
        for combination in sorted(all_combinations):
            oracle_count = oracle_profile_map.get(combination, 0)
            s3_count = s3_profile_map.get(combination, 0)
            
            if oracle_count == s3_count:
                combinaciones_coincidentes += 1
            else:
                combinaciones_diferentes += 1
                from_prof, to_prof, trans_type = combination
                print(f"{from_prof:<30} {to_prof:<30} {trans_type:<20} {oracle_count:<10} {s3_count:<10}")
        
        # 6. RESUMEN FINAL EXHAUSTIVO
        print(f"\n6️⃣ RESUMEN FINAL EXHAUSTIVO:")
        print("-" * 60)
        
        print(f"📊 ESTADÍSTICAS GENERALES:")
        print(f"  Oracle total registros: {oracle_total:,}")
        print(f"  S3 total registros: {s3_total:,}")
        print(f"  Diferencia total: {abs(oracle_total - s3_total):,}")
        
        print(f"\n📊 ESTADÍSTICAS From_Profile:")
        print(f"  Perfiles coincidentes: {from_coincidencias}")
        print(f"  Perfiles diferentes: {from_diferencias}")
        print(f"  Registros Oracle: {from_registros_oracle:,}")
        print(f"  Registros S3: {from_registros_s3:,}")
        
        from_porcentaje = (from_coincidencias / len(all_from_profiles)) * 100 if all_from_profiles else 0
        print(f"  Porcentaje coincidencia: {from_porcentaje:.1f}%")
        
        print(f"\n📊 ESTADÍSTICAS To_Profile:")
        print(f"  Perfiles coincidentes: {to_coincidencias}")
        print(f"  Perfiles diferentes: {to_diferencias}")
        print(f"  Registros Oracle: {to_registros_oracle:,}")
        print(f"  Registros S3: {to_registros_s3:,}")
        
        to_porcentaje = (to_coincidencias / len(all_to_profiles)) * 100 if all_to_profiles else 0
        print(f"  Porcentaje coincidencia: {to_porcentaje:.1f}%")
        
        print(f"\n📊 ESTADÍSTICAS COMBINACIONES:")
        print(f"  Combinaciones coincidentes: {combinaciones_coincidentes}")
        print(f"  Combinaciones diferentes: {combinaciones_diferentes}")
        
        comb_porcentaje = (combinaciones_coincidentes / len(all_combinations)) * 100 if all_combinations else 0
        print(f"  Porcentaje coincidencia: {comb_porcentaje:.1f}%")
        
        # 7. VEREDICTO FINAL
        print(f"\n7️⃣ VEREDICTO FINAL:")
        print("-" * 60)
        
        if (from_diferencias == 0 and to_diferencias == 0 and 
            combinaciones_diferentes == 0 and oracle_total == s3_total):
            print("🎉 ¡HOMOLOGACIÓN PERFECTA!")
            print("✅ From_Profile: 100% coincidente")
            print("✅ To_Profile: 100% coincidente") 
            print("✅ Combinaciones: 100% coincidentes")
            print("✅ Total registros: 100% coincidentes")
            print("🚀 No se requieren correcciones")
        else:
            print(f"⚠️  HOMOLOGACIÓN NECESITA REVISIÓN")
            if from_diferencias > 0:
                print(f"❌ {from_diferencias} diferencias en From_Profile")
            if to_diferencias > 0:
                print(f"❌ {to_diferencias} diferencias en To_Profile")
            if combinaciones_diferentes > 0:
                print(f"❌ {combinaciones_diferentes} combinaciones diferentes")
            if oracle_total != s3_total:
                print(f"❌ Total registros diferentes: Oracle={oracle_total:,}, S3={s3_total:,}")
            print("🔧 Revisar lógica de perfiles en el pipeline")
        
    except Exception as e:
        print(f"❌ Error en homologación: {e}")
        import traceback
        traceback.print_exc()

def main():
    print("🚀 HOMOLOGACIÓN EXHAUSTIVA DE PERFILES")
    print("=" * 80)
    print("Análisis completo por tipos de From_Profile y To_Profile")
    print()
    
    homologar_perfiles_completo()
    
    print("\n🏁 HOMOLOGACIÓN COMPLETADA")

if __name__ == "__main__":
    main()
