#!/usr/bin/env python3
"""
Investigación específica del caso 945661
¿Por qué este usuario numérico tiene comportamiento especial?
"""

import oracledb
import sys

def investigar_caso_945661():
    """Investiga por qué el usuario 945661 es especial"""
    print("🔍 INVESTIGACIÓN CASO ESPECIAL: 945661")
    print("=" * 80)
    
    user_id = '945661'
    
    try:
        connection = oracledb.connect(
            user='usr_datalake',
            password='U2024b1mD4t4l4k5',
            dsn='10.240.131.10:1521/MMONEY'
        )
        
        cursor = connection.cursor()
        
        print("1️⃣ ANÁLISIS COMPLETO DEL USUARIO 945661:")
        print("-" * 60)
        
        # 1. USER_PROFILE
        cursor.execute("""
            SELECT
                USER_ID,
                ATTR7,
                ATTR8,
                MSISDN,
                STATUS
            FROM PDP_PROD10_MAINDB.USER_PROFILE
            WHERE USER_ID = :user_id
        """, {'user_id': user_id})
        
        profile_data = cursor.fetchone()
        if profile_data:
            print(f"  USER_PROFILE:")
            print(f"    USER_ID: {profile_data[0]}")
            print(f"    ATTR7: {profile_data[1]}")
            print(f"    ATTR8: {profile_data[2]} ⭐ VALOR ESPERADO")
            print(f"    MSISDN: {profile_data[3]}")
            print(f"    STATUS: {profile_data[4]}")
        
        # 2. MTX_WALLET (todas las wallets)
        cursor.execute("""
            SELECT 
                USER_ID,
                WALLET_NUMBER,
                STATUS,
                CREATED_ON,
                MODIFIED_ON,
                ROW_NUMBER() OVER (PARTITION BY USER_ID ORDER BY MODIFIED_ON DESC) as RN_DESC,
                ROW_NUMBER() OVER (PARTITION BY USER_ID ORDER BY MODIFIED_ON ASC) as RN_ASC
            FROM PDP_PROD10_MAINDBBUS.MTX_WALLET
            WHERE USER_ID = :user_id
            ORDER BY MODIFIED_ON DESC
        """, {'user_id': user_id})
        
        wallet_data = cursor.fetchall()
        
        print(f"\n  MTX_WALLET (todas las wallets):")
        oracle_from_wallet = '1866570'
        oracle_to_wallet = '501101120105612302'
        
        for i, row in enumerate(wallet_data):
            wallet_num = row[1]
            status = row[2]
            created = row[3]
            modified = row[4]
            rn_desc = row[5]
            rn_asc = row[6]
            
            status_desc = "ACTIVA" if status == 'Y' else "INACTIVA"
            
            markers = []
            if str(wallet_num) == oracle_from_wallet:
                markers.append("⭐ ORACLE FROM")
            if str(wallet_num) == oracle_to_wallet:
                markers.append("⭐ ORACLE TO")
            
            marker_str = " " + " ".join(markers) if markers else ""
            
            print(f"    [{i+1}] WALLET: {wallet_num}, STATUS: {status} ({status_desc}){marker_str}")
            print(f"        RN_DESC: {rn_desc}, RN_ASC: {rn_asc}")
            print(f"        CREATED: {created}, MODIFIED: {modified}")
        
        # 3. USER_DATA_TRX
        cursor.execute("""
            SELECT 
                USER_ID,
                O_USER_ID,
                WALLET_NUMBER,
                PROFILE_TRX,
                MSISDN,
                LOGIN_ID
            FROM USR_DATALAKE.USER_DATA_TRX
            WHERE O_USER_ID = :user_id
        """, {'user_id': user_id})
        
        user_data_result = cursor.fetchone()
        if user_data_result:
            print(f"\n  USER_DATA_TRX:")
            print(f"    USER_ID: {user_data_result[0]}")
            print(f"    O_USER_ID: {user_data_result[1]}")
            print(f"    WALLET_NUMBER: {user_data_result[2]} ⭐ VALOR USER_DATA_TRX")
            print(f"    PROFILE_TRX: {user_data_result[3]}")
            print(f"    MSISDN: {user_data_result[4]}")
            print(f"    LOGIN_ID: {user_data_result[5]}")
        
        # 4. Análisis de patrón
        print(f"\n2️⃣ ANÁLISIS DE PATRÓN:")
        print("-" * 60)
        
        print(f"  Oracle PRE_LOG_TRX usa:")
        print(f"    From_AccountID_Mobiquity: {oracle_from_wallet}")
        print(f"    To_AccountID_Mobiquity: {oracle_to_wallet}")
        
        print(f"  USER_DATA_TRX tiene:")
        print(f"    WALLET_NUMBER: {user_data_result[2] if user_data_result else 'N/A'}")
        
        print(f"  MTX_WALLET más reciente (RN_DESC=1):")
        if wallet_data:
            latest_wallet = wallet_data[0]
            print(f"    WALLET: {latest_wallet[1]}, STATUS: {latest_wallet[2]}")
        
        print(f"  🔍 PATRÓN IDENTIFICADO:")
        print(f"    - Oracle FROM usa wallet INACTIVA (RN_DESC=2): {oracle_from_wallet}")
        print(f"    - Oracle TO usa wallet ACTIVA (RN_DESC=1): {oracle_to_wallet}")
        print(f"    - Este usuario tiene lógica especial diferente al patrón general")
        
        # 5. Buscar otros usuarios con patrón similar
        print(f"\n3️⃣ BÚSQUEDA DE OTROS USUARIOS CON PATRÓN SIMILAR:")
        print("-" * 60)
        
        # Buscar usuarios numéricos que también usan MTX_WALLET en lugar de USER_DATA_TRX
        cursor.execute("""
            SELECT 
                P."FromID_Mobiquity",
                P."From_AccountID_Mobiquity",
                U.WALLET_NUMBER,
                CASE 
                    WHEN REGEXP_LIKE(P."FromID_Mobiquity", '^[0-9]+$') THEN 'NUMERIC'
                    ELSE 'OTHER'
                END as USER_TYPE
            FROM (
                SELECT DISTINCT "FromID_Mobiquity", "From_AccountID_Mobiquity"
                FROM USR_DATALAKE.PRE_LOG_TRX
                WHERE REGEXP_LIKE("FromID_Mobiquity", '^[0-9]+$')
                AND ROWNUM <= 100
            ) P
            LEFT JOIN USR_DATALAKE.USER_DATA_TRX U ON P."FromID_Mobiquity" = U.O_USER_ID
            WHERE P."From_AccountID_Mobiquity" <> U.WALLET_NUMBER
            ORDER BY P."FromID_Mobiquity"
        """)
        
        similar_cases = cursor.fetchall()
        
        print(f"  Usuarios numéricos que NO usan USER_DATA_TRX: {len(similar_cases)}")
        for i, case in enumerate(similar_cases[:5]):
            print(f"    [{i+1}] USER: {case[0]}, Oracle: {case[1]}, UserData: {case[2]}")
        
        # 6. Verificar si hay lógica especial en SP
        print(f"\n4️⃣ VERIFICACIÓN DE LÓGICA ESPECIAL:")
        print("-" * 60)
        
        print(f"  ¿Es usuario especial en alguna lista?")
        
        # Verificar si está en listas especiales
        special_lists = [
            ('SPECIAL_USERS', ['466787', '580943', '1597312', '1885838']),
            ('ADJUSTMENT_USERS', ['945661']),  # Hipótesis
        ]
        
        for list_name, user_list in special_lists:
            if user_id in user_list:
                print(f"    ✅ Encontrado en {list_name}")
            else:
                print(f"    ❌ NO encontrado en {list_name}")
        
        cursor.close()
        connection.close()
        
        print(f"\n💡 CONCLUSIONES:")
        print("-" * 60)
        print("1. Usuario 945661 es numérico pero tiene comportamiento especial")
        print("2. Oracle usa wallets específicas de MTX_WALLET, no USER_DATA_TRX")
        print("3. Patrón: FROM usa wallet inactiva, TO usa wallet activa")
        print("4. Necesitamos lógica específica para este tipo de usuarios")
        
    except Exception as e:
        print(f"❌ Error en investigación: {e}")
        import traceback
        traceback.print_exc()

def main():
    print("🚀 INVESTIGACIÓN CASO ESPECIAL 945661")
    print("=" * 80)
    print("¿Por qué este usuario numérico tiene comportamiento especial?")
    print()
    
    investigar_caso_945661()
    
    print("\n🏁 INVESTIGACIÓN COMPLETADA")

if __name__ == "__main__":
    main()
