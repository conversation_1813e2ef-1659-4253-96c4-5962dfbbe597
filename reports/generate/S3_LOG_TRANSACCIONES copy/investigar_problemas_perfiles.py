#!/usr/bin/env python3
"""
Investigar los problemas específicos de perfiles identificados
"""

import oracledb
import sys

def investigar_problemas_perfiles():
    """Investiga los problemas específicos de perfiles"""
    print("🔍 INVESTIGACIÓN: Problemas Específicos de Perfiles")
    print("=" * 80)
    
    try:
        connection = oracledb.connect(
            user='usr_datalake',
            password='U2024b1mD4t4l4k5',
            dsn='10.240.131.10:1521/MMONEY'
        )
        
        cursor = connection.cursor()
        
        print("1️⃣ PROBLEMA REFUND - BACKUS:")
        print("-" * 60)
        
        # Analizar casos REFUND con BACKUS
        cursor.execute("""
            SELECT 
                P."TransferID",
                P."FromID_Mobiquity",
                P."From_Profile",
                P."TransactionType",
                U.LOGIN_ID,
                U.PROFILE_TRX
            FROM USR_DATALAKE.PRE_LOG_TRX P
            LEFT JOIN USR_DATALAKE.USER_DATA_TRX U ON P."FromID_Mobiquity" = U.O_USER_ID
            WHERE P."TransactionType" = 'REFUND'
            AND P."From_Profile" LIKE '%BACKUS%'
            AND ROWNUM <= 5
        """)
        
        refund_cases = cursor.fetchall()
        
        print(f"  Casos REFUND con BACKUS:")
        for row in refund_cases:
            print(f"    TransferID: {row[0]}")
            print(f"    FromID: {row[1]}")
            print(f"    From_Profile: '{row[2]}'")
            print(f"    LOGIN_ID: {row[4]}")
            print(f"    PROFILE_TRX: {row[5]}")
            print()
        
        print("2️⃣ PROBLEMA PAYMENT - COMERCIO:")
        print("-" * 60)
        
        # Analizar casos PAYMENT con COMERCIO
        cursor.execute("""
            SELECT 
                P."TransferID",
                P."ToID_Mobiquity",
                P."To_Profile",
                P."TransactionType",
                U.LOGIN_ID,
                U.PROFILE_TRX
            FROM USR_DATALAKE.PRE_LOG_TRX P
            LEFT JOIN USR_DATALAKE.USER_DATA_TRX U ON P."ToID_Mobiquity" = U.O_USER_ID
            WHERE P."TransactionType" = 'PAYMENT'
            AND P."To_Profile" = 'FCOMPARTAMOS COMERCIO'
            AND ROWNUM <= 5
        """)
        
        payment_cases = cursor.fetchall()
        
        print(f"  Casos PAYMENT con COMERCIO:")
        for row in payment_cases:
            print(f"    TransferID: {row[0]}")
            print(f"    ToID: {row[1]}")
            print(f"    To_Profile: '{row[2]}'")
            print(f"    LOGIN_ID: {row[4]}")
            print(f"    PROFILE_TRX: {row[5]}")
            print()
        
        print("3️⃣ PROBLEMA EXTERNAL_PAYMENT - CLARO:")
        print("-" * 60)
        
        # Analizar casos EXTERNAL_PAYMENT con CLARO
        cursor.execute("""
            SELECT 
                P."TransferID",
                P."ToID_Mobiquity",
                P."To_Profile",
                P."TransactionType",
                U.LOGIN_ID,
                U.PROFILE_TRX
            FROM USR_DATALAKE.PRE_LOG_TRX P
            LEFT JOIN USR_DATALAKE.USER_DATA_TRX U ON P."ToID_Mobiquity" = U.O_USER_ID
            WHERE P."TransactionType" = 'EXTERNAL_PAYMENT'
            AND P."To_Profile" LIKE '%CLARO%'
            AND ROWNUM <= 5
        """)
        
        claro_cases = cursor.fetchall()
        
        print(f"  Casos EXTERNAL_PAYMENT con CLARO:")
        for row in claro_cases:
            print(f"    TransferID: {row[0]}")
            print(f"    ToID: {row[1]}")
            print(f"    To_Profile: '{row[2]}'")
            print(f"    LOGIN_ID: {row[4]}")
            print(f"    PROFILE_TRX: {row[5]}")
            print()
        
        print("4️⃣ ANÁLISIS DE LÓGICA From_Profile PARA REFUND:")
        print("-" * 60)
        
        # Verificar lógica específica para REFUND
        cursor.execute("""
            SELECT DISTINCT
                P."TransactionType",
                P."From_Profile",
                U.LOGIN_ID,
                U.PROFILE_TRX,
                COUNT(*) as CASOS
            FROM USR_DATALAKE.PRE_LOG_TRX P
            LEFT JOIN USR_DATALAKE.USER_DATA_TRX U ON P."FromID_Mobiquity" = U.O_USER_ID
            WHERE P."TransactionType" = 'REFUND'
            GROUP BY P."TransactionType", P."From_Profile", U.LOGIN_ID, U.PROFILE_TRX
            ORDER BY COUNT(*) DESC
        """)
        
        refund_patterns = cursor.fetchall()
        
        print(f"  Patrones REFUND:")
        print(f"{'TransactionType':<20} {'From_Profile':<35} {'LOGIN_ID':<15} {'PROFILE_TRX':<15} {'CASOS'}")
        print("-" * 100)
        
        for row in refund_patterns:
            print(f"{row[0]:<20} {row[1]:<35} {row[2]:<15} {row[3]:<15} {row[4]}")
        
        print("\n5️⃣ ANÁLISIS DE LÓGICA To_Profile PARA PAYMENT:")
        print("-" * 60)
        
        # Verificar lógica específica para PAYMENT
        cursor.execute("""
            SELECT DISTINCT
                P."TransactionType",
                P."To_Profile",
                U.LOGIN_ID,
                U.PROFILE_TRX,
                COUNT(*) as CASOS
            FROM USR_DATALAKE.PRE_LOG_TRX P
            LEFT JOIN USR_DATALAKE.USER_DATA_TRX U ON P."ToID_Mobiquity" = U.O_USER_ID
            WHERE P."TransactionType" = 'PAYMENT'
            AND P."To_Profile" IN ('FCOMPARTAMOS COMERCIO', 'FCOMPARTAMOS Biller')
            GROUP BY P."TransactionType", P."To_Profile", U.LOGIN_ID, U.PROFILE_TRX
            ORDER BY COUNT(*) DESC
        """)
        
        payment_patterns = cursor.fetchall()
        
        print(f"  Patrones PAYMENT:")
        print(f"{'TransactionType':<20} {'To_Profile':<25} {'LOGIN_ID':<15} {'PROFILE_TRX':<15} {'CASOS'}")
        print("-" * 90)
        
        for row in payment_patterns:
            print(f"{row[0]:<20} {row[1]:<25} {row[2]:<15} {row[3]:<15} {row[4]}")
        
        cursor.close()
        connection.close()
        
        print("\n6️⃣ CONCLUSIONES:")
        print("-" * 60)
        print("1. REFUND: Oracle usa 'PROVEEDOR DE SERVICIOS', S3 usa 'Biller'")
        print("2. PAYMENT: Oracle usa 'COMERCIO', S3 usa 'Biller'")
        print("3. EXTERNAL_PAYMENT: Oracle usa 'CLARO', S3 usa 'AIRTIMECLARO'")
        print("4. Necesitamos corregir la lógica específica para estos casos")
        
        print(f"\n💡 CORRECCIONES REQUERIDAS:")
        print("1. Para REFUND: From_Profile debe usar 'PROVEEDOR DE SERVICIOS'")
        print("2. Para PAYMENT: To_Profile debe usar 'COMERCIO' no 'Biller'")
        print("3. Para EXTERNAL_PAYMENT: Mapear 'airtimeclaro' → 'claro'")
        
    except Exception as e:
        print(f"❌ Error en investigación: {e}")
        import traceback
        traceback.print_exc()

def main():
    print("🚀 INVESTIGACIÓN PROBLEMAS DE PERFILES")
    print("=" * 80)
    print("Analizando diferencias específicas identificadas")
    print()
    
    investigar_problemas_perfiles()
    
    print("\n🏁 INVESTIGACIÓN COMPLETADA")

if __name__ == "__main__":
    main()
