#!/usr/bin/env python3
"""
Script para analizar las diferencias en la LÓGICA entre Oracle y S3
Compara cómo se obtiene From_AccountID_Mobiquity en ambos procesos
"""

import oracledb
import duckdb
import boto3
import sys
import pandas as pd

def analizar_logica_oracle():
    """Analiza la lógica de Oracle paso a paso"""
    print("🔍 ANÁLISIS DE LÓGICA ORACLE")
    print("=" * 60)
    
    try:
        connection = oracledb.connect(
            user='usr_datalake',
            password='U2024b1mD4t4l4k5',
            dsn='*************:1521/MMONEY'
        )
        
        cursor = connection.cursor()
        user_id = '945661'
        
        print("1️⃣ PASO 1: USER_DATA_TRX (Oracle)")
        print("-" * 40)
        
        # Paso 1: ¿Qué devuelve USER_DATA_TRX?
        cursor.execute("""
            SELECT 
                USER_ID,
                O_USER_ID AS M_USER_ID,
                WALLET_NUMBER AS ACCOUNT_ID,
                PROFILE_TRX AS PROFILE,
                MSISDN,
                LOGIN_ID
            FROM USR_DATALAKE.USER_DATA_TRX
            WHERE O_USER_ID = :user_id
        """, {'user_id': user_id})
        
        user_data_result = cursor.fetchall()
        if user_data_result:
            row = user_data_result[0]
            print(f"  USER_ID: {row[0]}")
            print(f"  M_USER_ID (O_USER_ID): {row[1]}")
            print(f"  ACCOUNT_ID (WALLET_NUMBER): {row[2]} ⭐ ESTE ES EL VALOR CLAVE")
            print(f"  PROFILE: {row[3]}")
            print(f"  MSISDN: {row[4]}")
            print(f"  LOGIN_ID: {row[5]}")
            
            oracle_account_id = row[2]
        else:
            print("  ❌ No encontrado en USER_DATA_TRX")
            oracle_account_id = None
        
        print("\n2️⃣ PASO 2: ¿Cómo se construye USER_DATA_TRX?")
        print("-" * 40)
        print("  USER_DATA_TRX es una tabla consolidada en USR_DATALAKE")
        print("  Necesitamos ver cómo se pobló originalmente")
        
        # Verificar si USER_DATA_TRX tiene lógica especial
        cursor.execute("""
            SELECT COUNT(*) as total_records
            FROM USR_DATALAKE.USER_DATA_TRX
            WHERE O_USER_ID = :user_id
        """, {'user_id': user_id})
        
        count_result = cursor.fetchone()
        print(f"  Total registros para USER_ID {user_id}: {count_result[0]}")
        
        print("\n3️⃣ PASO 3: Verificar fuentes originales")
        print("-" * 40)
        
        # USER_ACCOUNTS
        cursor.execute("""
            SELECT INSTRUMENT_VALUE, IS_DEFAULT_INSTRUMENT, STATUS
            FROM PDP_PROD10_MAINDB.USER_ACCOUNTS
            WHERE USER_ID = :user_id AND INSTRUMENT_TYPE = 'WALLET'
            ORDER BY IS_DEFAULT_INSTRUMENT DESC
        """, {'user_id': user_id})
        
        user_accounts = cursor.fetchall()
        print("  USER_ACCOUNTS:")
        for row in user_accounts:
            print(f"    INSTRUMENT_VALUE: {row[0]}, DEFAULT: {row[1]}, STATUS: {row[2]}")
        
        # MTX_WALLET
        cursor.execute("""
            SELECT WALLET_NUMBER, STATUS, MODIFIED_ON
            FROM PDP_PROD10_MAINDBBUS.MTX_WALLET
            WHERE USER_ID = :user_id
            ORDER BY MODIFIED_ON DESC
        """, {'user_id': user_id})
        
        mtx_wallet = cursor.fetchall()
        print("  MTX_WALLET:")
        for i, row in enumerate(mtx_wallet[:3]):
            print(f"    [{i+1}] WALLET_NUMBER: {row[0]}, STATUS: {row[1]}, MODIFIED: {row[2]}")
        
        print(f"\n4️⃣ RESULTADO FINAL ORACLE:")
        print("-" * 40)
        print(f"  Oracle USER_DATA_TRX.ACCOUNT_ID: {oracle_account_id}")
        print(f"  Este valor se usa directamente en SP_PRE_LOG_TRX como UPAYER.ACCOUNT_ID")
        
        cursor.close()
        connection.close()
        
        return oracle_account_id
        
    except Exception as e:
        print(f"❌ Error analizando Oracle: {e}")
        return None

def analizar_logica_s3():
    """Analiza la lógica de S3 paso a paso"""
    print("\n🔍 ANÁLISIS DE LÓGICA S3")
    print("=" * 60)
    
    try:
        # Configurar DuckDB con S3
        conn = duckdb.connect()
        
        # Configurar credenciales S3
        session = boto3.Session()
        credentials = session.get_credentials().get_frozen_credentials()
        
        conn.sql("INSTALL httpfs;")
        conn.sql("LOAD httpfs;")
        conn.sql("SET s3_region='us-east-1';")
        conn.sql("SET s3_use_ssl=true;")
        conn.sql("SET s3_url_style='path';")
        conn.sql(f"SET s3_access_key_id='{credentials.access_key}';")
        conn.sql(f"SET s3_secret_access_key='{credentials.secret_key}';")
        if credentials.token:
            conn.sql(f"SET s3_session_token='{credentials.token}';")
        
        # Rutas S3
        s3_bucket_silver = "prd-datalake-silver-zone-************"
        s3_bucket_golden = "prd-datalake-golden-zone-************"
        
        user_data_trx_path = f's3://{s3_bucket_golden}/LOGS_USUARIOS/USER_DATA_TRX.parquet'
        user_accounts_path = f's3://{s3_bucket_silver}/PDP_PROD10_MAINDB/USER_ACCOUNTS_ORA/consolidado_puro.parquet'
        
        user_id = '945661'
        
        print("1️⃣ PASO 1: USER_DATA_TRX (S3)")
        print("-" * 40)
        
        # Paso 1: ¿Qué devuelve USER_DATA_TRX en S3?
        result = conn.execute(f"""
            SELECT 
                USER_ID,
                O_USER_ID,
                WALLET_NUMBER AS ACCOUNT_ID,
                PROFILE_TRX AS PROFILE,
                MSISDN,
                LOGIN_ID
            FROM read_parquet('{user_data_trx_path}')
            WHERE O_USER_ID = '{user_id}'
        """).fetchall()
        
        if result:
            row = result[0]
            print(f"  USER_ID: {row[0]}")
            print(f"  O_USER_ID: {row[1]}")
            print(f"  ACCOUNT_ID (WALLET_NUMBER): {row[2]} ⭐ ESTE ES EL VALOR CLAVE")
            print(f"  PROFILE: {row[3]}")
            print(f"  MSISDN: {row[4]}")
            print(f"  LOGIN_ID: {row[5]}")
            
            s3_account_id = row[2]
        else:
            print("  ❌ No encontrado en USER_DATA_TRX S3")
            s3_account_id = None
        
        print("\n2️⃣ PASO 2: USER_ACCOUNTS (S3) - Tu lógica adicional")
        print("-" * 40)
        
        # Paso 2: ¿Qué devuelve USER_ACCOUNTS en S3?
        result = conn.execute(f"""
            SELECT 
                INSTRUMENT_VALUE,
                IS_DEFAULT_INSTRUMENT,
                STATUS
            FROM read_parquet('{user_accounts_path}')
            WHERE USER_ID = '{user_id}' 
            AND INSTRUMENT_TYPE = 'WALLET'
            AND IS_DEFAULT_INSTRUMENT = 'Y'
        """).fetchall()
        
        if result:
            row = result[0]
            print(f"  INSTRUMENT_VALUE: {row[0]} ⭐ TU LÓGICA USA ESTE")
            print(f"  IS_DEFAULT_INSTRUMENT: {row[1]}")
            print(f"  STATUS: {row[2]}")
            
            s3_instrument_value = row[0]
        else:
            print("  ❌ No encontrado en USER_ACCOUNTS S3")
            s3_instrument_value = None
        
        print("\n3️⃣ PASO 3: Tu lógica COALESCE")
        print("-" * 40)
        print("  Tu código hace: COALESCE(UA_PAYER.INSTRUMENT_VALUE, UPAYER.ACCOUNT_ID)")
        print(f"  INSTRUMENT_VALUE: {s3_instrument_value}")
        print(f"  ACCOUNT_ID: {s3_account_id}")
        
        final_value = s3_instrument_value if s3_instrument_value else s3_account_id
        print(f"  RESULTADO FINAL: {final_value}")
        
        conn.close()
        
        return s3_account_id, s3_instrument_value, final_value
        
    except Exception as e:
        print(f"❌ Error analizando S3: {e}")
        return None, None, None

def comparar_logicas():
    """Compara las lógicas y encuentra la diferencia"""
    print("\n🎯 COMPARACIÓN DE LÓGICAS")
    print("=" * 60)
    
    oracle_account_id = analizar_logica_oracle()
    s3_account_id, s3_instrument_value, s3_final = analizar_logica_s3()
    
    print("\n📊 RESUMEN COMPARATIVO:")
    print("-" * 40)
    print(f"Oracle USER_DATA_TRX.ACCOUNT_ID: {oracle_account_id}")
    print(f"S3 USER_DATA_TRX.ACCOUNT_ID: {s3_account_id}")
    print(f"S3 USER_ACCOUNTS.INSTRUMENT_VALUE: {s3_instrument_value}")
    print(f"S3 RESULTADO FINAL (COALESCE): {s3_final}")
    
    print("\n🔍 ANÁLISIS DE LA DIFERENCIA:")
    print("-" * 40)
    
    if oracle_account_id == s3_account_id:
        print("✅ USER_DATA_TRX.ACCOUNT_ID coincide entre Oracle y S3")
    else:
        print("❌ USER_DATA_TRX.ACCOUNT_ID difiere entre Oracle y S3")
        print(f"   Oracle: {oracle_account_id}")
        print(f"   S3: {s3_account_id}")
    
    if oracle_account_id == s3_final:
        print("✅ El resultado final de S3 coincide con Oracle")
    else:
        print("❌ El resultado final de S3 NO coincide con Oracle")
        print(f"   Oracle usa: {oracle_account_id}")
        print(f"   S3 usa: {s3_final}")
        print("   🔧 AQUÍ ESTÁ LA DIFERENCIA EN LA LÓGICA")
    
    print("\n💡 CONCLUSIÓN:")
    print("-" * 40)
    print("Oracle usa directamente USER_DATA_TRX.ACCOUNT_ID")
    print("Tu implementación S3 usa COALESCE(USER_ACCOUNTS.INSTRUMENT_VALUE, USER_DATA_TRX.ACCOUNT_ID)")
    print("La diferencia está en que USER_DATA_TRX tiene valores diferentes entre Oracle y S3")

def main():
    print("🚀 ANÁLISIS DE DIFERENCIAS EN LÓGICA: ORACLE vs S3")
    print("=" * 80)
    print("Analizando cómo se obtiene From_AccountID_Mobiquity en cada proceso")
    print()
    
    comparar_logicas()
    
    print("\n🏁 ANÁLISIS COMPLETADO")

if __name__ == "__main__":
    main()
