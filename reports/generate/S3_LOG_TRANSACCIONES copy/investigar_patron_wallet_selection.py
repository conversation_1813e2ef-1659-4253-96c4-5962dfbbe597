#!/usr/bin/env python3
"""
Investigación del patrón de selección de wallets en Oracle
¿Cuándo usa wallet activa vs inactiva?
"""

import oracledb
import sys

def investigar_patron_wallet():
    """Investiga el patrón de selección de wallets"""
    print("🔍 INVESTIGACIÓN PATRÓN DE SELECCIÓN DE WALLETS")
    print("=" * 80)
    
    try:
        connection = oracledb.connect(
            user='usr_datalake',
            password='U2024b1mD4t4l4k5',
            dsn='*************:1521/MMONEY'
        )
        
        cursor = connection.cursor()
        
        print("1️⃣ ANÁLISIS DE MÚLTIPLES CASOS:")
        print("-" * 60)
        
        # Obtener casos donde Oracle difiere de USER_DATA_TRX
        cursor.execute("""
            SELECT 
                P."FromID_Mobiquity",
                P."From_AccountID_Mobiquity" as ORACLE_WALLET,
                U.WALLET_NUMBER as USER_DATA_WALLET,
                P."TransactionType",
                P."From_BankDomain"
            FROM (
                SELECT "FromID_Mobiquity", "From_AccountID_Mobiquity", "TransactionType", "From_BankDomain"
                FROM USR_DATALAKE.PRE_LOG_TRX
                WHERE ROWNUM <= 50
            ) P
            LEFT JOIN USR_DATALAKE.USER_DATA_TRX U ON P."FromID_Mobiquity" = U.O_USER_ID
            WHERE P."From_AccountID_Mobiquity" <> U.WALLET_NUMBER
            ORDER BY P."FromID_Mobiquity"
        """)
        
        different_cases = cursor.fetchall()
        
        print(f"  Casos donde Oracle difiere de USER_DATA_TRX: {len(different_cases)}")
        
        for i, case in enumerate(different_cases[:5]):
            from_id = case[0]
            oracle_wallet = case[1]
            user_data_wallet = case[2]
            transaction_type = case[3]
            bank_domain = case[4]
            
            print(f"\n  [{i+1}] FromID: {from_id}")
            print(f"      Oracle: {oracle_wallet}")
            print(f"      UserData: {user_data_wallet}")
            print(f"      TransactionType: {transaction_type}")
            print(f"      BankDomain: {bank_domain}")
            
            # Analizar MTX_WALLET para este usuario
            cursor.execute("""
                SELECT 
                    WALLET_NUMBER,
                    STATUS,
                    MODIFIED_ON,
                    ROW_NUMBER() OVER (PARTITION BY USER_ID ORDER BY MODIFIED_ON DESC) as RN_DESC,
                    ROW_NUMBER() OVER (PARTITION BY USER_ID ORDER BY MODIFIED_ON ASC) as RN_ASC
                FROM PDP_PROD10_MAINDBBUS.MTX_WALLET
                WHERE USER_ID = :from_id
                ORDER BY MODIFIED_ON DESC
            """, {'from_id': from_id})
            
            wallets = cursor.fetchall()
            
            print(f"      MTX_WALLET:")
            oracle_wallet_found = False
            for wallet in wallets:
                wallet_num = wallet[0]
                status = wallet[1]
                modified = wallet[2]
                rn_desc = wallet[3]
                rn_asc = wallet[4]
                
                status_desc = "ACTIVA" if status == 'Y' else "INACTIVA"
                marker = " ⭐ ORACLE USA ESTA" if str(wallet_num) == str(oracle_wallet) else ""
                
                print(f"        WALLET: {wallet_num}, STATUS: {status} ({status_desc})")
                print(f"        RN_DESC: {rn_desc}, RN_ASC: {rn_asc}, MODIFIED: {modified}{marker}")
                
                if str(wallet_num) == str(oracle_wallet):
                    oracle_wallet_found = True
                    print(f"        🎯 PATRÓN: Oracle usa RN_DESC={rn_desc}, STATUS={status}")
            
            if not oracle_wallet_found:
                print(f"        ❌ Oracle wallet no encontrada en MTX_WALLET")
        
        print(f"\n2️⃣ ANÁLISIS DE PATRÓN POR TRANSACTION_TYPE:")
        print("-" * 60)
        
        # Analizar si el patrón depende del tipo de transacción
        cursor.execute("""
            SELECT 
                P."TransactionType",
                COUNT(*) as TOTAL_CASES,
                SUM(CASE WHEN P."From_AccountID_Mobiquity" = U.WALLET_NUMBER THEN 1 ELSE 0 END) as USER_DATA_MATCHES,
                SUM(CASE WHEN P."From_AccountID_Mobiquity" <> U.WALLET_NUMBER THEN 1 ELSE 0 END) as DIFFERENT_CASES
            FROM (
                SELECT "FromID_Mobiquity", "From_AccountID_Mobiquity", "TransactionType"
                FROM USR_DATALAKE.PRE_LOG_TRX
                WHERE ROWNUM <= 1000
            ) P
            LEFT JOIN USR_DATALAKE.USER_DATA_TRX U ON P."FromID_Mobiquity" = U.O_USER_ID
            WHERE U.WALLET_NUMBER IS NOT NULL
            GROUP BY P."TransactionType"
            ORDER BY DIFFERENT_CASES DESC
        """)
        
        pattern_by_type = cursor.fetchall()
        
        print(f"  Patrón por tipo de transacción:")
        for row in pattern_by_type:
            transaction_type = row[0]
            total = row[1]
            matches = row[2]
            different = row[3]
            percentage_different = (different / total * 100) if total > 0 else 0
            
            print(f"    {transaction_type}: {different}/{total} diferentes ({percentage_different:.1f}%)")
        
        print(f"\n3️⃣ ANÁLISIS DE PATRÓN POR BANK_DOMAIN:")
        print("-" * 60)
        
        # Analizar si el patrón depende del bank domain
        cursor.execute("""
            SELECT 
                P."From_BankDomain",
                COUNT(*) as TOTAL_CASES,
                SUM(CASE WHEN P."From_AccountID_Mobiquity" = U.WALLET_NUMBER THEN 1 ELSE 0 END) as USER_DATA_MATCHES,
                SUM(CASE WHEN P."From_AccountID_Mobiquity" <> U.WALLET_NUMBER THEN 1 ELSE 0 END) as DIFFERENT_CASES
            FROM (
                SELECT "FromID_Mobiquity", "From_AccountID_Mobiquity", "From_BankDomain"
                FROM USR_DATALAKE.PRE_LOG_TRX
                WHERE ROWNUM <= 1000
            ) P
            LEFT JOIN USR_DATALAKE.USER_DATA_TRX U ON P."FromID_Mobiquity" = U.O_USER_ID
            WHERE U.WALLET_NUMBER IS NOT NULL
            GROUP BY P."From_BankDomain"
            ORDER BY DIFFERENT_CASES DESC
        """)
        
        pattern_by_domain = cursor.fetchall()
        
        print(f"  Patrón por bank domain:")
        for row in pattern_by_domain:
            bank_domain = row[0]
            total = row[1]
            matches = row[2]
            different = row[3]
            percentage_different = (different / total * 100) if total > 0 else 0
            
            print(f"    {bank_domain}: {different}/{total} diferentes ({percentage_different:.1f}%)")
        
        print(f"\n4️⃣ ANÁLISIS DE PATRÓN POR TIPO DE USER_ID:")
        print("-" * 60)
        
        # Analizar si el patrón depende del formato del USER_ID
        cursor.execute("""
            SELECT 
                CASE 
                    WHEN P."FromID_Mobiquity" LIKE 'US.%' THEN 'US_FORMAT'
                    WHEN REGEXP_LIKE(P."FromID_Mobiquity", '^[0-9]+$') THEN 'NUMERIC'
                    ELSE 'OTHER'
                END as USER_ID_TYPE,
                COUNT(*) as TOTAL_CASES,
                SUM(CASE WHEN P."From_AccountID_Mobiquity" = U.WALLET_NUMBER THEN 1 ELSE 0 END) as USER_DATA_MATCHES,
                SUM(CASE WHEN P."From_AccountID_Mobiquity" <> U.WALLET_NUMBER THEN 1 ELSE 0 END) as DIFFERENT_CASES
            FROM (
                SELECT "FromID_Mobiquity", "From_AccountID_Mobiquity"
                FROM USR_DATALAKE.PRE_LOG_TRX
                WHERE ROWNUM <= 1000
            ) P
            LEFT JOIN USR_DATALAKE.USER_DATA_TRX U ON P."FromID_Mobiquity" = U.O_USER_ID
            WHERE U.WALLET_NUMBER IS NOT NULL
            GROUP BY CASE 
                WHEN P."FromID_Mobiquity" LIKE 'US.%' THEN 'US_FORMAT'
                WHEN REGEXP_LIKE(P."FromID_Mobiquity", '^[0-9]+$') THEN 'NUMERIC'
                ELSE 'OTHER'
            END
            ORDER BY DIFFERENT_CASES DESC
        """)
        
        pattern_by_user_type = cursor.fetchall()
        
        print(f"  Patrón por tipo de USER_ID:")
        for row in pattern_by_user_type:
            user_type = row[0]
            total = row[1]
            matches = row[2]
            different = row[3]
            percentage_different = (different / total * 100) if total > 0 else 0
            
            print(f"    {user_type}: {different}/{total} diferentes ({percentage_different:.1f}%)")
        
        cursor.close()
        connection.close()
        
        print(f"\n💡 CONCLUSIONES:")
        print("-" * 60)
        print("1. Oracle tiene lógica específica que no siempre usa USER_DATA_TRX")
        print("2. El patrón puede depender de TransactionType, BankDomain o formato de USER_ID")
        print("3. Algunos usuarios usan wallet más reciente, otros usan wallets más antiguas")
        print("4. Necesitamos identificar la regla exacta de selección")
        
    except Exception as e:
        print(f"❌ Error en investigación: {e}")
        import traceback
        traceback.print_exc()

def main():
    print("🚀 INVESTIGACIÓN PATRÓN DE SELECCIÓN DE WALLETS")
    print("=" * 80)
    print("¿Cuándo Oracle usa wallet activa vs inactiva?")
    print()
    
    investigar_patron_wallet()
    
    print("\n🏁 INVESTIGACIÓN COMPLETADA")

if __name__ == "__main__":
    main()
