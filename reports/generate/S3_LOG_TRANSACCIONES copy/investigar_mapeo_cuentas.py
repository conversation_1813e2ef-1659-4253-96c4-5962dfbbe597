#!/usr/bin/env python3
"""
Script para investigar el mapeo de cuentas entre Oracle y S3
Analiza las diferencias en From_AccountID_Mobiquity
"""

import oracledb
import duckdb
import sys
import pandas as pd
from pathlib import Path

def investigar_usuario_oracle(from_id_mobiquity: str):
    """Investiga todas las fuentes de datos en Oracle para un usuario específico"""
    try:
        connection = oracledb.connect(
            user='usr_datalake',
            password='U2024b1mD4t4l4k5',
            dsn='*************:1521/MMONEY'
        )
        
        print(f"🔍 INVESTIGANDO USUARIO {from_id_mobiquity} EN ORACLE")
        print("=" * 80)
        
        cursor = connection.cursor()
        
        # 1. USER_ACCOUNTS
        print("1️⃣ PDP_PROD10_MAINDB.USER_ACCOUNTS:")
        cursor.execute("""
            SELECT USER_ID, INSTRUMENT_VALUE, IS_DEFAULT_INSTRUMENT, STATUS, INSTRUMENT_TYPE
            FROM PDP_PROD10_MAINDB.USER_ACCOUNTS
            WHERE USER_ID = :user_id
            AND INSTRUMENT_TYPE = 'WALLET'
            ORDER BY IS_DEFAULT_INSTRUMENT DESC, CREATED_ON DESC
        """, {'user_id': from_id_mobiquity})
        
        user_accounts = cursor.fetchall()
        if user_accounts:
            for row in user_accounts:
                print(f"  USER_ID: {row[0]}, INSTRUMENT_VALUE: {row[1]}, DEFAULT: {row[2]}, STATUS: {row[3]}")
        else:
            print("  ❌ No encontrado en USER_ACCOUNTS")
        
        # 2. MTX_WALLET
        print("\n2️⃣ PDP_PROD10_MAINDBBUS.MTX_WALLET:")
        cursor.execute("""
            SELECT USER_ID, WALLET_NUMBER, STATUS, CREATED_ON, MODIFIED_ON
            FROM PDP_PROD10_MAINDBBUS.MTX_WALLET
            WHERE USER_ID = :user_id
            ORDER BY MODIFIED_ON DESC
        """, {'user_id': from_id_mobiquity})
        
        mtx_wallet = cursor.fetchall()
        if mtx_wallet:
            for i, row in enumerate(mtx_wallet):
                if i < 3:  # Solo mostrar los primeros 3
                    print(f"  USER_ID: {row[0]}, WALLET_NUMBER: {row[1]}, STATUS: {row[2]}, MODIFIED: {row[4]}")
            if len(mtx_wallet) > 3:
                print(f"  ... y {len(mtx_wallet) - 3} registros más")
        else:
            print("  ❌ No encontrado en MTX_WALLET")
        
        # 3. USER_DATA_TRX
        print("\n3️⃣ USR_DATALAKE.USER_DATA_TRX:")
        cursor.execute("""
            SELECT USER_ID, O_USER_ID, WALLET_NUMBER, MSISDN, LOGIN_ID, PROFILE_TRX
            FROM USR_DATALAKE.USER_DATA_TRX
            WHERE O_USER_ID = :user_id
        """, {'user_id': from_id_mobiquity})
        
        user_data_trx = cursor.fetchall()
        if user_data_trx:
            for row in user_data_trx:
                print(f"  USER_ID: {row[0]}, O_USER_ID: {row[1]}, WALLET_NUMBER: {row[2]}")
                print(f"  MSISDN: {row[3]}, LOGIN_ID: {row[4]}, PROFILE: {row[5]}")
        else:
            print("  ❌ No encontrado en USER_DATA_TRX")
        
        # 4. Buscar en PRE_LOG_TRX otros casos similares
        print("\n4️⃣ OTROS CASOS EN PRE_LOG_TRX con mismo FromID_Mobiquity:")
        cursor.execute("""
            SELECT "TransferID", "From_AccountID_Mobiquity", "TransferDate"
            FROM USR_DATALAKE.PRE_LOG_TRX
            WHERE "FromID_Mobiquity" = :user_id
            AND ROWNUM <= 5
            ORDER BY "TransferDate" DESC
        """, {'user_id': from_id_mobiquity})
        
        otros_casos = cursor.fetchall()
        if otros_casos:
            for row in otros_casos:
                print(f"  TransferID: {row[0]}, From_AccountID_Mobiquity: {row[1]}, Fecha: {row[2]}")
        else:
            print("  ❌ No se encontraron otros casos")
        
        cursor.close()
        connection.close()
        
        return user_accounts, mtx_wallet, user_data_trx, otros_casos
        
    except Exception as e:
        print(f"❌ Error investigando Oracle: {str(e)}")
        return None, None, None, None

def investigar_usuario_s3(from_id_mobiquity: str):
    """Investiga las fuentes S3 para el mismo usuario"""
    try:
        conn = duckdb.connect()
        
        # Configurar S3 (simplificado)
        conn.sql("INSTALL httpfs;")
        conn.sql("LOAD httpfs;")
        
        print(f"\n🔍 INVESTIGANDO USUARIO {from_id_mobiquity} EN S3")
        print("=" * 80)
        
        # Rutas S3
        s3_bucket_silver = "prd-datalake-silver-zone-************"
        s3_bucket_golden = "prd-datalake-golden-zone-************"
        
        user_accounts_path = f's3://{s3_bucket_silver}/PDP_PROD10_MAINDB/USER_ACCOUNTS_ORA/consolidado_puro.parquet'
        mtx_wallet_path = f's3://{s3_bucket_silver}/PDP_PROD10_MAINDBBUS/MTX_WALLET_ORA/consolidado_puro.parquet'
        user_data_trx_path = f's3://{s3_bucket_golden}/LOGS_USUARIOS/USER_DATA_TRX.parquet'
        
        # 1. USER_ACCOUNTS S3
        print("1️⃣ USER_ACCOUNTS S3:")
        try:
            result = conn.execute(f"""
                SELECT USER_ID, INSTRUMENT_VALUE, IS_DEFAULT_INSTRUMENT, STATUS, INSTRUMENT_TYPE
                FROM read_parquet('{user_accounts_path}')
                WHERE USER_ID = '{from_id_mobiquity}'
                AND INSTRUMENT_TYPE = 'WALLET'
                ORDER BY IS_DEFAULT_INSTRUMENT DESC
            """).fetchall()
            
            if result:
                for row in result:
                    print(f"  USER_ID: {row[0]}, INSTRUMENT_VALUE: {row[1]}, DEFAULT: {row[2]}, STATUS: {row[3]}")
            else:
                print("  ❌ No encontrado en USER_ACCOUNTS S3")
        except Exception as e:
            print(f"  ❌ Error consultando USER_ACCOUNTS S3: {e}")
        
        # 2. MTX_WALLET S3
        print("\n2️⃣ MTX_WALLET S3:")
        try:
            result = conn.execute(f"""
                SELECT USER_ID, WALLET_NUMBER, STATUS, MODIFIED_ON
                FROM read_parquet('{mtx_wallet_path}')
                WHERE USER_ID = '{from_id_mobiquity}'
                ORDER BY MODIFIED_ON DESC
                LIMIT 3
            """).fetchall()
            
            if result:
                for row in result:
                    print(f"  USER_ID: {row[0]}, WALLET_NUMBER: {row[1]}, STATUS: {row[2]}, MODIFIED: {row[3]}")
            else:
                print("  ❌ No encontrado en MTX_WALLET S3")
        except Exception as e:
            print(f"  ❌ Error consultando MTX_WALLET S3: {e}")
        
        # 3. USER_DATA_TRX S3
        print("\n3️⃣ USER_DATA_TRX S3:")
        try:
            result = conn.execute(f"""
                SELECT USER_ID, O_USER_ID, WALLET_NUMBER, MSISDN, LOGIN_ID, PROFILE_TRX
                FROM read_parquet('{user_data_trx_path}')
                WHERE O_USER_ID = '{from_id_mobiquity}'
            """).fetchall()
            
            if result:
                for row in result:
                    print(f"  USER_ID: {row[0]}, O_USER_ID: {row[1]}, WALLET_NUMBER: {row[2]}")
                    print(f"  MSISDN: {row[3]}, LOGIN_ID: {row[4]}, PROFILE: {row[5]}")
            else:
                print("  ❌ No encontrado en USER_DATA_TRX S3")
        except Exception as e:
            print(f"  ❌ Error consultando USER_DATA_TRX S3: {e}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error investigando S3: {str(e)}")

def main():
    from_id_mobiquity = '945661'
    
    print("🚀 INVESTIGACIÓN DETALLADA DEL MAPEO DE CUENTAS")
    print("=" * 80)
    print(f"Usuario investigado: {from_id_mobiquity}")
    print(f"Caso específico: TransferID 175003230421458")
    print()
    
    # Investigar Oracle
    oracle_results = investigar_usuario_oracle(from_id_mobiquity)
    
    # Investigar S3
    investigar_usuario_s3(from_id_mobiquity)
    
    print("\n🎯 CONCLUSIONES:")
    print("=" * 80)
    print("1. Compara los valores de INSTRUMENT_VALUE/WALLET_NUMBER entre Oracle y S3")
    print("2. Verifica si hay diferencias en las fuentes de datos")
    print("3. Identifica qué lógica usa Oracle para llegar a '1866570'")
    print("4. Tu caso edge está funcionando correctamente para homologar con Oracle")

if __name__ == "__main__":
    main()
