#!/usr/bin/env python3
"""
Script para verificar el orden exacto de las columnas
entre Oracle PRE_LOG_TRX y nuestro resultado S3
"""

import oracledb
import duckdb
import boto3
import sys
import pandas as pd
from pathlib import Path

def verificar_orden_columnas():
    """Verifica el orden exacto de las columnas Oracle vs S3"""
    print("🔍 VERIFICACIÓN DEL ORDEN DE COLUMNAS")
    print("=" * 80)
    
    parquet_path = '/home/<USER>/REPORTE_2025/reports/generate/S3_LOG_TRANSACCIONES/TEMP_LOGS_TRANSACCIONES/20250615/PRE_LOG_TRX.parquet'
    
    print(f"Archivo S3: {parquet_path}")
    print()
    
    # 1. OBTENER COLUMNAS DE ORACLE
    print("1️⃣ CONSULTANDO ORDEN DE COLUMNAS EN ORACLE:")
    print("-" * 60)
    
    try:
        connection = oracledb.connect(
            user='usr_datalake',
            password='U2024b1mD4t4l4k5',
            dsn='*************:1521/MMONEY'
        )
        
        cursor = connection.cursor()
        
        # Obtener estructura de la tabla Oracle
        cursor.execute("""
            SELECT COLUMN_NAME, COLUMN_ID, DATA_TYPE
            FROM USER_TAB_COLUMNS 
            WHERE TABLE_NAME = 'PRE_LOG_TRX'
            ORDER BY COLUMN_ID
        """)
        
        oracle_columns = cursor.fetchall()
        
        print(f"✅ Oracle PRE_LOG_TRX tiene {len(oracle_columns)} columnas:")
        print()
        
        oracle_column_names = []
        for i, (col_name, col_id, data_type) in enumerate(oracle_columns):
            oracle_column_names.append(col_name)
            print(f"  [{col_id:2d}] {col_name:<30} ({data_type})")
        
        cursor.close()
        connection.close()
        
    except Exception as e:
        print(f"❌ Error consultando Oracle: {e}")
        return
    
    # 2. OBTENER COLUMNAS DE S3
    print(f"\n2️⃣ CONSULTANDO ORDEN DE COLUMNAS EN S3:")
    print("-" * 60)
    
    try:
        # Configurar DuckDB con S3
        conn = duckdb.connect()
        
        session = boto3.Session()
        credentials = session.get_credentials().get_frozen_credentials()
        
        conn.sql("INSTALL httpfs;")
        conn.sql("LOAD httpfs;")
        conn.sql("SET s3_region='us-east-1';")
        conn.sql("SET s3_use_ssl=true;")
        conn.sql("SET s3_url_style='path';")
        conn.sql(f"SET s3_access_key_id='{credentials.access_key}';")
        conn.sql(f"SET s3_secret_access_key='{credentials.secret_key}';")
        if credentials.token:
            conn.sql(f"SET s3_session_token='{credentials.token}';")
        
        # Obtener estructura del archivo Parquet
        result = conn.execute(f"DESCRIBE SELECT * FROM read_parquet('{parquet_path}')").fetchall()
        
        print(f"✅ S3 PRE_LOG_TRX tiene {len(result)} columnas:")
        print()
        
        s3_column_names = []
        for i, (col_name, col_type, null_allowed, key, default, extra) in enumerate(result):
            s3_column_names.append(col_name)
            print(f"  [{i+1:2d}] {col_name:<30} ({col_type})")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error consultando S3: {e}")
        return
    
    # 3. COMPARACIÓN DEL ORDEN
    print(f"\n3️⃣ COMPARACIÓN DEL ORDEN DE COLUMNAS:")
    print("-" * 60)
    
    print(f"Oracle tiene {len(oracle_column_names)} columnas")
    print(f"S3 tiene {len(s3_column_names)} columnas")
    print()
    
    # Comparar columna por columna
    max_cols = max(len(oracle_column_names), len(s3_column_names))
    
    coincidencias = 0
    diferencias = 0
    
    print("COMPARACIÓN POSICIÓN POR POSICIÓN:")
    print("-" * 80)
    print(f"{'POS':<4} {'ORACLE':<30} {'S3':<30} {'ESTADO'}")
    print("-" * 80)
    
    for i in range(max_cols):
        oracle_col = oracle_column_names[i] if i < len(oracle_column_names) else "---"
        s3_col = s3_column_names[i] if i < len(s3_column_names) else "---"
        
        if oracle_col == s3_col:
            estado = "✅ COINCIDE"
            coincidencias += 1
        else:
            estado = "❌ DIFIERE"
            diferencias += 1
        
        print(f"{i+1:<4} {oracle_col:<30} {s3_col:<30} {estado}")
    
    # 4. ANÁLISIS DE DIFERENCIAS
    print(f"\n4️⃣ ANÁLISIS DE DIFERENCIAS:")
    print("-" * 60)
    
    # Columnas que están en Oracle pero no en S3 (en la misma posición)
    oracle_set = set(oracle_column_names)
    s3_set = set(s3_column_names)
    
    solo_oracle = oracle_set - s3_set
    solo_s3 = s3_set - oracle_set
    comunes = oracle_set & s3_set
    
    print(f"📊 ESTADÍSTICAS:")
    print(f"  Columnas en ambos: {len(comunes)}")
    print(f"  Solo en Oracle: {len(solo_oracle)}")
    print(f"  Solo en S3: {len(solo_s3)}")
    print(f"  Posiciones coincidentes: {coincidencias}")
    print(f"  Posiciones diferentes: {diferencias}")
    
    if solo_oracle:
        print(f"\n📝 COLUMNAS SOLO EN ORACLE:")
        for col in sorted(solo_oracle):
            oracle_pos = oracle_column_names.index(col) + 1
            print(f"  - {col} (posición {oracle_pos})")
    
    if solo_s3:
        print(f"\n📝 COLUMNAS SOLO EN S3:")
        for col in sorted(solo_s3):
            s3_pos = s3_column_names.index(col) + 1
            print(f"  - {col} (posición {s3_pos})")
    
    # 5. COLUMNAS DESORDENADAS
    print(f"\n5️⃣ ANÁLISIS DE COLUMNAS DESORDENADAS:")
    print("-" * 60)
    
    desordenadas = []
    for col in comunes:
        oracle_pos = oracle_column_names.index(col)
        s3_pos = s3_column_names.index(col)
        
        if oracle_pos != s3_pos:
            desordenadas.append({
                'columna': col,
                'oracle_pos': oracle_pos + 1,
                's3_pos': s3_pos + 1
            })
    
    if desordenadas:
        print(f"❌ {len(desordenadas)} columnas están en posiciones diferentes:")
        print(f"{'COLUMNA':<30} {'ORACLE POS':<12} {'S3 POS':<12}")
        print("-" * 60)
        for item in desordenadas:
            print(f"{item['columna']:<30} {item['oracle_pos']:<12} {item['s3_pos']:<12}")
    else:
        print(f"✅ Todas las columnas comunes están en las mismas posiciones")
    
    # 6. RESUMEN FINAL
    print(f"\n6️⃣ RESUMEN FINAL:")
    print("-" * 60)
    
    if diferencias == 0 and len(solo_oracle) == 0 and len(solo_s3) == 0:
        print("🎉 ¡ORDEN PERFECTO!")
        print("✅ Todas las columnas coinciden en nombre y posición")
        print("🚀 El orden de columnas es idéntico a Oracle")
    else:
        print(f"⚠️  ORDEN NECESITA CORRECCIÓN")
        print(f"❌ {diferencias} posiciones diferentes")
        if solo_oracle:
            print(f"❌ {len(solo_oracle)} columnas faltan en S3")
        if solo_s3:
            print(f"❌ {len(solo_s3)} columnas extra en S3")
        if desordenadas:
            print(f"❌ {len(desordenadas)} columnas desordenadas")
        print("🔧 Necesita ajuste del SELECT en el pipeline")

def main():
    print("🚀 VERIFICACIÓN DEL ORDEN DE COLUMNAS")
    print("=" * 80)
    print("Comparando Oracle PRE_LOG_TRX vs S3 PRE_LOG_TRX")
    print()
    
    verificar_orden_columnas()
    
    print("\n🏁 VERIFICACIÓN COMPLETADA")

if __name__ == "__main__":
    main()
