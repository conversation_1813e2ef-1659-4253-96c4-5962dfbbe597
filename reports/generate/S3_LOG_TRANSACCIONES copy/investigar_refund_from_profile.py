#!/usr/bin/env python3
"""
Investigar específicamente cómo Oracle determina From_Profile para REFUND
"""

import oracledb
import sys

def investigar_refund_from_profile():
    """Investiga la lógica específica de From_Profile para REFUND"""
    print("🔍 INVESTIGACIÓN: From_Profile para REFUND")
    print("=" * 80)
    
    try:
        connection = oracledb.connect(
            user='usr_datalake',
            password='U2024b1mD4t4l4k5',
            dsn='10.240.131.10:1521/MMONEY'
        )
        
        cursor = connection.cursor()
        
        print("1️⃣ ANÁLISIS DE CASOS REFUND ESPECÍFICOS:")
        print("-" * 60)
        
        # Analizar casos específicos de REFUND
        cursor.execute("""
            SELECT 
                P."TransferID",
                P."FromID_Mobiquity",
                P."From_Profile",
                P."TransactionType",
                P."From_BankDomain"
            FROM USR_DATALAKE.PRE_LOG_TRX P
            WHERE P."TransactionType" = 'REFUND'
            AND P."From_Profile" IN ('BACKUS PROVEEDOR DE SERVICIOS', 'LINDLEY PROVEEDOR DE SERVICIOS')
            AND ROWNUM <= 10
            ORDER BY P."TransferID"
        """)
        
        refund_cases = cursor.fetchall()
        
        print(f"  Casos REFUND con PROVEEDOR DE SERVICIOS:")
        print(f"{'TransferID':<20} {'FromID':<15} {'From_Profile':<35} {'From_BankDomain'}")
        print("-" * 90)
        
        user_ids_to_check = set()
        for row in refund_cases:
            transfer_id = row[0]
            from_id = row[1]
            from_profile = row[2]
            from_bank_domain = row[4]
            
            user_ids_to_check.add(from_id)
            print(f"{transfer_id:<20} {from_id:<15} {from_profile:<35} {from_bank_domain}")
        
        print(f"\n2️⃣ ANÁLISIS DE USER_DATA_TRX PARA ESTOS USUARIOS:")
        print("-" * 60)
        
        # Verificar USER_DATA_TRX para estos usuarios
        for user_id in sorted(user_ids_to_check):
            cursor.execute("""
                SELECT 
                    USER_ID,
                    O_USER_ID,
                    PROFILE_TRX,
                    LOGIN_ID,
                    MSISDN,
                    ISSUER_CODE
                FROM USR_DATALAKE.USER_DATA_TRX
                WHERE O_USER_ID = :user_id
            """, {'user_id': user_id})
            
            user_data = cursor.fetchone()
            if user_data:
                print(f"  USER_ID {user_id}:")
                print(f"    PROFILE_TRX: {user_data[2]}")
                print(f"    LOGIN_ID: {user_data[3]}")
                print(f"    ISSUER_CODE: {user_data[5]}")
            else:
                print(f"  USER_ID {user_id}: NO ENCONTRADO en USER_DATA_TRX")
        
        print(f"\n3️⃣ ANÁLISIS DE USER_PROFILE ORIGINAL:")
        print("-" * 60)
        
        # Verificar USER_PROFILE original para estos usuarios
        for user_id in sorted(user_ids_to_check):
            cursor.execute("""
                SELECT 
                    USER_ID,
                    LOGIN_ID,
                    PROFILE_CODE,
                    MSISDN
                FROM PDP_PROD10_MAINDB.USER_PROFILE
                WHERE USER_ID = :user_id
            """, {'user_id': user_id})
            
            profile_data = cursor.fetchone()
            if profile_data:
                print(f"  USER_PROFILE {user_id}:")
                print(f"    LOGIN_ID: {profile_data[1]} ⭐ CLAVE")
                print(f"    PROFILE_CODE: {profile_data[2]}")
            else:
                print(f"  USER_PROFILE {user_id}: NO ENCONTRADO")
        
        print(f"\n4️⃣ ANÁLISIS DE PATRÓN GENERAL REFUND:")
        print("-" * 60)
        
        # Obtener patrón general de REFUND
        cursor.execute("""
            SELECT 
                P."From_Profile",
                P."From_BankDomain",
                COUNT(*) as CASOS
            FROM USR_DATALAKE.PRE_LOG_TRX P
            WHERE P."TransactionType" = 'REFUND'
            GROUP BY P."From_Profile", P."From_BankDomain"
            ORDER BY COUNT(*) DESC
        """)
        
        refund_patterns = cursor.fetchall()
        
        print(f"  Patrones REFUND:")
        print(f"{'From_Profile':<40} {'From_BankDomain':<20} {'CASOS'}")
        print("-" * 70)
        
        for row in refund_patterns:
            from_profile = row[0]
            from_bank_domain = row[1]
            casos = row[2]
            
            print(f"{from_profile:<40} {from_bank_domain:<20} {casos}")
        
        print(f"\n5️⃣ VERIFICACIÓN DE LÓGICA SP_PRE_LOG_TRX:")
        print("-" * 60)
        
        print("  Según SP_PRE_LOG_TRX líneas 180-181:")
        print("  From_Profile se construye con UPAYER.PROFILE")
        print("  Para REFUND líneas 138-139:")
        print("  CASE WHEN UPAYER.PROFILE <> 'COMERCIO'")
        print("       THEN UPPER(MTH.PAYER_IDENTIFIER_VALUE) || ' ' || UPAYER.PROFILE")
        print("       ELSE 'FCOMPARTAMOS COMERCIO' END")
        
        print(f"\n  🔍 VERIFICANDO PAYER_IDENTIFIER_VALUE:")
        
        # Verificar PAYER_IDENTIFIER_VALUE para casos REFUND
        cursor.execute("""
            SELECT 
                P."TransferID",
                P."FromID_Mobiquity",
                P."From_Profile",
                MTH.PAYER_IDENTIFIER_VALUE,
                U.PROFILE_TRX
            FROM USR_DATALAKE.PRE_LOG_TRX P
            LEFT JOIN PDP_PROD10_MAINDBBUS.MTX_TRANSACTION_HEADER MTH ON P."TransferID_Mob" = MTH.TRANSFER_ID
            LEFT JOIN USR_DATALAKE.USER_DATA_TRX U ON P."FromID_Mobiquity" = U.O_USER_ID
            WHERE P."TransactionType" = 'REFUND'
            AND P."From_Profile" LIKE '%PROVEEDOR DE SERVICIOS'
            AND ROWNUM <= 5
        """)
        
        identifier_cases = cursor.fetchall()
        
        print(f"    Casos con PAYER_IDENTIFIER_VALUE:")
        for row in identifier_cases:
            transfer_id = row[0]
            from_id = row[1]
            from_profile = row[2]
            payer_identifier = row[3]
            profile_trx = row[4]
            
            print(f"    TransferID: {transfer_id}")
            print(f"      From_Profile: {from_profile}")
            print(f"      PAYER_IDENTIFIER_VALUE: {payer_identifier}")
            print(f"      PROFILE_TRX: {profile_trx}")
            
            # Verificar si coincide con la fórmula
            if payer_identifier and profile_trx:
                expected = f"{payer_identifier.upper()} {profile_trx}"
                print(f"      FÓRMULA ESPERADA: {expected}")
                print(f"      ¿COINCIDE? {'✅' if expected == from_profile else '❌'}")
            print()
        
        cursor.close()
        connection.close()
        
        print(f"\n6️⃣ CONCLUSIONES:")
        print("-" * 60)
        print("1. Oracle usa PAYER_IDENTIFIER_VALUE + PROFILE_TRX para REFUND")
        print("2. PAYER_IDENTIFIER_VALUE contiene el nombre del proveedor (BACKUS, LINDLEY)")
        print("3. PROFILE_TRX contiene 'PROVEEDOR DE SERVICIOS'")
        print("4. Fórmula: UPPER(PAYER_IDENTIFIER_VALUE) + ' ' + PROFILE_TRX")
        print("5. Necesitamos acceder a MTX_TRANSACTION_HEADER.PAYER_IDENTIFIER_VALUE")
        
    except Exception as e:
        print(f"❌ Error en investigación: {e}")
        import traceback
        traceback.print_exc()

def main():
    print("🚀 INVESTIGACIÓN From_Profile REFUND")
    print("=" * 80)
    print("Identificando lógica exacta para REFUND")
    print()
    
    investigar_refund_from_profile()
    
    print("\n🏁 INVESTIGACIÓN COMPLETADA")

if __name__ == "__main__":
    main()
