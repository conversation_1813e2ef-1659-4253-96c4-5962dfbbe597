#!/usr/bin/env python3
"""
Investigar por qué los TransferIDs de Oracle no están en S3
"""

import oracledb
import duckdb
import boto3
import sys

def investigar_transferids_faltantes():
    """Investiga por qué faltan TransferIDs en S3"""
    print("🔍 INVESTIGACIÓN: TransferIDs Faltantes en S3")
    print("=" * 80)
    
    parquet_path = '/home/<USER>/REPORTE_2025/reports/generate/S3_LOG_TRANSACCIONES/TEMP_LOGS_TRANSACCIONES/20250615/PRE_LOG_TRX.parquet'
    fecha = '2025-06-15'
    
    try:
        # 1. CONSULTAR ORACLE
        print("1️⃣ CONSULTANDO ORACLE PRE_LOG_TRX:")
        print("-" * 60)
        
        connection = oracledb.connect(
            user='usr_datalake',
            password='U2024b1mD4t4l4k5',
            dsn='10.240.131.10:1521/MMONEY'
        )
        
        cursor = connection.cursor()
        
        # Contar registros en Oracle para la fecha
        cursor.execute("""
            SELECT COUNT(*) as TOTAL_ORACLE
            FROM USR_DATALAKE.PRE_LOG_TRX 
            WHERE TRUNC("TransferDate") = TO_DATE(:fecha, 'YYYY-MM-DD')
        """, {'fecha': fecha})
        
        oracle_count = cursor.fetchone()[0]
        print(f"  Total registros Oracle para {fecha}: {oracle_count:,}")
        
        # Obtener rango de TransferIDs en Oracle
        cursor.execute("""
            SELECT 
                MIN("TransferID") as MIN_ID,
                MAX("TransferID") as MAX_ID,
                COUNT(DISTINCT "TransferID") as UNIQUE_IDS
            FROM USR_DATALAKE.PRE_LOG_TRX 
            WHERE TRUNC("TransferDate") = TO_DATE(:fecha, 'YYYY-MM-DD')
        """, {'fecha': fecha})
        
        oracle_range = cursor.fetchone()
        print(f"  Rango TransferIDs Oracle: {oracle_range[0]} - {oracle_range[1]}")
        print(f"  TransferIDs únicos Oracle: {oracle_range[2]:,}")
        
        # Obtener muestra de TransferIDs de Oracle
        cursor.execute("""
            SELECT "TransferID", "TransferDate", "ToID_Mobiquity", "To_Profile"
            FROM USR_DATALAKE.PRE_LOG_TRX 
            WHERE TRUNC("TransferDate") = TO_DATE(:fecha, 'YYYY-MM-DD')
            AND ROWNUM <= 10
            ORDER BY "TransferID"
        """, {'fecha': fecha})
        
        oracle_sample = cursor.fetchall()
        print(f"\n  Muestra TransferIDs Oracle:")
        for row in oracle_sample:
            print(f"    {row[0]} | {row[1]} | {row[2]} | {row[3]}")
        
        cursor.close()
        connection.close()
        
        # 2. CONSULTAR S3
        print(f"\n2️⃣ CONSULTANDO S3 PRE_LOG_TRX:")
        print("-" * 60)
        
        # Configurar DuckDB con S3
        conn = duckdb.connect()
        
        session = boto3.Session()
        credentials = session.get_credentials().get_frozen_credentials()
        
        conn.sql("INSTALL httpfs;")
        conn.sql("LOAD httpfs;")
        conn.sql("SET s3_region='us-east-1';")
        conn.sql("SET s3_use_ssl=true;")
        conn.sql("SET s3_url_style='path';")
        conn.sql(f"SET s3_access_key_id='{credentials.access_key}';")
        conn.sql(f"SET s3_secret_access_key='{credentials.secret_key}';")
        if credentials.token:
            conn.sql(f"SET s3_session_token='{credentials.token}';")
        
        # Contar registros en S3
        s3_count = conn.execute(f"""
            SELECT COUNT(*) as TOTAL_S3
            FROM read_parquet('{parquet_path}')
        """).fetchone()[0]
        
        print(f"  Total registros S3: {s3_count:,}")
        
        # Obtener rango de TransferIDs en S3
        s3_range = conn.execute(f"""
            SELECT 
                MIN("TransferID") as MIN_ID,
                MAX("TransferID") as MAX_ID,
                COUNT(DISTINCT "TransferID") as UNIQUE_IDS
            FROM read_parquet('{parquet_path}')
        """).fetchone()
        
        print(f"  Rango TransferIDs S3: {s3_range[0]} - {s3_range[1]}")
        print(f"  TransferIDs únicos S3: {s3_range[2]:,}")
        
        # Obtener muestra de TransferIDs de S3
        s3_sample = conn.execute(f"""
            SELECT "TransferID", "TransferDate", "ToID_Mobiquity", "To_Profile"
            FROM read_parquet('{parquet_path}')
            ORDER BY "TransferID"
            LIMIT 10
        """).fetchall()
        
        print(f"\n  Muestra TransferIDs S3:")
        for row in s3_sample:
            print(f"    {row[0]} | {row[1]} | {row[2]} | {row[3]}")
        
        # 3. COMPARACIÓN DE RANGOS
        print(f"\n3️⃣ COMPARACIÓN DE RANGOS:")
        print("-" * 60)
        
        print(f"  Oracle MIN: {oracle_range[0]}")
        print(f"  S3 MIN:     {s3_range[0]}")
        print(f"  Diferencia: {int(s3_range[0]) - int(oracle_range[0]):,}")
        
        print(f"\n  Oracle MAX: {oracle_range[1]}")
        print(f"  S3 MAX:     {s3_range[1]}")
        print(f"  Diferencia: {int(s3_range[1]) - int(oracle_range[1]):,}")
        
        # 4. VERIFICAR OVERLAP
        print(f"\n4️⃣ VERIFICACIÓN DE OVERLAP:")
        print("-" * 60)
        
        # Verificar si hay TransferIDs comunes
        oracle_min = int(oracle_range[0])
        oracle_max = int(oracle_range[1])
        s3_min = int(s3_range[0])
        s3_max = int(s3_range[1])
        
        if s3_max < oracle_min or oracle_max < s3_min:
            print("  ❌ NO HAY OVERLAP: Los rangos no se superponen")
            print("  🔍 CAUSA: Oracle y S3 tienen datos de períodos diferentes")
        else:
            overlap_min = max(oracle_min, s3_min)
            overlap_max = min(oracle_max, s3_max)
            print(f"  ✅ HAY OVERLAP: {overlap_min} - {overlap_max}")
            
            # Verificar TransferIDs específicos en el overlap
            overlap_sample = conn.execute(f"""
                SELECT "TransferID", "To_Profile"
                FROM read_parquet('{parquet_path}')
                WHERE CAST("TransferID" AS BIGINT) BETWEEN {overlap_min} AND {overlap_max}
                ORDER BY "TransferID"
                LIMIT 5
            """).fetchall()
            
            print(f"  Muestra en overlap:")
            for row in overlap_sample:
                print(f"    {row[0]} | {row[1]}")
        
        # 5. VERIFICAR FECHAS
        print(f"\n5️⃣ VERIFICACIÓN DE FECHAS:")
        print("-" * 60)
        
        # Fechas en S3
        s3_dates = conn.execute(f"""
            SELECT 
                DATE_TRUNC('day', "TransferDate") as FECHA,
                COUNT(*) as REGISTROS
            FROM read_parquet('{parquet_path}')
            GROUP BY DATE_TRUNC('day', "TransferDate")
            ORDER BY FECHA
        """).fetchall()
        
        print(f"  Fechas en S3:")
        for row in s3_dates:
            print(f"    {row[0]}: {row[1]:,} registros")
        
        conn.close()
        
        # 6. CONCLUSIONES
        print(f"\n6️⃣ CONCLUSIONES:")
        print("-" * 60)
        
        if oracle_count != s3_count:
            print(f"  ❌ CONTEOS DIFERENTES:")
            print(f"    Oracle: {oracle_count:,} registros")
            print(f"    S3: {s3_count:,} registros")
            print(f"    Diferencia: {abs(oracle_count - s3_count):,}")
        
        if oracle_range[0] != s3_range[0] or oracle_range[1] != s3_range[1]:
            print(f"  ❌ RANGOS DIFERENTES:")
            print(f"    Posible causa: Filtros de fecha diferentes")
            print(f"    Solución: Verificar lógica de filtrado en pipeline")
        
        if oracle_count == s3_count and oracle_range[0] == s3_range[0]:
            print(f"  ✅ DATOS COINCIDEN:")
            print(f"    Problema puede estar en la muestra específica")
            print(f"    Usar TransferIDs del rango S3 para homologación")
        
    except Exception as e:
        print(f"❌ Error en investigación: {e}")
        import traceback
        traceback.print_exc()

def main():
    print("🚀 INVESTIGACIÓN TransferIDs FALTANTES")
    print("=" * 80)
    print("¿Por qué los TransferIDs de Oracle no están en S3?")
    print()
    
    investigar_transferids_faltantes()
    
    print("\n🏁 INVESTIGACIÓN COMPLETADA")

if __name__ == "__main__":
    main()
