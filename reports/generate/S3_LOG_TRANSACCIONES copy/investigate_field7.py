#!/usr/bin/env python3
"""
Investigación FIELD7 - Clave del mapeo de TransferIDs
"""

import oracledb
import sys

def main():
    try:
        # Conectar a Oracle
        conn = oracledb.connect(
            user='usr_datalake',
            password='U2024b1mD4t4l4k5',
            dsn='10.240.131.10:1521/MMONEY'
        )
        
        cursor = conn.cursor()
        
        print("🔍 INVESTIGANDO RELACIÓN TRANSFER_ID vs FIELD7")
        print("=" * 60)
        
        # 1. Investigar Oracle PRE_LOG_TRX
        print("📊 Oracle PRE_LOG_TRX (TransferID vs TransferID_Mob):")
        cursor.execute("""
            SELECT "TransferID", "TransferID_Mob", "TransferDate"
            FROM USR_DATALAKE.PRE_LOG_TRX
            WHERE TRUNC("TransferDate") = TO_DATE('2025-06-15', 'YYYY-MM-DD')
            AND ROWNUM <= 5
            ORDER BY "TransferID"
        """)
        
        results = cursor.fetchall()
        for i, row in enumerate(results, 1):
            print(f"  {i}. TransferID: {row[0]}, TransferID_Mob: {row[1]}")
        
        # 2. Investigar MTX_TRANSACTION_HEADER
        print(f"\n📊 MTX_TRANSACTION_HEADER (TRANSFER_ID vs FIELD7):")
        cursor.execute("""
            SELECT TRANSFER_ID, FIELD7, TRANSFER_DATE
            FROM PDP_PROD10_MAINDBBUS.MTX_TRANSACTION_HEADER
            WHERE TRUNC(TRANSFER_DATE) = TO_DATE('2025-06-15', 'YYYY-MM-DD')
            AND TRANSFER_STATUS IN ('TA','TS')
            AND TRANSFER_VALUE <> 0
            AND ROWNUM <= 5
            ORDER BY TRANSFER_ID
        """)
        
        results2 = cursor.fetchall()
        for i, row in enumerate(results2, 1):
            print(f"  {i}. TRANSFER_ID: {row[0]}, FIELD7: {row[1]}")
        
        # 3. Buscar coincidencias entre FIELD7 y TransferID de PRE_LOG_TRX
        print(f"\n🔍 BUSCANDO COINCIDENCIAS FIELD7 → TransferID:")
        
        # Tomar algunos FIELD7 de MTX_TRANSACTION_HEADER
        cursor.execute("""
            SELECT FIELD7
            FROM PDP_PROD10_MAINDBBUS.MTX_TRANSACTION_HEADER
            WHERE TRUNC(TRANSFER_DATE) = TO_DATE('2025-06-15', 'YYYY-MM-DD')
            AND TRANSFER_STATUS IN ('TA','TS')
            AND TRANSFER_VALUE <> 0
            AND FIELD7 IS NOT NULL
            AND ROWNUM <= 3
        """)
        
        field7_samples = [row[0] for row in cursor.fetchall()]
        
        for field7 in field7_samples:
            # Buscar si este FIELD7 aparece como TransferID en PRE_LOG_TRX
            cursor.execute(f"""
                SELECT COUNT(*)
                FROM USR_DATALAKE.PRE_LOG_TRX
                WHERE "TransferID" = '{field7}'
                AND TRUNC("TransferDate") = TO_DATE('2025-06-15', 'YYYY-MM-DD')
            """)
            
            count = cursor.fetchone()[0]
            print(f"  FIELD7 {field7} → PRE_LOG_TRX: {'✅ ENCONTRADO' if count > 0 else '❌ NO ENCONTRADO'}")
        
        # 4. Investigar si TransferID_Mob coincide con TRANSFER_ID original
        print(f"\n🔍 VERIFICANDO TransferID_Mob vs TRANSFER_ID original:")
        
        cursor.execute("""
            SELECT p."TransferID_Mob", m.TRANSFER_ID
            FROM USR_DATALAKE.PRE_LOG_TRX p,
                 PDP_PROD10_MAINDBBUS.MTX_TRANSACTION_HEADER m
            WHERE p."TransferID_Mob" = m.TRANSFER_ID
            AND TRUNC(p."TransferDate") = TO_DATE('2025-06-15', 'YYYY-MM-DD')
            AND TRUNC(m.TRANSFER_DATE) = TO_DATE('2025-06-15', 'YYYY-MM-DD')
            AND ROWNUM <= 3
        """)
        
        matches = cursor.fetchall()
        if matches:
            print("✅ COINCIDENCIAS ENCONTRADAS:")
            for i, (mob_id, orig_id) in enumerate(matches, 1):
                print(f"  {i}. TransferID_Mob: {mob_id} = TRANSFER_ID: {orig_id}")
        else:
            print("❌ NO SE ENCONTRARON COINCIDENCIAS")
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
