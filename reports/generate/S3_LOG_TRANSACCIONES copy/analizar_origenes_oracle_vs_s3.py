#!/usr/bin/env python3
"""
Script para analizar las diferencias entre tablas origen Oracle vs S3
Compara datos específicos para el usuario 945661 en todas las fuentes
"""

import oracledb
import duckdb
import boto3
import sys
import pandas as pd
from pathlib import Path

class AnalizadorOrigenDatos:
    def __init__(self):
        self.setup_s3_credentials()
        
        # Configuración de rutas S3 (igual que en pipeline_log_transacciones_duckdb.py)
        self.s3_bucket_silver = "prd-datalake-silver-zone-637423440311"
        self.s3_bucket_golden = "prd-datalake-golden-zone-637423440311"
        
        self.s3_sources = {
            # Tablas PDP (Sistema Principal)
            'mtx_transaction_header': f's3://{self.s3_bucket_silver}/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_HEADER_ORA/*/*/*/*.parquet',
            'mtx_transaction_items': f's3://{self.s3_bucket_silver}/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_ITEMS_ORA/*/*/*/*.parquet',
            'mtx_wallet': f's3://{self.s3_bucket_silver}/PDP_PROD10_MAINDBBUS/MTX_WALLET_ORA/consolidado_puro.parquet',
            'sys_service_types': f's3://{self.s3_bucket_silver}/PDP_PROD10_MAINDB/SYS_SERVICE_TYPES_ORA/consolidado_puro.parquet',
            'channel_grades': f's3://{self.s3_bucket_silver}/PDP_PROD10_MAINDB/CHANNEL_GRADES_ORA/consolidado_puro.parquet',
            'marketing_profile': f's3://{self.s3_bucket_silver}/PDP_PROD10_MAINDB/MARKETING_PROFILE_ORA/consolidado_puro.parquet',
            'mtx_categories': f's3://{self.s3_bucket_silver}/PDP_PROD10_MAINDB/MTX_CATEGORIES_ORA/consolidado_puro.parquet',
            'issuer_details': f's3://{self.s3_bucket_silver}/PDP_PROD10_MAINDB/ISSUER_DETAILS_ORA/consolidado_puro.parquet',
            'sys_service_provider': f's3://{self.s3_bucket_silver}/PDP_PROD10_MAINDB/SYS_SERVICE_PROVIDER_ORA/consolidado_puro.parquet',
            
            # Tablas Datalake (Golden Zone)
            'user_data_trx': f's3://{self.s3_bucket_golden}/LOGS_USUARIOS/USER_DATA_TRX.parquet',
            'user_account_history': f's3://{self.s3_bucket_golden}/LOGS_USUARIOS/USER_ACCOUNT_HISTORY.parquet',
            # USER_ACCOUNTS para mapeo exacto de ACCOUNT_ID
            'user_accounts': f's3://{self.s3_bucket_silver}/PDP_PROD10_MAINDB/USER_ACCOUNTS_ORA/consolidado_puro.parquet',
            # MTX_WALLET raw para lógica temporal
            'mtx_wallet_raw': f's3://{self.s3_bucket_silver}/PDP_PROD10_MAINDBBUS/MTX_WALLET_ORA/consolidado_puro.parquet'
        }
        
        # Mapeo Oracle -> S3
        self.oracle_to_s3_mapping = {
            'PDP_PROD10_MAINDB.USER_ACCOUNTS': 'user_accounts',
            'PDP_PROD10_MAINDBBUS.MTX_WALLET': 'mtx_wallet',
            'USR_DATALAKE.USER_DATA_TRX': 'user_data_trx',
            'PDP_PROD10_MAINDBBUS.MTX_TRANSACTION_HEADER': 'mtx_transaction_header',
            'PDP_PROD10_MAINDBBUS.MTX_TRANSACTION_ITEMS': 'mtx_transaction_items'
        }
    
    def setup_s3_credentials(self):
        """Configura credenciales S3 en DuckDB"""
        try:
            self.conn = duckdb.connect()
            
            # Obtener credenciales activas de AWS CLI
            session = boto3.Session()
            credentials = session.get_credentials().get_frozen_credentials()

            # Cargar soporte para S3 en DuckDB
            self.conn.sql("INSTALL httpfs;")
            self.conn.sql("LOAD httpfs;")
            self.conn.sql("SET s3_region='us-east-1';")
            self.conn.sql("SET s3_use_ssl=true;")
            self.conn.sql("SET s3_url_style='path';")

            # Pasar credenciales explícitamente
            self.conn.sql(f"SET s3_access_key_id='{credentials.access_key}';")
            self.conn.sql(f"SET s3_secret_access_key='{credentials.secret_key}';")
            if credentials.token:
                self.conn.sql(f"SET s3_session_token='{credentials.token}';")

            print("✅ Credenciales S3 configuradas exitosamente")
            
        except Exception as e:
            print(f"❌ Error configurando credenciales S3: {e}")
            raise
    
    def consultar_oracle_user_accounts(self, user_id: str):
        """Consulta USER_ACCOUNTS en Oracle"""
        try:
            connection = oracledb.connect(
                user='usr_datalake',
                password='U2024b1mD4t4l4k5',
                dsn='*************:1521/MMONEY'
            )
            
            cursor = connection.cursor()
            
            query = """
                SELECT 
                    USER_ID, 
                    INSTRUMENT_VALUE, 
                    IS_DEFAULT_INSTRUMENT, 
                    STATUS, 
                    INSTRUMENT_TYPE,
                    CREATED_ON,
                    MODIFIED_ON
                FROM PDP_PROD10_MAINDB.USER_ACCOUNTS
                WHERE USER_ID = :user_id
                AND INSTRUMENT_TYPE = 'WALLET'
                ORDER BY IS_DEFAULT_INSTRUMENT DESC, CREATED_ON DESC
            """
            
            cursor.execute(query, {'user_id': user_id})
            results = cursor.fetchall()
            columns = [desc[0] for desc in cursor.description]
            
            cursor.close()
            connection.close()
            
            return results, columns
            
        except Exception as e:
            print(f"❌ Error consultando Oracle USER_ACCOUNTS: {e}")
            return None, None
    
    def consultar_s3_user_accounts(self, user_id: str):
        """Consulta USER_ACCOUNTS en S3"""
        try:
            query = f"""
                SELECT 
                    USER_ID, 
                    INSTRUMENT_VALUE, 
                    IS_DEFAULT_INSTRUMENT, 
                    STATUS, 
                    INSTRUMENT_TYPE,
                    CREATED_ON,
                    MODIFIED_ON
                FROM read_parquet('{self.s3_sources['user_accounts']}')
                WHERE USER_ID = '{user_id}'
                AND INSTRUMENT_TYPE = 'WALLET'
                ORDER BY IS_DEFAULT_INSTRUMENT DESC, CREATED_ON DESC
            """
            
            results = self.conn.execute(query).fetchall()
            columns = [desc[0] for desc in self.conn.description]
            
            return results, columns
            
        except Exception as e:
            print(f"❌ Error consultando S3 USER_ACCOUNTS: {e}")
            return None, None
    
    def consultar_oracle_mtx_wallet(self, user_id: str):
        """Consulta MTX_WALLET en Oracle"""
        try:
            connection = oracledb.connect(
                user='usr_datalake',
                password='U2024b1mD4t4l4k5',
                dsn='*************:1521/MMONEY'
            )
            
            cursor = connection.cursor()
            
            query = """
                SELECT 
                    USER_ID, 
                    WALLET_NUMBER, 
                    STATUS,
                    CREATED_ON,
                    MODIFIED_ON,
                    USER_GRADE,
                    ISSUER_ID
                FROM PDP_PROD10_MAINDBBUS.MTX_WALLET
                WHERE USER_ID = :user_id
                ORDER BY MODIFIED_ON DESC
            """
            
            cursor.execute(query, {'user_id': user_id})
            results = cursor.fetchall()
            columns = [desc[0] for desc in cursor.description]
            
            cursor.close()
            connection.close()
            
            return results, columns
            
        except Exception as e:
            print(f"❌ Error consultando Oracle MTX_WALLET: {e}")
            return None, None
    
    def consultar_s3_mtx_wallet(self, user_id: str):
        """Consulta MTX_WALLET en S3"""
        try:
            query = f"""
                SELECT 
                    USER_ID, 
                    WALLET_NUMBER, 
                    STATUS,
                    CREATED_ON,
                    MODIFIED_ON,
                    USER_GRADE,
                    ISSUER_ID
                FROM read_parquet('{self.s3_sources['mtx_wallet']}')
                WHERE USER_ID = '{user_id}'
                ORDER BY MODIFIED_ON DESC
            """
            
            results = self.conn.execute(query).fetchall()
            columns = [desc[0] for desc in self.conn.description]
            
            return results, columns
            
        except Exception as e:
            print(f"❌ Error consultando S3 MTX_WALLET: {e}")
            return None, None
    
    def comparar_tablas(self, user_id: str):
        """Compara todas las tablas relevantes entre Oracle y S3"""
        print(f"🔍 ANÁLISIS COMPARATIVO ORACLE vs S3 para USER_ID: {user_id}")
        print("=" * 80)
        
        # 1. USER_ACCOUNTS
        print("\n1️⃣ COMPARACIÓN USER_ACCOUNTS")
        print("-" * 50)
        
        oracle_ua, oracle_ua_cols = self.consultar_oracle_user_accounts(user_id)
        s3_ua, s3_ua_cols = self.consultar_s3_user_accounts(user_id)
        
        print("📊 ORACLE USER_ACCOUNTS:")
        if oracle_ua:
            for row in oracle_ua:
                print(f"  USER_ID: {row[0]}, INSTRUMENT_VALUE: {row[1]}, DEFAULT: {row[2]}, STATUS: {row[3]}")
                print(f"  CREATED: {row[5]}, MODIFIED: {row[6]}")
        else:
            print("  ❌ Sin datos")
        
        print("\n📊 S3 USER_ACCOUNTS:")
        if s3_ua:
            for row in s3_ua:
                print(f"  USER_ID: {row[0]}, INSTRUMENT_VALUE: {row[1]}, DEFAULT: {row[2]}, STATUS: {row[3]}")
                print(f"  CREATED: {row[5]}, MODIFIED: {row[6]}")
        else:
            print("  ❌ Sin datos")
        
        # Comparar valores
        if oracle_ua and s3_ua:
            oracle_instrument = oracle_ua[0][1]  # INSTRUMENT_VALUE
            s3_instrument = s3_ua[0][1]
            
            if oracle_instrument == s3_instrument:
                print("  ✅ INSTRUMENT_VALUE coincide entre Oracle y S3")
            else:
                print(f"  ❌ INSTRUMENT_VALUE difiere: Oracle={oracle_instrument} vs S3={s3_instrument}")
        
        # 2. MTX_WALLET
        print("\n2️⃣ COMPARACIÓN MTX_WALLET")
        print("-" * 50)
        
        oracle_mw, oracle_mw_cols = self.consultar_oracle_mtx_wallet(user_id)
        s3_mw, s3_mw_cols = self.consultar_s3_mtx_wallet(user_id)
        
        print("📊 ORACLE MTX_WALLET:")
        if oracle_mw:
            for i, row in enumerate(oracle_mw[:3]):  # Solo primeros 3
                print(f"  [{i+1}] USER_ID: {row[0]}, WALLET_NUMBER: {row[1]}, STATUS: {row[2]}")
                print(f"      CREATED: {row[3]}, MODIFIED: {row[4]}")
        else:
            print("  ❌ Sin datos")
        
        print("\n📊 S3 MTX_WALLET:")
        if s3_mw:
            for i, row in enumerate(s3_mw[:3]):  # Solo primeros 3
                print(f"  [{i+1}] USER_ID: {row[0]}, WALLET_NUMBER: {row[1]}, STATUS: {row[2]}")
                print(f"      CREATED: {row[3]}, MODIFIED: {row[4]}")
        else:
            print("  ❌ Sin datos")
        
        # Análisis de diferencias
        print("\n🎯 ANÁLISIS DE DIFERENCIAS:")
        print("-" * 50)
        
        if oracle_ua and s3_ua and oracle_mw and s3_mw:
            oracle_ua_value = oracle_ua[0][1]
            s3_ua_value = s3_ua[0][1]
            oracle_mw_active = [row[1] for row in oracle_mw if row[2] == 'Y']
            s3_mw_active = [row[1] for row in s3_mw if row[2] == 'Y']
            
            print(f"Oracle USER_ACCOUNTS INSTRUMENT_VALUE: {oracle_ua_value}")
            print(f"S3 USER_ACCOUNTS INSTRUMENT_VALUE: {s3_ua_value}")
            print(f"Oracle MTX_WALLET activos: {oracle_mw_active}")
            print(f"S3 MTX_WALLET activos: {s3_mw_active}")
            
            if oracle_ua_value == s3_ua_value:
                print("✅ USER_ACCOUNTS coincide entre Oracle y S3")
            else:
                print("❌ USER_ACCOUNTS difiere entre Oracle y S3")
                
            if oracle_mw_active == s3_mw_active:
                print("✅ MTX_WALLET activos coinciden entre Oracle y S3")
            else:
                print("❌ MTX_WALLET activos difieren entre Oracle y S3")

def main():
    user_id = '945661'
    
    print("🚀 ANÁLISIS DE ORÍGENES DE DATOS: ORACLE vs S3")
    print("=" * 80)
    print(f"Usuario analizado: {user_id}")
    print(f"Caso: TransferID ***************")
    print()
    
    try:
        analizador = AnalizadorOrigenDatos()
        analizador.comparar_tablas(user_id)
        
        print("\n🏁 ANÁLISIS COMPLETADO")
        print("=" * 80)
        print("Revisa las diferencias encontradas para entender el mapeo de Oracle")
        
    except Exception as e:
        print(f"❌ Error en el análisis: {e}")

if __name__ == "__main__":
    main()
