#!/usr/bin/env python3
"""
Investigar la lógica específica de Oracle para AccountID_Mobiquity
"""

import oracledb
import sys

def investigar_logica_oracle_accountid():
    """Investiga la lógica específica de Oracle para AccountID_Mobiquity"""
    print("🔍 INVESTIGACIÓN: Lógica Oracle AccountID_Mobiquity")
    print("=" * 80)
    
    try:
        connection = oracledb.connect(
            user='usr_datalake',
            password='U2024b1mD4t4l4k5',
            dsn='10.240.131.10:1521/MMONEY'
        )
        
        cursor = connection.cursor()
        
        print("1️⃣ ANÁLISIS DE CASOS ESPECÍFICOS:")
        print("-" * 60)
        
        # Casos específicos problemáticos
        casos = [
            ('3262191', '***********5533600'),
            ('3662073', '***********5554646'),
            ('2078452', '***********5554650'),
            ('1932101', '***********5554643')
        ]
        
        for to_id, oracle_account in casos:
            print(f"\n📋 CASO ToID: {to_id}")
            
            # Verificar datos en USER_DATA_TRX
            cursor.execute("""
                SELECT
                    USER_ID,
                    O_USER_ID,
                    WALLET_NUMBER,
                    PROFILE_TRX,
                    MSISDN
                FROM USR_DATALAKE.USER_DATA_TRX
                WHERE O_USER_ID = :user_id
            """, {'user_id': to_id})
            
            user_data = cursor.fetchone()
            if user_data:
                print(f"  USER_DATA_TRX:")
                print(f"    WALLET_NUMBER: {user_data[2]} ⭐ VALOR BASE")
                print(f"    PROFILE_TRX: {user_data[3]}")
                print(f"    MSISDN: {user_data[4]}")

                # Comparar con Oracle AccountID
                wallet_number = user_data[2]
                print(f"  🔍 COMPARACIÓN:")
                print(f"    WALLET_NUMBER: {wallet_number}")
                print(f"    Oracle AccountID: {oracle_account}")
                print(f"    ¿Coincide? {'✅' if str(wallet_number) == str(oracle_account) else '❌'}")
                
                # Analizar patrón
                if str(wallet_number) != str(oracle_account):
                    print(f"  📊 ANÁLISIS DE PATRÓN:")
                    wallet_str = str(wallet_number)
                    oracle_str = str(oracle_account)
                    print(f"    WALLET longitud: {len(wallet_str)}")
                    print(f"    Oracle longitud: {len(oracle_str)}")
                    
                    # Verificar si Oracle contiene WALLET
                    if wallet_str in oracle_str:
                        print(f"    ✅ WALLET está contenido en Oracle AccountID")
                        prefix = oracle_str.replace(wallet_str, '')
                        print(f"    Prefijo Oracle: '{prefix}'")
                    else:
                        print(f"    ❌ WALLET NO está contenido en Oracle AccountID")
            else:
                print(f"  ❌ No encontrado en USER_DATA_TRX")
        
        print(f"\n2️⃣ ANÁLISIS DE PATRÓN GENERAL:")
        print("-" * 60)
        
        # Buscar patrón general de cuentas largas
        cursor.execute("""
            SELECT
                P."ToID_Mobiquity",
                P."To_AccountID_Mobiquity",
                U.WALLET_NUMBER,
                U.PROFILE_TRX
            FROM USR_DATALAKE.PRE_LOG_TRX P
            LEFT JOIN USR_DATALAKE.USER_DATA_TRX U ON P."ToID_Mobiquity" = U.O_USER_ID
            WHERE LENGTH(P."To_AccountID_Mobiquity") > 15
            AND P."To_AccountID_Mobiquity" != U.WALLET_NUMBER
            AND ROWNUM <= 10
        """)
        
        pattern_data = cursor.fetchall()
        
        print(f"  Casos donde Oracle AccountID != WALLET_NUMBER:")
        print(f"{'ToID':<15} {'Oracle AccountID':<20} {'WALLET_NUMBER':<15} {'PROFILE':<25}")
        print("-" * 85)
        
        prefixes_found = set()
        for row in pattern_data:
            to_id = row[0]
            oracle_account = row[1]
            wallet_number = row[2]
            profile = row[3]
            
            print(f"{to_id:<15} {oracle_account:<20} {wallet_number:<15} {profile:<25}")
            
            # Analizar prefijo
            if wallet_number and oracle_account:
                wallet_str = str(wallet_number)
                oracle_str = str(oracle_account)
                if wallet_str in oracle_str:
                    prefix = oracle_str.replace(wallet_str, '')
                    prefixes_found.add(prefix)
        
        print(f"\n  Prefijos encontrados: {prefixes_found}")
        
        print(f"\n3️⃣ INVESTIGACIÓN DE LÓGICA SP_PRE_LOG_TRX:")
        print("-" * 60)
        
        print("  Según el SP_PRE_LOG_TRX original de Oracle:")
        print("  - To_AccountID_Mobiquity se construye con lógica específica")
        print("  - Puede involucrar concatenación de prefijos")
        print("  - Posible lógica basada en PROFILE o tipo de cuenta")
        
        # Verificar si hay lógica específica en el SP
        print(f"\n  🔍 VERIFICANDO LÓGICA ESPECÍFICA:")
        print("  - Prefijo '***********' parece ser común")
        print("  - Longitud total: 18 dígitos")
        print("  - Patrón: *********** + WALLET_NUMBER")
        
        # Verificar hipótesis
        for to_id, oracle_account in casos:
            cursor.execute("""
                SELECT WALLET_NUMBER
                FROM USR_DATALAKE.USER_DATA_TRX
                WHERE O_USER_ID = :user_id
            """, {'user_id': to_id})
            
            result = cursor.fetchone()
            if result:
                wallet_number = result[0]
                expected = f"***********{wallet_number}"
                print(f"  ToID {to_id}: ***********{wallet_number} = {expected}")
                print(f"    Oracle: {oracle_account}")
                print(f"    ¿Coincide? {'✅' if expected == oracle_account else '❌'}")
        
        cursor.close()
        connection.close()
        
        print(f"\n4️⃣ CONCLUSIONES:")
        print("-" * 60)
        print("1. Oracle usa prefijo '***********' + WALLET_NUMBER para algunos casos")
        print("2. Esta lógica no está implementada en nuestro pipeline S3")
        print("3. Necesitamos identificar cuándo aplicar esta lógica")
        print("4. Posible criterio: tipo de perfil o características específicas")
        
        print(f"\n💡 CORRECCIÓN REQUERIDA:")
        print("Implementar lógica condicional en To_AccountID_Mobiquity:")
        print("CASE")
        print("    WHEN [condición específica] THEN '***********' || UPAYEE.WALLET_NUMBER")
        print("    ELSE UPAYEE.WALLET_NUMBER")
        print("END")
        
    except Exception as e:
        print(f"❌ Error en investigación: {e}")
        import traceback
        traceback.print_exc()

def main():
    print("🚀 INVESTIGACIÓN LÓGICA Oracle AccountID")
    print("=" * 80)
    print("Identificando lógica oculta de Oracle")
    print()
    
    investigar_logica_oracle_accountid()
    
    print("\n🏁 INVESTIGACIÓN COMPLETADA")

if __name__ == "__main__":
    main()
