#!/usr/bin/env python3
"""
Validación Exhaustiva de Cantidades - Pipeline vs Oracle
Análisis estructurado para lograr homologación perfecta "dos gotas de agua" 100%
"""

import oracledb
import duckdb
import pandas as pd
import sys
from datetime import datetime

class ValidacionExhaustivaCantidades:
    def __init__(self):
        self.fecha = '2025-06-15'
        self.setup_connections()
        
    def setup_connections(self):
        """Configura conexiones a Oracle y DuckDB"""
        try:
            # Oracle
            self.oracle_conn = oracledb.connect(
                user='usr_datalake',
                password='U2024b1mD4t4l4k5',
                dsn='*************:1521/MMONEY'
            )
            
            # DuckDB
            self.duck_conn = duckdb.connect()
            self.duck_conn.sql("INSTALL httpfs;")
            self.duck_conn.sql("LOAD httpfs;")
            
            print("✅ Conexiones establecidas")
            
        except Exception as e:
            print(f"❌ Error en conexiones: {e}")
            raise

    def validar_cantidades_sp_pre_log_trx(self):
        """1. VALIDACIÓN SP_PRE_LOG_TRX - Cantidades exactas"""
        print("🔍 1. VALIDACIÓN SP_PRE_LOG_TRX - CANTIDADES EXACTAS")
        print("=" * 80)
        
        cursor = self.oracle_conn.cursor()
        
        # Oracle PRE_LOG_TRX
        cursor.execute(f"""
            SELECT COUNT(*)
            FROM USR_DATALAKE.PRE_LOG_TRX
            WHERE TRUNC("TransferDate") = TO_DATE('{self.fecha}', 'YYYY-MM-DD')
        """)
        oracle_count = cursor.fetchone()[0]
        
        # Nuestro Pipeline PRE_LOG_TRX
        parquet_path = f"TEMP_LOGS_TRANSACCIONES/20250615/PRE_LOG_TRX.parquet"
        
        pipeline_result = self.duck_conn.execute(f"""
            SELECT COUNT(*)
            FROM read_parquet('{parquet_path}')
            WHERE CAST("TransferDate" AS DATE) = CAST('{self.fecha}' AS DATE)
        """).fetchone()
        pipeline_count = pipeline_result[0]
        
        print(f"📊 CONTEOS SP_PRE_LOG_TRX:")
        print(f"   Oracle:    {oracle_count:,} registros")
        print(f"   Pipeline:  {pipeline_count:,} registros")
        print(f"   Diferencia: {pipeline_count - oracle_count:+,} registros")
        
        diferencia_pre_log = pipeline_count - oracle_count
        coincidencia_pre_log = diferencia_pre_log == 0
        
        print(f"🎯 SP_PRE_LOG_TRX: {'✅ PERFECTO' if coincidencia_pre_log else '❌ REQUIERE CORRECCIÓN'}")
        
        cursor.close()
        return oracle_count, pipeline_count, diferencia_pre_log

    def validar_cantidades_sp_log_trx(self):
        """2. VALIDACIÓN SP_LOG_TRX - Cantidades exactas"""
        print(f"\n🔍 2. VALIDACIÓN SP_LOG_TRX - CANTIDADES EXACTAS")
        print("=" * 80)
        
        cursor = self.oracle_conn.cursor()
        
        # Oracle LOG_TRX (verificar si existe, sino usar PRE_LOG_TRX como base)
        try:
            cursor.execute(f"""
                SELECT COUNT(*)
                FROM USR_DATALAKE.LOG_TRX
                WHERE TRUNC("DateTime") = TO_DATE('{self.fecha}', 'YYYY-MM-DD')
            """)
        except:
            # Si LOG_TRX no existe, usar PRE_LOG_TRX como referencia
            cursor.execute(f"""
                SELECT COUNT(*)
                FROM USR_DATALAKE.PRE_LOG_TRX
                WHERE TRUNC("TransferDate") = TO_DATE('{self.fecha}', 'YYYY-MM-DD')
            """)
        oracle_log_count = cursor.fetchone()[0]
        
        # Nuestro Pipeline LOG_TRX_FINAL
        log_trx_path = f"TEMP_LOGS_TRANSACCIONES/20250615/LOG_TRX_FINAL.parquet"
        
        pipeline_log_result = self.duck_conn.execute(f"""
            SELECT COUNT(*)
            FROM read_parquet('{log_trx_path}')
            WHERE CAST("DateTime" AS DATE) = CAST('{self.fecha}' AS DATE)
        """).fetchone()
        pipeline_log_count = pipeline_log_result[0]
        
        print(f"📊 CONTEOS SP_LOG_TRX:")
        print(f"   Oracle LOG_TRX:     {oracle_log_count:,} registros")
        print(f"   Pipeline LOG_TRX:   {pipeline_log_count:,} registros")
        print(f"   Diferencia:         {pipeline_log_count - oracle_log_count:+,} registros")
        
        diferencia_log_trx = pipeline_log_count - oracle_log_count
        coincidencia_log_trx = diferencia_log_trx == 0
        
        print(f"🎯 SP_LOG_TRX: {'✅ PERFECTO' if coincidencia_log_trx else '❌ REQUIERE CORRECCIÓN'}")
        
        cursor.close()
        return oracle_log_count, pipeline_log_count, diferencia_log_trx

    def validar_csv_final(self):
        """3. VALIDACIÓN CSV FINAL - Cantidades exactas"""
        print(f"\n🔍 3. VALIDACIÓN CSV FINAL - CANTIDADES EXACTAS")
        print("=" * 80)
        
        # Nuestro CSV generado
        csv_path = f"output/TR-20250615.csv"
        
        try:
            # Contar líneas del CSV (excluyendo header)
            with open(csv_path, 'r') as f:
                csv_lines = sum(1 for line in f) - 1  # -1 para excluir header
            
            print(f"📊 CONTEOS CSV FINAL:")
            print(f"   CSV generado: {csv_lines:,} registros")
            
            # Comparar con LOG_TRX_FINAL
            log_trx_path = f"TEMP_LOGS_TRANSACCIONES/20250615/LOG_TRX_FINAL.parquet"
            
            parquet_result = self.duck_conn.execute(f"""
                SELECT COUNT(*)
                FROM read_parquet('{log_trx_path}')
                WHERE CAST("DateTime" AS DATE) = CAST('{self.fecha}' AS DATE)
            """).fetchone()
            parquet_count = parquet_result[0]
            
            print(f"   Parquet LOG_TRX: {parquet_count:,} registros")
            print(f"   Diferencia: {csv_lines - parquet_count:+,} registros")
            
            coincidencia_csv = csv_lines == parquet_count
            print(f"🎯 CSV FINAL: {'✅ PERFECTO' if coincidencia_csv else '❌ REQUIERE CORRECCIÓN'}")
            
            return csv_lines, parquet_count, csv_lines - parquet_count
            
        except FileNotFoundError:
            print(f"❌ Archivo CSV no encontrado: {csv_path}")
            return 0, 0, 0

    def analizar_discrepancias_pre_log_trx(self, diferencia):
        """4. ANÁLISIS DE DISCREPANCIAS SP_PRE_LOG_TRX"""
        if diferencia == 0:
            print(f"\n✅ SP_PRE_LOG_TRX: Sin discrepancias - Homologación perfecta")
            return True
        
        print(f"\n🔍 4. ANÁLISIS DE DISCREPANCIAS SP_PRE_LOG_TRX")
        print("=" * 80)
        print(f"⚠️  Diferencia detectada: {diferencia:+,} registros")
        
        # Obtener TransferIDs únicos de Oracle
        cursor = self.oracle_conn.cursor()
        cursor.execute(f"""
            SELECT "TransferID"
            FROM USR_DATALAKE.PRE_LOG_TRX
            WHERE TRUNC("TransferDate") = TO_DATE('{self.fecha}', 'YYYY-MM-DD')
            ORDER BY "TransferID"
        """)
        oracle_ids = set(row[0] for row in cursor.fetchall())
        
        # Obtener TransferIDs únicos de nuestro pipeline
        parquet_path = f"TEMP_LOGS_TRANSACCIONES/20250615/PRE_LOG_TRX.parquet"
        
        pipeline_result = self.duck_conn.execute(f"""
            SELECT "TransferID"
            FROM read_parquet('{parquet_path}')
            WHERE CAST("TransferDate" AS DATE) = CAST('{self.fecha}' AS DATE)
            ORDER BY "TransferID"
        """).fetchall()
        pipeline_ids = set(row[0] for row in pipeline_result)
        
        # Encontrar diferencias
        extra_en_pipeline = pipeline_ids - oracle_ids
        falta_en_pipeline = oracle_ids - pipeline_ids
        
        print(f"📊 ANÁLISIS DE TRANSFERIDS:")
        print(f"   Oracle únicos:     {len(oracle_ids):,}")
        print(f"   Pipeline únicos:   {len(pipeline_ids):,}")
        print(f"   Extra en Pipeline: {len(extra_en_pipeline):,}")
        print(f"   Falta en Pipeline: {len(falta_en_pipeline):,}")
        
        if extra_en_pipeline:
            print(f"\n❌ REGISTROS EXTRA EN PIPELINE:")
            for i, transfer_id in enumerate(sorted(list(extra_en_pipeline)[:5]), 1):
                print(f"   {i}. TransferID: {transfer_id}")
        
        if falta_en_pipeline:
            print(f"\n❌ REGISTROS FALTANTES EN PIPELINE:")
            for i, transfer_id in enumerate(sorted(list(falta_en_pipeline)[:5]), 1):
                print(f"   {i}. TransferID: {transfer_id}")
        
        cursor.close()
        return False

    def investigar_proceso_original_pre_log_trx(self):
        """5. INVESTIGACIÓN PROCESO ORIGINAL SP_PRE_LOG_TRX"""
        print(f"\n🔍 5. INVESTIGACIÓN PROCESO ORIGINAL SP_PRE_LOG_TRX")
        print("=" * 80)
        
        cursor = self.oracle_conn.cursor()
        
        # Analizar filtros paso a paso del SP original
        print("📋 ANÁLISIS PASO A PASO DE FILTROS ORACLE:")
        
        # 1. MTX_TRANSACTION_HEADER base
        cursor.execute(f"""
            SELECT COUNT(*)
            FROM PDP_PROD10_MAINDBBUS.MTX_TRANSACTION_HEADER MTH
            WHERE MTH.TRANSFER_DATE >= TO_DATE('{self.fecha}', 'YYYY-MM-DD HH24:MI:SS')
            AND MTH.TRANSFER_DATE < TO_DATE('{self.fecha}', 'YYYY-MM-DD HH24:MI:SS')+1
        """)
        base_count = cursor.fetchone()[0]
        print(f"   1. MTX_HEADER base (fecha): {base_count:,}")
        
        # 2. Con filtro TRANSFER_STATUS
        cursor.execute(f"""
            SELECT COUNT(*)
            FROM PDP_PROD10_MAINDBBUS.MTX_TRANSACTION_HEADER MTH
            WHERE MTH.TRANSFER_DATE >= TO_DATE('{self.fecha}', 'YYYY-MM-DD HH24:MI:SS')
            AND MTH.TRANSFER_DATE < TO_DATE('{self.fecha}', 'YYYY-MM-DD HH24:MI:SS')+1
            AND MTH.TRANSFER_STATUS IN ('TA','TS')
        """)
        status_count = cursor.fetchone()[0]
        print(f"   2. + TRANSFER_STATUS: {status_count:,} (-{base_count - status_count})")
        
        # 3. Con filtro TRANSFER_VALUE
        cursor.execute(f"""
            SELECT COUNT(*)
            FROM PDP_PROD10_MAINDBBUS.MTX_TRANSACTION_HEADER MTH
            WHERE MTH.TRANSFER_DATE >= TO_DATE('{self.fecha}', 'YYYY-MM-DD HH24:MI:SS')
            AND MTH.TRANSFER_DATE < TO_DATE('{self.fecha}', 'YYYY-MM-DD HH24:MI:SS')+1
            AND MTH.TRANSFER_STATUS IN ('TA','TS')
            AND MTH.TRANSFER_VALUE <> 0
        """)
        value_count = cursor.fetchone()[0]
        print(f"   3. + TRANSFER_VALUE <> 0: {value_count:,} (-{status_count - value_count})")
        
        # 4. Con filtro IS_FINANCIAL
        cursor.execute(f"""
            SELECT COUNT(*)
            FROM PDP_PROD10_MAINDBBUS.MTX_TRANSACTION_HEADER MTH
            LEFT JOIN PDP_PROD10_MAINDB.SYS_SERVICE_TYPES SST ON MTH.SERVICE_TYPE = SST.SERVICE_TYPE 
            WHERE MTH.TRANSFER_DATE >= TO_DATE('{self.fecha}', 'YYYY-MM-DD HH24:MI:SS')
            AND MTH.TRANSFER_DATE < TO_DATE('{self.fecha}', 'YYYY-MM-DD HH24:MI:SS')+1
            AND MTH.TRANSFER_STATUS IN ('TA','TS')
            AND MTH.TRANSFER_VALUE <> 0
            AND SST.IS_FINANCIAL = 'Y'
        """)
        financial_count = cursor.fetchone()[0]
        print(f"   4. + IS_FINANCIAL = 'Y': {financial_count:,} (-{value_count - financial_count})")
        
        # 5. Resultado final Oracle PRE_LOG_TRX
        cursor.execute(f"""
            SELECT COUNT(*)
            FROM USR_DATALAKE.PRE_LOG_TRX
            WHERE TRUNC("TransferDate") = TO_DATE('{self.fecha}', 'YYYY-MM-DD')
        """)
        final_oracle = cursor.fetchone()[0]
        print(f"   5. Oracle PRE_LOG_TRX final: {final_oracle:,} (-{financial_count - final_oracle})")
        
        print(f"\n🎯 DIFERENCIA CRÍTICA: {financial_count - final_oracle:,} registros perdidos en Oracle")
        
        cursor.close()
        return financial_count, final_oracle

    def run_validacion_exhaustiva(self):
        """Ejecuta la validación exhaustiva completa"""
        print("🕵️ VALIDACIÓN EXHAUSTIVA DE CANTIDADES - HOMOLOGACIÓN 100%")
        print("=" * 80)
        print(f"📅 Fecha: {self.fecha}")
        print(f"🎯 Objetivo: Coincidencia perfecta 'dos gotas de agua'")
        
        try:
            # 1. Validar SP_PRE_LOG_TRX
            oracle_pre, pipeline_pre, diff_pre = self.validar_cantidades_sp_pre_log_trx()
            
            # 2. Validar SP_LOG_TRX
            oracle_log, pipeline_log, diff_log = self.validar_cantidades_sp_log_trx()
            
            # 3. Validar CSV Final
            csv_count, parquet_count, diff_csv = self.validar_csv_final()
            
            # 4. Análisis de discrepancias si las hay
            if diff_pre != 0:
                self.analizar_discrepancias_pre_log_trx(diff_pre)
                self.investigar_proceso_original_pre_log_trx()
            
            # 5. Resultado final
            print(f"\n{'='*20} RESULTADO VALIDACIÓN EXHAUSTIVA {'='*20}")
            
            homologacion_perfecta = (diff_pre == 0 and diff_log == 0 and diff_csv == 0)
            
            print(f"📊 RESUMEN DE CANTIDADES:")
            print(f"   SP_PRE_LOG_TRX: Oracle={oracle_pre:,}, Pipeline={pipeline_pre:,}, Diff={diff_pre:+,}")
            print(f"   SP_LOG_TRX:     Oracle={oracle_log:,}, Pipeline={pipeline_log:,}, Diff={diff_log:+,}")
            print(f"   CSV_FINAL:      CSV={csv_count:,}, Parquet={parquet_count:,}, Diff={diff_csv:+,}")
            
            print(f"\n🎯 HOMOLOGACIÓN: {'✅ 100% PERFECTA' if homologacion_perfecta else '❌ REQUIERE CORRECCIÓN'}")
            
            if homologacion_perfecta:
                print("🎉 ¡FELICITACIONES! Homologación 'dos gotas de agua' confirmada")
            else:
                print("🔧 Se requieren correcciones para lograr homologación perfecta")
            
            return homologacion_perfecta
            
        except Exception as e:
            print(f"❌ Error en validación exhaustiva: {e}")
            raise
        
        finally:
            if hasattr(self, 'oracle_conn'):
                self.oracle_conn.close()
            if hasattr(self, 'duck_conn'):
                self.duck_conn.close()

def main():
    """Función principal"""
    try:
        validador = ValidacionExhaustivaCantidades()
        success = validador.run_validacion_exhaustiva()
        
        sys.exit(0 if success else 1)
        
    except Exception as e:
        print(f"❌ Error crítico: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
