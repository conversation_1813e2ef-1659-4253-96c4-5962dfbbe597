#!/usr/bin/env python3
"""
Investigación exacta de las diferencias específicas encontradas
en la homologación para corregir la lógica
"""

import oracledb
import sys

def investigar_diferencias_exactas():
    """Investiga las diferencias exactas encontradas en la homologación"""
    print("🔍 INVESTIGACIÓN DE DIFERENCIAS EXACTAS")
    print("=" * 80)
    
    transfer_id = '175003230421458'
    
    try:
        connection = oracledb.connect(
            user='usr_datalake',
            password='U2024b1mD4t4l4k5',
            dsn='10.240.131.10:1521/MMONEY'
        )
        
        cursor = connection.cursor()
        
        print("1️⃣ DATOS EXACTOS EN ORACLE PRE_LOG_TRX:")
        print("-" * 60)
        
        # Obtener TODOS los datos de Oracle PRE_LOG_TRX
        cursor.execute("""
            SELECT 
                "TransferID",
                "FromID_Mobiquity",
                "From_AccountID_Mobiquity",
                "ToID_Mobiquity", 
                "To_AccountID_Mobiquity",
                "Comment",
                "TransactionType",
                "From_BankDomain",
                "To_BankDomain"
            FROM USR_DATALAKE.PRE_LOG_TRX
            WHERE "TransferID" = :transfer_id
        """, {'transfer_id': transfer_id})
        
        oracle_data = cursor.fetchall()
        if oracle_data:
            row = oracle_data[0]
            print(f"  TransferID: {row[0]}")
            print(f"  FromID_Mobiquity: {row[1]}")
            print(f"  From_AccountID_Mobiquity: {row[2]} ⭐ ORACLE VALUE")
            print(f"  ToID_Mobiquity: {row[3]}")
            print(f"  To_AccountID_Mobiquity: {row[4]} ⭐ ORACLE VALUE")
            print(f"  Comment: {repr(row[5])} ⭐ ORACLE VALUE")
            print(f"  TransactionType: {row[6]}")
            print(f"  From_BankDomain: {row[7]}")
            print(f"  To_BankDomain: {row[8]}")
            
            oracle_from_account = row[2]
            oracle_to_account = row[4]
            oracle_comment = row[5]
            from_id = row[1]
            to_id = row[3]
        else:
            print("  ❌ No encontrado en Oracle PRE_LOG_TRX")
            return
        
        print("\n2️⃣ ANÁLISIS DE USER_DATA_TRX:")
        print("-" * 60)
        
        # Verificar USER_DATA_TRX para ambos usuarios
        cursor.execute("""
            SELECT 
                USER_ID,
                O_USER_ID,
                WALLET_NUMBER
            FROM USR_DATALAKE.USER_DATA_TRX
            WHERE O_USER_ID IN (:from_id, :to_id)
            ORDER BY O_USER_ID
        """, {'from_id': from_id, 'to_id': to_id})
        
        user_data_results = cursor.fetchall()
        
        print("  USER_DATA_TRX valores:")
        for row in user_data_results:
            print(f"    O_USER_ID {row[1]}: WALLET_NUMBER = {row[2]}")
            if row[1] == from_id:
                user_data_from_wallet = row[2]
            if row[1] == to_id:
                user_data_to_wallet = row[2]
        
        print(f"\n  🔍 COMPARACIÓN From_AccountID_Mobiquity:")
        print(f"    Oracle PRE_LOG_TRX: {oracle_from_account}")
        print(f"    USER_DATA_TRX: {user_data_from_wallet}")
        print(f"    ¿Coinciden? {'✅ SÍ' if str(oracle_from_account) == str(user_data_from_wallet) else '❌ NO'}")
        
        print(f"\n  🔍 COMPARACIÓN To_AccountID_Mobiquity:")
        print(f"    Oracle PRE_LOG_TRX: {oracle_to_account}")
        print(f"    USER_DATA_TRX: {user_data_to_wallet}")
        print(f"    ¿Coinciden? {'✅ SÍ' if str(oracle_to_account) == str(user_data_to_wallet) else '❌ NO'}")
        
        print("\n3️⃣ INVESTIGACIÓN DE LÓGICA ESPECIAL:")
        print("-" * 60)
        
        # Si no coinciden, investigar por qué Oracle usa valores diferentes
        if str(oracle_from_account) != str(user_data_from_wallet):
            print(f"  🔍 Oracle usa {oracle_from_account} en lugar de {user_data_from_wallet}")
            print("  Investigando posibles fuentes...")
            
            # Buscar en MTX_WALLET
            cursor.execute("""
                SELECT 
                    USER_ID,
                    WALLET_NUMBER,
                    STATUS,
                    MODIFIED_ON
                FROM PDP_PROD10_MAINDBBUS.MTX_WALLET
                WHERE USER_ID = :user_id
                AND WALLET_NUMBER IN (:oracle_val, :user_data_val)
                ORDER BY MODIFIED_ON DESC
            """, {
                'user_id': from_id, 
                'oracle_val': oracle_from_account, 
                'user_data_val': user_data_from_wallet
            })
            
            wallet_results = cursor.fetchall()
            if wallet_results:
                print(f"    MTX_WALLET matches:")
                for row in wallet_results:
                    status_desc = "ACTIVA" if row[2] == 'Y' else "INACTIVA"
                    print(f"      WALLET: {row[1]}, STATUS: {row[2]} ({status_desc}), MODIFIED: {row[3]}")
            
            # Buscar en USER_ACCOUNT_HISTORY
            cursor.execute("""
                SELECT 
                    USER_ID,
                    ACCOUNT_ID,
                    ATTR7_OLD,
                    ATTR8_OLD
                FROM USER_ACCOUNT_HISTORY
                WHERE USER_ID = :user_id
                AND (ACCOUNT_ID = :oracle_val OR ATTR8_OLD = :oracle_val)
            """, {
                'user_id': from_id,
                'oracle_val': oracle_from_account
            })
            
            history_results = cursor.fetchall()
            if history_results:
                print(f"    USER_ACCOUNT_HISTORY matches:")
                for row in history_results:
                    print(f"      USER_ID: {row[0]}, ACCOUNT_ID: {row[1]}")
                    print(f"      ATTR7_OLD: {row[2]}, ATTR8_OLD: {row[3]}")
        
        print("\n4️⃣ ANÁLISIS DE COMMENT:")
        print("-" * 60)
        
        print(f"  Oracle Comment: {repr(oracle_comment)}")
        print(f"  Tipo: {type(oracle_comment)}")
        
        if oracle_comment is None:
            print("  ✅ Oracle usa NULL")
            print("  🔧 S3 debe usar NULL, no string vacío")
        else:
            print(f"  ✅ Oracle usa valor: {oracle_comment}")
        
        print("\n5️⃣ BÚSQUEDA DE LÓGICA OCULTA EN SP_PRE_LOG_TRX:")
        print("-" * 60)
        
        # Buscar si hay lógica especial en el SP original
        print("  Analizando stored procedure SP_PRE_LOG_TRX...")
        print("  Líneas 180-181: UPAYER.ACCOUNT_ID, UPAYEE.ACCOUNT_ID")
        print("  Esto sugiere que Oracle usa USER_DATA.ACCOUNT_ID directamente")
        print("  Pero USER_DATA.ACCOUNT_ID = USER_DATA_TRX.WALLET_NUMBER")
        
        # Verificar si hay diferencia en cómo se construye USER_DATA
        print("\n  🔍 Verificando construcción de USER_DATA en Oracle...")
        print("  SP_PRE_LOG_USR líneas 34-37:")
        print("    CASE")
        print("      WHEN UP.ATTR8 IS NOT NULL THEN UP.ATTR8")
        print("      WHEN LENGTH(REPLACE(MW.WALLET_NUMBER,'UA.',''))>15 THEN SUBSTR(...)")
        print("      ELSE REPLACE(MW.WALLET_NUMBER,'UA.','')")
        print("    END AS WALLET_NUMBER")
        
        # Verificar ATTR8 vs WALLET_NUMBER para este usuario
        cursor.execute("""
            SELECT 
                UP.USER_ID,
                UP.ATTR8,
                MW.WALLET_NUMBER,
                MW.STATUS,
                MW.MODIFIED_ON,
                ROW_NUMBER() OVER (PARTITION BY MW.USER_ID ORDER BY MW.MODIFIED_ON DESC) as RN
            FROM PDP_PROD10_MAINDB.USER_PROFILE UP
            LEFT JOIN PDP_PROD10_MAINDBBUS.MTX_WALLET MW ON UP.USER_ID = MW.USER_ID
            WHERE UP.USER_ID = :user_id
            ORDER BY MW.MODIFIED_ON DESC
        """, {'user_id': from_id})
        
        profile_wallet_data = cursor.fetchall()
        if profile_wallet_data:
            print(f"\n  USER_PROFILE + MTX_WALLET para {from_id}:")
            for i, row in enumerate(profile_wallet_data[:3]):
                print(f"    [{i+1}] ATTR8: {row[1]}, WALLET: {row[2]}, STATUS: {row[3]}, RN: {row[5]}")
                
                if row[5] == 1:  # Primera wallet (más reciente)
                    if row[1] is not None:  # ATTR8 no es NULL
                        expected_wallet = row[1]
                        logic_used = "ATTR8"
                    else:
                        expected_wallet = row[2].replace('UA.', '') if row[2] else None
                        logic_used = "WALLET_NUMBER"
                    
                    print(f"    🎯 LÓGICA SP_PRE_LOG_USR: {logic_used} = {expected_wallet}")
        
        cursor.close()
        connection.close()
        
        print("\n6️⃣ CONCLUSIONES:")
        print("-" * 60)
        print("1. Oracle PRE_LOG_TRX tiene valores específicos que difieren de USER_DATA_TRX")
        print("2. Necesitamos identificar la lógica exacta que produce estos valores")
        print("3. Comment debe ser NULL, no string vacío")
        print("4. Hay lógica oculta que no está en los SP documentados")
        
    except Exception as e:
        print(f"❌ Error en investigación: {e}")
        import traceback
        traceback.print_exc()

def main():
    print("🚀 INVESTIGACIÓN DE DIFERENCIAS EXACTAS")
    print("=" * 80)
    print("Analizando las 3 diferencias encontradas en la homologación")
    print()
    
    investigar_diferencias_exactas()
    
    print("\n🏁 INVESTIGACIÓN COMPLETADA")

if __name__ == "__main__":
    main()
