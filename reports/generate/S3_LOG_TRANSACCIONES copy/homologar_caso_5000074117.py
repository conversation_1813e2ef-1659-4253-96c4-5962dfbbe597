#!/usr/bin/env python3
"""
Script para homologar el caso específico TransferID = '5000074117'
Verificar si la nueva lógica afectó otros casos
"""

import oracledb
import duckdb
import boto3
import sys
import pandas as pd
from pathlib import Path

def homologar_caso_especifico():
    """Homologa el caso TransferID = '5000074117'"""
    print("🔍 HOMOLOGACIÓN CASO ESPECÍFICO: TransferID = '5000074117'")
    print("=" * 80)
    
    transfer_id = '5000074117'
    fecha = '2025-06-15'
    parquet_path = '/home/<USER>/REPORTE_2025/reports/generate/S3_LOG_TRANSACCIONES/TEMP_LOGS_TRANSACCIONES/20250615/PRE_LOG_TRX.parquet'
    
    print(f"TransferID analizado: {transfer_id}")
    print(f"Fecha: {fecha}")
    print(f"Archivo S3: {parquet_path}")
    print()
    
    # 1. CONSULTAR ORACLE
    print("1️⃣ CONSULTANDO ORACLE PRE_LOG_TRX:")
    print("-" * 50)
    
    try:
        connection = oracledb.connect(
            user='usr_datalake',
            password='U2024b1mD4t4l4k5',
            dsn='*************:1521/MMONEY'
        )
        
        cursor = connection.cursor()
        
        # Obtener datos específicos de Oracle
        cursor.execute("""
            SELECT 
                "TransferID",
                "FromID_Mobiquity",
                "From_AccountID_Mobiquity",
                "ToID_Mobiquity", 
                "To_AccountID_Mobiquity",
                "Comment",
                "TransactionType",
                "From_BankDomain",
                "To_BankDomain",
                "TransferDate"
            FROM USR_DATALAKE.PRE_LOG_TRX 
            WHERE "TransferID" = :transfer_id
        """, {'transfer_id': transfer_id})
        
        oracle_data = cursor.fetchall()
        if oracle_data:
            oracle_row = oracle_data[0]
            oracle_dict = dict(zip([
                'TransferID', 'FromID_Mobiquity', 'From_AccountID_Mobiquity',
                'ToID_Mobiquity', 'To_AccountID_Mobiquity', 'Comment',
                'TransactionType', 'From_BankDomain', 'To_BankDomain', 'TransferDate'
            ], oracle_row))
            
            print(f"✅ Registro encontrado en Oracle")
            print("\n📊 DATOS ORACLE:")
            for key, value in oracle_dict.items():
                print(f"  {key}: {value}")
        else:
            print("❌ No se encontró el registro en Oracle")
            return
        
        cursor.close()
        connection.close()
        
    except Exception as e:
        print(f"❌ Error consultando Oracle: {e}")
        return
    
    # 2. CONSULTAR S3
    print("\n2️⃣ CONSULTANDO S3 PRE_LOG_TRX:")
    print("-" * 50)
    
    try:
        # Configurar DuckDB con S3
        conn = duckdb.connect()
        
        session = boto3.Session()
        credentials = session.get_credentials().get_frozen_credentials()
        
        conn.sql("INSTALL httpfs;")
        conn.sql("LOAD httpfs;")
        conn.sql("SET s3_region='us-east-1';")
        conn.sql("SET s3_use_ssl=true;")
        conn.sql("SET s3_url_style='path';")
        conn.sql(f"SET s3_access_key_id='{credentials.access_key}';")
        conn.sql(f"SET s3_secret_access_key='{credentials.secret_key}';")
        if credentials.token:
            conn.sql(f"SET s3_session_token='{credentials.token}';")
        
        # Obtener datos específicos de S3
        s3_data = conn.execute(f"""
            SELECT 
                "TransferID",
                "FromID_Mobiquity",
                "From_AccountID_Mobiquity",
                "ToID_Mobiquity", 
                "To_AccountID_Mobiquity",
                "Comment",
                "TransactionType",
                "From_BankDomain",
                "To_BankDomain",
                "TransferDate"
            FROM read_parquet('{parquet_path}')
            WHERE "TransferID" = '{transfer_id}'
        """).fetchall()
        
        if s3_data:
            s3_row = s3_data[0]
            s3_dict = dict(zip([
                'TransferID', 'FromID_Mobiquity', 'From_AccountID_Mobiquity',
                'ToID_Mobiquity', 'To_AccountID_Mobiquity', 'Comment',
                'TransactionType', 'From_BankDomain', 'To_BankDomain', 'TransferDate'
            ], s3_row))
            
            print(f"✅ Registro encontrado en S3")
            print("\n📊 DATOS S3:")
            for key, value in s3_dict.items():
                print(f"  {key}: {value}")
        else:
            print("❌ No se encontró el registro en S3")
            return
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error consultando S3: {e}")
        return
    
    # 3. COMPARACIÓN DETALLADA
    print("\n3️⃣ COMPARACIÓN DETALLADA:")
    print("-" * 50)
    
    campos_clave = [
        'TransferID', 'FromID_Mobiquity', 'From_AccountID_Mobiquity',
        'ToID_Mobiquity', 'To_AccountID_Mobiquity', 'Comment',
        'TransactionType', 'From_BankDomain', 'To_BankDomain'
    ]
    
    coincidencias = 0
    diferencias = 0
    
    for campo in campos_clave:
        oracle_val = oracle_dict.get(campo)
        s3_val = s3_dict.get(campo)
        
        # Normalizar valores para comparación
        oracle_str = str(oracle_val) if oracle_val is not None else 'NULL'
        s3_str = str(s3_val) if s3_val is not None else 'NULL'
        
        if oracle_str == s3_str:
            coincidencias += 1
            status = "✅"
        else:
            diferencias += 1
            status = "❌"
        
        print(f"  {status} {campo}:")
        print(f"      Oracle: {oracle_str}")
        print(f"      S3:     {s3_str}")
        if oracle_str != s3_str:
            print(f"      🔍 DIFERENCIA DETECTADA")
        print()
    
    # 4. ANÁLISIS ESPECÍFICO DE From_AccountID_Mobiquity
    print("4️⃣ ANÁLISIS ESPECÍFICO From_AccountID_Mobiquity:")
    print("-" * 50)
    
    oracle_from_account = oracle_dict.get('From_AccountID_Mobiquity')
    s3_from_account = s3_dict.get('From_AccountID_Mobiquity')
    from_id = oracle_dict.get('FromID_Mobiquity')
    
    print(f"  FromID_Mobiquity: {from_id}")
    print(f"  Oracle From_AccountID_Mobiquity: {oracle_from_account}")
    print(f"  S3 From_AccountID_Mobiquity: {s3_from_account}")
    
    if str(oracle_from_account) == str(s3_from_account):
        print("  ✅ COINCIDEN: Los valores son iguales")
    else:
        print("  ❌ DIFIEREN: Los valores son diferentes")
        print(f"     🔍 PROBLEMA: La nueva lógica afectó este caso")
        print(f"     🔧 NECESITA CORRECCIÓN: Revisar lógica de mapeo")
    
    # 5. RESUMEN FINAL
    print("\n5️⃣ RESUMEN:")
    print("-" * 50)
    
    porcentaje_coincidencia = (coincidencias / len(campos_clave)) * 100
    
    print(f"📊 ESTADÍSTICAS:")
    print(f"  Campos coincidentes: {coincidencias}")
    print(f"  Campos diferentes: {diferencias}")
    print(f"  Porcentaje de coincidencia: {porcentaje_coincidencia:.1f}%")
    
    if diferencias == 0:
        print("\n🎉 ¡CASO CORRECTO!")
        print("✅ Este caso no fue afectado por la nueva lógica")
    else:
        print(f"\n⚠️  CASO AFECTADO")
        print("❌ La nueva lógica causó diferencias en este caso")
        print("🔧 Necesita revisión y corrección")

def main():
    print("🚀 HOMOLOGACIÓN CASO ESPECÍFICO")
    print("=" * 80)
    print("Verificando si la nueva lógica afectó otros casos")
    print()
    
    homologar_caso_especifico()
    
    print("\n🏁 HOMOLOGACIÓN COMPLETADA")

if __name__ == "__main__":
    main()
