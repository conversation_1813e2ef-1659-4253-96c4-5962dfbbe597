#!/usr/bin/env python3
"""
Verificar qué valor tiene PROFILE_TRX en USER_DATA_TRX de S3
vs Oracle para entender la diferencia
"""

import oracledb
import duckdb
import boto3
import sys

def verificar_profile_trx():
    """Verifica PROFILE_TRX en S3 vs Oracle"""
    print("🔍 VERIFICACIÓN: PROFILE_TRX en S3 vs Oracle")
    print("=" * 80)
    
    user_ids = ['3461446', '3507575']  # UNIQUE y BACKUS
    
    try:
        # 1. CONSULTAR ORACLE USER_DATA_TRX
        print("1️⃣ CONSULTANDO ORACLE USER_DATA_TRX:")
        print("-" * 60)
        
        connection = oracledb.connect(
            user='usr_datalake',
            password='U2024b1mD4t4l4k5',
            dsn='10.240.131.10:1521/MMONEY'
        )
        
        cursor = connection.cursor()
        
        for user_id in user_ids:
            cursor.execute("""
                SELECT 
                    USER_ID,
                    O_USER_ID,
                    PROFILE_TRX,
                    LOGIN_ID,
                    MSISDN
                FROM USR_DATALAKE.USER_DATA_TRX
                WHERE O_USER_ID = :user_id
            """, {'user_id': user_id})
            
            oracle_data = cursor.fetchone()
            if oracle_data:
                print(f"\n  📋 Oracle USER_DATA_TRX para {user_id}:")
                print(f"    USER_ID: {oracle_data[0]}")
                print(f"    O_USER_ID: {oracle_data[1]}")
                print(f"    PROFILE_TRX: '{oracle_data[2]}' ⭐ VALOR ORACLE")
                print(f"    LOGIN_ID: {oracle_data[3]}")
                print(f"    MSISDN: {oracle_data[4]}")
        
        cursor.close()
        connection.close()
        
        # 2. CONSULTAR S3 USER_DATA_TRX
        print(f"\n2️⃣ CONSULTANDO S3 USER_DATA_TRX:")
        print("-" * 60)
        
        # Configurar DuckDB con S3
        conn = duckdb.connect()
        
        session = boto3.Session()
        credentials = session.get_credentials().get_frozen_credentials()
        
        conn.sql("INSTALL httpfs;")
        conn.sql("LOAD httpfs;")
        conn.sql("SET s3_region='us-east-1';")
        conn.sql("SET s3_use_ssl=true;")
        conn.sql("SET s3_url_style='path';")
        conn.sql(f"SET s3_access_key_id='{credentials.access_key}';")
        conn.sql(f"SET s3_secret_access_key='{credentials.secret_key}';")
        if credentials.token:
            conn.sql(f"SET s3_session_token='{credentials.token}';")
        
        # Obtener path de USER_DATA_TRX en S3
        s3_sources = {
            'user_data_trx': 's3://ec-landing-process-01/datalake/MMONEY/USR_DATALAKE/USER_DATA_TRX/**/*.parquet'
        }
        
        for user_id in user_ids:
            s3_data = conn.execute(f"""
                SELECT 
                    USER_ID,
                    O_USER_ID,
                    PROFILE_TRX,
                    LOGIN_ID,
                    MSISDN
                FROM read_parquet('{s3_sources['user_data_trx']}', union_by_name=true)
                WHERE O_USER_ID = '{user_id}'
                LIMIT 1
            """).fetchall()
            
            if s3_data:
                row = s3_data[0]
                print(f"\n  📋 S3 USER_DATA_TRX para {user_id}:")
                print(f"    USER_ID: {row[0]}")
                print(f"    O_USER_ID: {row[1]}")
                print(f"    PROFILE_TRX: '{row[2]}' ⭐ VALOR S3")
                print(f"    LOGIN_ID: {row[3]}")
                print(f"    MSISDN: {row[4]}")
        
        # 3. VERIFICAR CÓMO SE CONSTRUYE USER_DATA EN EL PIPELINE
        print(f"\n3️⃣ VERIFICANDO CONSTRUCCIÓN EN PIPELINE:")
        print("-" * 60)
        
        # Simular la lógica del pipeline
        for user_id in user_ids:
            pipeline_data = conn.execute(f"""
                SELECT DISTINCT
                    USER_ID,
                    O_USER_ID AS M_USER_ID,
                    PROFILE_TRX AS PROFILE,
                    WALLET_NUMBER AS ACCOUNT_ID,
                    WALLET_NUMBER,
                    MSISDN,
                    USER_CODE,
                    LOGIN_ID,
                    WORKSPACE_ID,
                    ISSUER_CODE,
                    ID_TYPE
                FROM read_parquet('{s3_sources['user_data_trx']}', union_by_name=true)
                WHERE O_USER_ID = '{user_id}'
                LIMIT 1
            """).fetchall()
            
            if pipeline_data:
                row = pipeline_data[0]
                print(f"\n  📋 Pipeline USER_DATA para {user_id}:")
                print(f"    M_USER_ID: {row[1]}")
                print(f"    PROFILE: '{row[2]}' ⭐ VALOR USADO EN PIPELINE")
                print(f"    LOGIN_ID: {row[7]}")
        
        # 4. VERIFICAR LÓGICA To_Profile EN PIPELINE
        print(f"\n4️⃣ SIMULANDO LÓGICA To_Profile:")
        print("-" * 60)
        
        for user_id in user_ids:
            # Simular la lógica actual del pipeline
            user_data = conn.execute(f"""
                SELECT 
                    O_USER_ID,
                    PROFILE_TRX,
                    LOGIN_ID
                FROM read_parquet('{s3_sources['user_data_trx']}', union_by_name=true)
                WHERE O_USER_ID = '{user_id}'
                LIMIT 1
            """).fetchone()
            
            if user_data:
                o_user_id = user_data[0]
                profile_trx = user_data[1]
                login_id = user_data[2]
                
                print(f"\n  📋 Simulación para {user_id}:")
                print(f"    PROFILE_TRX: '{profile_trx}'")
                print(f"    LOGIN_ID: '{login_id}'")
                
                # Simular lógica To_Profile
                if profile_trx and 'PROVEEDOR' in profile_trx:
                    to_profile_caso1 = profile_trx  # CASE 1: PROFILE LIKE '%PROVEEDOR%'
                    print(f"    CASO 1 (PROVEEDOR): '{to_profile_caso1}'")
                
                # Para EXTERNAL_PAYMENT
                to_profile_caso2 = profile_trx  # CASE 2: EXTERNAL_PAYMENT
                print(f"    CASO 2 (EXTERNAL_PAYMENT): '{to_profile_caso2}'")
                
                # Caso general
                to_profile_caso3 = f"FCOMPARTAMOS {profile_trx}"  # CASE 3: General
                print(f"    CASO 3 (GENERAL): '{to_profile_caso3}'")
                
                print(f"    🎯 ORACLE ESPERADO: '{login_id.upper()} PROVEEDOR DE SERVICIOS'")
        
        conn.close()
        
        print(f"\n5️⃣ CONCLUSIONES:")
        print("-" * 60)
        print("1. Verificar si PROFILE_TRX en S3 tiene el valor completo")
        print("2. Si no, necesitamos construir el valor correcto")
        print("3. Para EXTERNAL_PAYMENT: LOGIN_ID.upper() + ' PROVEEDOR DE SERVICIOS'")
        
    except Exception as e:
        print(f"❌ Error en verificación: {e}")
        import traceback
        traceback.print_exc()

def main():
    print("🚀 VERIFICACIÓN PROFILE_TRX")
    print("=" * 80)
    print("Comparando valores en S3 vs Oracle")
    print()
    
    verificar_profile_trx()
    
    print("\n🏁 VERIFICACIÓN COMPLETADA")

if __name__ == "__main__":
    main()
