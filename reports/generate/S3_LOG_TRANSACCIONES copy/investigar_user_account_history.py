#!/usr/bin/env python3
"""
Script para investigar USER_ACCOUNT_HISTORY - LA CLAVE DEL PROBLEMA
En SP_LOG_TRX líneas 32 y 57 Oracle usa USER_ACCOUNT_HISTORY para mapear valores
"""

import oracledb
import duckdb
import boto3
import sys

def investigar_user_account_history_oracle():
    """Investiga USER_ACCOUNT_HISTORY en Oracle"""
    print("🔍 INVESTIGANDO USER_ACCOUNT_HISTORY EN ORACLE")
    print("=" * 60)
    
    try:
        connection = oracledb.connect(
            user='usr_datalake',
            password='U2024b1mD4t4l4k5',
            dsn='*************:1521/MMONEY'
        )
        
        cursor = connection.cursor()
        user_id = '945661'
        
        print("1️⃣ ESTRUCTURA DE USER_ACCOUNT_HISTORY:")
        print("-" * 40)
        
        # Ver estructura de la tabla
        cursor.execute("""
            SELECT COLUMN_NAME, DATA_TYPE, DATA_LENGTH
            FROM ALL_TAB_COLUMNS
            WHERE TABLE_NAME = 'USER_ACCOUNT_HISTORY'
            AND OWNER = 'USR_DATALAKE'
            ORDER BY COLUMN_ID
        """)
        
        columns = cursor.fetchall()
        print("Columnas en USER_ACCOUNT_HISTORY:")
        for col in columns:
            print(f"  {col[0]}: {col[1]}({col[2]})")
        
        print("\n2️⃣ DATOS PARA USER_ID 945661:")
        print("-" * 40)
        
        # Buscar datos para nuestro usuario
        cursor.execute("""
            SELECT
                USER_ID,
                ACCOUNT_ID,
                ATTR7_OLD,
                ATTR8_OLD,
                CREATED_AT
            FROM USER_ACCOUNT_HISTORY
            WHERE USER_ID = :user_id
            ORDER BY CREATED_AT DESC
        """, {'user_id': user_id})
        
        history_data = cursor.fetchall()
        if history_data:
            print(f"Registros encontrados: {len(history_data)}")
            for i, row in enumerate(history_data):
                print(f"  [{i+1}] USER_ID: {row[0]}")
                print(f"      ACCOUNT_ID: {row[1]}")
                print(f"      ATTR7_OLD: {row[2]} ⭐ ESTE PODRÍA SER EL VALOR CLAVE")
                print(f"      ATTR8_OLD: {row[3]} ⭐ ESTE TAMBIÉN")
                print(f"      CREATED: {row[4]}")
                print()
        else:
            print("❌ No se encontraron registros en USER_ACCOUNT_HISTORY")
        
        print("\n3️⃣ LÓGICA DE SP_LOG_TRX:")
        print("-" * 40)
        print("En SP_LOG_TRX línea 32:")
        print("  WHEN H_PAYER.USER_ID IS NOT NULL AND H_PAYER.ACCOUNT_ID = MTH.\"From_AccountID_Mobiquity\"")
        print("  THEN H_PAYER.ATTR7_OLD")
        print()
        print("En SP_LOG_TRX línea 57:")
        print("  WHEN H_PAYER.USER_ID IS NOT NULL AND H_PAYER.ACCOUNT_ID = MTH.\"From_AccountID_Mobiquity\"")
        print("  THEN H_PAYER.ATTR8_OLD")
        print()
        print("Esto significa que Oracle:")
        print("1. Busca en USER_ACCOUNT_HISTORY donde USER_ID = FromID_Mobiquity")
        print("2. Si encuentra match con ACCOUNT_ID = From_AccountID_Mobiquity")
        print("3. Usa ATTR8_OLD como FromAccountID final")
        
        # Verificar la lógica específica
        print("\n4️⃣ VERIFICACIÓN DE LA LÓGICA:")
        print("-" * 40)
        
        # Simular la lógica de Oracle
        cursor.execute("""
            SELECT 
                H.USER_ID,
                H.ACCOUNT_ID,
                H.ATTR7_OLD,
                H.ATTR8_OLD,
                P."From_AccountID_Mobiquity"
            FROM USER_ACCOUNT_HISTORY H,
                 (SELECT "FromID_Mobiquity", "From_AccountID_Mobiquity" 
                  FROM USR_DATALAKE.PRE_LOG_TRX 
                  WHERE "TransferID" = '***************') P
            WHERE H.USER_ID = P."FromID_Mobiquity"
            AND H.ACCOUNT_ID = P."From_AccountID_Mobiquity"
        """)
        
        logic_result = cursor.fetchall()
        if logic_result:
            row = logic_result[0]
            print("✅ MATCH ENCONTRADO en USER_ACCOUNT_HISTORY:")
            print(f"  USER_ID: {row[0]}")
            print(f"  ACCOUNT_ID: {row[1]}")
            print(f"  ATTR7_OLD: {row[2]}")
            print(f"  ATTR8_OLD: {row[3]} ⭐ ESTE ES EL VALOR QUE USA ORACLE")
            print(f"  From_AccountID_Mobiquity: {row[4]}")
            print()
            print(f"🎯 EXPLICACIÓN: Oracle usa ATTR8_OLD = {row[3]} en lugar de From_AccountID_Mobiquity = {row[4]}")
        else:
            print("❌ No hay match en USER_ACCOUNT_HISTORY con la lógica de Oracle")
        
        cursor.close()
        connection.close()
        
        return history_data, logic_result
        
    except Exception as e:
        print(f"❌ Error investigando Oracle: {e}")
        return None, None

def investigar_user_account_history_s3():
    """Investiga USER_ACCOUNT_HISTORY en S3"""
    print("\n🔍 INVESTIGANDO USER_ACCOUNT_HISTORY EN S3")
    print("=" * 60)
    
    try:
        # Configurar DuckDB con S3
        conn = duckdb.connect()
        
        session = boto3.Session()
        credentials = session.get_credentials().get_frozen_credentials()
        
        conn.sql("INSTALL httpfs;")
        conn.sql("LOAD httpfs;")
        conn.sql("SET s3_region='us-east-1';")
        conn.sql("SET s3_use_ssl=true;")
        conn.sql("SET s3_url_style='path';")
        conn.sql(f"SET s3_access_key_id='{credentials.access_key}';")
        conn.sql(f"SET s3_secret_access_key='{credentials.secret_key}';")
        if credentials.token:
            conn.sql(f"SET s3_session_token='{credentials.token}';")
        
        s3_bucket_golden = "prd-datalake-golden-zone-************"
        user_account_history_path = f's3://{s3_bucket_golden}/LOGS_USUARIOS/USER_ACCOUNT_HISTORY.parquet'
        
        user_id = '945661'
        
        print("1️⃣ DATOS EN S3 PARA USER_ID 945661:")
        print("-" * 40)
        
        result = conn.execute(f"""
            SELECT 
                USER_ID,
                ACCOUNT_ID,
                ATTR7_OLD,
                ATTR8_OLD
            FROM read_parquet('{user_account_history_path}')
            WHERE USER_ID = '{user_id}'
            ORDER BY USER_ID
        """).fetchall()
        
        if result:
            print(f"Registros encontrados: {len(result)}")
            for i, row in enumerate(result):
                print(f"  [{i+1}] USER_ID: {row[0]}")
                print(f"      ACCOUNT_ID: {row[1]}")
                print(f"      ATTR7_OLD: {row[2]}")
                print(f"      ATTR8_OLD: {row[3]}")
                print()
        else:
            print("❌ No se encontraron registros en USER_ACCOUNT_HISTORY S3")
        
        conn.close()
        return result
        
    except Exception as e:
        print(f"❌ Error investigando S3: {e}")
        return None

def main():
    print("🚀 INVESTIGACIÓN USER_ACCOUNT_HISTORY - LA CLAVE DEL PROBLEMA")
    print("=" * 80)
    print("Analizando por qué Oracle usa valores diferentes")
    print()
    
    # Investigar Oracle
    oracle_history, oracle_logic = investigar_user_account_history_oracle()
    
    # Investigar S3
    s3_history = investigar_user_account_history_s3()
    
    print("\n🎯 CONCLUSIONES:")
    print("=" * 60)
    print("1. Oracle usa USER_ACCOUNT_HISTORY para mapear valores finales")
    print("2. En SP_LOG_TRX, Oracle busca match entre:")
    print("   - USER_ACCOUNT_HISTORY.USER_ID = PRE_LOG_TRX.FromID_Mobiquity")
    print("   - USER_ACCOUNT_HISTORY.ACCOUNT_ID = PRE_LOG_TRX.From_AccountID_Mobiquity")
    print("3. Si encuentra match, usa USER_ACCOUNT_HISTORY.ATTR8_OLD como valor final")
    print("4. Tu implementación S3 NO incluye esta lógica de USER_ACCOUNT_HISTORY")
    print()
    print("🔧 SOLUCIÓN:")
    print("Necesitas agregar la lógica de USER_ACCOUNT_HISTORY a tu código S3")

if __name__ == "__main__":
    main()
