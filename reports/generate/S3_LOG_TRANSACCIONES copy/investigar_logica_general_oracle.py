#!/usr/bin/env python3
"""
Investigación para encontrar la lógica GENERAL que Oracle usa
para AccountID_Mobiquity, no casos específicos hardcodeados
"""

import oracledb
import sys

def investigar_logica_general():
    """Investiga la lógica general de Oracle para AccountID_Mobiquity"""
    print("🔍 INVESTIGACIÓN DE LÓGICA GENERAL ORACLE")
    print("=" * 80)
    
    try:
        connection = oracledb.connect(
            user='usr_datalake',
            password='U2024b1mD4t4l4k5',
            dsn='*************:1521/MMONEY'
        )
        
        cursor = connection.cursor()
        
        print("1️⃣ ANÁLISIS DE MÚLTIPLES USUARIOS CON MÚLTIPLES WALLETS:")
        print("-" * 60)
        
        # Buscar usuarios que tengan múltiples wallets para entender el patrón
        cursor.execute("""
            SELECT 
                MW.USER_ID,
                COUNT(*) as WALLET_COUNT,
                LISTAGG(MW.WALLET_NUMBER || '(' || MW.STATUS || ')', ', ') 
                    WITHIN GROUP (ORDER BY MW.MODIFIED_ON DESC) as WALLETS
            FROM PDP_PROD10_MAINDBBUS.MTX_WALLET MW
            WHERE MW.USER_ID IN (
                SELECT DISTINCT "FromID_Mobiquity" 
                FROM USR_DATALAKE.PRE_LOG_TRX 
                WHERE ROWNUM <= 100
            )
            GROUP BY MW.USER_ID
            HAVING COUNT(*) > 1
            ORDER BY COUNT(*) DESC
        """)
        
        multi_wallet_users = cursor.fetchall()
        
        print(f"  Usuarios con múltiples wallets: {len(multi_wallet_users)}")
        for i, row in enumerate(multi_wallet_users[:5]):
            print(f"    [{i+1}] USER_ID: {row[0]}, WALLETS: {row[1]}, DETALLE: {row[2]}")
        
        print("\n2️⃣ ANÁLISIS DE PATRÓN EN PRE_LOG_TRX:")
        print("-" * 60)
        
        # Analizar qué wallet usa Oracle para usuarios con múltiples wallets
        if multi_wallet_users:
            sample_user = multi_wallet_users[0][0]  # Tomar el primer usuario
            
            print(f"  Analizando usuario: {sample_user}")
            
            # Ver qué wallet usa Oracle en PRE_LOG_TRX
            cursor.execute("""
                SELECT DISTINCT
                    "FromID_Mobiquity",
                    "From_AccountID_Mobiquity",
                    COUNT(*) as USAGE_COUNT
                FROM USR_DATALAKE.PRE_LOG_TRX
                WHERE "FromID_Mobiquity" = :user_id
                GROUP BY "FromID_Mobiquity", "From_AccountID_Mobiquity"
                ORDER BY COUNT(*) DESC
            """, {'user_id': sample_user})
            
            pre_log_usage = cursor.fetchall()
            
            print(f"    Oracle PRE_LOG_TRX usa:")
            for row in pre_log_usage:
                print(f"      WALLET: {row[1]}, VECES: {row[2]}")
            
            # Ver todas las wallets disponibles para este usuario
            cursor.execute("""
                SELECT 
                    USER_ID,
                    WALLET_NUMBER,
                    STATUS,
                    MODIFIED_ON,
                    ROW_NUMBER() OVER (PARTITION BY USER_ID ORDER BY MODIFIED_ON DESC) as RN_DESC,
                    ROW_NUMBER() OVER (PARTITION BY USER_ID ORDER BY MODIFIED_ON ASC) as RN_ASC
                FROM PDP_PROD10_MAINDBBUS.MTX_WALLET
                WHERE USER_ID = :user_id
                ORDER BY MODIFIED_ON DESC
            """, {'user_id': sample_user})
            
            wallet_options = cursor.fetchall()
            
            print(f"    Wallets disponibles:")
            for row in wallet_options:
                status_desc = "ACTIVA" if row[2] == 'Y' else "INACTIVA"
                print(f"      WALLET: {row[1]}, STATUS: {row[2]} ({status_desc})")
                print(f"      MODIFIED: {row[3]}, RN_DESC: {row[4]}, RN_ASC: {row[5]}")
            
            # Determinar el patrón
            oracle_wallet = pre_log_usage[0][1] if pre_log_usage else None
            
            print(f"\n    🔍 ANÁLISIS DEL PATRÓN:")
            print(f"      Oracle usa: {oracle_wallet}")
            
            # Verificar si coincide con algún patrón
            patterns = []
            for row in wallet_options:
                if row[1] == oracle_wallet:
                    patterns.append(f"STATUS={row[2]}, RN_DESC={row[4]}, RN_ASC={row[5]}")
            
            if patterns:
                print(f"      Patrón identificado: {patterns[0]}")
        
        print("\n3️⃣ ANÁLISIS DE USER_ACCOUNT_HISTORY PATTERN:")
        print("-" * 60)
        
        # Buscar si hay patrón en USER_ACCOUNT_HISTORY
        cursor.execute("""
            SELECT 
                H.USER_ID,
                H.ACCOUNT_ID,
                H.ATTR8_OLD,
                P."From_AccountID_Mobiquity",
                CASE 
                    WHEN H.ACCOUNT_ID = P."From_AccountID_Mobiquity" THEN 'DIRECT_MATCH'
                    WHEN H.ATTR8_OLD = P."From_AccountID_Mobiquity" THEN 'ATTR8_MATCH'
                    ELSE 'NO_MATCH'
                END as MATCH_TYPE
            FROM USER_ACCOUNT_HISTORY H
            INNER JOIN (
                SELECT DISTINCT "FromID_Mobiquity", "From_AccountID_Mobiquity"
                FROM USR_DATALAKE.PRE_LOG_TRX
                WHERE ROWNUM <= 20
            ) P ON H.USER_ID = P."FromID_Mobiquity"
            WHERE H.ACCOUNT_ID = P."From_AccountID_Mobiquity" 
               OR H.ATTR8_OLD = P."From_AccountID_Mobiquity"
        """)
        
        history_patterns = cursor.fetchall()
        
        print(f"  Matches en USER_ACCOUNT_HISTORY: {len(history_patterns)}")
        for i, row in enumerate(history_patterns[:5]):
            print(f"    [{i+1}] USER: {row[0]}, ACCOUNT: {row[1]}, ATTR8: {row[2]}")
            print(f"        PRE_LOG: {row[3]}, MATCH: {row[4]}")
        
        print("\n4️⃣ ANÁLISIS DE LÓGICA SP_PRE_LOG_USR:")
        print("-" * 60)
        
        # Verificar si la diferencia está en cómo se construye USER_DATA_TRX
        print("  Verificando construcción de USER_DATA_TRX...")
        
        # Simular la lógica de SP_PRE_LOG_USR para el usuario 945661
        cursor.execute("""
            SELECT 
                UP.USER_ID,
                UP.ATTR8,
                MW.WALLET_NUMBER,
                MW.STATUS,
                MW.MODIFIED_ON,
                CASE 
                    WHEN UP.ATTR8 IS NOT NULL THEN UP.ATTR8
                    WHEN LENGTH(REPLACE(MW.WALLET_NUMBER,'UA.',''))>15 
                        THEN SUBSTR(REPLACE(MW.WALLET_NUMBER,'UA.',''),-15) 
                    ELSE REPLACE(MW.WALLET_NUMBER,'UA.','')
                END AS COMPUTED_WALLET,
                ROW_NUMBER() OVER (PARTITION BY MW.USER_ID ORDER BY MW.MODIFIED_ON DESC) as RN
            FROM PDP_PROD10_MAINDB.USER_PROFILE UP
            LEFT JOIN PDP_PROD10_MAINDBBUS.MTX_WALLET MW ON UP.USER_ID = MW.USER_ID
            WHERE UP.USER_ID = '945661'
            ORDER BY MW.MODIFIED_ON DESC
        """)
        
        sp_logic_results = cursor.fetchall()
        
        if sp_logic_results:
            print(f"    Lógica SP_PRE_LOG_USR para USER_ID 945661:")
            for i, row in enumerate(sp_logic_results):
                print(f"      [{i+1}] ATTR8: {row[1]}, WALLET: {row[2]}, STATUS: {row[3]}")
                print(f"          COMPUTED: {row[5]}, RN: {row[6]}")
                
                if row[6] == 1:  # Primera wallet (más reciente)
                    expected_user_data = row[5]
                    print(f"      🎯 USER_DATA_TRX debería usar: {expected_user_data}")
        
        # Verificar qué tiene realmente USER_DATA_TRX
        cursor.execute("""
            SELECT WALLET_NUMBER
            FROM USR_DATALAKE.USER_DATA_TRX
            WHERE O_USER_ID = '945661'
        """)
        
        actual_user_data = cursor.fetchone()
        if actual_user_data:
            print(f"      ✅ USER_DATA_TRX actual: {actual_user_data[0]}")
            
            if str(actual_user_data[0]) == str(expected_user_data):
                print("      ✅ USER_DATA_TRX coincide con lógica SP_PRE_LOG_USR")
            else:
                print("      ❌ USER_DATA_TRX NO coincide con lógica SP_PRE_LOG_USR")
        
        cursor.close()
        connection.close()
        
        print("\n5️⃣ CONCLUSIONES:")
        print("-" * 60)
        print("1. Oracle tiene lógica oculta para seleccionar wallets")
        print("2. No siempre usa la wallet más reciente o activa")
        print("3. Puede haber lógica en USER_ACCOUNT_HISTORY")
        print("4. Necesitamos identificar el patrón general, no casos específicos")
        
    except Exception as e:
        print(f"❌ Error en investigación: {e}")
        import traceback
        traceback.print_exc()

def main():
    print("🚀 INVESTIGACIÓN DE LÓGICA GENERAL ORACLE")
    print("=" * 80)
    print("Buscando patrones generales, no casos específicos")
    print()
    
    investigar_logica_general()
    
    print("\n🏁 INVESTIGACIÓN COMPLETADA")

if __name__ == "__main__":
    main()
