#!/usr/bin/env python3
"""
Script para investigar ATTR8 en USER_PROFILE - LA VERDADERA CLAVE DEL PROBLEMA
En SP_PRE_LOG_USR líneas 34-37 Oracle prioriza UP.ATTR8 sobre MW.WALLET_NUMBER
"""

import oracledb
import duckdb
import boto3
import sys

def investigar_attr8_oracle():
    """Investiga ATTR8 en USER_PROFILE Oracle"""
    print("🔍 INVESTIGANDO ATTR8 EN USER_PROFILE ORACLE")
    print("=" * 60)
    
    try:
        connection = oracledb.connect(
            user='usr_datalake',
            password='U2024b1mD4t4l4k5',
            dsn='*************:1521/MMONEY'
        )
        
        cursor = connection.cursor()
        user_id = '945661'
        
        print("1️⃣ DATOS DE USER_PROFILE:")
        print("-" * 40)
        
        # Buscar ATTR8 en USER_PROFILE
        cursor.execute("""
            SELECT 
                USER_ID,
                ATTR8,
                ATTR7,
                MSISDN,
                STATUS,
                CREATED_ON,
                M<PERSON><PERSON>IED_ON
            FROM PDP_PROD10_MAINDB.USER_PROFILE
            WHERE USER_ID = :user_id
        """, {'user_id': user_id})
        
        user_profile = cursor.fetchall()
        if user_profile:
            row = user_profile[0]
            print(f"  USER_ID: {row[0]}")
            print(f"  ATTR8: {row[1]} ⭐ ESTE ES EL VALOR CLAVE")
            print(f"  ATTR7: {row[2]}")
            print(f"  MSISDN: {row[3]}")
            print(f"  STATUS: {row[4]}")
            print(f"  CREATED: {row[5]}")
            print(f"  MODIFIED: {row[6]}")
            
            attr8_value = row[1]
        else:
            print("  ❌ No encontrado en USER_PROFILE")
            attr8_value = None
        
        print("\n2️⃣ DATOS DE MTX_WALLET:")
        print("-" * 40)
        
        # Buscar WALLET_NUMBER en MTX_WALLET
        cursor.execute("""
            SELECT 
                USER_ID,
                WALLET_NUMBER,
                STATUS,
                MODIFIED_ON,
                ROW_NUMBER() OVER (PARTITION BY USER_ID ORDER BY MODIFIED_ON DESC) AS ORDEN
            FROM PDP_PROD10_MAINDBBUS.MTX_WALLET
            WHERE USER_ID = :user_id
            ORDER BY MODIFIED_ON DESC
        """, {'user_id': user_id})
        
        mtx_wallet = cursor.fetchall()
        if mtx_wallet:
            print("  Wallets encontradas:")
            for i, row in enumerate(mtx_wallet[:3]):
                orden_marker = " ⭐ ORDEN=1 (USADA)" if row[4] == 1 else ""
                print(f"    [{i+1}] WALLET_NUMBER: {row[1]}, STATUS: {row[2]}, ORDEN: {row[4]}{orden_marker}")
                print(f"        MODIFIED: {row[3]}")
            
            wallet_number = mtx_wallet[0][1]  # Primera wallet (ORDEN=1)
        else:
            print("  ❌ No encontrado en MTX_WALLET")
            wallet_number = None
        
        print("\n3️⃣ LÓGICA DE SP_PRE_LOG_USR:")
        print("-" * 40)
        print("Oracle usa esta lógica para WALLET_NUMBER:")
        print("  CASE")
        print("    WHEN UP.ATTR8 IS NOT NULL THEN UP.ATTR8")
        print("    WHEN LENGTH(REPLACE(MW.WALLET_NUMBER,'UA.',''))>15 THEN SUBSTR(REPLACE(MW.WALLET_NUMBER,'UA.',''),-15)")
        print("    ELSE REPLACE(MW.WALLET_NUMBER,'UA.','')")
        print("  END AS WALLET_NUMBER")
        print()
        
        # Simular la lógica
        if attr8_value is not None:
            final_wallet = attr8_value
            logic_used = "ATTR8"
        elif wallet_number and len(wallet_number.replace('UA.', '')) > 15:
            final_wallet = wallet_number.replace('UA.', '')[-15:]
            logic_used = "SUBSTR(WALLET_NUMBER, -15)"
        elif wallet_number:
            final_wallet = wallet_number.replace('UA.', '')
            logic_used = "REPLACE(WALLET_NUMBER, 'UA.', '')"
        else:
            final_wallet = None
            logic_used = "NULL"
        
        print(f"🎯 RESULTADO DE LA LÓGICA:")
        print(f"  ATTR8: {attr8_value}")
        print(f"  WALLET_NUMBER: {wallet_number}")
        print(f"  LÓGICA USADA: {logic_used}")
        print(f"  VALOR FINAL: {final_wallet}")
        
        print("\n4️⃣ VERIFICACIÓN CON USER_DATA_TRX:")
        print("-" * 40)
        
        # Verificar qué tiene USER_DATA_TRX
        cursor.execute("""
            SELECT 
                USER_ID,
                O_USER_ID,
                WALLET_NUMBER
            FROM USR_DATALAKE.USER_DATA_TRX
            WHERE O_USER_ID = :user_id
        """, {'user_id': user_id})
        
        user_data_trx = cursor.fetchall()
        if user_data_trx:
            row = user_data_trx[0]
            print(f"  USER_DATA_TRX.WALLET_NUMBER: {row[2]}")
            
            if str(row[2]) == str(final_wallet):
                print("  ✅ USER_DATA_TRX coincide con la lógica de SP_PRE_LOG_USR")
            else:
                print("  ❌ USER_DATA_TRX NO coincide con la lógica de SP_PRE_LOG_USR")
                print(f"     Esperado: {final_wallet}")
                print(f"     Actual: {row[2]}")
        else:
            print("  ❌ No encontrado en USER_DATA_TRX")
        
        cursor.close()
        connection.close()
        
        return attr8_value, wallet_number, final_wallet, logic_used
        
    except Exception as e:
        print(f"❌ Error investigando Oracle: {e}")
        return None, None, None, None

def investigar_attr8_s3():
    """Investiga ATTR8 en USER_PROFILE S3"""
    print("\n🔍 INVESTIGANDO ATTR8 EN USER_PROFILE S3")
    print("=" * 60)
    
    try:
        # Configurar DuckDB con S3
        conn = duckdb.connect()
        
        session = boto3.Session()
        credentials = session.get_credentials().get_frozen_credentials()
        
        conn.sql("INSTALL httpfs;")
        conn.sql("LOAD httpfs;")
        conn.sql("SET s3_region='us-east-1';")
        conn.sql("SET s3_use_ssl=true;")
        conn.sql("SET s3_url_style='path';")
        conn.sql(f"SET s3_access_key_id='{credentials.access_key}';")
        conn.sql(f"SET s3_secret_access_key='{credentials.secret_key}';")
        if credentials.token:
            conn.sql(f"SET s3_session_token='{credentials.token}';")
        
        s3_bucket_silver = "prd-datalake-silver-zone-637423440311"
        s3_bucket_golden = "prd-datalake-golden-zone-637423440311"
        
        user_profile_path = f's3://{s3_bucket_silver}/PDP_PROD10_MAINDB/USER_PROFILE_ORA/consolidado_puro.parquet'
        mtx_wallet_path = f's3://{s3_bucket_silver}/PDP_PROD10_MAINDBBUS/MTX_WALLET_ORA/consolidado_puro.parquet'
        user_data_trx_path = f's3://{s3_bucket_golden}/LOGS_USUARIOS/USER_DATA_TRX.parquet'
        
        user_id = '945661'
        
        print("1️⃣ DATOS DE USER_PROFILE S3:")
        print("-" * 40)
        
        try:
            result = conn.execute(f"""
                SELECT 
                    USER_ID,
                    ATTR8,
                    ATTR7,
                    MSISDN,
                    STATUS
                FROM read_parquet('{user_profile_path}')
                WHERE USER_ID = '{user_id}'
            """).fetchall()
            
            if result:
                row = result[0]
                print(f"  USER_ID: {row[0]}")
                print(f"  ATTR8: {row[1]} ⭐ ESTE ES EL VALOR CLAVE")
                print(f"  ATTR7: {row[2]}")
                print(f"  MSISDN: {row[3]}")
                print(f"  STATUS: {row[4]}")
                
                s3_attr8 = row[1]
            else:
                print("  ❌ No encontrado en USER_PROFILE S3")
                s3_attr8 = None
        except Exception as e:
            print(f"  ❌ Error consultando USER_PROFILE S3: {e}")
            s3_attr8 = None
        
        print("\n2️⃣ DATOS DE MTX_WALLET S3:")
        print("-" * 40)
        
        try:
            result = conn.execute(f"""
                SELECT 
                    USER_ID,
                    WALLET_NUMBER,
                    STATUS,
                    MODIFIED_ON
                FROM read_parquet('{mtx_wallet_path}')
                WHERE USER_ID = '{user_id}'
                ORDER BY MODIFIED_ON DESC
                LIMIT 3
            """).fetchall()
            
            if result:
                print("  Wallets encontradas:")
                for i, row in enumerate(result):
                    marker = " ⭐ MÁS RECIENTE" if i == 0 else ""
                    print(f"    [{i+1}] WALLET_NUMBER: {row[1]}, STATUS: {row[2]}{marker}")
                
                s3_wallet = result[0][1]
            else:
                print("  ❌ No encontrado en MTX_WALLET S3")
                s3_wallet = None
        except Exception as e:
            print(f"  ❌ Error consultando MTX_WALLET S3: {e}")
            s3_wallet = None
        
        print("\n3️⃣ DATOS DE USER_DATA_TRX S3:")
        print("-" * 40)
        
        try:
            result = conn.execute(f"""
                SELECT 
                    USER_ID,
                    O_USER_ID,
                    WALLET_NUMBER
                FROM read_parquet('{user_data_trx_path}')
                WHERE O_USER_ID = '{user_id}'
            """).fetchall()
            
            if result:
                row = result[0]
                print(f"  USER_DATA_TRX.WALLET_NUMBER: {row[2]}")
                s3_user_data_wallet = row[2]
            else:
                print("  ❌ No encontrado en USER_DATA_TRX S3")
                s3_user_data_wallet = None
        except Exception as e:
            print(f"  ❌ Error consultando USER_DATA_TRX S3: {e}")
            s3_user_data_wallet = None
        
        conn.close()
        
        return s3_attr8, s3_wallet, s3_user_data_wallet
        
    except Exception as e:
        print(f"❌ Error investigando S3: {e}")
        return None, None, None

def main():
    print("🚀 INVESTIGACIÓN ATTR8 - LA VERDADERA CLAVE DEL PROBLEMA")
    print("=" * 80)
    print("Analizando por qué Oracle prioriza ATTR8 sobre WALLET_NUMBER")
    print()
    
    # Investigar Oracle
    oracle_attr8, oracle_wallet, oracle_final, oracle_logic = investigar_attr8_oracle()
    
    # Investigar S3
    s3_attr8, s3_wallet, s3_user_data_wallet = investigar_attr8_s3()
    
    print("\n🎯 COMPARACIÓN FINAL:")
    print("=" * 60)
    print(f"Oracle ATTR8: {oracle_attr8}")
    print(f"Oracle WALLET_NUMBER: {oracle_wallet}")
    print(f"Oracle LÓGICA USADA: {oracle_logic}")
    print(f"Oracle RESULTADO FINAL: {oracle_final}")
    print()
    print(f"S3 ATTR8: {s3_attr8}")
    print(f"S3 WALLET_NUMBER: {s3_wallet}")
    print(f"S3 USER_DATA_TRX.WALLET_NUMBER: {s3_user_data_wallet}")
    
    print("\n💡 CONCLUSIÓN:")
    print("=" * 60)
    print("1. Oracle usa ATTR8 de USER_PROFILE como prioridad en SP_PRE_LOG_USR")
    print("2. Tu implementación S3 NO incluye esta lógica de ATTR8")
    print("3. Necesitas replicar la lógica exacta de SP_PRE_LOG_USR en tu código")
    print("4. La diferencia está en cómo se construye USER_DATA_TRX")

if __name__ == "__main__":
    main()
