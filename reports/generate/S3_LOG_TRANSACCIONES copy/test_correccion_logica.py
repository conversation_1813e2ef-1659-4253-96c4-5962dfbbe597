#!/usr/bin/env python3
"""
Script para probar la corrección de lógica
Verifica que ahora From_AccountID_Mobiquity coincida con Oracle
"""

import sys
import os
sys.path.append('/home/<USER>/REPORTE_2025/reports/generate/S3_LOG_TRANSACCIONES')

from pipeline_log_transacciones_duckdb import LogTransaccionesPipeline
import oracledb

def test_correccion():
    """Prueba la corrección de lógica"""
    print("🧪 PROBANDO CORRECCIÓN DE LÓGICA")
    print("=" * 60)
    
    fecha = '2025-06-15'
    transfer_id = '***************'
    
    try:
        # Ejecutar solo SP_PRE_LOG_TRX con la nueva lógica
        pipeline = LogTransaccionesPipeline()
        
        print("1️⃣ Ejecutando SP_PRE_LOG_TRX con lógica corregida...")
        date_folder = fecha.replace('-', '')
        pre_log_path = pipeline.process_sp_pre_log_trx(fecha, date_folder)
        
        print(f"✅ SP_PRE_LOG_TRX completado: {pre_log_path}")
        
        # Consultar el resultado en S3
        print("\n2️⃣ Consultando resultado en S3...")
        result = pipeline.conn.execute(f"""
            SELECT 
                "TransferID",
                "FromID_Mobiquity",
                "From_AccountID_Mobiquity",
                "TransferDate"
            FROM read_parquet('{pre_log_path}')
            WHERE "TransferID" = '{transfer_id}'
        """).fetchall()
        
        if result:
            row = result[0]
            s3_transfer_id = row[0]
            s3_from_id = row[1]
            s3_account_id = row[2]
            s3_date = row[3]
            
            print(f"  TransferID: {s3_transfer_id}")
            print(f"  FromID_Mobiquity: {s3_from_id}")
            print(f"  From_AccountID_Mobiquity: {s3_account_id}")
            print(f"  TransferDate: {s3_date}")
        else:
            print("  ❌ No se encontró el TransferID en S3")
            return
        
        # Consultar Oracle para comparar
        print("\n3️⃣ Consultando Oracle para comparar...")
        connection = oracledb.connect(
            user='usr_datalake',
            password='U2024b1mD4t4l4k5',
            dsn='10.240.131.10:1521/MMONEY'
        )
        
        cursor = connection.cursor()
        cursor.execute("""
            SELECT 
                "TransferID",
                "FromID_Mobiquity",
                "From_AccountID_Mobiquity",
                "TransferDate"
            FROM USR_DATALAKE.PRE_LOG_TRX 
            WHERE "TransferID" = :transfer_id
        """, {'transfer_id': transfer_id})
        
        oracle_result = cursor.fetchall()
        if oracle_result:
            oracle_row = oracle_result[0]
            oracle_transfer_id = oracle_row[0]
            oracle_from_id = oracle_row[1]
            oracle_account_id = oracle_row[2]
            oracle_date = oracle_row[3]
            
            print(f"  TransferID: {oracle_transfer_id}")
            print(f"  FromID_Mobiquity: {oracle_from_id}")
            print(f"  From_AccountID_Mobiquity: {oracle_account_id}")
            print(f"  TransferDate: {oracle_date}")
        else:
            print("  ❌ No se encontró el TransferID en Oracle")
            return
        
        cursor.close()
        connection.close()
        
        # Comparar resultados
        print("\n4️⃣ COMPARACIÓN FINAL:")
        print("-" * 40)
        print(f"S3 From_AccountID_Mobiquity: {s3_account_id}")
        print(f"Oracle From_AccountID_Mobiquity: {oracle_account_id}")
        
        if str(s3_account_id) == str(oracle_account_id):
            print("✅ ¡ÉXITO! Los valores ahora coinciden")
            print("🎉 La corrección de lógica funcionó correctamente")
        else:
            print("❌ Los valores aún difieren")
            print("🔧 Se necesita más investigación")
        
        # Verificar que sea el valor esperado de USER_DATA_TRX
        expected_value = '*************'
        if str(s3_account_id) == expected_value:
            print(f"✅ S3 usa correctamente USER_DATA_TRX.ACCOUNT_ID: {expected_value}")
        else:
            print(f"❌ S3 no usa USER_DATA_TRX.ACCOUNT_ID esperado: {expected_value}")
        
    except Exception as e:
        print(f"❌ Error en la prueba: {e}")
        import traceback
        traceback.print_exc()

def main():
    print("🚀 PRUEBA DE CORRECCIÓN DE LÓGICA")
    print("=" * 80)
    print("Verificando que la lógica ahora coincida con Oracle")
    print()
    
    test_correccion()
    
    print("\n🏁 PRUEBA COMPLETADA")

if __name__ == "__main__":
    main()
