#!/usr/bin/env python3
"""
Investigar las diferencias restantes en AccountID_Mobiquity para lograr 100% perfección
"""

import oracledb
import duckdb
import boto3
import sys

def investigar_diferencias_accountid_restantes():
    """Investiga las diferencias restantes en AccountID_Mobiquity"""
    print("🔍 INVESTIGACIÓN: Diferencias Restantes AccountID_Mobiquity")
    print("=" * 80)
    print("OBJETIVO: 100% PERFECCIÓN - DOS GOTAS DE AGUA")
    
    parquet_path = '/home/<USER>/REPORTE_2025/reports/generate/S3_LOG_TRANSACCIONES/TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet'
    fecha = '2025-06-15'
    
    try:
        # 1. IDENTIFICAR DIFERENCIAS EXACTAS
        print("1️⃣ IDENTIFICANDO DIFERENCIAS EXACTAS:")
        print("-" * 60)
        
        connection = oracledb.connect(
            user='usr_datalake',
            password='U2024b1mD4t4l4k5',
            dsn='10.240.131.10:1521/MMONEY'
        )
        
        cursor = connection.cursor()
        
        # Configurar DuckDB con S3
        conn = duckdb.connect()
        
        session = boto3.Session()
        credentials = session.get_credentials().get_frozen_credentials()
        
        conn.sql("INSTALL httpfs;")
        conn.sql("LOAD httpfs;")
        conn.sql("SET s3_region='us-east-1';")
        conn.sql("SET s3_use_ssl=true;")
        conn.sql("SET s3_url_style='path';")
        conn.sql(f"SET s3_access_key_id='{credentials.access_key}';")
        conn.sql(f"SET s3_secret_access_key='{credentials.secret_key}';")
        if credentials.token:
            conn.sql(f"SET s3_session_token='{credentials.token}';")
        
        print("📋 DIFERENCIAS EN To_AccountID_Mobiquity:")
        
        # Obtener estadísticas Oracle To_AccountID
        cursor.execute("""
            SELECT 
                "To_AccountID_Mobiquity",
                "ToID_Mobiquity",
                COUNT(*) as CASOS
            FROM USR_DATALAKE.PRE_LOG_TRX 
            WHERE TRUNC("TransferDate") = TO_DATE(:fecha, 'YYYY-MM-DD')
            GROUP BY "To_AccountID_Mobiquity", "ToID_Mobiquity"
            ORDER BY COUNT(*) DESC
        """, {'fecha': fecha})
        
        oracle_to_stats = cursor.fetchall()
        oracle_to_map = {(str(row[0]), str(row[1])): row[2] for row in oracle_to_stats}
        
        # Obtener estadísticas S3 To_AccountID
        s3_to_stats = conn.execute(f"""
            SELECT 
                "To_AccountID_Mobiquity",
                "ToID_Mobiquity",
                COUNT(*) as CASOS
            FROM read_parquet('{parquet_path}')
            GROUP BY "To_AccountID_Mobiquity", "ToID_Mobiquity"
            ORDER BY COUNT(*) DESC
        """).fetchall()
        
        s3_to_map = {(str(row[0]), str(row[1])): row[2] for row in s3_to_stats}
        
        # Encontrar diferencias To_AccountID
        print(f"{'Oracle_AccountID':<25} {'S3_AccountID':<25} {'ToID':<15} {'Oracle_Casos':<12} {'S3_Casos':<10}")
        print("-" * 100)
        
        diferencias_to = []
        all_combinations = set(oracle_to_map.keys()) | set(s3_to_map.keys())
        
        for combination in all_combinations:
            oracle_count = oracle_to_map.get(combination, 0)
            s3_count = s3_to_map.get(combination, 0)
            
            if oracle_count != s3_count:
                diferencias_to.append((combination, oracle_count, s3_count))
        
        # Mostrar top 20 diferencias To_AccountID
        for i, (combination, oracle_count, s3_count) in enumerate(sorted(diferencias_to, key=lambda x: abs(x[1] - x[2]), reverse=True)[:20]):
            account_id, to_id = combination
            
            # Buscar la contraparte
            contraparte_oracle = None
            contraparte_s3 = None
            
            for other_combination in all_combinations:
                other_account, other_to_id = other_combination
                if other_to_id == to_id and other_account != account_id:
                    if oracle_to_map.get(other_combination, 0) > 0 and oracle_count == 0:
                        contraparte_oracle = other_account
                    if s3_to_map.get(other_combination, 0) > 0 and s3_count == 0:
                        contraparte_s3 = other_account
            
            oracle_display = contraparte_oracle if oracle_count == 0 else account_id
            s3_display = contraparte_s3 if s3_count == 0 else account_id
            
            print(f"{oracle_display:<25} {s3_display:<25} {to_id:<15} {oracle_count:<12} {s3_count:<10}")
        
        print(f"\n📋 DIFERENCIAS EN From_AccountID_Mobiquity:")
        
        # Obtener estadísticas Oracle From_AccountID
        cursor.execute("""
            SELECT 
                "From_AccountID_Mobiquity",
                "FromID_Mobiquity",
                COUNT(*) as CASOS
            FROM USR_DATALAKE.PRE_LOG_TRX 
            WHERE TRUNC("TransferDate") = TO_DATE(:fecha, 'YYYY-MM-DD')
            GROUP BY "From_AccountID_Mobiquity", "FromID_Mobiquity"
            ORDER BY COUNT(*) DESC
        """, {'fecha': fecha})
        
        oracle_from_stats = cursor.fetchall()
        oracle_from_map = {(str(row[0]), str(row[1])): row[2] for row in oracle_from_stats}
        
        # Obtener estadísticas S3 From_AccountID
        s3_from_stats = conn.execute(f"""
            SELECT 
                "From_AccountID_Mobiquity",
                "FromID_Mobiquity",
                COUNT(*) as CASOS
            FROM read_parquet('{parquet_path}')
            GROUP BY "From_AccountID_Mobiquity", "FromID_Mobiquity"
            ORDER BY COUNT(*) DESC
        """).fetchall()
        
        s3_from_map = {(str(row[0]), str(row[1])): row[2] for row in s3_from_stats}
        
        # Encontrar diferencias From_AccountID
        print(f"{'Oracle_AccountID':<25} {'S3_AccountID':<25} {'FromID':<15} {'Oracle_Casos':<12} {'S3_Casos':<10}")
        print("-" * 100)
        
        diferencias_from = []
        all_from_combinations = set(oracle_from_map.keys()) | set(s3_from_map.keys())
        
        for combination in all_from_combinations:
            oracle_count = oracle_from_map.get(combination, 0)
            s3_count = s3_from_map.get(combination, 0)
            
            if oracle_count != s3_count:
                diferencias_from.append((combination, oracle_count, s3_count))
        
        # Mostrar top 20 diferencias From_AccountID
        for i, (combination, oracle_count, s3_count) in enumerate(sorted(diferencias_from, key=lambda x: abs(x[1] - x[2]), reverse=True)[:20]):
            account_id, from_id = combination
            
            # Buscar la contraparte
            contraparte_oracle = None
            contraparte_s3 = None
            
            for other_combination in all_from_combinations:
                other_account, other_from_id = other_combination
                if other_from_id == from_id and other_account != account_id:
                    if oracle_from_map.get(other_combination, 0) > 0 and oracle_count == 0:
                        contraparte_oracle = other_account
                    if s3_from_map.get(other_combination, 0) > 0 and s3_count == 0:
                        contraparte_s3 = other_account
            
            oracle_display = contraparte_oracle if oracle_count == 0 else account_id
            s3_display = contraparte_s3 if s3_count == 0 else account_id
            
            print(f"{oracle_display:<25} {s3_display:<25} {from_id:<15} {oracle_count:<12} {s3_count:<10}")
        
        # 2. ANÁLISIS DE PATRONES
        print(f"\n2️⃣ ANÁLISIS DE PATRONES:")
        print("-" * 60)
        
        # Analizar patrones en las diferencias
        usuarios_problematicos_to = set()
        usuarios_problematicos_from = set()
        
        for (account_id, user_id), oracle_count, s3_count in diferencias_to:
            usuarios_problematicos_to.add(user_id)
        
        for (account_id, user_id), oracle_count, s3_count in diferencias_from:
            usuarios_problematicos_from.add(user_id)
        
        print(f"  Usuarios problemáticos To_AccountID: {len(usuarios_problematicos_to)}")
        print(f"  Usuarios problemáticos From_AccountID: {len(usuarios_problematicos_from)}")
        
        # Verificar si son los mismos usuarios
        usuarios_comunes = usuarios_problematicos_to & usuarios_problematicos_from
        print(f"  Usuarios con problemas en ambos: {len(usuarios_comunes)}")
        
        # 3. INVESTIGAR USUARIOS ESPECÍFICOS
        print(f"\n3️⃣ INVESTIGAR USUARIOS ESPECÍFICOS:")
        print("-" * 60)
        
        # Tomar muestra de usuarios problemáticos
        usuarios_muestra = list(usuarios_problematicos_to)[:10]
        
        for user_id in usuarios_muestra:
            print(f"\n  📋 USER_ID: {user_id}")
            
            # Verificar en USER_DATA_TRX
            cursor.execute("""
                SELECT 
                    USER_ID,
                    O_USER_ID,
                    WALLET_NUMBER,
                    PROFILE_TRX
                FROM USR_DATALAKE.USER_DATA_TRX
                WHERE O_USER_ID = :user_id
            """, {'user_id': user_id})
            
            user_data = cursor.fetchone()
            if user_data:
                wallet_number = user_data[2]
                print(f"    USER_DATA_TRX WALLET: {wallet_number}")
                
                # Verificar en MTX_WALLET original
                cursor.execute("""
                    SELECT 
                        MW.USER_ID,
                        MW.WALLET_NUMBER,
                        MW.STATUS,
                        ROW_NUMBER() OVER (PARTITION BY MW.USER_ID ORDER BY LENGTH(MW.WALLET_NUMBER) DESC) as RN
                    FROM PDP_PROD10_MAINDBBUS.MTX_WALLET MW
                    WHERE MW.USER_ID = :user_id
                    ORDER BY LENGTH(MW.WALLET_NUMBER) DESC
                """, {'user_id': user_id})
                
                wallets = cursor.fetchall()
                print(f"    MTX_WALLET encontró {len(wallets)} wallets:")
                for row in wallets:
                    status_icon = "✅" if row[2] == 'Y' else "❌"
                    length_icon = "🔢" if len(str(row[1])) > 15 else "📱"
                    print(f"      {status_icon} {length_icon} {row[1]} (STATUS: {row[2]}, RN: {row[3]})")
                
                # Determinar cuál debería usar Oracle
                if wallets:
                    wallet_activo = None
                    for row in wallets:
                        if row[2] == 'Y':  # STATUS = 'Y'
                            wallet_activo = row[1]
                            break
                    
                    if not wallet_activo:
                        wallet_activo = wallets[0][1]  # El más largo
                    
                    print(f"    🎯 Oracle debería usar: {wallet_activo}")
                    print(f"    📊 S3 está usando: {wallet_number}")
                    print(f"    ¿Coincide? {'✅' if str(wallet_activo) == str(wallet_number) else '❌'}")
            else:
                print(f"    ❌ No encontrado en USER_DATA_TRX")
        
        cursor.close()
        connection.close()
        conn.close()
        
        print(f"\n4️⃣ CONCLUSIONES PARA PERFECCIÓN:")
        print("-" * 60)
        print("1. Identificar TODOS los usuarios que necesitan wallet activo")
        print("2. Expandir tabla WALLET_ACTIVOS con casos faltantes")
        print("3. Aplicar lógica sistemática para 100% coincidencia")
        print("4. NO ACEPTAR 99.x% - DEBE SER 100% PERFECTO")
        
        print(f"\n💡 ACCIÓN REQUERIDA:")
        print("Generar tabla WALLET_ACTIVOS completa con TODOS los casos")
        print("que Oracle maneja diferente para lograr 100% perfección")
        
    except Exception as e:
        print(f"❌ Error en investigación: {e}")
        import traceback
        traceback.print_exc()

def main():
    print("🚀 INVESTIGACIÓN DIFERENCIAS RESTANTES")
    print("=" * 80)
    print("OBJETIVO: 100% PERFECCIÓN ABSOLUTA")
    print()
    
    investigar_diferencias_accountid_restantes()
    
    print("\n🏁 INVESTIGACIÓN COMPLETADA")

if __name__ == "__main__":
    main()
