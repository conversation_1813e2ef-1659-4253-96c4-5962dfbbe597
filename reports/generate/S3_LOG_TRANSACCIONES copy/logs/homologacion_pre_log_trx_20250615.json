{"fecha": "2025-06-15", "timestamp": "2025-06-17T06:50:33.401443", "etapas": {"estructura": {"oracle_count": 42, "parquet_count": 42, "common_count": 42, "missing_count": 0, "extra_count": 0, "structure_match": true, "missing_columns": [], "extra_columns": [], "common_columns": ["To_Identifier", "From_Msisdn", "To_Workspace", "From_Identifier", "FromID_Mobiquity", "From_Workspace", "To_BankDomain", "ToID_Mobiquity", "From_AccountID", "Context", "TransferID_Mob", "To_Msisdn", "To_DocumentType", "ToID", "ExternalTransactionID", "Comment", "To_AccountID_Mobiquity", "From_Profile", "To_Profile", "From_DocumentType", "To_Grade", "From_BankDomain", "<PERSON><PERSON><PERSON><PERSON>", "FromID", "CurrencyCode", "Gateway_Code", "Source", "Remarks", "ReversalID", "TransferID", "TransferDate", "TransactionType", "To_LoginID", "TransferStatus", "Fee", "From_Grade", "CreatedBy", "To_AccountID", "From_LoginID", "From_AccountID_Mobiquity", "Amount", "ModifiedBy"]}, "conteos": {"oracle_count": 104116, "parquet_count": 104116, "difference": 0, "count_match": true}, "muestra": {"oracle_sample": [["*********", "***********", "BUSINESS", "********", "2386268"], ["**********", "***********", "SUBSCRIBER", "*********", "3212666"], ["*********", "***********", "BUSINESS", "********", "2929831"]], "parquet_sample": [["*********", "***********", "BUSINESS", "*********", "3390346"], [null, "***********", "SUBSCRIBER", null, "945661"], ["unique", "***********", "BUSINESS", "*********", "2338864"]], "samples_match": false}}, "homologacion_perfecta": false, "resumen": {"estructura_ok": true, "conteos_ok": true, "muestra_ok": false}}