#!/usr/bin/env python3
"""
Script de Homologación PRE_LOG_TRX
Compara el archivo Parquet generado vs tabla Oracle original
Objetivo: Lograr homologación 100% ('dos gotas de agua')
"""

import oracledb
import duckdb
import pandas as pd
import sys
from datetime import datetime
import json

class HomologacionPreLogTrx:
    def __init__(self):
        self.fecha = '2025-06-15'
        self.setup_connections()
        
    def setup_connections(self):
        """Configura conexiones a Oracle y DuckDB"""
        try:
            # Conexión Oracle
            print("🔌 Conectando a Oracle...")
            self.oracle_conn = oracledb.connect(
                user='usr_datalake',
                password='U2024b1mD4t4l4k5',
                dsn='*************:1521/MMONEY'
            )
            print("✅ Conexión Oracle establecida")
            
            # Conexión DuckDB
            print("🔌 Conectando a DuckDB...")
            self.duck_conn = duckdb.connect()
            self.duck_conn.sql("INSTALL httpfs;")
            self.duck_conn.sql("LOAD httpfs;")
            print("✅ Conexión DuckDB establecida")
            
        except Exception as e:
            print(f"❌ Error en conexiones: {e}")
            raise

    def get_oracle_structure(self):
        """Obtiene la estructura de PRE_LOG_TRX en Oracle"""
        print("\n📋 Obteniendo estructura Oracle PRE_LOG_TRX...")
        
        cursor = self.oracle_conn.cursor()
        cursor.execute("""
            SELECT COLUMN_NAME, DATA_TYPE, DATA_LENGTH, NULLABLE
            FROM ALL_TAB_COLUMNS
            WHERE TABLE_NAME = 'PRE_LOG_TRX'
            AND OWNER = 'USR_DATALAKE'
            ORDER BY COLUMN_ID
        """)
        
        oracle_columns = cursor.fetchall()
        cursor.close()
        
        print(f"📊 Oracle PRE_LOG_TRX tiene {len(oracle_columns)} columnas:")
        print("=" * 80)
        print(f"{'COLUMN_NAME':<30} {'DATA_TYPE':<20} {'LENGTH':<10} {'NULLABLE':<10}")
        print("-" * 80)
        for col in oracle_columns:
            print(f"{col[0]:<30} {col[1]:<20} {col[2]:<10} {col[3]:<10}")
        
        return oracle_columns

    def get_parquet_structure(self):
        """Obtiene la estructura del archivo Parquet generado"""
        print(f"\n📋 Obteniendo estructura Parquet...")
        
        parquet_path = f"TEMP_LOGS_TRANSACCIONES/20250615/PRE_LOG_TRX.parquet"
        
        try:
            result = self.duck_conn.execute(f"""
                DESCRIBE SELECT * FROM read_parquet('{parquet_path}') LIMIT 1
            """).fetchall()
            
            print(f"📊 Parquet PRE_LOG_TRX tiene {len(result)} columnas:")
            print("=" * 80)
            print(f"{'COLUMN_NAME':<30} {'DATA_TYPE':<20}")
            print("-" * 80)
            for col in result:
                print(f"{col[0]:<30} {col[1]:<20}")
            
            return result
            
        except Exception as e:
            print(f"❌ Error leyendo Parquet: {e}")
            raise

    def compare_structures(self, oracle_cols, parquet_cols):
        """Compara las estructuras de columnas"""
        print(f"\n🔍 COMPARACIÓN DE ESTRUCTURAS")
        print("=" * 80)
        
        oracle_names = [col[0] for col in oracle_cols]
        parquet_names = [col[0] for col in parquet_cols]
        
        # Columnas en Oracle pero no en Parquet
        missing_in_parquet = set(oracle_names) - set(parquet_names)
        if missing_in_parquet:
            print(f"❌ Columnas faltantes en Parquet ({len(missing_in_parquet)}):")
            for col in sorted(missing_in_parquet):
                print(f"   - {col}")
        
        # Columnas en Parquet pero no en Oracle
        extra_in_parquet = set(parquet_names) - set(oracle_names)
        if extra_in_parquet:
            print(f"⚠️  Columnas extra en Parquet ({len(extra_in_parquet)}):")
            for col in sorted(extra_in_parquet):
                print(f"   + {col}")
        
        # Columnas comunes
        common_cols = set(oracle_names) & set(parquet_names)
        print(f"✅ Columnas comunes: {len(common_cols)}")
        
        # Resumen
        print(f"\n📊 RESUMEN ESTRUCTURAL:")
        print(f"   Oracle:   {len(oracle_names)} columnas")
        print(f"   Parquet:  {len(parquet_names)} columnas")
        print(f"   Comunes:  {len(common_cols)} columnas")
        print(f"   Faltantes: {len(missing_in_parquet)} columnas")
        print(f"   Extras:    {len(extra_in_parquet)} columnas")
        
        estructura_match = len(missing_in_parquet) == 0 and len(extra_in_parquet) == 0
        print(f"   Estructura: {'✅ PERFECTA' if estructura_match else '❌ DIFERENTE'}")
        
        return {
            'oracle_count': len(oracle_names),
            'parquet_count': len(parquet_names),
            'common_count': len(common_cols),
            'missing_count': len(missing_in_parquet),
            'extra_count': len(extra_in_parquet),
            'structure_match': estructura_match,
            'missing_columns': list(missing_in_parquet),
            'extra_columns': list(extra_in_parquet),
            'common_columns': list(common_cols)
        }

    def get_oracle_data_count(self):
        """Obtiene el conteo de registros en Oracle"""
        print(f"\n📊 Contando registros Oracle para {self.fecha}...")
        
        cursor = self.oracle_conn.cursor()
        cursor.execute(f"""
            SELECT COUNT(*) 
            FROM USR_DATALAKE.PRE_LOG_TRX
            WHERE TRUNC("TransferDate") = TO_DATE('{self.fecha}', 'YYYY-MM-DD')
        """)
        
        oracle_count = cursor.fetchone()[0]
        cursor.close()
        
        print(f"📈 Oracle PRE_LOG_TRX: {oracle_count:,} registros")
        return oracle_count

    def get_parquet_data_count(self):
        """Obtiene el conteo de registros en Parquet"""
        print(f"\n📊 Contando registros Parquet para {self.fecha}...")
        
        parquet_path = f"TEMP_LOGS_TRANSACCIONES/20250615/PRE_LOG_TRX.parquet"
        
        result = self.duck_conn.execute(f"""
            SELECT COUNT(*)
            FROM read_parquet('{parquet_path}')
            WHERE CAST("TransferDate" AS DATE) = CAST('{self.fecha}' AS DATE)
        """).fetchone()
        
        parquet_count = result[0]
        print(f"📈 Parquet PRE_LOG_TRX: {parquet_count:,} registros")
        return parquet_count

    def compare_data_counts(self, oracle_count, parquet_count):
        """Compara los conteos de datos"""
        print(f"\n🔍 COMPARACIÓN DE CONTEOS")
        print("=" * 80)
        print(f"Oracle:   {oracle_count:,} registros")
        print(f"Parquet:  {parquet_count:,} registros")
        
        if oracle_count == parquet_count:
            print("✅ CONTEOS PERFECTOS - Mismo número de registros")
            count_match = True
        else:
            diferencia = abs(oracle_count - parquet_count)
            porcentaje = (diferencia / max(oracle_count, parquet_count)) * 100
            print(f"❌ DIFERENCIA: {diferencia:,} registros ({porcentaje:.2f}%)")
            count_match = False
        
        return {
            'oracle_count': oracle_count,
            'parquet_count': parquet_count,
            'difference': abs(oracle_count - parquet_count),
            'count_match': count_match
        }

    def sample_data_comparison(self, common_columns):
        """Compara una muestra de datos entre Oracle y Parquet"""
        print(f"\n🔍 COMPARACIÓN DE MUESTRA DE DATOS")
        print("=" * 80)
        
        if not common_columns:
            print("❌ No hay columnas comunes para comparar")
            return {}
        
        # Tomar las primeras 5 columnas comunes para la muestra
        sample_cols = common_columns[:5]
        cols_str = ', '.join([f'"{col}"' for col in sample_cols])
        
        print(f"📋 Comparando columnas: {', '.join(sample_cols)}")
        
        try:
            # Datos Oracle (muestra)
            cursor = self.oracle_conn.cursor()
            cursor.execute(f"""
                SELECT {cols_str}
                FROM USR_DATALAKE.PRE_LOG_TRX
                WHERE TRUNC("TransferDate") = TO_DATE('{self.fecha}', 'YYYY-MM-DD')
                AND ROWNUM <= 3
                ORDER BY "TransferID"
            """)
            oracle_sample = cursor.fetchall()
            cursor.close()
            
            # Datos Parquet (muestra)
            parquet_path = f"TEMP_LOGS_TRANSACCIONES/20250615/PRE_LOG_TRX.parquet"
            parquet_sample = self.duck_conn.execute(f"""
                SELECT {cols_str}
                FROM read_parquet('{parquet_path}')
                WHERE CAST("TransferDate" AS DATE) = CAST('{self.fecha}' AS DATE)
                ORDER BY "TransferID"
                LIMIT 3
            """).fetchall()
            
            print(f"\n📊 MUESTRA ORACLE (3 registros):")
            for i, row in enumerate(oracle_sample, 1):
                print(f"  {i}. {row}")
            
            print(f"\n📊 MUESTRA PARQUET (3 registros):")
            for i, row in enumerate(parquet_sample, 1):
                print(f"  {i}. {row}")
            
            # Comparar muestras
            samples_match = oracle_sample == parquet_sample
            print(f"\n🎯 MUESTRA: {'✅ IDÉNTICA' if samples_match else '❌ DIFERENTE'}")
            
            return {
                'oracle_sample': oracle_sample,
                'parquet_sample': parquet_sample,
                'samples_match': samples_match
            }
            
        except Exception as e:
            print(f"❌ Error en comparación de muestra: {e}")
            return {}

    def run_homologacion(self):
        """Ejecuta la homologación completa"""
        print("🚀 INICIANDO HOMOLOGACIÓN PRE_LOG_TRX")
        print("=" * 80)
        print(f"📅 Fecha: {self.fecha}")
        print(f"🎯 Objetivo: Homologación 100% ('dos gotas de agua')")
        
        resultados = {
            'fecha': self.fecha,
            'timestamp': datetime.now().isoformat(),
            'etapas': {}
        }
        
        try:
            # 1. Comparar estructuras
            print(f"\n{'='*20} ETAPA 1: ESTRUCTURA {'='*20}")
            oracle_cols = self.get_oracle_structure()
            parquet_cols = self.get_parquet_structure()
            estructura_result = self.compare_structures(oracle_cols, parquet_cols)
            resultados['etapas']['estructura'] = estructura_result
            
            # 2. Comparar conteos
            print(f"\n{'='*20} ETAPA 2: CONTEOS {'='*20}")
            oracle_count = self.get_oracle_data_count()
            parquet_count = self.get_parquet_data_count()
            conteo_result = self.compare_data_counts(oracle_count, parquet_count)
            resultados['etapas']['conteos'] = conteo_result
            
            # 3. Comparar muestra de datos
            print(f"\n{'='*20} ETAPA 3: MUESTRA DATOS {'='*20}")
            muestra_result = self.sample_data_comparison(estructura_result['common_columns'])
            resultados['etapas']['muestra'] = muestra_result
            
            # 4. Resultado final
            print(f"\n{'='*20} RESULTADO FINAL {'='*20}")
            
            estructura_ok = estructura_result['structure_match']
            conteos_ok = conteo_result['count_match']
            muestra_ok = muestra_result.get('samples_match', False)
            
            homologacion_perfecta = estructura_ok and conteos_ok and muestra_ok
            
            print(f"📋 Estructura: {'✅ PERFECTA' if estructura_ok else '❌ DIFERENTE'}")
            print(f"📊 Conteos:    {'✅ PERFECTOS' if conteos_ok else '❌ DIFERENTES'}")
            print(f"🔍 Muestra:    {'✅ IDÉNTICA' if muestra_ok else '❌ DIFERENTE'}")
            print(f"\n🎯 HOMOLOGACIÓN: {'✅ 100% PERFECTA' if homologacion_perfecta else '❌ REQUIERE AJUSTES'}")
            
            if homologacion_perfecta:
                print("🎉 ¡FELICITACIONES! Homologación 'dos gotas de agua' lograda")
            else:
                print("🔧 Se requieren ajustes para lograr homologación perfecta")
            
            resultados['homologacion_perfecta'] = homologacion_perfecta
            resultados['resumen'] = {
                'estructura_ok': estructura_ok,
                'conteos_ok': conteos_ok,
                'muestra_ok': muestra_ok
            }
            
            # Guardar resultados
            with open(f'logs/homologacion_pre_log_trx_{self.fecha.replace("-", "")}.json', 'w') as f:
                json.dump(resultados, f, indent=2, ensure_ascii=False, default=str)
            
            return resultados
            
        except Exception as e:
            print(f"❌ Error en homologación: {e}")
            raise
        
        finally:
            # Cerrar conexiones
            if hasattr(self, 'oracle_conn'):
                self.oracle_conn.close()
            if hasattr(self, 'duck_conn'):
                self.duck_conn.close()

def main():
    """Función principal"""
    try:
        homologador = HomologacionPreLogTrx()
        resultados = homologador.run_homologacion()
        
        # Código de salida basado en resultado
        sys.exit(0 if resultados['homologacion_perfecta'] else 1)
        
    except Exception as e:
        print(f"❌ Error crítico: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
