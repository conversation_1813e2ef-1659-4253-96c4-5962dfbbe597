#!/usr/bin/env python3
"""
Homologación correcta de To_Profile usando TransferIDs que existen en ambos sistemas
"""

import oracledb
import duckdb
import boto3
import sys

def homologar_to_profile_correcto():
    """Homologa To_Profile usando TransferIDs comunes"""
    print("🔍 HOMOLOGACIÓN CORRECTA: To_Profile")
    print("=" * 80)
    
    parquet_path = '/home/<USER>/REPORTE_2025/reports/generate/S3_LOG_TRANSACCIONES/TEMP_LOGS_TRANSACCIONES/20250615/PRE_LOG_TRX.parquet'
    fecha = '2025-06-15'
    
    try:
        # 1. OBTENER TRANSFERIDS COMUNES
        print("1️⃣ OBTENIENDO TransferIDs COMUNES:")
        print("-" * 60)
        
        # Configurar DuckDB con S3
        conn = duckdb.connect()
        
        session = boto3.Session()
        credentials = session.get_credentials().get_frozen_credentials()
        
        conn.sql("INSTALL httpfs;")
        conn.sql("LOAD httpfs;")
        conn.sql("SET s3_region='us-east-1';")
        conn.sql("SET s3_use_ssl=true;")
        conn.sql("SET s3_url_style='path';")
        conn.sql(f"SET s3_access_key_id='{credentials.access_key}';")
        conn.sql(f"SET s3_secret_access_key='{credentials.secret_key}';")
        if credentials.token:
            conn.sql(f"SET s3_session_token='{credentials.token}';")
        
        # Obtener muestra de TransferIDs de S3
        s3_sample = conn.execute(f"""
            SELECT "TransferID"
            FROM read_parquet('{parquet_path}')
            ORDER BY "TransferID"
            LIMIT 20
        """).fetchall()
        
        transfer_ids = [row[0] for row in s3_sample]
        print(f"  TransferIDs de S3 para comparar: {len(transfer_ids)}")
        for tid in transfer_ids[:5]:
            print(f"    {tid}")
        print(f"    ... y {len(transfer_ids)-5} más")
        
        # 2. CONSULTAR ORACLE CON TRANSFERIDS COMUNES
        print(f"\n2️⃣ CONSULTANDO ORACLE CON TransferIDs COMUNES:")
        print("-" * 60)
        
        connection = oracledb.connect(
            user='usr_datalake',
            password='U2024b1mD4t4l4k5',
            dsn='10.240.131.10:1521/MMONEY'
        )
        
        cursor = connection.cursor()
        
        # Crear lista de TransferIDs para la consulta
        transfer_ids_str = "', '".join(transfer_ids)
        
        cursor.execute(f"""
            SELECT 
                "TransferID",
                "ToID_Mobiquity",
                "To_Profile",
                "To_BankDomain",
                "TransactionType"
            FROM USR_DATALAKE.PRE_LOG_TRX 
            WHERE "TransferID" IN ('{transfer_ids_str}')
            ORDER BY "TransferID"
        """)
        
        oracle_data = cursor.fetchall()
        
        print(f"✅ Oracle encontró {len(oracle_data)} registros coincidentes")
        print()
        print(f"{'TransferID':<20} {'ToID_Mobiquity':<25} {'To_Profile':<40}")
        print("-" * 90)
        
        oracle_to_profiles = {}
        for row in oracle_data:
            transfer_id = row[0]
            to_id = row[1]
            to_profile = row[2]
            to_bank_domain = row[3]
            transaction_type = row[4]
            
            oracle_to_profiles[transfer_id] = {
                'to_id': to_id,
                'to_profile': to_profile,
                'to_bank_domain': to_bank_domain,
                'transaction_type': transaction_type
            }
            
            print(f"{transfer_id:<20} {to_id:<25} {to_profile:<40}")
        
        cursor.close()
        connection.close()
        
        # 3. CONSULTAR S3 CON TRANSFERIDS COMUNES
        print(f"\n3️⃣ CONSULTANDO S3 CON TransferIDs COMUNES:")
        print("-" * 60)
        
        s3_data = conn.execute(f"""
            SELECT 
                "TransferID",
                "ToID_Mobiquity",
                "To_Profile",
                "To_BankDomain",
                "TransactionType"
            FROM read_parquet('{parquet_path}')
            WHERE "TransferID" IN ('{transfer_ids_str}')
            ORDER BY "TransferID"
        """).fetchall()
        
        print(f"✅ S3 encontró {len(s3_data)} registros coincidentes")
        print()
        print(f"{'TransferID':<20} {'ToID_Mobiquity':<25} {'To_Profile':<40}")
        print("-" * 90)
        
        s3_to_profiles = {}
        for row in s3_data:
            transfer_id = row[0]
            to_id = row[1]
            to_profile = row[2]
            to_bank_domain = row[3]
            transaction_type = row[4]
            
            s3_to_profiles[transfer_id] = {
                'to_id': to_id,
                'to_profile': to_profile,
                'to_bank_domain': to_bank_domain,
                'transaction_type': transaction_type
            }
            
            print(f"{transfer_id:<20} {to_id:<25} {to_profile:<40}")
        
        conn.close()
        
        # 4. COMPARACIÓN DETALLADA DE To_Profile
        print(f"\n4️⃣ COMPARACIÓN DETALLADA DE To_Profile:")
        print("-" * 60)
        
        coincidencias = 0
        diferencias = 0
        
        print(f"{'TransferID':<20} {'ORACLE':<40} {'S3':<40} {'ESTADO'}")
        print("-" * 110)
        
        for transfer_id in oracle_to_profiles.keys():
            if transfer_id in s3_to_profiles:
                oracle_profile = oracle_to_profiles[transfer_id]['to_profile']
                s3_profile = s3_to_profiles[transfer_id]['to_profile']
                
                # Normalizar valores para comparación
                oracle_str = str(oracle_profile) if oracle_profile is not None else 'NULL'
                s3_str = str(s3_profile) if s3_profile is not None else 'NULL'
                
                if oracle_str == s3_str:
                    coincidencias += 1
                    status = "✅ COINCIDE"
                else:
                    diferencias += 1
                    status = "❌ DIFIERE"
                
                print(f"{transfer_id:<20} {oracle_str:<40} {s3_str:<40} {status}")
        
        # 5. ANÁLISIS DE DIFERENCIAS
        if diferencias > 0:
            print(f"\n5️⃣ ANÁLISIS DE DIFERENCIAS EN To_Profile:")
            print("-" * 60)
            
            for transfer_id in oracle_to_profiles.keys():
                if transfer_id in s3_to_profiles:
                    oracle_data_item = oracle_to_profiles[transfer_id]
                    s3_data_item = s3_to_profiles[transfer_id]
                    
                    oracle_profile = oracle_data_item['to_profile']
                    s3_profile = s3_data_item['to_profile']
                    
                    if str(oracle_profile) != str(s3_profile):
                        print(f"\n  🔍 DIFERENCIA EN {transfer_id}:")
                        print(f"    ToID_Mobiquity: {oracle_data_item['to_id']}")
                        print(f"    To_BankDomain: {oracle_data_item['to_bank_domain']}")
                        print(f"    TransactionType: {oracle_data_item['transaction_type']}")
                        print(f"    Oracle To_Profile: '{oracle_profile}'")
                        print(f"    S3 To_Profile: '{s3_profile}'")
                        
                        # Analizar la diferencia específica
                        if oracle_profile and s3_profile:
                            if len(oracle_profile) != len(s3_profile):
                                print(f"    📏 Longitud: Oracle={len(oracle_profile)}, S3={len(s3_profile)}")
                            
                            # Buscar diferencias carácter por carácter
                            min_len = min(len(oracle_profile), len(s3_profile))
                            for i in range(min_len):
                                if oracle_profile[i] != s3_profile[i]:
                                    print(f"    📍 Primer diferencia en posición {i}: '{oracle_profile[i]}' vs '{s3_profile[i]}'")
                                    break
        
        # 6. RESUMEN FINAL
        print(f"\n6️⃣ RESUMEN FINAL:")
        print("-" * 60)
        
        total_comparados = len(oracle_to_profiles)
        porcentaje = (coincidencias / total_comparados) * 100 if total_comparados > 0 else 0
        
        print(f"📊 ESTADÍSTICAS To_Profile:")
        print(f"  Registros comparados: {total_comparados}")
        print(f"  Registros coincidentes: {coincidencias}")
        print(f"  Registros diferentes: {diferencias}")
        print(f"  Porcentaje de coincidencia: {porcentaje:.1f}%")
        
        if diferencias == 0:
            print("\n🎉 ¡HOMOLOGACIÓN PERFECTA!")
            print("✅ To_Profile coincide 100%")
            print("🚀 No se requieren correcciones")
        else:
            print(f"\n⚠️  HOMOLOGACIÓN NECESITA CORRECCIÓN")
            print(f"❌ {diferencias} diferencias en To_Profile")
            print("🔧 Revisar lógica de To_Profile en el pipeline")
            
            # Sugerir correcciones
            print(f"\n💡 SUGERENCIAS DE CORRECCIÓN:")
            print("1. Verificar lógica de mapeo de perfiles")
            print("2. Revisar casos especiales en TransactionType")
            print("3. Validar concatenación de BankDomain + Profile")
        
    except Exception as e:
        print(f"❌ Error en homologación: {e}")
        import traceback
        traceback.print_exc()

def main():
    print("🚀 HOMOLOGACIÓN CORRECTA To_Profile")
    print("=" * 80)
    print("Usando TransferIDs que existen en ambos sistemas")
    print()
    
    homologar_to_profile_correcto()
    
    print("\n🏁 HOMOLOGACIÓN COMPLETADA")

if __name__ == "__main__":
    main()
