#!/usr/bin/env python3
"""
Homologación específica de la columna CurrencyCode
Compara Oracle vs Parquet para lograr homologación 100%
"""

import oracledb
import duckdb
import pandas as pd
import sys
from datetime import datetime

class CurrencyCodeHomologation:
    def __init__(self):
        self.fecha = '2025-06-15'
        self.setup_connections()
        
    def setup_connections(self):
        """Configura conexiones a Oracle y DuckDB"""
        try:
            # Conexión Oracle
            print("🔌 Conectando a Oracle...")
            self.oracle_conn = oracledb.connect(
                user='usr_datalake',
                password='U2024b1mD4t4l4k5',
                dsn='10.240.131.10:1521/MMONEY'
            )
            print("✅ Conexión Oracle establecida")
            
            # Conexión DuckDB
            print("🔌 Conectando a DuckDB...")
            self.duck_conn = duckdb.connect()
            self.duck_conn.sql("INSTALL httpfs;")
            self.duck_conn.sql("LOAD httpfs;")
            print("✅ Conexión DuckDB establecida")
            
        except Exception as e:
            print(f"❌ Error en conexiones: {e}")
            raise

    def analyze_oracle_currency_code(self):
        """Analiza la columna CurrencyCode en Oracle"""
        print("\n🔍 ANALIZANDO CurrencyCode EN ORACLE")
        print("=" * 60)
        
        cursor = self.oracle_conn.cursor()
        
        # 1. Verificar valores únicos de CurrencyCode
        cursor.execute(f"""
            SELECT "CurrencyCode", COUNT(*) as count
            FROM USR_DATALAKE.PRE_LOG_TRX
            WHERE TRUNC("TransferDate") = TO_DATE('{self.fecha}', 'YYYY-MM-DD')
            GROUP BY "CurrencyCode"
            ORDER BY count DESC
        """)
        
        oracle_values = cursor.fetchall()
        print(f"📊 Valores únicos de CurrencyCode en Oracle:")
        for value, count in oracle_values:
            print(f"   {value}: {count:,} registros")
        
        # 2. Muestra de registros con CurrencyCode
        cursor.execute(f"""
            SELECT "TransferID", "CurrencyCode", "Amount", "Currency"
            FROM USR_DATALAKE.PRE_LOG_TRX
            WHERE TRUNC("TransferDate") = TO_DATE('{self.fecha}', 'YYYY-MM-DD')
            AND ROWNUM <= 5
            ORDER BY "TransferID"
        """)
        
        oracle_sample = cursor.fetchall()
        print(f"\n📋 Muestra Oracle (TransferID, CurrencyCode, Amount, Currency):")
        for i, row in enumerate(oracle_sample, 1):
            print(f"  {i}. {row[0]} | {row[1]} | {row[2]} | {row[3]}")
        
        cursor.close()
        return oracle_values

    def analyze_parquet_currency_code(self):
        """Analiza la columna CurrencyCode en nuestro Parquet"""
        print(f"\n🔍 ANALIZANDO CurrencyCode EN PARQUET")
        print("=" * 60)
        
        parquet_path = f"TEMP_LOGS_TRANSACCIONES/20250615/PRE_LOG_TRX.parquet"
        
        # 1. Verificar valores únicos de CurrencyCode
        result = self.duck_conn.execute(f"""
            SELECT "CurrencyCode", COUNT(*) as count
            FROM read_parquet('{parquet_path}')
            WHERE CAST("TransferDate" AS DATE) = CAST('{self.fecha}' AS DATE)
            GROUP BY "CurrencyCode"
            ORDER BY count DESC
        """).fetchall()
        
        print(f"📊 Valores únicos de CurrencyCode en Parquet:")
        for value, count in result:
            print(f"   {value}: {count:,} registros")
        
        # 2. Muestra de registros con CurrencyCode
        sample_result = self.duck_conn.execute(f"""
            SELECT "TransferID", "CurrencyCode", "Amount", "Currency"
            FROM read_parquet('{parquet_path}')
            WHERE CAST("TransferDate" AS DATE) = CAST('{self.fecha}' AS DATE)
            ORDER BY "TransferID"
            LIMIT 5
        """).fetchall()
        
        print(f"\n📋 Muestra Parquet (TransferID, CurrencyCode, Amount, Currency):")
        for i, row in enumerate(sample_result, 1):
            print(f"  {i}. {row[0]} | {row[1]} | {row[2]} | {row[3]}")
        
        return result

    def compare_currency_codes(self, oracle_values, parquet_values):
        """Compara los valores de CurrencyCode"""
        print(f"\n🔍 COMPARACIÓN DE CurrencyCode")
        print("=" * 60)
        
        # Convertir a diccionarios para comparación
        oracle_dict = {str(value): count for value, count in oracle_values}
        parquet_dict = {str(value): count for value, count in parquet_values}
        
        print(f"📊 VALORES ORACLE:")
        for value, count in oracle_dict.items():
            print(f"   {value}: {count:,}")
        
        print(f"\n📊 VALORES PARQUET:")
        for value, count in parquet_dict.items():
            print(f"   {value}: {count:,}")
        
        # Verificar coincidencias
        oracle_keys = set(oracle_dict.keys())
        parquet_keys = set(parquet_dict.keys())
        
        missing_in_parquet = oracle_keys - parquet_keys
        extra_in_parquet = parquet_keys - oracle_keys
        common_keys = oracle_keys & parquet_keys
        
        print(f"\n🎯 ANÁLISIS:")
        if missing_in_parquet:
            print(f"❌ Valores en Oracle pero NO en Parquet: {missing_in_parquet}")
        
        if extra_in_parquet:
            print(f"⚠️  Valores en Parquet pero NO en Oracle: {extra_in_parquet}")
        
        if common_keys:
            print(f"✅ Valores comunes: {common_keys}")
            
            # Verificar conteos para valores comunes
            conteos_match = True
            for key in common_keys:
                oracle_count = oracle_dict[key]
                parquet_count = parquet_dict[key]
                if oracle_count != parquet_count:
                    print(f"❌ Conteo diferente para {key}: Oracle={oracle_count:,}, Parquet={parquet_count:,}")
                    conteos_match = False
                else:
                    print(f"✅ Conteo idéntico para {key}: {oracle_count:,}")
        
        values_match = len(missing_in_parquet) == 0 and len(extra_in_parquet) == 0 and conteos_match
        print(f"\n🎯 CurrencyCode: {'✅ PERFECTA' if values_match else '❌ REQUIERE CORRECCIÓN'}")
        
        return values_match, missing_in_parquet, extra_in_parquet

    def investigate_currency_logic(self):
        """Investiga la lógica de CurrencyCode en Oracle"""
        print(f"\n🔍 INVESTIGANDO LÓGICA DE CurrencyCode EN ORACLE")
        print("=" * 60)
        
        cursor = self.oracle_conn.cursor()
        
        # Verificar si CurrencyCode se relaciona con Currency o Amount
        cursor.execute(f"""
            SELECT "Currency", "CurrencyCode", COUNT(*) as count
            FROM USR_DATALAKE.PRE_LOG_TRX
            WHERE TRUNC("TransferDate") = TO_DATE('{self.fecha}', 'YYYY-MM-DD')
            GROUP BY "Currency", "CurrencyCode"
            ORDER BY count DESC
        """)
        
        currency_mapping = cursor.fetchall()
        print(f"📊 Relación Currency → CurrencyCode:")
        for currency, code, count in currency_mapping:
            print(f"   {currency} → {code}: {count:,} registros")
        
        # Verificar si hay algún patrón con Amount
        cursor.execute(f"""
            SELECT 
                CASE 
                    WHEN "Amount" IS NULL THEN 'NULL'
                    WHEN "Amount" = 0 THEN 'ZERO'
                    WHEN "Amount" > 0 THEN 'POSITIVE'
                    ELSE 'NEGATIVE'
                END as amount_type,
                "CurrencyCode",
                COUNT(*) as count
            FROM USR_DATALAKE.PRE_LOG_TRX
            WHERE TRUNC("TransferDate") = TO_DATE('{self.fecha}', 'YYYY-MM-DD')
            GROUP BY 
                CASE 
                    WHEN "Amount" IS NULL THEN 'NULL'
                    WHEN "Amount" = 0 THEN 'ZERO'
                    WHEN "Amount" > 0 THEN 'POSITIVE'
                    ELSE 'NEGATIVE'
                END,
CurrencyCode
            ORDER BY count DESC
        """)
        
        amount_mapping = cursor.fetchall()
        print(f"\n📊 Relación Amount Type → CurrencyCode:")
        for amount_type, code, count in amount_mapping:
            print(f"   {amount_type} → {code}: {count:,} registros")
        
        cursor.close()
        return currency_mapping, amount_mapping

    def run_homologation(self):
        """Ejecuta la homologación de CurrencyCode"""
        print("🚀 INICIANDO HOMOLOGACIÓN CurrencyCode")
        print("=" * 80)
        print(f"📅 Fecha: {self.fecha}")
        print(f"🎯 Objetivo: Homologar columna CurrencyCode al 100%")
        
        try:
            # 1. Analizar Oracle
            oracle_values = self.analyze_oracle_currency_code()
            
            # 2. Analizar Parquet
            parquet_values = self.analyze_parquet_currency_code()
            
            # 3. Comparar valores
            values_match, missing, extra = self.compare_currency_codes(oracle_values, parquet_values)
            
            # 4. Investigar lógica si hay diferencias
            if not values_match:
                currency_mapping, amount_mapping = self.investigate_currency_logic()
                
                print(f"\n🔧 RECOMENDACIONES PARA CORRECCIÓN:")
                if missing:
                    print(f"   1. Agregar valores faltantes: {missing}")
                if extra:
                    print(f"   2. Revisar valores extra: {extra}")
                
                # Analizar patrones
                if currency_mapping:
                    print(f"   3. Aplicar mapeo Currency → CurrencyCode según Oracle")
                    for currency, code, count in currency_mapping[:3]:
                        print(f"      {currency} → {code}")
            
            # 5. Resultado final
            print(f"\n{'='*20} RESULTADO HOMOLOGACIÓN {'='*20}")
            print(f"🎯 CurrencyCode: {'✅ 100% HOMOLOGADA' if values_match else '❌ REQUIERE CORRECCIÓN'}")
            
            if values_match:
                print("🎉 ¡CurrencyCode ya está perfectamente homologada!")
            else:
                print("🔧 Se requieren ajustes en la lógica de CurrencyCode")
            
            return values_match
            
        except Exception as e:
            print(f"❌ Error en homologación: {e}")
            raise
        
        finally:
            if hasattr(self, 'oracle_conn'):
                self.oracle_conn.close()
            if hasattr(self, 'duck_conn'):
                self.duck_conn.close()

def main():
    """Función principal"""
    try:
        homologador = CurrencyCodeHomologation()
        success = homologador.run_homologation()
        
        sys.exit(0 if success else 1)
        
    except Exception as e:
        print(f"❌ Error crítico: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
