import datetime
import time

from constants import PERIODO
from globals import *

def get_amount_from_cloudwatch(
	log_groups: list[str],
	start_time: datetime.datetime,
	end_time: datetime.datetime,
	query
):
	response = LOGS_CLIENT.start_query(
		logGroupNames=log_groups,
		startTime=int(start_time.replace(tzinfo=datetime.timezone.utc).timestamp()),
		endTime=int(end_time.replace(tzinfo=datetime.timezone.utc).timestamp()),
		queryString=query,
	)

	query_id = response["queryId"]

	response = None
	while response == None or response["status"] == "Running":
		print('Waiting for query to complete...')
		time.sleep(1)
		response = LOGS_CLIENT.get_query_results(
			queryId=query_id
		)

	print(response)
	
	try:
		return int(response["results"][0][0]["value"])
	except:
		print("ERROR QUERY")
		return 0

def transform_date_period(date: datetime, period):
	start_time = datetime.datetime.strptime(period[0], "%H:%M:%S").time()
	end_time = datetime.datetime.strptime(period[1], "%H:%M:%S").time()

	return datetime.datetime.combine(date, start_time), datetime.datetime.combine(date, end_time)