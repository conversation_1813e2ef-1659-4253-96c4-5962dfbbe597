import datetime

from constants import ID_ENTIDAD, NOMBRE_ENTIDAD, ROL_ENTIDAD
from globals import *
from utils import get_amount_from_cloudwatch, transform_date_period

TIEMPO_MINIMO = 3000  # ms
TIEMPO_MAXIMO = 4000  # ms


def generate_report_line(start_datetime, end_datetime, lambda_names):
    query = rf"""
filter @message like "REPORT"
| stats count(*) 	
"""
    total_invocaciones = get_amount_from_cloudwatch(
        lambda_names, start_datetime, end_datetime, query
    )

    query = rf"""
parse @message /Duration: (?<duration>\d+\.\d+) ms/
| filter duration <= {TIEMPO_MINIMO}
| stats count(*)
"""

    total_menor = get_amount_from_cloudwatch(
        lambda_names, start_datetime, end_datetime, query
    )

    query = rf"""
parse @message /Duration: (?<duration>\d+\.\d+) ms/
| filter duration >= {TIEMPO_MAXIMO}
| stats count(*)
"""

    total_mayor = get_amount_from_cloudwatch(
        lambda_names, start_datetime, end_datetime, query
    )

    return total_invocaciones, total_menor, total_mayor


def generate_line(
    date, func_especifica, tipo_kpi, total, total_menor, total_mayor, kpi
):
    return [
        ID_ENTIDAD,
        NOMBRE_ENTIDAD,
        ROL_ENTIDAD,
        "Rendimiento",
        str(date.year),
        str(date.month),
        str(date.day),
        "Busqueda Alias",
        func_especifica,
        tipo_kpi,
        total,
        total_menor,
        total_mayor,
        kpi,
        "",
        "0030",
    ]


def generate_rend_alias_report(date, period) -> list[list[str]]:
    output: list[list[str]] = []
    start_datetime, end_datetime = transform_date_period(date, period)

    # INQUIRY
    total, total_menor, total_mayor = generate_report_line(
        start_datetime,
        end_datetime,
        [
            "/aws/lambda/lf-mdw-inicio-flujo-p2p_app-bim",
            "/aws/lambda/lf-mdw-inicio-flujo-p2p-arn_app-bim",
            "/aws/lambda/prod_inicio-flujo-p2p-v7_app-bim-3-1-15",
            "/aws/lambda/prod_inicio-flujo-p2p-v6_app-bim-3-1-13",
            "/aws/lambda/prod_inicio-flujo-p2p",
        ],
    )

    output.append(
        generate_line(
            date,
            "Alias Inquiry",
            "Percentil",
            str(total),
            str(total_menor),
            "",
            f"{total_menor/total * 100:.2f}%",
        )
    )
    output.append(
        generate_line(
            date,
            "Alias Inquiry",
            "Tiempo Maximo",
            str(total),
            "",
            str(total_mayor),
            f"{total_mayor/total * 100:.2f}%",
        )
    )

    # RESOLVE
    total, total_menor, total_mayor = generate_report_line(
        start_datetime,
        end_datetime,
        [
            "/aws/lambda/prod_consultar-info-p2p-v7_app-bim-3-1-13",
            "/aws/lambda/prod_consultar-info-p2p-opt-v6_app-bim-3-1-10",
            "/aws/lambda/prod_consultar-info-p2p",
            "/aws/lambda/prod_inicio-pago-qr-otp-v13_app-bim-3-1-13",
            "/aws/lambda/prod_inicio-pago-qr-otp-v12_app-bim-3-1-13",
        ],
    )

    output.append(
        generate_line(
            date,
            "Alias Resolve",
            "Percentil",
            str(total),
            str(total_menor),
            "",
            f"{total_menor/total * 100:.2f}%",
        )
    )
    output.append(
        generate_line(
            date,
            "Alias Resolve",
            "Tiempo Maximo",
            str(total),
            "",
            str(total_mayor),
            f"{total_mayor/total * 100:.2f}%",
        )
    )

    return output
