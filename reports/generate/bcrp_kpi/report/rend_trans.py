import datetime

from constants import ID_ENTIDAD, NOMBRE_ENTIDAD, ROL_ENTIDAD
from globals import *
from utils import get_amount_from_cloudwatch, transform_date_period

TIEMPO_MINIMO = 5000 #ms
TIEMPO_MAXIMO = 7000 #ms


def generate_report_line(start_datetime, end_datetime, lambda_name):
	query = fr"""
filter @message like "REPORT"
| stats count(*) 	
"""
	total_invocaciones = get_amount_from_cloudwatch(
		lambda_name,
		start_datetime,
		end_datetime,
		query
	)

	query = fr"""
parse @message /Duration: (?<duration>\d+\.\d+) ms/
| filter duration <= {TIEMPO_MINIMO}
| stats count(*)
"""

	total_menor = get_amount_from_cloudwatch(
		lambda_name,
		start_datetime,
		end_datetime,
		query
	)

	query = fr"""
parse @message /Duration: (?<duration>\d+\.\d+) ms/
| filter duration >= {TIEMPO_MAXIMO}
| stats count(*)
"""

	total_mayor = get_amount_from_cloudwatch(
		lambda_name,
		start_datetime,
		end_datetime,
		query
	)

	return total_invocaciones, total_menor, total_mayor

def generate_line(date, tipo_kpi, total, total_menor, total_mayor, kpi):
	return [
		ID_ENTIDAD,
		NOMBRE_ENTIDAD,
		ROL_ENTIDAD,
		"Rendimiento",
		str(date.year),
		str(date.month),
		str(date.day),
		"Transferencia",
		"Transfer",
		tipo_kpi,
		total,
		total_menor,
		total_mayor,
		kpi,
		"",
		"0030"
	]

def generate_rend_trans_report(date, period) -> list[list[str]]:
	output: list[list[str]] = []
	start_datetime, end_datetime = transform_date_period(date, period)

	LAMBDA_NAMES = [
		"prod_transfer-niubiz-v9-external",
		"prod_pago-qr-dinamico-otp-v13_app-bim-3-1-14",
	]
	LOG_GROUP_NAME = "/aws/lambda/{}"
	log_groups = [LOG_GROUP_NAME.format(name) for name in LAMBDA_NAMES]

	total, total_menor, total_mayor = generate_report_line(
		start_datetime,
		end_datetime,
		log_groups
	)

	output.append(
		generate_line(
			date,
			"Percentil",
			str(total),
			str(total_menor),
			"",
			f"{total_menor/total * 100:.2f}%"
		)
	)
	output.append(
		generate_line(
			date,
			"Tiempo Maximo",
			str(total),
			"",
			str(total_mayor),
			f"{total_mayor/total * 100:.2f}%"
		)
	)
	

	return output