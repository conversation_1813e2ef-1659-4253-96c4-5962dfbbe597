from entity.db import <PERSON>S<PERSON><PERSON><PERSON>
from datetime import datetime
import json
from constants import (
    RIELES_DE_PAGO,
    ENTIDADES_BENEFICIARIAS,
    ID_ENTIDAD,
    NOMBRE_ENTIDAD,
    ROL_ENTIDAD,
)

QUERY_INFO = """
  SELECT 
    YEAR(created_at) anio, 
    MONTH(created_at) mes,
    process_type,
    status, 
    count(*) total, 
    sum(monto) suma_monto,
    trx_flow
  FROM 
    flujo_interoperabilidad
  WHERE 
    YEAR(created_at) != "0"
    AND (process_type IN ("ENVIO_DINERO", "ENVIO_DINERO_P2P"))
    AND (created_at) >= %(start_date)s AND (created_at) <= %(end_date)s
  GROUP BY
    YEAR(created_at), MONTH(created_at), process_type, status, trx_flow;
"""

destination_wallets = "', '".join(ENTIDADES_BENEFICIARIAS.keys())
QUERY_DETAIL = f"""
  SELECT
    destination_wallet,
    COUNT(CASE WHEN status = 'COMPLETED' THEN 1 END) AS completed_transactions,
    SUM(CASE WHEN status = 'COMPLETED' THEN monto END) AS total_completed_monto,
    COUNT(CASE WHEN status IN ('COMPLETED_REVERSAL', 'PROCESSING') THEN 1 END) AS failed_transactions,
    SUM(CASE WHEN status IN ('COMPLETED_REVERSAL', 'PROCESSING') THEN monto END) AS total_failed_monto
  FROM
    flujo_interoperabilidad
  WHERE
    destination_wallet IN ('{destination_wallets}')
    AND (created_at) >= %(start_date)s AND (created_at) <= %(end_date)s
  GROUP BY
    destination_wallet;
"""

mysql = MySQLData()

status_map = {
    "COMPLETED": "aprobadas",
    "COMPLETED_REVERSAL": "denegadas",
    "PROCESSING": "denegadas",
}
process_type_map = {
    "ENVIO_DINERO_P2P": "p2p",
    "ENVIO_DINERO": "p2m",
}

def generate_transf_reports(date, period):
    start_time = date.strftime(f"%Y-%m-%d {period[0]}")
    end_time = date.strftime(f"%Y-%m-%d {period[1]}")
    params = {
        "start_date": start_time,
        "end_date": end_time,
    }
    data = mysql.select_query(QUERY_INFO, params)

    failed_values = ["FAILED", "PROCESSING", "FAILED_REVERSAL"]
    efect_transf_body = {"total": 0, "failed": 0}

    transf_reporte_res_body = {
        str(value): {
            "aprobadas": {
                "numero": 0,
                "monto": 0,
            },
            "denegadas": {
                "numero": 0,
                "monto": 0,
            },
            "p2p": {
                "numero": 0,
                "monto": 0,
            },
            "p2m": {
                "numero": 0,
                "monto": 0,
            },
        }
        for value in RIELES_DE_PAGO.values()  # Por si en el futuro se agregan otros rieles de pago
    }
    transf_reporte_res_detail = {
        str(value): {
            "aprobadas": {
                "cantidad": 0,
                "monto": 0,
            },
            "denegadas": {
                "cantidad": 0,
                "monto": 0,
            },
        }
        for value in ENTIDADES_BENEFICIARIAS.values()  # Por si en el futuro se agregan otras entidades
    }

    for entry in data:
        transactions = entry.get("total")
        status = entry.get("status")
        monto = entry.get("suma_monto")

        # EFECT_TRANSF
        efect_transf_body["total"] += transactions
        if status in failed_values:
            efect_transf_body["failed"] += transactions

        process_type = entry.get("process_type")
        riel = entry.get("trx_flow")
        riel = RIELES_DE_PAGO.get(riel)

        # TRANSF_REPORTE_RES
        if status in status_map:
            key = status_map[status]
            transf_reporte_res_body[riel][key]["numero"] += transactions
            transf_reporte_res_body[riel][key]["monto"] += monto

            if status == "COMPLETED" and process_type in process_type_map:
                process_key = process_type_map[process_type]
                transf_reporte_res_body[riel][process_key]["numero"] += transactions
                transf_reporte_res_body[riel][process_key]["monto"] += monto

    data = mysql.select_query(QUERY_DETAIL, params)
    for entry in data:
        wallet = entry.get("destination_wallet")
        wallet = ENTIDADES_BENEFICIARIAS.get(wallet)
        completed = entry.get("completed_transactions")
        completed_monto = entry.get("total_completed_monto")
        failed = entry.get("failed_transactions")
        failed_monto = entry.get("total_failed_monto")

        transf_reporte_res_detail[wallet]["aprobadas"]["cantidad"] = completed
        transf_reporte_res_detail[wallet]["aprobadas"]["monto"] = completed_monto

        transf_reporte_res_detail[wallet]["denegadas"]["cantidad"] = failed
        transf_reporte_res_detail[wallet]["denegadas"]["monto"] = failed_monto

    efect_transf = [
        ID_ENTIDAD,
        NOMBRE_ENTIDAD,
        ROL_ENTIDAD,
        "Efectividad",
        str(date.year),
        str(date.month),
        str(date.day),
        "Transferencia",
        "Transfer",
        str(efect_transf_body["total"]),
        str(efect_transf_body["failed"]),
        "",
        "",
        "",
        f"""{str(round(100 * efect_transf_body["failed"] / efect_transf_body["total"], 2))}%""",
    ]

    transf_reporte_res = [
        [
            "ID Riel de Pago",
            "Transacciones aprobadas",
            "",
            "Transacciones denegadas",
            "",
            "Transacciones P2P",
            "",
            "Transacciones P2M"
        ],
        [
            "",
            "Número",
            "Monto (S/)",
            "Número",
            "Monto (S/)",
            "Número",
            "Monto (S/)",
            "Número",
            "Monto (S/)",
        ]
    ]

    for key, values in transf_reporte_res_body.items():
        result = [
            key,
            str(values["aprobadas"]["numero"]),
            str(round(values["aprobadas"]["monto"], 2)),
            str(values["denegadas"]["numero"]),
            str(round(values["denegadas"]["monto"], 2)),
            str(values["p2p"]["numero"]),
            str(round(values["p2p"]["monto"], 2)),
            str(values["p2m"]["numero"]),
            str(round(values["p2m"]["monto"], 2)),
        ]
        transf_reporte_res.append(result)

    transf_reporte_res.append([])
    transf_reporte_res.append([
        "Aprobadas",
        "",
        "",
        "",
        "Denegadas"
    ])
    transf_reporte_res.append([
        "ID Entidad Beneficiaria",
        "Cantidad",
        "Monto (S/)",
        "",
        "ID Entidad Beneficiaria",
        "Cantidad",
        "Monto (S/)",
    ])

    for key, values in transf_reporte_res_detail.items():
        result = [
            key,
            str(values["aprobadas"]["cantidad"]),
            str(values["aprobadas"]["monto"]),
            "",
            key,
            str(values["denegadas"]["cantidad"]),
            str(values["denegadas"]["monto"]),
        ]
        transf_reporte_res.append(result)

    return efect_transf, transf_reporte_res
