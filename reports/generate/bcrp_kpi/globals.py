import boto3

AWS_ACCESS_KEY_ID = "********************"
AWS_SECRET_ACCESS_KEY = "jWG/DrFkTeDzYcpJuMzqXk2xg1ihWoy6z3nTELOf"
REGION_NAME = "us-east-1"

CLOUDWATCH_CLIENT = boto3.client(
	"cloudwatch",
	aws_access_key_id=AWS_ACCESS_KEY_ID, 
	aws_secret_access_key=AWS_SECRET_ACCESS_KEY, 
	region_name=REGION_NAME
)
LOGS_CLIENT = boto3.client("logs",
	aws_access_key_id=AWS_ACCESS_KEY_ID, 
	aws_secret_access_key=AWS_SECRET_ACCESS_KEY, 
	region_name=REGION_NAME
)
