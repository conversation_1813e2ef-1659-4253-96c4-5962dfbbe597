import matplotlib.pyplot as plt
import matplotlib.dates

import datetime

from constants import ID_ENTIDAD, NOMBRE_ENTIDAD, ROL_ENTIDAD
from globals import *


def main():
	LAMBDA_NAME = "prod_alias-resolve-v5_directory"
	STARTTIME = datetime.datetime(2024, 9, 1, 6, 0, 0)
	ENDTIME = datetime.datetime(2024, 9, 1, 22, 00, 00)

	data = CLOUDWATCH_CLIENT.get_metric_data(
		MetricDataQueries=[
			{
				"Id": "errors",
				"MetricStat": {
					"Metric": {
						"Namespace": "AWS/Lambda",
						"MetricName": "Errors",
						"Dimensions": [
							{
								"Name": "FunctionName",
								"Value": LAMBDA_NAME
							},
						]
					},
					"Period": 60,
					"Stat": "Sum",
				},
				"ReturnData": True,
			},
			{
				"Id": "invocations",
				"MetricStat": {
					"Metric": {
						"Namespace": "AWS/Lambda",
						"MetricName": "Invocations",
						"Dimensions": [
							{
								"Name": "FunctionName",
								"Value": LAMBDA_NAME
							},
						]
					},
					"Period": 60,
					"Stat": "Sum",
				},
				"ReturnData": True,
			},
			{
				"Id": "availability",
				"Expression": "100 - 100 * errors / MAX([errors, invocations])",
				"ReturnData": True,
			},
		],
		StartTime=STARTTIME,
		EndTime=ENDTIME,
		#MaxDatapoints=123,
		LabelOptions={
			"Timezone": "-0500"
		}
	)

	#print(data["MetricDataResults"][0])

	x_values = data["MetricDataResults"][2]["Timestamps"]
	y_values = data["MetricDataResults"][2]["Values"]

	rango_medicion = int((ENDTIME - STARTTIME).total_seconds() / 60.0)

	min_indisponibilidad = 0
	for i in y_values:
		if i < 85:
			min_indisponibilidad += 1

	kpi_resultado = min_indisponibilidad / rango_medicion * 100

	#plt.plot(x_values, y_values)
	#plt.gcf().autofmt_xdate()
	#plt.show()

	campos = {
		"Id Entidad": ID_ENTIDAD,
		"Nombre de la Entidad Regulada": NOMBRE_ENTIDAD,
		"Rol de la Entidad Regulada": ROL_ENTIDAD,
		"Calidad del Servicio": "No Disponibilidad",
		"KPI": "Busqueda Alias",
		"Min. Totales Dia Mes": rango_medicion,
		"Min. Totales Indisponibilidad": min_indisponibilidad,
		"Min. Totales Mantenimientos": 0,
		"KPI Resultado": kpi_resultado + "%",
		"Comentarios": "",
		"Año": STARTTIME.year,
		"Mes": STARTTIME.month,
		"Dia": STARTTIME.day,
	}

	sep = ";"
	for i in campos:
		print(i, end=sep)

	print()

	for i in campos:
		print(campos[i], end=sep)

if __name__ == "__main__":
	main()