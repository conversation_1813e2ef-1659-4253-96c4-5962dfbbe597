#!/usr/bin/env python3
"""
Análisis de la diferencia de 55 registros entre S3 parquet y Oracle
"""

import oracledb
import duckdb
import boto3

def analizar_diferencia_55():
    """Analizar exactamente qué son los 55 registros de diferencia"""
    print("🥷 CODE NINJA MASTER: ANÁLISIS DE 55 REGISTROS DE DIFERENCIA")
    print("=" * 80)
    print("OBJETIVO: Entender por qué LOG_TRX_FINAL.parquet tiene +55 vs Oracle")
    print()
    
    try:
        # CONFIGURAR CONEXIONES
        print("🔧 CONFIGURANDO CONEXIONES:")
        print("-" * 60)
        
        # Oracle
        connection = oracledb.connect(
            user='usr_datalake',
            password='U2024b1mD4t4l4k5',
            dsn='10.240.131.10:1521/MMONEY'
        )
        cursor = connection.cursor()
        
        # DuckDB + S3
        conn = duckdb.connect()
        session = boto3.Session()
        credentials = session.get_credentials().get_frozen_credentials()
        
        conn.sql("INSTALL httpfs;")
        conn.sql("LOAD httpfs;")
        conn.sql("SET s3_region='us-east-1';")
        conn.sql("SET s3_use_ssl=true;")
        conn.sql("SET s3_url_style='path';")
        conn.sql(f"SET s3_access_key_id='{credentials.access_key}';")
        conn.sql(f"SET s3_secret_access_key='{credentials.secret_key}';")
        if credentials.token:
            conn.sql(f"SET s3_session_token='{credentials.token}';")
        
        print("  ✅ Conexiones establecidas")
        
        fecha = '2025-06-18'
        
        print(f"\n1️⃣ CONTEOS DETALLADOS:")
        print("-" * 60)
        
        # Oracle
        cursor.execute(f"""
            SELECT COUNT(*)
            FROM USR_DATALAKE.LOG_TRX_FINAL
            WHERE TRUNC(TO_DATE("DateTime", 'YYYY-MM-DD HH24:MI:SS')) = TO_DATE('{fecha}', 'YYYY-MM-DD')
        """)
        oracle_count = cursor.fetchone()[0]
        
        # S3 parquet total
        s3_total = conn.execute(f"""
            SELECT COUNT(*)
            FROM read_parquet('/home/<USER>/REPORTE_2025/reports/generate/S3_LOG_TRANSACCIONES/TEMP_LOGS_TRANSACCIONES/20250618/LOG_TRX_FINAL.parquet')
        """).fetchone()[0]
        
        # S3 con filtro de fecha
        s3_filtrado = conn.execute(f"""
            SELECT COUNT(*)
            FROM read_parquet('/home/<USER>/REPORTE_2025/reports/generate/S3_LOG_TRANSACCIONES/TEMP_LOGS_TRANSACCIONES/20250618/LOG_TRX_FINAL.parquet')
            WHERE CAST("DateTime" AS DATE) = CAST('{fecha}' AS DATE)
        """).fetchone()[0]
        
        print(f"  📊 Oracle LOG_TRX_FINAL:        {oracle_count:,} registros")
        print(f"  📊 S3 parquet total:            {s3_total:,} registros")
        print(f"  📊 S3 parquet filtrado fecha:   {s3_filtrado:,} registros")
        print(f"  📊 Diferencia S3 total vs Oracle: {s3_total - oracle_count:+,}")
        print(f"  📊 Diferencia S3 filtrado vs Oracle: {s3_filtrado - oracle_count:+,}")
        
        print(f"\n2️⃣ ANALIZANDO LOS 55 REGISTROS ADICIONALES:")
        print("-" * 60)
        
        # Analizar qué fechas tienen los 55 registros adicionales
        fechas_adicionales = conn.execute(f"""
            SELECT 
                CAST("DateTime" AS DATE) as fecha,
                COUNT(*) as count
            FROM read_parquet('/home/<USER>/REPORTE_2025/reports/generate/S3_LOG_TRANSACCIONES/TEMP_LOGS_TRANSACCIONES/20250618/LOG_TRX_FINAL.parquet')
            WHERE CAST("DateTime" AS DATE) != CAST('{fecha}' AS DATE)
            GROUP BY CAST("DateTime" AS DATE)
            ORDER BY fecha
        """).fetchall()
        
        if len(fechas_adicionales) > 0:
            print(f"  📋 Registros con fechas diferentes a {fecha}:")
            total_otros_dias = 0
            for row in fechas_adicionales:
                fecha_reg = row[0]
                count = row[1]
                total_otros_dias += count
                print(f"    📅 {fecha_reg}: {count:,} registros")
            
            print(f"  📊 Total registros otros días: {total_otros_dias:,}")
            
            if total_otros_dias == (s3_total - s3_filtrado):
                print(f"  ✅ EXPLICACIÓN ENCONTRADA:")
                print(f"    Los {s3_total - s3_filtrado} registros adicionales son de otros días")
                print(f"    Oracle filtra automáticamente por fecha en su query")
                print(f"    Nuestro parquet contiene datos de múltiples días")
        else:
            print(f"  🔍 Todos los registros son del día {fecha}")
            print(f"  ⚠️ Los 55 registros adicionales requieren análisis más profundo")
        
        print(f"\n3️⃣ VERIFICANDO FILTRO EN EXTRACCIÓN CSV:")
        print("-" * 60)
        
        # Verificar qué hace el filtro en la extracción CSV
        print(f"  📋 Filtro aplicado en extracción CSV:")
        print(f"     WHERE CAST(\"DateTime\" AS DATE) = CAST('{fecha}' AS DATE)")
        
        # Contar registros que pasan el filtro
        registros_filtro = conn.execute(f"""
            SELECT COUNT(*)
            FROM read_parquet('/home/<USER>/REPORTE_2025/reports/generate/S3_LOG_TRANSACCIONES/TEMP_LOGS_TRANSACCIONES/20250618/LOG_TRX_FINAL.parquet')
            WHERE CAST("DateTime" AS DATE) = CAST('{fecha}' AS DATE)
        """).fetchone()[0]
        
        print(f"  📊 Registros que pasan filtro: {registros_filtro:,}")
        print(f"  📊 Registros filtrados: {s3_total - registros_filtro:,}")
        
        print(f"\n4️⃣ ANÁLISIS DE FECHAS EN NUESTRO PARQUET:")
        print("-" * 60)
        
        # Analizar todas las fechas en nuestro parquet
        todas_fechas = conn.execute(f"""
            SELECT 
                CAST("DateTime" AS DATE) as fecha,
                COUNT(*) as count,
                MIN("DateTime") as min_datetime,
                MAX("DateTime") as max_datetime
            FROM read_parquet('/home/<USER>/REPORTE_2025/reports/generate/S3_LOG_TRANSACCIONES/TEMP_LOGS_TRANSACCIONES/20250618/LOG_TRX_FINAL.parquet')
            GROUP BY CAST("DateTime" AS DATE)
            ORDER BY fecha
        """).fetchall()
        
        print(f"  📋 Distribución de fechas en LOG_TRX_FINAL.parquet:")
        for row in todas_fechas:
            fecha_reg = row[0]
            count = row[1]
            min_dt = row[2]
            max_dt = row[3]
            
            if str(fecha_reg) == fecha:
                print(f"    📅 {fecha_reg}: {count:,} registros ✅ (fecha objetivo)")
            else:
                print(f"    📅 {fecha_reg}: {count:,} registros ⚠️ (fecha diferente)")
            print(f"        🕐 Rango: {min_dt} - {max_dt}")
        
        print(f"\n5️⃣ CONCLUSIONES:")
        print("-" * 60)
        
        if len(fechas_adicionales) > 0:
            print(f"  🎯 PROBLEMA IDENTIFICADO:")
            print(f"    ✅ Los 55 registros adicionales son de otros días")
            print(f"    ✅ Oracle filtra automáticamente por fecha")
            print(f"    ✅ Nuestro CSV final aplica el mismo filtro")
            print(f"    ✅ El resultado final es correcto (homologación perfecta)")
            print(f"  🔧 RECOMENDACIÓN:")
            print(f"    - El parquet intermedio puede contener datos de múltiples días")
            print(f"    - El filtro final en CSV es correcto y necesario")
            print(f"    - La homologación está funcionando perfectamente")
        else:
            print(f"  🔍 REQUIERE INVESTIGACIÓN ADICIONAL:")
            print(f"    - Todos los registros son del mismo día")
            print(f"    - Los 55 registros adicionales tienen otra causa")
            print(f"    - Necesitamos analizar diferencias específicas")
        
        cursor.close()
        connection.close()
        conn.close()
        
    except Exception as e:
        print(f"❌ Error en análisis: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    analizar_diferencia_55()
