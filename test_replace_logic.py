#!/usr/bin/env python3
"""
Test REPLACE logic for GRADE_NAME
"""

import duckdb

def test_replace_logic():
    """Test REPLACE logic"""
    print("🔍 TEST: REPLACE LOGIC")
    print("=" * 80)
    
    try:
        conn = duckdb.connect()
        
        # Test REPLACE logic
        test_query = """
        SELECT 
            'Normal General Account Profile' as original,
            REPLACE('Normal General Account Profile', 'General ', '') as replaced,
            UPPER(REPLACE('Normal General Account Profile', 'General ', '')) as upper_replaced,
            CASE 
                WHEN 'Normal General Account Profile' LIKE '%General %' 
                THEN UPPER(REPLACE('Normal General Account Profile', 'General ', ''))
                ELSE UPPER('Normal General Account Profile')
            END as case_logic
        """
        
        result = conn.execute(test_query).fetchone()
        
        print(f"Original: '{result[0]}'")
        print(f"Replaced: '{result[1]}'")
        print(f"Upper Replaced: '{result[2]}'")
        print(f"Case Logic: '{result[3]}'")
        
        # Test with exact Oracle value
        oracle_expected = 'NORMAL ACCOUNT PROFILE'
        print(f"\nOracle expected: '{oracle_expected}'")
        print(f"Match with Upper Replaced: {result[2] == oracle_expected}")
        print(f"Match with Case Logic: {result[3] == oracle_expected}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error en test: {e}")
        import traceback
        traceback.print_exc()

def main():
    print("🚀 TEST REPLACE LOGIC")
    print("=" * 80)
    
    test_replace_logic()
    
    print("\n🏁 TEST COMPLETADO")

if __name__ == "__main__":
    main()
