#!/usr/bin/env python3
"""
Análisis exacto de la diferencia de -117 registros
"""

import oracledb
import duckdb
import boto3

def analisis_diferencia_exacta():
    """Análisis exacto de dónde están los 117 registros faltantes"""
    print("🥷 ANÁLISIS EXACTO: ¿DÓNDE ESTÁN LOS 117 REGISTROS FALTANTES?")
    print("=" * 80)
    
    try:
        # Oracle
        connection = oracledb.connect(
            user='usr_datalake',
            password='U2024b1mD4t4l4k5',
            dsn='10.240.131.10:1521/MMONEY'
        )
        cursor = connection.cursor()
        
        # DuckDB + S3
        conn = duckdb.connect()
        session = boto3.Session()
        credentials = session.get_credentials().get_frozen_credentials()
        
        conn.sql("INSTALL httpfs;")
        conn.sql("LOAD httpfs;")
        conn.sql("SET s3_region='us-east-1';")
        conn.sql("SET s3_use_ssl=true;")
        conn.sql("SET s3_url_style='path';")
        conn.sql(f"SET s3_access_key_id='{credentials.access_key}';")
        conn.sql(f"SET s3_secret_access_key='{credentials.secret_key}';")
        if credentials.token:
            conn.sql(f"SET s3_session_token='{credentials.token}';")
        
        fecha = '2025-06-18'
        
        print("1️⃣ CONTEOS EXACTOS:")
        print("-" * 60)
        
        # Contar Oracle
        cursor.execute(f"""
            SELECT COUNT(*)
            FROM USR_DATALAKE.LOG_TRX_FINAL
            WHERE TRUNC(TO_DATE("DateTime", 'YYYY-MM-DD HH24:MI:SS')) = TO_DATE('{fecha}', 'YYYY-MM-DD')
        """)
        oracle_count = cursor.fetchone()[0]
        
        # Contar S3
        s3_count = conn.execute(f"""
            SELECT COUNT(*)
            FROM read_parquet('/home/<USER>/REPORTE_2025/reports/generate/S3_LOG_TRANSACCIONES/TEMP_LOGS_TRANSACCIONES/20250618/LOG_TRX_FINAL.parquet')
        """).fetchone()[0]
        
        print(f"  📊 Oracle LOG_TRX_FINAL: {oracle_count:,} registros")
        print(f"  📊 S3 LOG_TRX_FINAL:     {s3_count:,} registros")
        print(f"  📊 Diferencia:           {s3_count - oracle_count:+,} registros")
        
        print(f"\n2️⃣ OBTENIENDO TODOS LOS IDs DE ORACLE:")
        print("-" * 60)
        
        # Obtener TODOS los TransactionIDs de Oracle
        cursor.execute(f"""
            SELECT "TransactionID"
            FROM USR_DATALAKE.LOG_TRX_FINAL
            WHERE TRUNC(TO_DATE("DateTime", 'YYYY-MM-DD HH24:MI:SS')) = TO_DATE('{fecha}', 'YYYY-MM-DD')
            ORDER BY "TransactionID"
        """)
        
        oracle_ids = set([row[0] for row in cursor.fetchall()])
        print(f"  📊 IDs Oracle obtenidos: {len(oracle_ids):,}")
        
        print(f"\n3️⃣ OBTENIENDO TODOS LOS IDs DE S3:")
        print("-" * 60)
        
        # Obtener TODOS los TransactionIDs de S3
        s3_ids_result = conn.execute(f"""
            SELECT "TransactionID"
            FROM read_parquet('/home/<USER>/REPORTE_2025/reports/generate/S3_LOG_TRANSACCIONES/TEMP_LOGS_TRANSACCIONES/20250618/LOG_TRX_FINAL.parquet')
            ORDER BY "TransactionID"
        """).fetchall()
        
        s3_ids = set([row[0] for row in s3_ids_result])
        print(f"  📊 IDs S3 obtenidos: {len(s3_ids):,}")
        
        print(f"\n4️⃣ CALCULANDO DIFERENCIAS EXACTAS:")
        print("-" * 60)
        
        # Casos que están en Oracle pero NO en S3 (los que estamos eliminando incorrectamente)
        faltantes_en_s3 = oracle_ids - s3_ids
        
        # Casos que están en S3 pero NO en Oracle (casos adicionales que no deberíamos tener)
        adicionales_en_s3 = s3_ids - oracle_ids
        
        print(f"  📊 Casos en Oracle pero NO en S3: {len(faltantes_en_s3):,}")
        print(f"  📊 Casos en S3 pero NO en Oracle: {len(adicionales_en_s3):,}")
        print(f"  📊 Diferencia neta: {len(adicionales_en_s3) - len(faltantes_en_s3):+,}")
        
        if len(faltantes_en_s3) > 0:
            print(f"\n5️⃣ ANALIZANDO CASOS FALTANTES EN S3:")
            print("-" * 60)
            
            # Analizar algunos casos faltantes
            casos_faltantes_muestra = list(faltantes_en_s3)[:20]
            casos_faltantes_str = "', '".join(casos_faltantes_muestra)
            
            print(f"  📋 Muestra de casos faltantes en S3:")
            for i, caso in enumerate(casos_faltantes_muestra[:10], 1):
                print(f"    {i}. {caso}")
            
            # Verificar características en Oracle
            cursor.execute(f"""
                SELECT 
                    "TransactionType",
                    "Context",
                    COUNT(*) as count
                FROM USR_DATALAKE.LOG_TRX_FINAL
                WHERE "TransactionID" IN ('{casos_faltantes_str}')
                GROUP BY "TransactionType", "Context"
                ORDER BY count DESC
            """)
            
            caracteristicas_faltantes = cursor.fetchall()
            print(f"  📋 Características de casos faltantes (en Oracle):")
            for row in caracteristicas_faltantes:
                txn_type = row[0] if row[0] is not None else 'NULL'
                context = row[1] if row[1] is not None else 'NULL'
                count = row[2]
                print(f"    TransactionType: {txn_type} | Context: {context} | Count: {count}")
        
        if len(adicionales_en_s3) > 0:
            print(f"\n6️⃣ ANALIZANDO CASOS ADICIONALES EN S3:")
            print("-" * 60)
            
            # Analizar algunos casos adicionales
            casos_adicionales_muestra = list(adicionales_en_s3)[:20]
            casos_adicionales_str = "', '".join(casos_adicionales_muestra)
            
            print(f"  📋 Muestra de casos adicionales en S3:")
            for i, caso in enumerate(casos_adicionales_muestra[:10], 1):
                print(f"    {i}. {caso}")
            
            # Verificar características en S3
            caracteristicas_adicionales = conn.execute(f"""
                SELECT 
                    "TransactionType",
                    "Context",
                    COUNT(*) as count
                FROM read_parquet('/home/<USER>/REPORTE_2025/reports/generate/S3_LOG_TRANSACCIONES/TEMP_LOGS_TRANSACCIONES/20250618/LOG_TRX_FINAL.parquet')
                WHERE "TransactionID" IN ('{casos_adicionales_str}')
                GROUP BY "TransactionType", "Context"
                ORDER BY count DESC
            """).fetchall()
            
            print(f"  📋 Características de casos adicionales (en S3):")
            for row in caracteristicas_adicionales:
                txn_type = row[0] if row[0] is not None else 'NULL'
                context = row[1] if row[1] is not None else 'NULL'
                count = row[2]
                print(f"    TransactionType: {txn_type} | Context: {context} | Count: {count}")
        
        print(f"\n7️⃣ CONCLUSIONES:")
        print("-" * 60)
        
        if len(faltantes_en_s3) > len(adicionales_en_s3):
            print(f"  🎯 PROBLEMA PRINCIPAL: Estamos eliminando {len(faltantes_en_s3)} casos válidos")
            print(f"  🔧 SOLUCIÓN: Afinar filtros para ser menos agresivos")
        elif len(adicionales_en_s3) > len(faltantes_en_s3):
            print(f"  🎯 PROBLEMA PRINCIPAL: Tenemos {len(adicionales_en_s3)} casos adicionales")
            print(f"  🔧 SOLUCIÓN: Agregar filtros para eliminar casos adicionales")
        else:
            print(f"  ✅ Diferencias balanceadas - revisar ambos lados")
        
        cursor.close()
        connection.close()
        conn.close()
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    analisis_diferencia_exacta()
