#!/usr/bin/env python3
"""
CODE NINJA: Aná<PERSON>is Específico de los 57 Casos
Mostrar cada caso individual y buscar en tablas origen Oracle
"""

import oracledb
import duckdb
import boto3
import pandas as pd

def analizar_57_casos_especificos():
    """Análisis detallado de cada uno de los 57 casos específicos"""
    print("🥷 CODE NINJA: ANÁLISIS ESPECÍFICO DE LOS 57 CASOS")
    print("=" * 80)
    
    fecha = '2025-06-18'
    
    try:
        # CONFIGURAR CONEXIONES
        print("🔧 CONFIGURANDO CONEXIONES:")
        print("-" * 60)
        
        # Oracle
        connection = oracledb.connect(
            user='usr_datalake',
            password='U2024b1mD4t4l4k5',
            dsn='10.240.131.10:1521/MMONEY'
        )
        cursor = connection.cursor()
        
        # DuckDB + S3
        conn = duckdb.connect()
        session = boto3.Session()
        credentials = session.get_credentials().get_frozen_credentials()
        
        conn.sql("INSTALL httpfs;")
        conn.sql("LOAD httpfs;")
        conn.sql("SET s3_region='us-east-1';")
        conn.sql("SET s3_use_ssl=true;")
        conn.sql("SET s3_url_style='path';")
        conn.sql(f"SET s3_access_key_id='{credentials.access_key}';")
        conn.sql(f"SET s3_secret_access_key='{credentials.secret_key}';")
        if credentials.token:
            conn.sql(f"SET s3_session_token='{credentials.token}';")
        
        print("  ✅ Conexiones establecidas")
        
        # 1. OBTENER LOS 57 CASOS EXACTOS
        print(f"\n1️⃣ OBTENIENDO LOS 57 CASOS EXACTOS:")
        print("-" * 60)
        
        # S3 TransactionIDs
        s3_ids = set([row[0] for row in conn.execute(f"""
            SELECT "TransactionID"
            FROM read_parquet('/home/<USER>/REPORTE_2025/reports/generate/S3_LOG_TRANSACCIONES/TEMP_LOGS_TRANSACCIONES/20250618/LOG_TRX_FINAL.parquet')
        """).fetchall()])
        
        # Oracle TransactionIDs
        oracle_ids = set([row[0] for row in cursor.execute(f"""
            SELECT "TransactionID"
            FROM USR_DATALAKE.LOG_TRX_FINAL
            WHERE TRUNC(TO_DATE("DateTime", 'YYYY-MM-DD HH24:MI:SS')) = TO_DATE('{fecha}', 'YYYY-MM-DD')
        """).fetchall()])
        
        # Los 57 casos específicos
        casos_especificos = sorted(list(s3_ids - oracle_ids))
        
        print(f"  📊 Total casos únicos en S3: {len(casos_especificos)}")
        print(f"  📋 LISTA COMPLETA DE LOS {len(casos_especificos)} CASOS:")
        print("-" * 80)
        
        for i, caso in enumerate(casos_especificos, 1):
            print(f"  {i:2d}. {caso}")
        
        # 2. OBTENER DETALLES COMPLETOS DE CADA CASO EN S3
        print(f"\n2️⃣ DETALLES COMPLETOS DE CADA CASO EN S3:")
        print("-" * 60)
        
        casos_str = "', '".join(casos_especificos)
        
        detalles_s3 = conn.execute(f"""
            SELECT 
                "TransactionID",
                "DateTime",
                "TransactionType",
                "Amount",
                "Currency",
                "FromID",
                "ToID",
                "Context",
                "TransactionStatus"
            FROM read_parquet('/home/<USER>/REPORTE_2025/reports/generate/S3_LOG_TRANSACCIONES/TEMP_LOGS_TRANSACCIONES/20250618/LOG_TRX_FINAL.parquet')
            WHERE "TransactionID" IN ('{casos_str}')
            ORDER BY "TransactionID"
        """).fetchall()
        
        print(f"  📋 DETALLES DE LOS {len(detalles_s3)} CASOS EN S3:")
        print(f"  {'#':<3} {'TransactionID':<20} {'DateTime':<20} {'Type':<25} {'Amount':<12} {'Context':<15}")
        print("-" * 110)
        
        for i, row in enumerate(detalles_s3, 1):
            context = str(row[7]) if row[7] is not None else 'NULL'
            amount = str(row[3]) if row[3] is not None else 'NULL'
            txn_type = str(row[2]) if row[2] is not None else 'NULL'
            datetime_str = str(row[1])[:19] if row[1] is not None else 'NULL'
            print(f"  {i:<3} {row[0]:<20} {datetime_str:<20} {txn_type:<25} {amount:<12} {context:<15}")
        
        # 3. BUSCAR CADA CASO EN TABLAS ORIGEN ORACLE
        print(f"\n3️⃣ BUSCANDO CADA CASO EN TABLAS ORIGEN ORACLE:")
        print("-" * 60)
        
        print(f"  🔍 BÚSQUEDA EN MTX_TRANSACTION_HEADER:")
        print(f"  {'TransactionID':<20} {'Existe':<8} {'Transfer_Date':<15} {'Status':<8} {'Value'}")
        print("-" * 70)
        
        casos_encontrados_header = 0
        casos_con_detalles = []
        
        for caso in casos_especificos:
            # Buscar en MTX_TRANSACTION_HEADER con rango de fechas amplio
            cursor.execute(f"""
                SELECT 
                    TRANSFER_ID,
                    TRANSFER_DATE,
                    TRANSFER_STATUS,
                    TRANSFER_VALUE,
                    SERVICE_TYPE
                FROM PDP_PROD10_MAINDBBUS.MTX_TRANSACTION_HEADER
                WHERE TRANSFER_ID = '{caso}'
                AND TRANSFER_DATE >= TO_DATE('2025-06-17', 'YYYY-MM-DD')
                AND TRANSFER_DATE <= TO_DATE('2025-06-19', 'YYYY-MM-DD')
            """)
            
            resultado = cursor.fetchone()
            
            if resultado:
                casos_encontrados_header += 1
                casos_con_detalles.append({
                    'transaction_id': caso,
                    'transfer_date': resultado[1],
                    'transfer_status': resultado[2],
                    'transfer_value': resultado[3],
                    'service_type': resultado[4],
                    'encontrado_header': True
                })
                print(f"  {caso:<20} {'✅':<8} {str(resultado[1])[:10]:<15} {resultado[2]:<8} {resultado[3]}")
            else:
                casos_con_detalles.append({
                    'transaction_id': caso,
                    'encontrado_header': False
                })
                print(f"  {caso:<20} {'❌':<8} {'N/A':<15} {'N/A':<8} {'N/A'}")
        
        print(f"\n  📊 RESUMEN MTX_TRANSACTION_HEADER:")
        print(f"    Casos encontrados: {casos_encontrados_header}/{len(casos_especificos)}")
        print(f"    Casos no encontrados: {len(casos_especificos) - casos_encontrados_header}/{len(casos_especificos)}")
        
        # 4. BUSCAR EN MTX_TRANSACTION_ITEMS
        print(f"\n  🔍 BÚSQUEDA EN MTX_TRANSACTION_ITEMS:")
        print(f"  {'TransactionID':<20} {'Items':<8} {'Detalles'}")
        print("-" * 50)
        
        casos_encontrados_items = 0
        
        for caso in casos_especificos:
            cursor.execute(f"""
                SELECT COUNT(*), MIN(ITEM_TYPE), MAX(ITEM_TYPE)
                FROM PDP_PROD10_MAINDBBUS.MTX_TRANSACTION_ITEMS
                WHERE TRANSFER_ID = '{caso}'
            """)
            
            resultado = cursor.fetchone()
            
            if resultado and resultado[0] > 0:
                casos_encontrados_items += 1
                print(f"  {caso:<20} {'✅':<8} {resultado[0]} items ({resultado[1]} - {resultado[2]})")
            else:
                print(f"  {caso:<20} {'❌':<8} No items")
        
        print(f"\n  📊 RESUMEN MTX_TRANSACTION_ITEMS:")
        print(f"    Casos con items: {casos_encontrados_items}/{len(casos_especificos)}")
        print(f"    Casos sin items: {len(casos_especificos) - casos_encontrados_items}/{len(casos_especificos)}")
        
        # 5. BUSCAR EN PRE_LOG_TRX ORACLE
        print(f"\n  🔍 BÚSQUEDA EN PRE_LOG_TRX ORACLE:")
        print(f"  {'TransactionID':<20} {'Existe':<8} {'TransferDate':<15} {'Type'}")
        print("-" * 60)
        
        casos_encontrados_pre = 0
        
        for caso in casos_especificos:
            cursor.execute(f"""
                SELECT 
                    "TransferID",
                    "TransferDate",
                    "TransactionType"
                FROM USR_DATALAKE.PRE_LOG_TRX
                WHERE "TransferID" = '{caso}'
                AND CAST("TransferDate" AS DATE) BETWEEN TO_DATE('2025-06-17', 'YYYY-MM-DD') 
                    AND TO_DATE('2025-06-19', 'YYYY-MM-DD')
            """)
            
            resultado = cursor.fetchone()
            
            if resultado:
                casos_encontrados_pre += 1
                print(f"  {caso:<20} {'✅':<8} {str(resultado[1])[:10]:<15} {resultado[2]}")
            else:
                print(f"  {caso:<20} {'❌':<8} {'N/A':<15} {'N/A'}")
        
        print(f"\n  📊 RESUMEN PRE_LOG_TRX ORACLE:")
        print(f"    Casos encontrados: {casos_encontrados_pre}/{len(casos_especificos)}")
        print(f"    Casos no encontrados: {len(casos_especificos) - casos_encontrados_pre}/{len(casos_especificos)}")
        
        # 6. ANÁLISIS FINAL
        print(f"\n4️⃣ ANÁLISIS FINAL:")
        print("-" * 60)
        
        print(f"  📊 RESUMEN GENERAL:")
        print(f"    Total casos únicos S3: {len(casos_especificos)}")
        print(f"    Encontrados en Oracle HEADER: {casos_encontrados_header}")
        print(f"    Encontrados en Oracle ITEMS: {casos_encontrados_items}")
        print(f"    Encontrados en Oracle PRE_LOG_TRX: {casos_encontrados_pre}")
        print(f"    Encontrados en Oracle LOG_TRX_FINAL: 0 (por definición)")
        print()
        
        print(f"  🔍 INTERPRETACIÓN:")
        if casos_encontrados_header > 0:
            print(f"    ✅ {casos_encontrados_header} casos SÍ existen en tablas origen Oracle")
            print(f"    ❌ Oracle los pierde durante el procesamiento SP_PRE_LOG_TRX o SP_LOG_TRX")
            print(f"    ✅ S3/DuckDB los preserva correctamente")
        else:
            print(f"    ❌ Los casos NO existen en tablas origen Oracle")
            print(f"    🤔 Posible diferencia en timing de extracción S3 vs Oracle")
            print(f"    ✅ S3 captura datos que Oracle no tiene")
        
        print(f"\n  🎯 CONCLUSIÓN:")
        if casos_encontrados_header > 0:
            print(f"    🏆 ORACLE PIERDE DATOS: Existen en origen pero no en resultado final")
            print(f"    🥇 S3/DUCKDB ES SUPERIOR: Preserva datos que Oracle pierde")
        else:
            print(f"    🏆 S3 TIENE MEJOR COBERTURA: Captura datos que Oracle no extrae")
            print(f"    🥇 S3/DUCKDB ES SUPERIOR: Mayor completitud de datos")
        
        cursor.close()
        connection.close()
        conn.close()
        
        return casos_especificos, casos_con_detalles
        
    except Exception as e:
        print(f"❌ Error en análisis específico: {e}")
        import traceback
        traceback.print_exc()
        return [], []

def main():
    print("🥷 CODE NINJA: ANÁLISIS ESPECÍFICO DE LOS 57 CASOS")
    print("=" * 80)
    print("OBJETIVO: Mostrar cada caso individual y buscar en Oracle")
    print()
    
    casos, detalles = analizar_57_casos_especificos()
    
    print("\n🏁 ANÁLISIS ESPECÍFICO COMPLETADO")
    print(f"📊 {len(casos)} casos analizados individualmente")

if __name__ == "__main__":
    main()
