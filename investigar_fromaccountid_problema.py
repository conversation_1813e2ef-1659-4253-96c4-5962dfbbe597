#!/usr/bin/env python3
"""
Investigar problema específico de FromAccountID
"""

import oracledb
import duckdb
import boto3

def investigar_fromaccountid():
    """Investiga el problema de FromAccountID"""
    print("🔍 INVESTIGACIÓN: PROBLEMA FromAccountID")
    print("=" * 80)
    
    fecha = '2025-06-15'
    parquet_path = '/home/<USER>/REPORTE_2025/reports/generate/S3_LOG_TRANSACCIONES/TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet'
    
    try:
        # 1. CONFIGURAR CONEXIONES
        print("1️⃣ CONFIGURANDO CONEXIONES:")
        print("-" * 60)
        
        connection = oracledb.connect(
            user='usr_datalake',
            password='U2024b1mD4t4l4k5',
            dsn='10.240.131.10:1521/MMONEY'
        )
        cursor = connection.cursor()
        
        conn = duckdb.connect()
        session = boto3.Session()
        credentials = session.get_credentials().get_frozen_credentials()
        
        conn.sql("INSTALL httpfs;")
        conn.sql("LOAD httpfs;")
        conn.sql("SET s3_region='us-east-1';")
        conn.sql("SET s3_use_ssl=true;")
        conn.sql("SET s3_url_style='path';")
        conn.sql(f"SET s3_access_key_id='{credentials.access_key}';")
        conn.sql(f"SET s3_secret_access_key='{credentials.secret_key}';")
        if credentials.token:
            conn.sql(f"SET s3_session_token='{credentials.token}';")
        
        print("  ✅ Conexiones configuradas")
        
        # 2. VERIFICAR CASOS ESPECÍFICOS
        print(f"\n2️⃣ VERIFICAR CASOS ESPECÍFICOS:")
        print("-" * 60)
        
        casos_test = ['***************', '***************', '**********', '**********', '**********']
        
        for txn_id in casos_test:
            print(f"\n  📋 TransactionID: {txn_id}")
            print(f"  {'-' * 40}")
            
            # Oracle
            cursor.execute("""
                SELECT "TransactionID", "FromAccountID", "FromID", "ToID", "ToAccountID"
                FROM USR_DATALAKE.LOG_TRX_FINAL
                WHERE "TransactionID" = :txn_id
            """, {'txn_id': txn_id})
            
            oracle_result = cursor.fetchone()
            if oracle_result:
                print(f"    Oracle:")
                print(f"      FromAccountID: '{oracle_result[1]}'")
                print(f"      FromID: '{oracle_result[2]}'")
                print(f"      ToID: '{oracle_result[3]}'")
                print(f"      ToAccountID: '{oracle_result[4]}'")
            
            # S3
            s3_result = conn.execute(f"""
                SELECT "TransactionID", "FromAccountID", "FromID", "ToID", "ToAccountID"
                FROM read_parquet('{parquet_path}')
                WHERE "TransactionID" = '{txn_id}'
            """).fetchone()
            
            if s3_result:
                print(f"    S3:")
                print(f"      FromAccountID: '{s3_result[1]}'")
                print(f"      FromID: '{s3_result[2]}'")
                print(f"      ToID: '{s3_result[3]}'")
                print(f"      ToAccountID: '{s3_result[4]}'")
            
            # Comparar
            if oracle_result and s3_result:
                print(f"    Comparación:")
                for i, campo in enumerate(['TransactionID', 'FromAccountID', 'FromID', 'ToID', 'ToAccountID']):
                    oracle_val = str(oracle_result[i]) if oracle_result[i] is not None else 'NULL'
                    s3_val = str(s3_result[i]) if s3_result[i] is not None else 'NULL'
                    
                    if oracle_val == s3_val:
                        print(f"      {campo}: ✅ COINCIDE")
                    else:
                        print(f"      {campo}: ❌ DIFIERE (Oracle: '{oracle_val}' vs S3: '{s3_val}')")
        
        # 3. VERIFICAR LÓGICA DE BANKDOMAIN
        print(f"\n3️⃣ VERIFICAR LÓGICA DE BANKDOMAIN:")
        print("-" * 60)
        
        # Verificar distribución de BankDomain en Oracle LOG_TRX_FINAL
        cursor.execute("""
            SELECT 
                PLT."From_BankDomain",
                LTF."FromAccountID",
                COUNT(*) as CASOS
            FROM USR_DATALAKE.LOG_TRX_FINAL LTF
            INNER JOIN USR_DATALAKE.PRE_LOG_TRX PLT ON LTF."TransactionID" = PLT."TransferID"
            WHERE TRUNC(TO_DATE(LTF."DateTime", 'YYYY-MM-DD HH24:MI:SS')) = TO_DATE(:fecha, 'YYYY-MM-DD')
            AND PLT."From_BankDomain" IN ('FCOMPARTAMOS', 'BNACION', 'CCUSCO', 'CRANDES')
            GROUP BY PLT."From_BankDomain", LTF."FromAccountID"
            ORDER BY PLT."From_BankDomain", COUNT(*) DESC
        """, {'fecha': fecha})
        
        bankdomain_oracle = cursor.fetchall()
        
        print(f"  Oracle LOG_TRX_FINAL por BankDomain:")
        print(f"{'BANKDOMAIN':<15} {'FROM_ACCOUNT_ID':<20} {'CASOS'}")
        print("-" * 45)
        
        for row in bankdomain_oracle[:20]:  # Primeros 20
            print(f"{row[0]:<15} {row[1]:<20} {row[2]}")
        
        # 4. VERIFICAR LÓGICA EN S3
        print(f"\n4️⃣ VERIFICAR LÓGICA EN S3:")
        print("-" * 60)
        
        # Verificar distribución en S3
        s3_bankdomain = conn.execute(f"""
            SELECT 
                PLT."From_BankDomain",
                LTF."FromAccountID",
                COUNT(*) as CASOS
            FROM read_parquet('{parquet_path}') LTF
            INNER JOIN read_parquet('/home/<USER>/REPORTE_2025/reports/generate/S3_LOG_TRANSACCIONES/TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet') PLT 
                ON LTF."TransactionID" = PLT."TransferID"
            WHERE PLT."From_BankDomain" IN ('FCOMPARTAMOS', 'BNACION', 'CCUSCO', 'CRANDES')
            GROUP BY PLT."From_BankDomain", LTF."FromAccountID"
            ORDER BY PLT."From_BankDomain", COUNT(*) DESC
        """).fetchall()
        
        print(f"  S3 LOG_TRX_FINAL por BankDomain:")
        print(f"{'BANKDOMAIN':<15} {'FROM_ACCOUNT_ID':<20} {'CASOS'}")
        print("-" * 45)
        
        for row in s3_bankdomain[:20]:  # Primeros 20
            print(f"{row[0]:<15} {row[1]:<20} {row[2]}")
        
        # 5. VERIFICAR USER_ACCOUNT_HISTORY
        print(f"\n5️⃣ VERIFICAR USER_ACCOUNT_HISTORY:")
        print("-" * 60)
        
        # Verificar si USER_ACCOUNT_HISTORY tiene datos para los casos problemáticos
        cursor.execute("""
            SELECT 
                UAH.USER_ID,
                UAH.ACCOUNT_ID,
                UAH.ATTR8_OLD,
                PLT."From_BankDomain"
            FROM USR_DATALAKE.USER_ACCOUNT_HISTORY UAH
            INNER JOIN USR_DATALAKE.PRE_LOG_TRX PLT ON UAH.USER_ID = PLT."FromID_Mobiquity"
            WHERE PLT."TransferID" IN ('***************', '***************', '**********')
            AND TRUNC(PLT."TransferDate") = TO_DATE(:fecha, 'YYYY-MM-DD')
        """, {'fecha': fecha})
        
        uah_results = cursor.fetchall()
        
        print(f"  USER_ACCOUNT_HISTORY para casos problemáticos:")
        print(f"{'USER_ID':<15} {'ACCOUNT_ID':<20} {'ATTR8_OLD':<15} {'BANKDOMAIN'}")
        print("-" * 70)
        
        for row in uah_results:
            print(f"{row[0]:<15} {row[1]:<20} {row[2]:<15} {row[3]}")
        
        cursor.close()
        connection.close()
        conn.close()
        
        # 6. CONCLUSIONES
        print(f"\n6️⃣ CONCLUSIONES:")
        print("-" * 60)
        print(f"1. Verificar si la lógica CASE de BankDomain se está aplicando")
        print(f"2. Revisar condiciones de USER_ACCOUNT_HISTORY")
        print(f"3. Verificar orden de precedencia en CASE statements")
        
    except Exception as e:
        print(f"❌ Error en investigación: {e}")
        import traceback
        traceback.print_exc()

def main():
    print("🚀 INVESTIGACIÓN FromAccountID")
    print("=" * 80)
    
    investigar_fromaccountid()
    
    print("\n🏁 INVESTIGACIÓN COMPLETADA")

if __name__ == "__main__":
    main()
