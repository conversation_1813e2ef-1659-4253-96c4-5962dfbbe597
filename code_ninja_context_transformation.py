#!/usr/bin/env python3
"""
CODE NINJA: Investigación transformación Context en SP_LOG_TRX
"""

import oracledb

def investigate_context_transformation():
    """Investigar lógica de transformación Context"""
    print("🥷 CODE NINJA: INVESTIGACIÓN TRANSFORMACIÓN Context")
    print("=" * 80)
    
    fecha = '2025-06-15'
    
    try:
        connection = oracledb.connect(
            user='usr_datalake',
            password='U2024b1mD4t4l4k5',
            dsn='10.240.131.10:1521/MMONEY'
        )
        cursor = connection.cursor()
        
        print("1️⃣ CASOS DONDE Context CAMBIA PRE → LOG:")
        print("-" * 60)
        
        # Buscar casos donde Context cambia entre PRE_LOG_TRX y LOG_TRX_FINAL
        cursor.execute(f"""
            SELECT
                PLT."TransferID",
                PLT."Context" as PRE_CONTEXT,
                LTF."Context" as LOG_CONTEXT,
                PLT."TransactionType"
            FROM USR_DATALAKE.PRE_LOG_TRX PLT
            INNER JOIN USR_DATALAKE.LOG_TRX_FINAL LTF ON PLT."TransferID" = LTF."TransactionID"
            WHERE TRUNC(PLT."TransferDate") = TO_DATE('{fecha}', 'YYYY-MM-DD')
            AND PLT."Context" <> LTF."Context"
            AND ROWNUM <= 20
            ORDER BY PLT."TransferID"
        """)
        
        context_changes = cursor.fetchall()
        
        print(f"{'TRANSFER_ID':<20} {'PRE_CONTEXT':<25} {'LOG_CONTEXT':<25} {'TXN_TYPE'}")
        print("-" * 90)
        
        transformation_pattern = {}
        for row in context_changes:
            transfer_id = row[0]
            pre_context = row[1]
            log_context = row[2]
            txn_type = row[3]
            
            # Identificar patrón de transformación
            if pre_context and log_context:
                pattern = f"{pre_context} → {log_context}"
                transformation_pattern[pattern] = transformation_pattern.get(pattern, 0) + 1
            
            print(f"{transfer_id:<20} {pre_context:<25} {log_context:<25} {txn_type}")
        
        print(f"\n2️⃣ PATRONES DE TRANSFORMACIÓN:")
        print("-" * 60)
        
        for pattern, count in transformation_pattern.items():
            print(f"  {pattern}: {count} casos")
        
        # Investigar condiciones específicas para http-awspdp → http-xml_awspdp
        print(f"\n3️⃣ CONDICIONES ESPECÍFICAS http-awspdp → http-xml_awspdp:")
        print("-" * 60)
        
        cursor.execute(f"""
            SELECT
                PLT."TransferID",
                PLT."TransactionType",
                PLT."From_BankDomain",
                PLT."To_BankDomain",
                PLT."Amount",
                PLT."Context" as PRE_CONTEXT,
                LTF."Context" as LOG_CONTEXT
            FROM USR_DATALAKE.PRE_LOG_TRX PLT
            INNER JOIN USR_DATALAKE.LOG_TRX_FINAL LTF ON PLT."TransferID" = LTF."TransactionID"
            WHERE TRUNC(PLT."TransferDate") = TO_DATE('{fecha}', 'YYYY-MM-DD')
            AND PLT."Context" = 'http-awspdp'
            AND LTF."Context" = 'http-xml_awspdp'
            AND ROWNUM <= 10
        """)
        
        specific_cases = cursor.fetchall()
        
        print(f"{'TRANSFER_ID':<20} {'TXN_TYPE':<15} {'FROM_DOMAIN':<15} {'TO_DOMAIN':<15} {'AMOUNT'}")
        print("-" * 85)

        for row in specific_cases:
            print(f"{row[0]:<20} {row[1]:<15} {row[2]:<15} {row[3]:<15} {row[4]}")
        
        # Buscar patrón común
        print(f"\n4️⃣ ANÁLISIS PATRÓN COMÚN:")
        print("-" * 60)
        
        cursor.execute(f"""
            SELECT 
                PLT."TransactionType",
                COUNT(*) as COUNT_CASES
            FROM USR_DATALAKE.PRE_LOG_TRX PLT
            INNER JOIN USR_DATALAKE.LOG_TRX_FINAL LTF ON PLT."TransferID" = LTF."TransactionID"
            WHERE TRUNC(PLT."TransferDate") = TO_DATE('{fecha}', 'YYYY-MM-DD')
            AND PLT."Context" = 'http-awspdp'
            AND LTF."Context" = 'http-xml_awspdp'
            GROUP BY PLT."TransactionType"
            ORDER BY COUNT(*) DESC
        """)
        
        pattern_analysis = cursor.fetchall()
        
        print(f"  📊 Por TransactionType:")
        for txn_type, count in pattern_analysis:
            print(f"    {txn_type}: {count} casos")
        
        cursor.close()
        connection.close()
        
        print(f"\n5️⃣ CONCLUSIONES:")
        print("-" * 60)
        print(f"1. Oracle aplica transformación Context en SP_LOG_TRX")
        print(f"2. Patrón principal: http-awspdp → http-xml_awspdp")
        print(f"3. Necesario implementar lógica específica")
        
    except Exception as e:
        print(f"❌ Error: {e}")

def main():
    investigate_context_transformation()

if __name__ == "__main__":
    main()
