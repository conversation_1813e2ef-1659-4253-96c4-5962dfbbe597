#!/usr/bin/env python3
"""
Analizar impacto de la corrección To_Grade en todos los casos
"""

import oracledb
import duckdb
import boto3

def analizar_impacto_correccion():
    """Analiza el impacto de la corrección To_Grade"""
    print("🔍 ANÁLISIS: IMPACTO CORRECCIÓN To_Grade")
    print("=" * 80)
    
    fecha = '2025-06-15'
    parquet_path = '/home/<USER>/REPORTE_2025/reports/generate/S3_LOG_TRANSACCIONES/TEMP_LOGS_TRANSACCIONES/20250615/PRE_LOG_TRX.parquet'
    
    try:
        # 1. OBTENER DATOS DE ORACLE
        print("1️⃣ OBTENIENDO DATOS DE ORACLE:")
        print("-" * 60)
        
        connection = oracledb.connect(
            user='usr_datalake',
            password='U2024b1mD4t4l4k5',
            dsn='10.240.131.10:1521/MMONEY'
        )
        
        cursor = connection.cursor()
        
        cursor.execute("""
            SELECT 
                "To_Grade",
                COUNT(*) as CASOS
            FROM USR_DATALAKE.PRE_LOG_TRX
            WHERE TRUNC("TransferDate") = TO_DATE(:fecha, 'YYYY-MM-DD')
            GROUP BY "To_Grade"
            ORDER BY COUNT(*) DESC
        """, {'fecha': fecha})
        
        oracle_grades = cursor.fetchall()
        oracle_map = {row[0]: row[1] for row in oracle_grades}
        
        print(f"  Oracle To_Grade distribution:")
        print(f"{'TO_GRADE':<50} {'CASOS'}")
        print("-" * 60)
        for row in oracle_grades:
            print(f"{row[0]:<50} {row[1]}")
        
        # 2. OBTENER DATOS DE S3
        print(f"\n2️⃣ OBTENIENDO DATOS DE S3:")
        print("-" * 60)
        
        conn = duckdb.connect()
        session = boto3.Session()
        credentials = session.get_credentials().get_frozen_credentials()
        
        conn.sql("INSTALL httpfs;")
        conn.sql("LOAD httpfs;")
        conn.sql("SET s3_region='us-east-1';")
        conn.sql("SET s3_use_ssl=true;")
        conn.sql("SET s3_url_style='path';")
        conn.sql(f"SET s3_access_key_id='{credentials.access_key}';")
        conn.sql(f"SET s3_secret_access_key='{credentials.secret_key}';")
        if credentials.token:
            conn.sql(f"SET s3_session_token='{credentials.token}';")
        
        s3_grades = conn.execute(f"""
            SELECT 
                "To_Grade",
                COUNT(*) as CASOS
            FROM read_parquet('{parquet_path}')
            GROUP BY "To_Grade"
            ORDER BY COUNT(*) DESC
        """).fetchall()
        
        s3_map = {row[0]: row[1] for row in s3_grades}
        
        print(f"  S3 To_Grade distribution:")
        print(f"{'TO_GRADE':<50} {'CASOS'}")
        print("-" * 60)
        for row in s3_grades:
            print(f"{row[0]:<50} {row[1]}")
        
        # 3. COMPARACIÓN DETALLADA
        print(f"\n3️⃣ COMPARACIÓN DETALLADA:")
        print("-" * 60)
        
        all_grades = set(oracle_map.keys()) | set(s3_map.keys())
        
        print(f"{'TO_GRADE':<50} {'ORACLE':<10} {'S3':<10} {'ESTADO'}")
        print("-" * 80)
        
        coincidencias = 0
        diferencias = 0
        
        for grade in sorted(all_grades):
            oracle_count = oracle_map.get(grade, 0)
            s3_count = s3_map.get(grade, 0)
            
            if oracle_count == s3_count:
                estado = "✅ COINCIDE"
                coincidencias += 1
            else:
                estado = "❌ DIFIERE"
                diferencias += 1
            
            print(f"{grade:<50} {oracle_count:<10} {s3_count:<10} {estado}")
        
        # 4. ANÁLISIS DE TRANSFORMACIONES
        print(f"\n4️⃣ ANÁLISIS DE TRANSFORMACIONES:")
        print("-" * 60)
        
        # Verificar qué grades de Oracle contienen "GENERAL"
        oracle_with_general = [grade for grade in oracle_map.keys() if 'GENERAL' in grade]
        s3_with_general = [grade for grade in s3_map.keys() if 'GENERAL' in grade]
        
        print(f"  Oracle grades con 'GENERAL': {len(oracle_with_general)}")
        for grade in oracle_with_general:
            print(f"    {grade} ({oracle_map[grade]} casos)")
        
        print(f"\n  S3 grades con 'GENERAL': {len(s3_with_general)}")
        for grade in s3_with_general:
            print(f"    {grade} ({s3_map[grade]} casos)")
        
        # 5. IDENTIFICAR CASOS PROBLEMÁTICOS
        print(f"\n5️⃣ CASOS PROBLEMÁTICOS IDENTIFICADOS:")
        print("-" * 60)
        
        # Casos que antes coincidían y ahora no
        problemas = []
        
        for grade in oracle_map.keys():
            oracle_count = oracle_map[grade]
            s3_count = s3_map.get(grade, 0)
            
            if oracle_count != s3_count:
                # Verificar si hay una versión "sin GENERAL" en S3
                grade_sin_general = grade.replace('GENERAL ', '')
                s3_sin_general = s3_map.get(grade_sin_general, 0)
                
                if s3_sin_general > 0:
                    problemas.append({
                        'oracle_grade': grade,
                        'oracle_count': oracle_count,
                        's3_grade': grade_sin_general,
                        's3_count': s3_sin_general,
                        'tipo': 'TRANSFORMACION_INCORRECTA'
                    })
        
        print(f"  Problemas identificados: {len(problemas)}")
        for problema in problemas:
            print(f"    Oracle: '{problema['oracle_grade']}' ({problema['oracle_count']})")
            print(f"    S3: '{problema['s3_grade']}' ({problema['s3_count']})")
            print(f"    Tipo: {problema['tipo']}")
            print()
        
        cursor.close()
        connection.close()
        conn.close()
        
        # 6. CONCLUSIONES Y RECOMENDACIONES
        print(f"6️⃣ CONCLUSIONES Y RECOMENDACIONES:")
        print("-" * 60)
        
        print(f"1. Total grades únicos: {len(all_grades)}")
        print(f"2. Grades coincidentes: {coincidencias}")
        print(f"3. Grades diferentes: {diferencias}")
        print(f"4. Problemas identificados: {len(problemas)}")
        
        if problemas:
            print(f"\n🔧 RECOMENDACIÓN:")
            print(f"   La corrección REPLACE es demasiado agresiva")
            print(f"   Necesita ser más selectiva según el caso específico")
            print(f"   Algunos grades SÍ deben mantener 'GENERAL'")
        else:
            print(f"\n✅ CORRECCIÓN EXITOSA:")
            print(f"   Todos los grades coinciden perfectamente")
        
    except Exception as e:
        print(f"❌ Error en análisis: {e}")
        import traceback
        traceback.print_exc()

def main():
    print("🚀 ANÁLISIS IMPACTO CORRECCIÓN To_Grade")
    print("=" * 80)
    print("OBJETIVO: Verificar si la corrección afectó casos correctos")
    print()
    
    analizar_impacto_correccion()
    
    print("\n🏁 ANÁLISIS COMPLETADO")

if __name__ == "__main__":
    main()
