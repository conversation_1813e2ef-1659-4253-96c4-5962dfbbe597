#!/usr/bin/env python3
"""
Verificación final de homologación Oracle vs S3/DuckDB
"""

import oracledb
import duckdb
import boto3

def verificacion_homologacion_final():
    """Verificación final de homologación usando la query exacta proporcionada"""
    print("🥷 CODE NINJA MASTER: VERIFICACIÓN FINAL DE HOMOLOGACIÓN")
    print("=" * 80)
    print("OBJETIVO: Comparar Oracle vs S3/DuckDB con query exacta")
    print()
    
    try:
        # CONFIGURAR CONEXIONES
        print("🔧 CONFIGURANDO CONEXIONES:")
        print("-" * 60)
        
        # Oracle
        connection = oracledb.connect(
            user='usr_datalake',
            password='U2024b1mD4t4l4k5',
            dsn='10.240.131.10:1521/MMONEY'
        )
        cursor = connection.cursor()
        
        # DuckDB + S3
        conn = duckdb.connect()
        session = boto3.Session()
        credentials = session.get_credentials().get_frozen_credentials()
        
        conn.sql("INSTALL httpfs;")
        conn.sql("LOAD httpfs;")
        conn.sql("SET s3_region='us-east-1';")
        conn.sql("SET s3_use_ssl=true;")
        conn.sql("SET s3_url_style='path';")
        conn.sql(f"SET s3_access_key_id='{credentials.access_key}';")
        conn.sql(f"SET s3_secret_access_key='{credentials.secret_key}';")
        if credentials.token:
            conn.sql(f"SET s3_session_token='{credentials.token}';")
        
        print("  ✅ Conexiones establecidas")
        
        fecha = '2025-06-18'
        
        print(f"\n1️⃣ CONTEO ORACLE (QUERY EXACTA PROPORCIONADA):")
        print("-" * 60)
        
        # Query exacta proporcionada por el usuario
        oracle_query = """
        SELECT COUNT(*)
        FROM USR_DATALAKE.LOG_TRX_FINAL
        WHERE TRUNC(TO_DATE("DateTime", 'YYYY-MM-DD HH24:MI:SS')) = TO_DATE('2025-06-18', 'YYYY-MM-DD')
        """
        
        print(f"  📋 Ejecutando query Oracle:")
        print(f"     {oracle_query.strip()}")
        
        cursor.execute(oracle_query)
        oracle_count = cursor.fetchone()[0]
        
        print(f"  📊 Resultado Oracle: {oracle_count:,} registros")
        
        print(f"\n2️⃣ CONTEO S3/DUCKDB (LOG_TRX_FINAL.parquet):")
        print("-" * 60)
        
        # Contar nuestro LOG_TRX_FINAL.parquet
        s3_log_trx_count = conn.execute(f"""
            SELECT COUNT(*)
            FROM read_parquet('/home/<USER>/REPORTE_2025/reports/generate/S3_LOG_TRANSACCIONES/TEMP_LOGS_TRANSACCIONES/20250618/LOG_TRX_FINAL.parquet')
        """).fetchone()[0]
        
        print(f"  📊 S3 LOG_TRX_FINAL.parquet: {s3_log_trx_count:,} registros")
        
        print(f"\n3️⃣ CONTEO S3/DUCKDB (CSV FINAL):")
        print("-" * 60)
        
        # Contar nuestro CSV final
        s3_csv_count = conn.execute(f"""
            SELECT COUNT(*)
            FROM read_csv('/home/<USER>/REPORTE_2025/reports/generate/S3_LOG_TRANSACCIONES/output/TR-20250618.csv', header=true)
        """).fetchone()[0]
        
        print(f"  📊 S3 CSV Final (TR-20250618.csv): {s3_csv_count:,} registros")
        
        print(f"\n4️⃣ VERIFICACIÓN CON FILTRO DE FECHA (COMO ORACLE):")
        print("-" * 60)
        
        # Verificar con filtro de fecha exacto como Oracle
        s3_filtered_count = conn.execute(f"""
            SELECT COUNT(*)
            FROM read_parquet('/home/<USER>/REPORTE_2025/reports/generate/S3_LOG_TRANSACCIONES/TEMP_LOGS_TRANSACCIONES/20250618/LOG_TRX_FINAL.parquet')
            WHERE CAST("DateTime" AS DATE) = CAST('{fecha}' AS DATE)
        """).fetchone()[0]
        
        print(f"  📊 S3 con filtro fecha: {s3_filtered_count:,} registros")
        
        print(f"\n5️⃣ COMPARACIÓN FINAL:")
        print("-" * 60)
        
        print(f"  📊 RESULTADOS COMPARATIVOS:")
        print(f"    Oracle LOG_TRX_FINAL:           {oracle_count:,} registros")
        print(f"    S3 LOG_TRX_FINAL.parquet:       {s3_log_trx_count:,} registros")
        print(f"    S3 CSV Final:                   {s3_csv_count:,} registros")
        print(f"    S3 con filtro fecha:            {s3_filtered_count:,} registros")
        
        print(f"\n  📊 DIFERENCIAS:")
        print(f"    Oracle vs S3 LOG_TRX_FINAL:     {s3_log_trx_count - oracle_count:+,}")
        print(f"    Oracle vs S3 CSV Final:         {s3_csv_count - oracle_count:+,}")
        print(f"    Oracle vs S3 Filtrado:          {s3_filtered_count - oracle_count:+,}")
        
        print(f"\n6️⃣ EVALUACIÓN DE HOMOLOGACIÓN:")
        print("-" * 60)
        
        if s3_csv_count == oracle_count:
            print(f"  🎉 ¡HOMOLOGACIÓN PERFECTA!")
            print(f"  ✅ CSV Final: {s3_csv_count:,} = Oracle: {oracle_count:,}")
            print(f"  🏆 Diferencia: 0 registros")
            print(f"  🎯 Resultado: 100% IDÉNTICO ('dos gotas de agua')")
        elif abs(s3_csv_count - oracle_count) <= 5:
            print(f"  ✅ HOMOLOGACIÓN EXCELENTE!")
            print(f"  📊 Diferencia mínima: {abs(s3_csv_count - oracle_count)} registros")
            print(f"  🎯 Resultado: 99.9%+ de precisión")
        elif abs(s3_csv_count - oracle_count) <= 50:
            print(f"  ⚠️ HOMOLOGACIÓN BUENA")
            print(f"  📊 Diferencia aceptable: {abs(s3_csv_count - oracle_count)} registros")
            print(f"  🎯 Resultado: 99%+ de precisión")
        else:
            print(f"  ❌ HOMOLOGACIÓN REQUIERE AJUSTES")
            print(f"  📊 Diferencia significativa: {abs(s3_csv_count - oracle_count)} registros")
            print(f"  🔍 Requiere investigación adicional")
        
        print(f"\n7️⃣ CONCLUSIONES:")
        print("-" * 60)
        
        if s3_csv_count == oracle_count:
            print(f"  🎯 MISIÓN COMPLETADA:")
            print(f"    ✅ Pipeline S3/DuckDB replica Oracle exactamente")
            print(f"    ✅ Homologación 100% perfecta lograda")
            print(f"    ✅ Filtros Oracle aplicados correctamente")
            print(f"    ✅ Lógica de negocio preservada")
            print(f"  🏅 RESULTADO: ÉXITO TOTAL")
        else:
            print(f"  🔍 ANÁLISIS REQUERIDO:")
            print(f"    - Verificar filtros adicionales")
            print(f"    - Revisar transformaciones específicas")
            print(f"    - Analizar casos edge")
        
        cursor.close()
        connection.close()
        conn.close()
        
    except Exception as e:
        print(f"❌ Error en verificación: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    verificacion_homologacion_final()
