#!/usr/bin/env python3
"""
Pipeline ETL LOG_TRANSACCIONES Modernizado - DuckDB + Parquet
Migración completa de Oracle a Parquet usando solo DuckDB
100% INDEPENDIENTE DE ORACLE - Sin dependencias externas
Autor: CODE NINJA
Fecha: 2025-06-18
"""

import duckdb
import boto3
import sys
import json
from datetime import datetime, timedelta
from pathlib import Path
import logging

class LogTransaccionesPipeline:
    def __init__(self):
        """Inicializa el pipeline LOG_TRANSACCIONES modernizado"""
        
        # Configurar logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger('LogTransaccionesPipeline')
        
        # Configuración S3
        self.s3_bucket_silver = 'ec-silver-data-lake'
        
        # Conexión DuckDB
        self.conn = duckdb.connect()
        
        # Configurar S3 en DuckDB
        self.setup_s3_credentials()
        
        # Rutas S3 de las tablas fuente
        self.s3_sources = {
            'mtx_transaction_header': f's3://{self.s3_bucket_silver}/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_HEADER/consolidado_puro.parquet',
            'mtx_transaction_items': f's3://{self.s3_bucket_silver}/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_ITEMS/consolidado_puro.parquet',
            'user_data_trx': f's3://{self.s3_bucket_silver}/PDP_PROD10_MAINDB/USER_DATA_TRX/consolidado_puro.parquet',
            'user_profile': f's3://{self.s3_bucket_silver}/PDP_PROD10_MAINDB/USER_PROFILE/consolidado_puro.parquet',
            'sys_service_types': f's3://{self.s3_bucket_silver}/PDP_PROD10_MAINDB/SYS_SERVICE_TYPES/consolidado_puro.parquet',
            'marketing_profile': f's3://{self.s3_bucket_silver}/PDP_PROD10_MAINDB/MARKETING_PROFILE/consolidado_puro.parquet',
            'mtx_categories': f's3://{self.s3_bucket_silver}/PDP_PROD10_MAINDBBUS/MTX_CATEGORIES/consolidado_puro.parquet',
            'channel_grades': f's3://{self.s3_bucket_silver}/PDP_PROD10_MAINDBBUS/CHANNEL_GRADES/consolidado_puro.parquet',
            'issuer_details': f's3://{self.s3_bucket_silver}/PDP_PROD10_MAINDB/ISSUER_DETAILS/consolidado_puro.parquet',
            'mtx_wallet': f's3://{self.s3_bucket_silver}/PDP_PROD10_MAINDBBUS/MTX_WALLET/consolidado_puro.parquet',
            'mtx_wallet_raw': f's3://{self.s3_bucket_silver}/PDP_PROD10_MAINDBBUS/MTX_WALLET_ORA/consolidado_puro.parquet'
        }

        # Mapeo de Bank Domains para cuentas especiales (igual que Oracle)
        self.bank_domain_accounts = {
            'BNACION': '1334853',
            'CCUSCO': '1464437',
            'CRANDES': '1414519',
            '0231FCONFIANZA': '1882233',
            '0144QAPAQ': '1131834',
            'FCOMPARTAMOS': '1188057'
        }

        # Usuarios especiales para lógica de BILLPAY (igual que Oracle líneas 138-139)
        self.special_users = [
            '466787', '580943', '1597312', '1885838',
            'US.****************', 'US.****************', 'US.****************'
        ]

    def setup_s3_credentials(self):
        """Configura las credenciales S3 en DuckDB"""
        try:
            self.logger.info("Configurando credenciales S3...")
            
            # Obtener credenciales de AWS
            session = boto3.Session()
            credentials = session.get_credentials().get_frozen_credentials()
            
            # Configurar DuckDB para S3
            self.conn.sql("INSTALL httpfs;")
            self.conn.sql("LOAD httpfs;")
            self.conn.sql("SET s3_region='us-east-1';")
            self.conn.sql("SET s3_use_ssl=true;")
            self.conn.sql("SET s3_url_style='path';")
            self.conn.sql(f"SET s3_access_key_id='{credentials.access_key}';")
            self.conn.sql(f"SET s3_secret_access_key='{credentials.secret_key}';")
            
            if credentials.token:
                self.conn.sql(f"SET s3_session_token='{credentials.token}';")
            
            self.logger.info("Credenciales S3 configuradas exitosamente")
            
        except Exception as e:
            self.logger.error(f"Error configurando credenciales S3: {e}")
            raise

    def setup_working_directory(self):
        """Configura el directorio de trabajo"""
        # Obtener la ruta base del proyecto
        current_path = Path.cwd()
        
        # Buscar la carpeta S3_LOG_TRANSACCIONES
        if current_path.name == 'S3_LOG_TRANSACCIONES':
            base_path = current_path
        else:
            # Buscar en subdirectorios
            s3_log_path = current_path / 'reports' / 'generate' / 'S3_LOG_TRANSACCIONES'
            if s3_log_path.exists():
                base_path = s3_log_path
            else:
                # Crear si no existe
                base_path = current_path / 'S3_LOG_TRANSACCIONES'
                base_path.mkdir(parents=True, exist_ok=True)
        
        # Cambiar directorio de trabajo a S3_LOG_TRANSACCIONES
        import os
        os.chdir(base_path)

    def get_partitioned_path(self, base_path: str, fecha: str) -> str:
        """
        Construye la ruta particionada para archivos por fecha
        Formato: s3://bucket/table/year=YYYY/month=MM/day=DD/*.parquet
        """
        date_obj = datetime.strptime(fecha, '%Y-%m-%d')
        year = date_obj.strftime('%Y')
        month = date_obj.strftime('%m')
        day = date_obj.strftime('%d')
        
        return f"{base_path}/year={year}/month={month}/day={day}/*.parquet"

    def log_execution_status(self, part: str, status: str):
        """Registra el estado de ejecución"""
        with open("execution_status.log", "a") as f:
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            f.write(f"{timestamp} - {part}: {status}\n")
        self.logger.info(f"{part}: {status}")

    def check_if_processed(self, fecha: str, table_name: str) -> bool:
        """
        Verifica si el proceso ya fue ejecutado para la fecha
        Replica la lógica de Oracle líneas 11-23
        """
        try:
            if table_name == 'PRE_LOG_TRX':
                temp_path = f"TEMP_LOGS_TRANSACCIONES/{fecha.replace('-', '')}/PRE_LOG_TRX.parquet"
            else:
                temp_path = f"TEMP_LOGS_TRANSACCIONES/{fecha.replace('-', '')}/LOG_TRX_FINAL.parquet"
            
            if Path(temp_path).exists():
                count_result = self.conn.execute(f"""
                    SELECT COUNT(*) FROM read_parquet('{temp_path}')
                    WHERE CAST("TransferDate" AS DATE) = CAST('{fecha}' AS DATE)
                """).fetchone()
                
                if count_result and count_result[0] > 0:
                    self.logger.info(f"Proceso {table_name} ya ejecutado para {fecha}: {count_result[0]} registros")
                    return True
            
            return False

        except Exception as e:
            self.logger.warning(f"Error verificando si {table_name} fue procesado: {e}")
            return False

    def run_pipeline(self, fecha: str):
        """
        Ejecuta el pipeline completo LOG_TRANSACCIONES
        100% S3/DuckDB - SIN DEPENDENCIAS ORACLE
        """
        try:
            start_time = datetime.now()
            self.logger.info(f"🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: {fecha}")
            
            # Configurar directorio de trabajo
            self.setup_working_directory()
            
            # Crear carpeta de fecha
            date_folder = fecha.replace('-', '')
            
            # 1. Ejecutar SP_PRE_LOG_TRX (100% S3)
            self.log_execution_status("SP_PRE_LOG_TRX", "INICIANDO")
            pre_log_path = self.process_sp_pre_log_trx(fecha, date_folder)
            self.log_execution_status("SP_PRE_LOG_TRX", "COMPLETADO")
            
            # 2. Ejecutar SP_LOG_TRX (100% S3)
            self.log_execution_status("SP_LOG_TRX", "INICIANDO")
            log_trx_path = self.process_sp_log_trx(fecha, date_folder)
            self.log_execution_status("SP_LOG_TRX", "COMPLETADO")
            
            # 3. Generar CSV final
            self.log_execution_status("EXTRACCION_CSV", "INICIANDO")
            csv_path = self.extract_final_csv(fecha, date_folder)
            self.log_execution_status("EXTRACCION_CSV", "COMPLETADO")
            
            # Calcular tiempo total
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            self.logger.info(f"✅ Pipeline LOG_TRANSACCIONES completado exitosamente en {duration:.2f} segundos")
            self.log_execution_status("PIPELINE_COMPLETO", "EXITOSO")
            
            # Guardar resumen de ejecución
            self.save_execution_summary(fecha, duration, [pre_log_path, log_trx_path, csv_path])
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Error en pipeline LOG_TRANSACCIONES: {e}")
            self.log_execution_status("PIPELINE_COMPLETO", f"ERROR: {e}")
            raise

    def save_execution_summary(self, fecha: str, duration: float, files: list):
        """Guarda resumen de la ejecución"""
        summary = {
            'fecha_procesada': fecha,
            'timestamp': datetime.now().isoformat(),
            'duracion_segundos': duration,
            'estado': 'EXITOSO',
            'archivos_generados': files,
            'version': '2.0 - 100% S3/DuckDB'
        }
        
        with open(f"logs/pipeline_results_{fecha.replace('-', '')}.json", "w") as f:
            json.dump(summary, f, indent=2)

def main():
    """Función principal"""
    if len(sys.argv) != 2:
        print("Uso: python pipeline_log_transacciones_clean.py YYYY-MM-DD")
        sys.exit(1)
    
    fecha = sys.argv[1]
    
    # Validar formato de fecha
    try:
        datetime.strptime(fecha, '%Y-%m-%d')
    except ValueError:
        print("Error: Formato de fecha inválido. Use YYYY-MM-DD")
        sys.exit(1)
    
    print("🚀 Iniciando Pipeline LOG_TRANSACCIONES Modernizado")
    print(f"📅 Fecha de procesamiento: {fecha}")
    print("🏗️  Arquitectura: DuckDB + Parquet (S3)")
    print("=" * 80)
    
    # Ejecutar pipeline
    pipeline = LogTransaccionesPipeline()
    success = pipeline.run_pipeline(fecha)
    
    if success:
        print("\n" + "=" * 80)
        print("📊 RESUMEN DE EJECUCIÓN")
        print("=" * 80)
        print(f"📅 Fecha procesada: {fecha}")
        print("🎯 Estado final: EXITOSO")
        print("✅ Pipeline 100% S3/DuckDB - SIN DEPENDENCIAS ORACLE")
    else:
        print("❌ Pipeline falló")
        sys.exit(1)

if __name__ == "__main__":
    main()
