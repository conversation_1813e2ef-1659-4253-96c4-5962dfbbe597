#!/usr/bin/env python3
"""
Homologación completa LOG_TRX_FINAL: Oracle vs S3
Objetivo: 100% perfección "como dos gotas de agua"
"""

import oracledb
import duckdb
import boto3
from collections import defaultdict
import sys

def homologar_log_trx_final():
    """Homologa LOG_TRX_FINAL con precisión absoluta"""
    print("🎯 HOMOLOGACIÓN LOG_TRX_FINAL: OBJETIVO 100% PERFECCIÓN")
    print("=" * 80)
    
    fecha = '2025-06-15'
    parquet_path = '/home/<USER>/REPORTE_2025/reports/generate/S3_LOG_TRANSACCIONES/TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet'
    
    # Campos críticos a homologar
    campos_criticos = [
        "TransactionID",
        "FromAccountID", 
        "ToID",
        "ToAccountID",
        "FromID"
    ]
    
    try:
        # 1. CONFIGURAR CONEXIONES
        print("1️⃣ CONFIGURANDO CONEXIONES:")
        print("-" * 60)
        
        connection = oracledb.connect(
            user='usr_datalake',
            password='U2024b1mD4t4l4k5',
            dsn='10.240.131.10:1521/MMONEY'
        )
        cursor = connection.cursor()
        
        conn = duckdb.connect()
        session = boto3.Session()
        credentials = session.get_credentials().get_frozen_credentials()
        
        conn.sql("INSTALL httpfs;")
        conn.sql("LOAD httpfs;")
        conn.sql("SET s3_region='us-east-1';")
        conn.sql("SET s3_use_ssl=true;")
        conn.sql("SET s3_url_style='path';")
        conn.sql(f"SET s3_access_key_id='{credentials.access_key}';")
        conn.sql(f"SET s3_secret_access_key='{credentials.secret_key}';")
        if credentials.token:
            conn.sql(f"SET s3_session_token='{credentials.token}';")
        
        print("  ✅ Conexiones configuradas exitosamente")
        
        # 2. OBTENER DATOS DE ORACLE
        print(f"\n2️⃣ OBTENIENDO DATOS DE ORACLE LOG_TRX_FINAL:")
        print("-" * 60)
        
        oracle_query = f"""
            SELECT "TransactionID","FromAccountID","ToID", "ToAccountID", "FromID"
            FROM USR_DATALAKE.LOG_TRX_FINAL
            WHERE TRUNC(TO_DATE("DateTime", 'YYYY-MM-DD HH24:MI:SS')) = TO_DATE('{fecha}', 'YYYY-MM-DD')
            ORDER BY "TransactionID"
        """
        
        cursor.execute(oracle_query)
        oracle_data = cursor.fetchall()
        oracle_count = len(oracle_data)
        
        print(f"  📊 Oracle LOG_TRX_FINAL: {oracle_count:,} registros")
        
        # 3. OBTENER DATOS DE S3
        print(f"\n3️⃣ OBTENIENDO DATOS DE S3 LOG_TRX_FINAL:")
        print("-" * 60)
        
        s3_query = f"""
            SELECT "TransactionID","FromAccountID","ToID", "ToAccountID", "FromID"
            FROM read_parquet('{parquet_path}')
            ORDER BY "TransactionID"
        """
        
        s3_data = conn.execute(s3_query).fetchall()
        s3_count = len(s3_data)
        
        print(f"  📊 S3 LOG_TRX_FINAL: {s3_count:,} registros")
        
        # 4. VALIDACIÓN DE CONTEO
        print(f"\n4️⃣ VALIDACIÓN DE CONTEO:")
        print("-" * 60)
        
        if oracle_count == s3_count:
            print(f"  ✅ CONTEO PERFECTO: {oracle_count:,} registros en ambos")
        else:
            print(f"  ❌ DIFERENCIA EN CONTEO:")
            print(f"    Oracle: {oracle_count:,}")
            print(f"    S3: {s3_count:,}")
            print(f"    Diferencia: {abs(oracle_count - s3_count):,}")
            return False
        
        # 5. HOMOLOGACIÓN CAMPO POR CAMPO
        print(f"\n5️⃣ HOMOLOGACIÓN CAMPO POR CAMPO:")
        print("-" * 60)
        
        resultados_homologacion = {}
        
        for i, campo in enumerate(campos_criticos):
            print(f"\n  📋 CAMPO: {campo}")
            print(f"  {'-' * 50}")
            
            # Extraer valores del campo específico
            oracle_valores = [str(row[i]) if row[i] is not None else 'NULL' for row in oracle_data]
            s3_valores = [str(row[i]) if row[i] is not None else 'NULL' for row in s3_data]
            
            # Comparar valores
            coincidencias = 0
            diferencias = []
            
            for j, (oracle_val, s3_val) in enumerate(zip(oracle_valores, s3_valores)):
                if oracle_val == s3_val:
                    coincidencias += 1
                else:
                    if len(diferencias) < 10:  # Limitar ejemplos
                        diferencias.append({
                            'posicion': j + 1,
                            'transaction_id': oracle_data[j][0],
                            'oracle': oracle_val,
                            's3': s3_val
                        })
            
            # Calcular porcentaje
            total_registros = len(oracle_valores)
            porcentaje = (coincidencias / total_registros * 100) if total_registros > 0 else 0
            
            resultados_homologacion[campo] = {
                'coincidencias': coincidencias,
                'total': total_registros,
                'porcentaje': porcentaje,
                'diferencias': diferencias
            }
            
            # Mostrar resultados
            if porcentaje == 100.0:
                print(f"    ✅ PERFECTO: {porcentaje:.1f}% ({coincidencias:,}/{total_registros:,})")
            else:
                print(f"    ❌ DIFERENCIAS: {porcentaje:.1f}% ({coincidencias:,}/{total_registros:,})")
                print(f"    🔍 Diferencias encontradas: {len(diferencias)}")
                
                # Mostrar ejemplos de diferencias
                for diff in diferencias[:5]:
                    print(f"      Pos {diff['posicion']}: TxnID={diff['transaction_id']}")
                    print(f"        Oracle: '{diff['oracle']}'")
                    print(f"        S3: '{diff['s3']}'")
        
        # 6. ANÁLISIS DE VALORES ÚNICOS
        print(f"\n6️⃣ ANÁLISIS DE VALORES ÚNICOS:")
        print("-" * 60)
        
        for i, campo in enumerate(campos_criticos):
            oracle_unicos = set(str(row[i]) if row[i] is not None else 'NULL' for row in oracle_data)
            s3_unicos = set(str(row[i]) if row[i] is not None else 'NULL' for row in s3_data)
            
            oracle_count_unicos = len(oracle_unicos)
            s3_count_unicos = len(s3_unicos)
            
            print(f"  📊 {campo}:")
            print(f"    Oracle únicos: {oracle_count_unicos:,}")
            print(f"    S3 únicos: {s3_count_unicos:,}")
            
            if oracle_count_unicos == s3_count_unicos:
                print(f"    ✅ Coinciden en cantidad")
            else:
                print(f"    ❌ Diferencia: {abs(oracle_count_unicos - s3_count_unicos):,}")
                
                # Valores solo en Oracle
                solo_oracle = oracle_unicos - s3_unicos
                if solo_oracle:
                    print(f"    🔍 Solo en Oracle: {len(solo_oracle)} valores")
                    for val in list(solo_oracle)[:3]:
                        print(f"      '{val}'")
                
                # Valores solo en S3
                solo_s3 = s3_unicos - oracle_unicos
                if solo_s3:
                    print(f"    🔍 Solo en S3: {len(solo_s3)} valores")
                    for val in list(solo_s3)[:3]:
                        print(f"      '{val}'")
        
        cursor.close()
        connection.close()
        conn.close()
        
        # 7. REPORTE FINAL DE HOMOLOGACIÓN
        print(f"\n7️⃣ REPORTE FINAL DE HOMOLOGACIÓN:")
        print("-" * 60)
        
        total_campos_perfectos = 0
        
        print(f"{'CAMPO':<20} {'PORCENTAJE':<12} {'COINCIDENCIAS':<15} {'ESTADO'}")
        print("-" * 65)
        
        for campo in campos_criticos:
            resultado = resultados_homologacion[campo]
            porcentaje = resultado['porcentaje']
            coincidencias = resultado['coincidencias']
            total = resultado['total']
            
            if porcentaje == 100.0:
                estado = "✅ PERFECTO"
                total_campos_perfectos += 1
            else:
                estado = "❌ DIFERENCIAS"
            
            print(f"{campo:<20} {porcentaje:>8.1f}% {coincidencias:>7,}/{total:<7,} {estado}")
        
        # 8. CONCLUSIÓN FINAL
        print(f"\n8️⃣ CONCLUSIÓN FINAL:")
        print("-" * 60)
        
        if total_campos_perfectos == len(campos_criticos):
            print(f"🎉 ¡HOMOLOGACIÓN 100% PERFECTA!")
            print(f"✅ Todos los {len(campos_criticos)} campos críticos coinciden perfectamente")
            print(f"✅ {oracle_count:,} registros homologados exitosamente")
            print(f"🏆 LOG_TRX_FINAL: 'COMO DOS GOTAS DE AGUA'")
            return True
        else:
            campos_con_diferencias = len(campos_criticos) - total_campos_perfectos
            print(f"❌ HOMOLOGACIÓN INCOMPLETA:")
            print(f"✅ Campos perfectos: {total_campos_perfectos}/{len(campos_criticos)}")
            print(f"❌ Campos con diferencias: {campos_con_diferencias}")
            print(f"🔧 CORRECCIÓN NECESARIA para lograr 100% perfección")
            
            # Mostrar campos que necesitan corrección
            print(f"\n🔍 CAMPOS QUE REQUIEREN CORRECCIÓN:")
            for campo in campos_criticos:
                resultado = resultados_homologacion[campo]
                if resultado['porcentaje'] < 100.0:
                    print(f"  ❌ {campo}: {resultado['porcentaje']:.1f}%")
            
            return False
        
    except Exception as e:
        print(f"❌ Error en homologación: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("🚀 HOMOLOGACIÓN LOG_TRX_FINAL")
    print("=" * 80)
    print("OBJETIVO: 100% PERFECCIÓN ABSOLUTA")
    print("ESTÁNDAR: 'Como dos gotas de agua'")
    print()
    
    exito = homologar_log_trx_final()
    
    if exito:
        print(f"\n🏁 ¡HOMOLOGACIÓN EXITOSA!")
        print(f"🎯 LOG_TRX_FINAL: 100% PERFECTO")
    else:
        print(f"\n🏁 HOMOLOGACIÓN REQUIERE CORRECCIONES")
        print(f"🔧 Analizar diferencias y aplicar correcciones")

if __name__ == "__main__":
    main()
