#!/usr/bin/env python3
"""
CODE NINJA: Investigación Origen S3 de los 57 Casos
Rastrear en qué tablas origen S3 se encuentran estos casos
"""

import duckdb
import boto3

def investigar_origen_s3_57_casos():
    """Investigar origen S3 de los 57 casos específicos"""
    print("🥷 CODE NINJA: INVESTIGACIÓN ORIGEN S3 DE LOS 57 CASOS")
    print("=" * 80)
    
    # Los 57 casos identificados
    casos_57 = [
        '175020514200061', '175020514200182', '175020514200241', '175020514200421', '175020514200530',
        '175020514200646', '175020514200728', '175020514200898', '175020514200959', '175020514201099',
        '175020514201116', '175020514201208', '175020514201436', '175020514201546', '175020514201647',
        '175020514201723', '175020514201817', '175020514201946', '175020514202084', '175020514202162',
        '175020514202275', '175020514202363', '175020514202579', '175020514202681', '175020514202724',
        '175020514202865', '175020514202972', '175020514203090', '175020514203132', '175020514203215',
        '175020514203322', '175020514203490', '175020514203653', '175020514203745', '175020514203909',
        '175020514204059', '175020514204182', '175020514204290', '175020514204351', '175020514204462',
        '175020514204530', '175020514204717', '175020514204983', '175020514205033', '175020514205132',
        '175020514205361', '175020514205488', '175020514205532', '175020514205638', '175020514205826',
        '175020514205920', '175020514206091', '175020514206185', '175020514299909', '175029151203224',
        '175029151206399', '5000077909'
    ]
    
    try:
        # CONFIGURAR DUCKDB + S3
        conn = duckdb.connect()
        session = boto3.Session()
        credentials = session.get_credentials().get_frozen_credentials()
        
        conn.sql("INSTALL httpfs;")
        conn.sql("LOAD httpfs;")
        conn.sql("SET s3_region='us-east-1';")
        conn.sql("SET s3_use_ssl=true;")
        conn.sql("SET s3_url_style='path';")
        conn.sql(f"SET s3_access_key_id='{credentials.access_key}';")
        conn.sql(f"SET s3_secret_access_key='{credentials.secret_key}';")
        if credentials.token:
            conn.sql(f"SET s3_session_token='{credentials.token}';")
        
        print("  ✅ Conexión S3 establecida")
        
        # Convertir lista a string para SQL
        casos_str = "', '".join(casos_57)
        
        # 1. BUSCAR EN MTX_TRANSACTION_HEADER S3 (2025-06-18)
        print(f"\n1️⃣ BÚSQUEDA EN MTX_TRANSACTION_HEADER S3 (2025-06-18):")
        print("-" * 60)
        
        try:
            header_18 = conn.execute(f"""
                SELECT 
                    TRANSFER_ID,
                    TRANSFER_DATE,
                    TRANSFER_STATUS,
                    TRANSFER_VALUE,
                    SERVICE_TYPE
                FROM read_parquet('s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_HEADER_ORA/2025/06/18/*.parquet')
                WHERE TRANSFER_ID IN ('{casos_str}')
                ORDER BY TRANSFER_ID
            """).fetchall()
            
            print(f"  📊 Casos encontrados en HEADER 2025-06-18: {len(header_18)}")
            
            if header_18:
                print(f"  📋 Detalles encontrados:")
                print(f"  {'TRANSFER_ID':<20} {'TRANSFER_DATE':<12} {'STATUS':<8} {'VALUE':<12} {'SERVICE_TYPE'}")
                print("-" * 80)
                for row in header_18:
                    print(f"  {row[0]:<20} {str(row[1])[:10]:<12} {row[2]:<8} {row[3]:<12} {row[4]}")
            
        except Exception as e:
            print(f"  ❌ Error en HEADER 2025-06-18: {str(e)[:100]}...")
        
        # 2. BUSCAR EN MTX_TRANSACTION_HEADER S3 (2025-06-17)
        print(f"\n2️⃣ BÚSQUEDA EN MTX_TRANSACTION_HEADER S3 (2025-06-17):")
        print("-" * 60)
        
        try:
            header_17 = conn.execute(f"""
                SELECT 
                    TRANSFER_ID,
                    TRANSFER_DATE,
                    TRANSFER_STATUS,
                    TRANSFER_VALUE,
                    SERVICE_TYPE
                FROM read_parquet('s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_HEADER_ORA/2025/06/17/*.parquet')
                WHERE TRANSFER_ID IN ('{casos_str}')
                ORDER BY TRANSFER_ID
            """).fetchall()
            
            print(f"  📊 Casos encontrados en HEADER 2025-06-17: {len(header_17)}")
            
            if header_17:
                print(f"  📋 Detalles encontrados:")
                print(f"  {'TRANSFER_ID':<20} {'TRANSFER_DATE':<12} {'STATUS':<8} {'VALUE':<12} {'SERVICE_TYPE'}")
                print("-" * 80)
                for row in header_17:
                    print(f"  {row[0]:<20} {str(row[1])[:10]:<12} {row[2]:<8} {row[3]:<12} {row[4]}")
            
        except Exception as e:
            print(f"  ❌ Error en HEADER 2025-06-17: {str(e)[:100]}...")
        
        # 3. BUSCAR EN MTX_TRANSACTION_HEADER S3 (2025-06-19)
        print(f"\n3️⃣ BÚSQUEDA EN MTX_TRANSACTION_HEADER S3 (2025-06-19):")
        print("-" * 60)
        
        try:
            header_19 = conn.execute(f"""
                SELECT 
                    TRANSFER_ID,
                    TRANSFER_DATE,
                    TRANSFER_STATUS,
                    TRANSFER_VALUE,
                    SERVICE_TYPE
                FROM read_parquet('s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_HEADER_ORA/2025/06/19/*.parquet')
                WHERE TRANSFER_ID IN ('{casos_str}')
                ORDER BY TRANSFER_ID
            """).fetchall()
            
            print(f"  📊 Casos encontrados en HEADER 2025-06-19: {len(header_19)}")
            
            if header_19:
                print(f"  📋 Detalles encontrados:")
                print(f"  {'TRANSFER_ID':<20} {'TRANSFER_DATE':<12} {'STATUS':<8} {'VALUE':<12} {'SERVICE_TYPE'}")
                print("-" * 80)
                for row in header_19:
                    print(f"  {row[0]:<20} {str(row[1])[:10]:<12} {row[2]:<8} {row[3]:<12} {row[4]}")
            
        except Exception as e:
            print(f"  ❌ Error en HEADER 2025-06-19: {str(e)[:100]}...")
        
        # 4. BUSCAR EN MTX_TRANSACTION_ITEMS S3
        print(f"\n4️⃣ BÚSQUEDA EN MTX_TRANSACTION_ITEMS S3:")
        print("-" * 60)
        
        fechas_items = ['2025/06/17', '2025/06/18', '2025/06/19']
        total_items_encontrados = 0
        
        for fecha in fechas_items:
            try:
                items = conn.execute(f"""
                    SELECT COUNT(*), MIN(TRANSFER_ID), MAX(TRANSFER_ID)
                    FROM read_parquet('s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_ITEMS_ORA/{fecha}/*.parquet')
                    WHERE TRANSFER_ID IN ('{casos_str}')
                """).fetchone()
                
                if items and items[0] > 0:
                    total_items_encontrados += items[0]
                    print(f"  📊 {fecha}: {items[0]} items encontrados ({items[1]} - {items[2]})")
                else:
                    print(f"  📊 {fecha}: 0 items encontrados")
                    
            except Exception as e:
                print(f"  ❌ Error en ITEMS {fecha}: {str(e)[:50]}...")
        
        print(f"  📊 Total items encontrados: {total_items_encontrados}")
        
        # 5. VERIFICAR EN PRE_LOG_TRX S3 (nuestro archivo generado)
        print(f"\n5️⃣ VERIFICACIÓN EN PRE_LOG_TRX S3 (NUESTRO ARCHIVO):")
        print("-" * 60)
        
        try:
            # Verificar si el archivo PRE_LOG_TRX existe y tiene datos
            pre_log_path = '/home/<USER>/REPORTE_2025/reports/generate/S3_LOG_TRANSACCIONES/TEMP_LOGS_TRANSACCIONES/20250618/PRE_LOG_TRX.parquet'
            
            # Primero verificar estructura del archivo
            estructura = conn.execute(f"""
                SELECT COUNT(*) as total_records
                FROM read_parquet('{pre_log_path}')
                LIMIT 1
            """).fetchone()
            
            print(f"  📊 Total registros en PRE_LOG_TRX: {estructura[0]:,}")
            
            # Buscar nuestros casos específicos
            casos_en_pre = conn.execute(f"""
                SELECT 
                    "TransferID",
                    "TransferDate",
                    "TransactionType",
                    "Amount",
                    "Context"
                FROM read_parquet('{pre_log_path}')
                WHERE "TransferID" IN ('{casos_str}')
                ORDER BY "TransferID"
            """).fetchall()
            
            print(f"  📊 Casos encontrados en PRE_LOG_TRX: {len(casos_en_pre)}")
            
            if casos_en_pre:
                print(f"  📋 Detalles en PRE_LOG_TRX:")
                print(f"  {'TransferID':<20} {'TransferDate':<12} {'Type':<25} {'Amount':<10} {'Context'}")
                print("-" * 90)
                for row in casos_en_pre:
                    context = str(row[4]) if row[4] is not None else 'NULL'
                    print(f"  {row[0]:<20} {str(row[1])[:10]:<12} {row[2]:<25} {row[3]:<10} {context}")
            
        except Exception as e:
            print(f"  ❌ Error en PRE_LOG_TRX: {str(e)[:100]}...")
        
        # 6. ANÁLISIS DE ORIGEN
        print(f"\n6️⃣ ANÁLISIS DE ORIGEN:")
        print("-" * 60)
        
        total_header = len(header_17) + len(header_18) + len(header_19) if 'header_17' in locals() and 'header_18' in locals() and 'header_19' in locals() else 0
        
        print(f"  📊 RESUMEN DE BÚSQUEDA:")
        print(f"    Total casos a buscar: {len(casos_57)}")
        print(f"    Encontrados en HEADER S3: {total_header}")
        print(f"    Encontrados en ITEMS S3: {total_items_encontrados}")
        print(f"    Encontrados en PRE_LOG_TRX: {len(casos_en_pre) if 'casos_en_pre' in locals() else 0}")
        print()
        
        print(f"  🔍 INTERPRETACIÓN:")
        if total_header == 0 and total_items_encontrados == 0:
            print(f"    ❌ Los 57 casos NO existen en tablas origen S3")
            print(f"    🤔 Posible origen: Generados durante el procesamiento")
            print(f"    🔍 Necesario investigar lógica de SP_PRE_LOG_TRX")
        elif total_header > 0:
            print(f"    ✅ {total_header} casos SÍ existen en tablas origen S3")
            print(f"    📊 Diferencia: {len(casos_57) - total_header} casos sin origen claro")
        
        if 'casos_en_pre' in locals() and len(casos_en_pre) == len(casos_57):
            print(f"    ✅ TODOS los casos están en PRE_LOG_TRX")
            print(f"    🎯 Origen: Generados por nuestro pipeline SP_PRE_LOG_TRX")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error en investigación origen S3: {e}")
        import traceback
        traceback.print_exc()

def main():
    print("🥷 CODE NINJA: INVESTIGACIÓN ORIGEN S3")
    print("=" * 80)
    print("OBJETIVO: Rastrear origen exacto de los 57 casos en S3")
    print()
    
    investigar_origen_s3_57_casos()
    
    print("\n🏁 INVESTIGACIÓN ORIGEN S3 COMPLETADA")

if __name__ == "__main__":
    main()
