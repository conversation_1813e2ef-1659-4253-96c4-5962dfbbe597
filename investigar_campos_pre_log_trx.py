#!/usr/bin/env python3
"""
Investigar campos exactos de PRE_LOG_TRX Oracle vs S3
"""

import oracledb
import duckdb
import boto3

def investigar_campos_pre_log_trx():
    """Investiga los campos exactos de PRE_LOG_TRX"""
    print("🔍 INVESTIGACIÓN: CAMPOS PRE_LOG_TRX Oracle vs S3")
    print("=" * 80)
    
    fecha = '2025-06-15'
    parquet_path = '/home/<USER>/REPORTE_2025/reports/generate/S3_LOG_TRANSACCIONES/TEMP_LOGS_TRANSACCIONES/20250615/PRE_LOG_TRX.parquet'
    
    try:
        # 1. VERIFIC<PERSON> CAMPOS EN ORACLE PRE_LOG_TRX
        print("1️⃣ CAMPOS EN ORACLE PRE_LOG_TRX:")
        print("-" * 60)
        
        connection = oracledb.connect(
            user='usr_datalake',
            password='U2024b1mD4t4l4k5',
            dsn='10.240.131.10:1521/MMONEY'
        )
        cursor = connection.cursor()
        
        # Obtener estructura de la tabla Oracle
        cursor.execute("""
            SELECT COLUMN_NAME, DATA_TYPE
            FROM ALL_TAB_COLUMNS
            WHERE TABLE_NAME = 'PRE_LOG_TRX'
            AND OWNER = 'USR_DATALAKE'
            ORDER BY COLUMN_ID
        """)
        
        oracle_columns = cursor.fetchall()
        
        print(f"  Campos en Oracle PRE_LOG_TRX:")
        for col_name, data_type in oracle_columns:
            print(f"    {col_name} ({data_type})")
        
        # 2. VERIFICAR CAMPOS EN S3 PRE_LOG_TRX
        print(f"\n2️⃣ CAMPOS EN S3 PRE_LOG_TRX:")
        print("-" * 60)
        
        conn = duckdb.connect()
        session = boto3.Session()
        credentials = session.get_credentials().get_frozen_credentials()
        
        conn.sql("INSTALL httpfs;")
        conn.sql("LOAD httpfs;")
        conn.sql("SET s3_region='us-east-1';")
        conn.sql("SET s3_use_ssl=true;")
        conn.sql("SET s3_url_style='path';")
        conn.sql(f"SET s3_access_key_id='{credentials.access_key}';")
        conn.sql(f"SET s3_secret_access_key='{credentials.secret_key}';")
        if credentials.token:
            conn.sql(f"SET s3_session_token='{credentials.token}';")
        
        # Obtener estructura del parquet S3
        s3_columns = conn.execute(f"DESCRIBE SELECT * FROM read_parquet('{parquet_path}') LIMIT 1").fetchall()
        
        print(f"  Campos en S3 PRE_LOG_TRX:")
        for col_name, data_type, null_type, key, default, extra in s3_columns:
            print(f"    {col_name} ({data_type})")
        
        # 3. COMPARAR CAMPOS ESPECÍFICOS
        print(f"\n3️⃣ COMPARAR CAMPOS ESPECÍFICOS:")
        print("-" * 60)
        
        oracle_field_names = [col[0] for col in oracle_columns]
        s3_field_names = [col[0] for col in s3_columns]
        
        campos_criticos = ['FromID', 'ToID', 'From_AccountID', 'To_AccountID', 'FromID_Mobiquity', 'ToID_Mobiquity', 'From_AccountID_Mobiquity', 'To_AccountID_Mobiquity']
        
        print(f"  Verificación de campos críticos:")
        for campo in campos_criticos:
            oracle_tiene = campo in oracle_field_names
            s3_tiene = campo in s3_field_names
            
            oracle_icon = "✅" if oracle_tiene else "❌"
            s3_icon = "✅" if s3_tiene else "❌"
            
            print(f"    {campo:<25} Oracle: {oracle_icon} S3: {s3_icon}")
        
        # 4. VERIFICAR VALORES ESPECÍFICOS
        print(f"\n4️⃣ VERIFICAR VALORES ESPECÍFICOS:")
        print("-" * 60)
        
        # Verificar un registro específico en Oracle
        cursor.execute("""
            SELECT "TransferID", "FromID", "ToID", "From_AccountID", "To_AccountID"
            FROM USR_DATALAKE.PRE_LOG_TRX
            WHERE "TransferID" = '**********'
        """)
        
        oracle_sample = cursor.fetchone()
        if oracle_sample:
            print(f"  Oracle PRE_LOG_TRX (TransferID=**********):")
            print(f"    TransferID: {oracle_sample[0]}")
            print(f"    FromID: {oracle_sample[1]}")
            print(f"    ToID: {oracle_sample[2]}")
            print(f"    From_AccountID: {oracle_sample[3]}")
            print(f"    To_AccountID: {oracle_sample[4]}")
        
        # Verificar el mismo registro en S3
        s3_sample = conn.execute(f"""
            SELECT "TransferID", "FromID", "ToID", "From_AccountID_Mobiquity", "To_AccountID_Mobiquity"
            FROM read_parquet('{parquet_path}')
            WHERE "TransferID" = '**********'
        """).fetchone()
        
        if s3_sample:
            print(f"\n  S3 PRE_LOG_TRX (TransferID=**********):")
            print(f"    TransferID: {s3_sample[0]}")
            print(f"    FromID: {s3_sample[1]}")
            print(f"    ToID: {s3_sample[2]}")
            print(f"    From_AccountID_Mobiquity: {s3_sample[3]}")
            print(f"    To_AccountID_Mobiquity: {s3_sample[4]}")
        
        # 5. VERIFICAR USER_ACCOUNT_HISTORY
        print(f"\n5️⃣ VERIFICAR USER_ACCOUNT_HISTORY:")
        print("-" * 60)
        
        # Verificar si existe USER_ACCOUNT_HISTORY en Oracle
        cursor.execute("""
            SELECT COUNT(*)
            FROM ALL_TABLES
            WHERE TABLE_NAME = 'USER_ACCOUNT_HISTORY'
            AND OWNER = 'USR_DATALAKE'
        """)
        
        oracle_uah_exists = cursor.fetchone()[0] > 0
        print(f"  Oracle USER_ACCOUNT_HISTORY existe: {'✅' if oracle_uah_exists else '❌'}")
        
        if oracle_uah_exists:
            cursor.execute("""
                SELECT COLUMN_NAME, DATA_TYPE
                FROM ALL_TAB_COLUMNS
                WHERE TABLE_NAME = 'USER_ACCOUNT_HISTORY'
                AND OWNER = 'USR_DATALAKE'
                ORDER BY COLUMN_ID
            """)
            
            uah_columns = cursor.fetchall()
            print(f"  Campos en USER_ACCOUNT_HISTORY:")
            for col_name, data_type in uah_columns[:10]:  # Primeros 10
                print(f"    {col_name} ({data_type})")
        
        cursor.close()
        connection.close()
        conn.close()
        
        # 6. CONCLUSIONES
        print(f"\n6️⃣ CONCLUSIONES:")
        print("-" * 60)
        print(f"1. Verificar diferencias en nombres de campos")
        print(f"2. Oracle SP_LOG_TRX usa campos diferentes a los que genero")
        print(f"3. Necesario ajustar mapeo de campos en SP_LOG_TRX")
        
    except Exception as e:
        print(f"❌ Error en investigación: {e}")
        import traceback
        traceback.print_exc()

def main():
    print("🚀 INVESTIGACIÓN CAMPOS PRE_LOG_TRX")
    print("=" * 80)
    
    investigar_campos_pre_log_trx()
    
    print("\n🏁 INVESTIGACIÓN COMPLETADA")

if __name__ == "__main__":
    main()
